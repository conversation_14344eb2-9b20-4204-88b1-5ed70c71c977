import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateIncoterms1752117899287 implements MigrationInterface {
    name = 'UpdateIncoterms1752117899287'
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "incotermId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "currencyId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD CONSTRAINT "FK_35b12d66dbfe7eaf03fcff48852" FOREIGN KEY ("incotermId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD CONSTRAINT "FK_9dd70361cd74e381382bc1ba258" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP CONSTRAINT "FK_9dd70361cd74e381382bc1ba258"`);
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP CONSTRAINT "FK_35b12d66dbfe7eaf03fcff48852"`);
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "currencyId"`);
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "incotermId"`);
    }
}
