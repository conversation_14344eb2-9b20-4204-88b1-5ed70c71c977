import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColum1752315348697 implements MigrationInterface {
    name = 'AddColum1752315348697'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "request_quote" ADD "requestQuoteParentId" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "request_quote" DROP COLUMN "requestQuoteParentId"`);
    }

}
