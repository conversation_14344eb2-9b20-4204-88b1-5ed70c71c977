import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { SupplierCapacityEntity } from './supplierCapacity.entity'

/** <PERSON><PERSON><PERSON> giá trị theo năm */
@Entity('supplier_capacity_year_value')
export class SupplierCapacityYearValueEntity extends BaseEntity {
  @Column({
    nullable: false,
  })
  value: string

  @Column({
    nullable: false,
  })
  year: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierCapacityId: string
  @ManyToOne(() => SupplierCapacityEntity, (p) => p.supplierCapacityYearValues)
  @JoinColumn({ name: 'supplierCapacityId', referencedColumnName: 'id' })
  supplierCapacity: Promise<SupplierCapacityEntity>
}
