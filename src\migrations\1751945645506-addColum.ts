import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColum1751945645506 implements MigrationInterface {
  name = 'AddColum1751945645506'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "request_quote_detail" ADD "externalMaterialGroupId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "request_quote_detail" ADD "unitId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "request_quote" ADD "code" varchar(100) NOT NULL`)
    await queryRunner.query(
      `ALTER TABLE "request_quote_detail" ADD CONSTRAINT "FK_5c48fb2ddadff0d2c065dc93d27" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_detail" ADD CONSTRAINT "FK_a756799dfa6352490b5d1ef2e56" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "request_quote_detail" DROP CONSTRAINT "FK_a756799dfa6352490b5d1ef2e56"`)
    await queryRunner.query(`ALTER TABLE "request_quote_detail" DROP CONSTRAINT "FK_5c48fb2ddadff0d2c065dc93d27"`)
    await queryRunner.query(`ALTER TABLE "request_quote" DROP COLUMN "code"`)
    await queryRunner.query(`ALTER TABLE "request_quote_detail" DROP COLUMN "unitId"`)
    await queryRunner.query(`ALTER TABLE "request_quote_detail" DROP COLUMN "externalMaterialGroupId"`)
  }
}
