import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { OfferEntity } from './offer.entity'
import { BaseEntity } from './base.entity'
import { ShipmentCostPriceEntity } from './shipmentCostPrice.entity'

@Entity('offer_shipment_price')
export class OfferShipmentPriceEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string
  @ManyToOne(() => OfferEntity, (p) => p.shipments)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPriceId: string
  @ManyToOne(() => ShipmentCostPriceEntity, (p) => p.shipmentPrice)
  @JoinColumn({ name: 'shipmentPriceId', referencedColumnName: 'id' })
  shipmentPrice: Promise<ShipmentCostPriceEntity>

  @Column({
    nullable: false,
  })
  value: number
}
