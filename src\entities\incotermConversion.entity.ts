import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { IncotermEntity } from './incoterm.entity'

@Entity('incoterm_conversion')
export class IncotermConversionEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermToId: string
  @ManyToOne(() => IncotermEntity, (p) => p.incotermToConversions)
  @JoinColumn({ name: 'incotermToId', referencedColumnName: 'id' })
  incotermTo: Promise<IncotermEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermFormId: string
  @ManyToOne(() => IncotermEntity, (p) => p.incotermFromConversions)
  @JoinColumn({ name: 'incotermFormId', referencedColumnName: 'id' })
  incotermForm: Promise<IncotermEntity>

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  lstSettingCostId: string[]

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  lstSettingCostCode: string[]

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  lstShipmentConditionTypeId: string[]

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  lstShipmentConditionTypeCode: string[]
}
