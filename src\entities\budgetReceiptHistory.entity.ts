import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BudgetReceiptEntity } from './budgetReceipt.entity'

@Entity('budget_receipt_history')
export class BudgetReceiptHistoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  createdByName: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  budgetReceiptId: string
  @ManyToOne(() => BudgetReceiptEntity, (p) => p.histories)
  @JoinColumn({ name: 'budgetReceiptId', referencedColumnName: 'id' })
  budgetReceipt: Promise<BudgetReceiptEntity>

  /** Trạng thái hiện tại */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  description: string
}
