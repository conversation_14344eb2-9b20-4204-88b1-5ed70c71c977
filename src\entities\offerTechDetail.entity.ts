import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { OfferTechEntity } from './offerTech.entity'

@Entity('offer_tech_list_detail')
export class OfferTechListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidTechId: string
  @ManyToOne(() => OfferTechEntity, (p) => p.offerTechListDetails)
  @JoinColumn({ name: 'bidTechId', referencedColumnName: 'id' })
  offerTech: Promise<OfferTechEntity>
}
