import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CompanyEntity } from './company.entity'
import { RoundUpContHistoryEntity } from './roundUpContHistory.entity'
import { PlantEntity } from './plant.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { BudgetReceiptEntity } from './budgetReceipt.entity'
import { PrEntity } from './pr.entity'

@Entity('round_up_cont')
export class RoundUpContEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string
  @ManyToOne(() => CompanyEntity, (p) => p.roundUpCont)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  @OneToMany(() => RoundUpContHistoryEntity, (p) => p.roundUpCont)
  histories: Promise<RoundUpContHistoryEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.roundUpCont)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /**  tháng - năm PR */
  @Column({
    type: 'datetime',
    nullable: true,
  })
  month: Date

  /** Quý */
  @Column({
    type: 'varchar',
    nullable: true,
    length: 10,
  })
  quarterly: string

  /**  năm */
  @Column({
    type: 'datetime',
    nullable: true,
  })
  year: Date

  /**  Số lượng tròn cont  */
  @Column({
    type: 'int',
    nullable: true,
  })
  numberCont: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.roundUpCont)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  /** Layout làm tròn cont (RoundUpContTemplateType) */
  @Column({
    length: 20,
    nullable: true,
  })
  type: string

  /**  CBM tròn cont */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  cbmRoundUp: number

  /** Loại Cont */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  contType: string

  /**  CBM cần bổ sung để tròn cont */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  cbmNeedRoundUp: number

  /**  CBM trung bình / Sku */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  averageCbmSku: number

  /**  Số lượng máy cần bổ sung  */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  numberOfMachines: number

  @OneToMany(() => BudgetReceiptEntity, (p) => p.roundUpCont)
  budgetReceipt: Promise<BudgetReceiptEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.roundUpCont)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  /**ID template động */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  templateExcelId: string

  /**Loại PR: Tổng hợp hoặc thông thường */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  prType: string

  /**Tên template động */
  @Column({
    type: 'nvarchar',
    nullable: true,
  })
  templateExcelName: string

  /**JSON lưu dữ liệu tính toán cho từng cont */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  dataCalculate: string

  /**JSON lưu dữ liệu cột động */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  colConfigData: string

  /**Excel template */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  excelId: string

  /**JSON lưu tính cho bảng phân bổ */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  dataPrAllocation: string
}
