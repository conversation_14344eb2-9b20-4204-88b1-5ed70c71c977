import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

@Entity('rate_data')
export class RateDataEntity extends BaseEntity {
  /* tên của entity đánh giá */
  @Column({
    length: 50,
    nullable: false,
  })
  entityName: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  targetId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  type: string

  @Column({
    nullable: true,
    default: false,
  })
  lock: boolean

  /** Điểm HĐXT giá */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  rateNumber: number
}
