import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOlumnSupplier1749974111465 implements MigrationInterface {
    name = 'AddOlumnSupplier1749974111465'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "supplierCode" varchar(100)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "supplierCode"`);
    }

}
