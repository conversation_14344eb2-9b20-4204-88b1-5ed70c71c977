import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeMigration1751338636408 implements MigrationInterface {
    name = 'ChangeMigration1751338636408'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "permission_approve" ADD "acceptedValueId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "totalValue" bigint`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "totalValue"`);
        await queryRunner.query(`ALTER TABLE "permission_approve" DROP COLUMN "acceptedValueId"`);
    }

}
