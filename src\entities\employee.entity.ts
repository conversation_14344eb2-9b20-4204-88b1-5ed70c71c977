import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyToOne, JoinColumn, OneToOne, OneToMany } from 'typeorm'
import { DepartmentEntity } from './department.entity'
import { UserEntity } from './user.entity'
import { ServiceAccessEntity } from './serviceAccess.entity'
import { BidEmployeeAccessEntity } from './bidEmployeeAccess.entity'
import { SupplierExpertiseEntity } from './supplierExpertise.entity'
import { ServiceEntity } from './service.entity'
import { SupplierExpertiseMemberEntity } from './supplierExpertiseMember.entity'
import { BidHistoryEntity } from './bidHistory.entity'
import { POHistoryEntity } from './poHistory.entity'
import { POMemberEntity } from './poMember.entity'
import { ContractMemberEntity } from './contractMember.entity'
import { ContractHistoryEntity } from './contractHistory.entity'
import { EmployeeNotifyEntity } from './employeeNotify.entity'
import { EmployeeWarningEntity } from './employeeWarning.entity'
import { CompanyEntity } from './company.entity'
import {
  OfferEntity,
  PlantEntity,
  PositionEntity,
  InboundEntity,
  PurchasingGroupEntity,
  PurchasingOrgEntity,
  BlockEntity,
  PartEntity,
  SettingRoleEntity,
  PrEntity,
} from '.'
import { EmployeePurchasingGroupEntity } from './employeePurchasingGroup.entity'
import { EmployeeRoleEntity } from './empRoleData.entity'
import { PermissionApproveEntity } from './permissionApprove.entity'
import { EvaluationHistoryPurchaseEmployeeEntity } from './evaluationHistoryPurchaseEmployee.entity'
import { ContractInspectionEmployeeEntity } from './contractInspectionEmployeeEntity.entity'
import { PoAcceptanceEmployeeEntity } from './poAcceptanceEmployee.entity'
import { ComplaintEntity } from './complaint.entity'
import { ComplaintEmployeeEntity } from './complaintEmployee.entity'
import { ComplaintItemCargoEntity } from './complaintItemCargo.entity'
import { ComplaintChatEntity } from './complaintChat.entity'
import { PoHistoryStatusExecutionEntity } from './poHistoryStatusExecution.entity'
import { BusinessPlanTemplateEntity } from './businessPlanTemplate.entity'
import { RecommendedPurchaseTemplateEntity } from './recommendedPurchaseTemplate.entity'
import { BusinessPlanEntity } from './businessPlan.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { ComplaintNotifyEntity } from './complaintNotify.entity'
import { PermissionIndividualEntity } from './permissionIndividual.entity'
import { TicketEvaluationKpiEmployeeEntity } from './ticketEvaluationKpiEmployee.entity'
import { ReservationEntity } from './reservation.entity'
import { TemplateEvaluationPotentialEntity } from './templateEvaluationPotential.entity'
import { RequestQuoteEntity } from './requestQuote.entity'

@Entity('employee')
export class EmployeeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  code: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  email: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  position: string

  // Số điện thoại
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  phone: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Số lượng KPI */
  @Column({
    nullable: false,
    type: 'float',
    default: 0,
  })
  kpi: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  companyCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string
  @ManyToOne(() => CompanyEntity, (p) => p.employee)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.employee)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  positionId: string
  @ManyToOne(() => PositionEntity, (p) => p.employee)
  @JoinColumn({ name: 'positionId', referencedColumnName: 'id' })
  ePosition: Promise<PositionEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  departmentId: string
  @ManyToOne(() => DepartmentEntity, (p) => p.employee)
  @JoinColumn({ name: 'departmentId', referencedColumnName: 'id' })
  department: Promise<DepartmentEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  blockId: string
  @ManyToOne(() => BlockEntity, (p) => p.employee)
  @JoinColumn({ name: 'blockId', referencedColumnName: 'id' })
  block: Promise<BlockEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  partId: string
  @ManyToOne(() => PartEntity, (p) => p.employee)
  @JoinColumn({ name: 'partId', referencedColumnName: 'id' })
  part: Promise<PartEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  userId: string

  @OneToOne(() => UserEntity, (p) => p.employee)
  @JoinColumn({ name: 'userId', referencedColumnName: 'id' })
  user: Promise<UserEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string
  @ManyToOne(() => PurchasingGroupEntity, (p) => p.employee)
  @JoinColumn({ name: 'purchasingGroupId', referencedColumnName: 'id' })
  purchasingGroup: Promise<PurchasingGroupEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string
  @ManyToOne(() => PurchasingOrgEntity, (p) => p.employee)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgCompanyId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgBlockId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgDepartmentId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgPartId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgPositionId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgTreeId: string

  /**  Nhân viên công ty holding ? */
  @Column({
    nullable: false,
    default: false,
  })
  isHolding: boolean

  // Nhân viên duyệt pháp lý
  @OneToMany(() => SupplierExpertiseEntity, (p) => p.approvedLaw)
  supplierExpertiseLaw: Promise<SupplierExpertiseEntity[]>

  // Nhân viên ban thẩm định
  @OneToMany(() => SupplierExpertiseMemberEntity, (p) => p.employee)
  supplierExpertiseMembers: Promise<SupplierExpertiseMemberEntity[]>

  @OneToMany(() => ServiceEntity, (p) => p.approveBy)
  serviceApprover: Promise<ServiceEntity[]>

  // Nhân viên có quyền truy cập vào loại hình dịch vụ này
  @OneToMany(() => ServiceAccessEntity, (p) => p.employee)
  serviceAccess: Promise<ServiceAccessEntity[]>

  // EmployeePurchasingGroupEntity
  @OneToMany(() => EmployeePurchasingGroupEntity, (p) => p.employee)
  employeePurchasingGroup: Promise<EmployeePurchasingGroupEntity[]>

  // Nhân viên có quyền truy cập vào loại gói thầu
  @OneToMany(() => BidEmployeeAccessEntity, (p) => p.employee)
  bidAccess: Promise<BidEmployeeAccessEntity[]>

  @OneToMany(() => OfferEntity, (p) => p.employee)
  offer: Promise<OfferEntity[]>

  /** Lịch sử bid */
  @OneToMany(() => BidHistoryEntity, (p) => p.employee)
  bidHistorys: Promise<BidHistoryEntity[]>

  // thành viên trong hd
  @OneToMany(() => ContractMemberEntity, (p) => p.employee)
  contractMembers: Promise<ContractMemberEntity[]>

  // Lịch sử hợp đồng
  @OneToMany(() => ContractHistoryEntity, (p) => p.employee)
  contractHistorys: Promise<ContractHistoryEntity[]>

  @OneToMany(() => POHistoryEntity, (p) => p.employee)
  poHistorys: Promise<POHistoryEntity[]>

  @OneToMany(() => POMemberEntity, (p) => p.employee)
  poMembers: Promise<POMemberEntity[]>

  /** Thông báo hệ thống */
  @OneToMany(() => EmployeeNotifyEntity, (p) => p.employee)
  employeeNotify: Promise<EmployeeNotifyEntity[]>

  // /** Thông báo hệ thống */
  @OneToMany(() => EmployeeRoleEntity, (p) => p.employee)
  employeeRole: Promise<EmployeeRoleEntity[]>

  /** Cảnh báo hệ thống */
  @OneToMany(() => EmployeeWarningEntity, (p) => p.employee)
  employeeWarning: Promise<EmployeeWarningEntity[]>

  /** Đánh giá lịch sử mua hàng */
  @OneToMany(() => EvaluationHistoryPurchaseEmployeeEntity, (p) => p.employee)
  evaluationPurchaseEmployees: Promise<EvaluationHistoryPurchaseEmployeeEntity[]>

  @OneToMany(() => PermissionApproveEntity, (p) => p.employee)
  permissionAapproves: Promise<PermissionApproveEntity[]>

  // Danh sách nhân viên nghiệm thu hợp đồng
  @OneToMany(() => ContractInspectionEmployeeEntity, (p) => p.employee)
  contractInspectionEmps: Promise<ContractInspectionEmployeeEntity[]>

  // DS Inbounds
  @OneToMany(() => InboundEntity, (p) => p.employeeIncharge)
  inbounds: Promise<InboundEntity[]>

  // Danh sách nhân viên nghiệm thu PO
  @OneToMany(() => PoAcceptanceEmployeeEntity, (p) => p.employee)
  poAcceptanceEmployees: Promise<PoAcceptanceEmployeeEntity[]>

  // Danh sách khiếu nại
  @OneToMany(() => ComplaintEntity, (p) => p.employee)
  complaints: Promise<ComplaintEntity[]>

  // Danh sách khiếu nại - nhân viên
  @OneToMany(() => ComplaintEmployeeEntity, (p) => p.employee)
  complaintEmployees: Promise<ComplaintEmployeeEntity[]>

  // Danh sách xử lí hàng hóa
  @OneToMany(() => ComplaintItemCargoEntity, (p) => p.employee)
  complaintItemCargos: Promise<ComplaintItemCargoEntity[]>

  // Danh sách chat người gửi
  @OneToMany(() => ComplaintChatEntity, (p) => p.employeeSender)
  complaintChatSenders: Promise<ComplaintChatEntity[]>

  /** Lịch sử trạng thái thực hiện PO */
  @OneToMany(() => PoHistoryStatusExecutionEntity, (p) => p.employee)
  poHistoryStatusExecutions: Promise<PoHistoryStatusExecutionEntity[]>

  /** Phương án kinh doanh */
  @OneToMany(() => BusinessPlanTemplateEntity, (p) => p.employee)
  businessPlanTemplates: Promise<BusinessPlanTemplateEntity[]>

  /** đề nghị mua hàng */
  @OneToMany(() => RecommendedPurchaseTemplateEntity, (p) => p.employee)
  recommendedPurchaseTemplates: Promise<RecommendedPurchaseTemplateEntity[]>

  @OneToMany(() => BusinessPlanEntity, (p) => p.employee)
  businessPlans: Promise<BusinessPlanEntity[]>

  @OneToMany(() => RecommendedPurchaseEntity, (p) => p.employee)
  recommendedPurchase: Promise<RecommendedPurchaseEntity[]>

  /** thông báo khiếu nại */
  @OneToMany(() => ComplaintNotifyEntity, (p) => p.employee)
  complaintNotifys: Promise<ComplaintNotifyEntity[]>

  /** Phân quyền cá nhân của nhân viên*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  permissionIndividualId: string
  @OneToOne(() => PermissionIndividualEntity, (p) => p.employee)
  @JoinColumn({ name: 'permissionIndividualId', referencedColumnName: 'id' })
  permissionIndividual: Promise<PermissionIndividualEntity>

  @OneToMany(() => SettingRoleEntity, (p) => p.employee)
  settingRoles: Promise<SettingRoleEntity[]>

  /** ds phiếu đánh giá KPI và NV */
  @OneToMany(() => TicketEvaluationKpiEmployeeEntity, (p) => p.employee)
  ticketEvaluationKpiEmployees: Promise<TicketEvaluationKpiEmployeeEntity[]>

  @OneToMany(() => PrEntity, (p) => p.requisitioner)
  pr: Promise<PrEntity[]>

  @OneToMany(() => ReservationEntity, (p) => p.requisitioner)
  reservations: Promise<ReservationEntity[]>

  @OneToMany(() => TemplateEvaluationPotentialEntity, (p) => p.employeeLawId)
  templateEvaluationLaws: Promise<TemplateEvaluationPotentialEntity[]>

  @OneToMany(() => TemplateEvaluationPotentialEntity, (p) => p.employeeCapacityId)
  templateEvaluationCapacities: Promise<TemplateEvaluationPotentialEntity[]>

  @OneToMany(() => RequestQuoteEntity, (p) => p.employee)
  requestQuotes: Promise<RequestQuoteEntity[]>
}
