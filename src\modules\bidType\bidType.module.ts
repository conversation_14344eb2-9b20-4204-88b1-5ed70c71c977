import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BidTypeService } from './bidType.service'
import { BidTypeController } from './bidType.controller'
import { BidTypeRepository } from '../../repositories'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BidTypeRepository]), OrganizationalPositionModule],
  controllers: [BidTypeController],
  providers: [BidTypeService],
})
export class BidTypeModule {}
