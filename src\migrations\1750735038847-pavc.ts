import { MigrationInterface, QueryRunner } from 'typeorm'

export class Pavc1750735038847 implements MigrationInterface {
  name = 'Pavc1750735038847'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "shipment_plan" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_17e3893b081e2134c6177bce423" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_95fcd2bc7a858562c620f409bc4" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "name" varchar(255) NOT NULL, "description" nvarchar(max), "status" varchar(50) NOT NULL, CONSTRAINT "PK_17e3893b081e2134c6177bce423" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "shipment_plan_number" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_be5c649439c43b626da658afcd2" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6d7be68a1de4609301fd9ae3e10" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "shipmentPlanId" uniqueidentifier, "shipmentConfigTemplateId" varchar(255), "shipmentPlanNumberType" varchar(255), "deliveryDate" date, "shipmentFeeConditionTypeCompactId" varchar(255), "shipmentFeeConditionTypeCompactCode" varchar(255), "shipmentFeeConditionTypeCompactValue" varchar(255), "shipmentPlanNumberConfigTable" nvarchar(max), CONSTRAINT "PK_be5c649439c43b626da658afcd2" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "shipment_plan_number_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_deef2a27f2a70758a9812b9548b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_9467f52403004cd4d23d0e84910" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "shipmentPlanNumberId" uniqueidentifier, "shipmentFeeConditionId" varchar(255), "value" varchar(255), "idValue" varchar(255), CONSTRAINT "PK_deef2a27f2a70758a9812b9548b" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "shipment_plan_number" ADD CONSTRAINT "FK_462767570a1faabe18564ad6041" FOREIGN KEY ("shipmentPlanId") REFERENCES "shipment_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "shipment_plan_number_detail" ADD CONSTRAINT "FK_dca3a6857310455091da77a5c4a" FOREIGN KEY ("shipmentPlanNumberId") REFERENCES "shipment_plan_number"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_plan_number_detail" DROP CONSTRAINT "FK_dca3a6857310455091da77a5c4a"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP CONSTRAINT "FK_462767570a1faabe18564ad6041"`)
    await queryRunner.query(`DROP TABLE "shipment_plan_number_detail"`)
    await queryRunner.query(`DROP TABLE "shipment_plan_number"`)
    await queryRunner.query(`DROP TABLE "shipment_plan"`)
  }
}
