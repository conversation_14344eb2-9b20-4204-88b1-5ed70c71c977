import { MigrationInterface, QueryRunner } from 'typeorm'

export class GenMediaFile1750251928623 implements MigrationInterface {
  name = 'GenMediaFile1750251928623'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "media_file" ADD "prId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "media_file" ADD CONSTRAINT "FK_112263190b66bd057682cedf346" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "media_file" DROP CONSTRAINT "FK_112263190b66bd057682cedf346"`)
    await queryRunner.query(`ALTER TABLE "media_file" DROP COLUMN "prId"`)
  }
}
