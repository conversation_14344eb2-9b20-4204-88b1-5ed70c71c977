import { Column, <PERSON>tity, JoinColumn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ShipmentConditionTypeEntity } from './shipmentCondictionType.entity'
import { ShipmentConditionTypeTemplateEntity } from './shipmentCondictionTypeTemplate.entity'
import { TransportationPlanEntity } from './transportationPlan.entity'

@Entity('transportation_plan_detail')
export class TransportationPlanDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  transportationPlanId: string
  @ManyToOne(() => TransportationPlanEntity, (p) => p.transportationPlanDetails)
  @JoinColumn({ name: 'transportationPlanId', referencedColumnName: 'id' })
  transportationPlan: Promise<TransportationPlanEntity>

  /* id của  shipment condiction type*/
  @Column({
    type: 'varchar',
    nullable: false,
  })
  shipmentConditionTypeId: string
  @ManyToOne(() => ShipmentConditionTypeEntity, (p) => p.transportationPlanDetails)
  @JoinColumn({ name: 'shipmentConditionTypeId', referencedColumnName: 'id' })
  shipmentConditionType: Promise<ShipmentConditionTypeEntity>

  /* Id của template được map ? */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentConditionTypeTemplateId: string
  @ManyToOne(() => ShipmentConditionTypeTemplateEntity, (p) => p.transportationPlanDetails)
  @JoinColumn({ name: 'shipmentConditionTypeTemplateId', referencedColumnName: 'id' })
  shipmentConditionTypeTemplate: Promise<ShipmentConditionTypeTemplateEntity>

  /** Giá của từng dòng trong phương án vận chuyển */
  @Column({ type: 'bigint', nullable: true })
  price?: number
}
