import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { RequestQuoteEntity } from './requestQuote.entity'
import { PrItemEntity } from './prItem.entity'
import { MaterialEntity } from './material.entity'
import { MaterialGroupEntity } from './materialGroup.entity'
import { PlantEntity } from './plant.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { UomEntity } from './uom.entity'

/** Yêu cầu báo giá */
@Entity('request_quote_detail')
export class RequestQuoteDetailEntity extends BaseEntity {
  /** Item Line */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  itemNo: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  requestQuoteId: string
  @ManyToOne(() => RequestQuoteEntity, (p) => p.requestQuoteDetails)
  @JoinColumn({ name: 'requestQuoteId', referencedColumnName: 'id' })
  requestQuote: Promise<RequestQuoteEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemId: string
  @ManyToOne(() => PrItemEntity, (p) => p.requestQuoteDetails)
  @JoinColumn({ name: 'prItemId', referencedColumnName: 'id' })
  prItem: Promise<PrItemEntity>

  /** Category */
  @Column({
    type: 'varchar',
    nullable: true,
    length: 1,
  })
  category: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.requestQuoteDetails)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string

  @ManyToOne(() => MaterialGroupEntity, (p) => p.requestQuoteDetails)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>

  /** Order */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  orderCode: string

  /** Order */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  orderName: string

  /** asset */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetCode: string

  /** asset Desc */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetDesc: string

  /** Công ty */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.requestQuoteDetails)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /** Kho hàng */
  @Column({
    length: 'max',
    nullable: true,
  })
  sloc: string

  @Column({ nullable: true, default: 0, type: 'float' })
  quantity: number

  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  /** tên hàng */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  shortText: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.requestQuoteDetails)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.requestQuoteDetails)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>
}
