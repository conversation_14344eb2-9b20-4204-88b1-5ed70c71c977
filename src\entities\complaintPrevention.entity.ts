import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ComplaintEntity } from './complaint.entity'
import { ComplaintItemEntity } from './complaintItem.entity'

/** Phòng ngừa khiếu nại */
@Entity('complaint_prevention')
export class ComplaintPreventionEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  complaintId: string
  @ManyToOne(() => ComplaintEntity, (p) => p.complaintPreventions)
  @JoinColumn({ name: 'complaintId', referencedColumnName: 'id' })
  complaint: Promise<ComplaintEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  complaintItemId: string
  @ManyToOne(() => ComplaintItemEntity, (p) => p.complaintPreventions)
  @JoinColumn({ name: 'complaintItemId', referencedColumnName: 'id' })
  complaintItem: Promise<ComplaintItemEntity>

  /**Code Group enumData: ActivityCode */
  @Column({ type: 'varchar', length: 50, nullable: true })
  activityCode: string

  /**Task Group enumData: ActivityGroup */
  @Column({ type: 'varchar', length: 50, nullable: true })
  activityGroup: string

  /**Acticity Code Text */
  @Column({ type: 'varchar', length: 250, nullable: true })
  activityCodeText: string

  /**Acticity Text */
  @Column({ type: 'varchar', length: 250, nullable: true })
  activityText: string

  /**Người tạo */
  @Column({ type: 'varchar', length: 250, nullable: true })
  employeeUser: string

  /**Start date */
  @Column({ nullable: true, type: 'datetime' })
  startDate: Date

  /**End date */
  @Column({ nullable: true, type: 'datetime' })
  endDate: Date
}
