import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'

@Entity('permission_employee')
export class PermissionEmployeeEntity extends BaseEntity {
  /**JSON Stringify of phân quyền */
  @Column({ type: 'varchar', length: 'max', nullable: true })
  roleStringify: string

  /** <PERSON><PERSON><PERSON> liên hệ với người dùng */
  @Column({ type: 'varchar', nullable: false })
  employeeId: string

  @OneToOne(() => EmployeeEntity, (employee) => employee.id)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>
}
