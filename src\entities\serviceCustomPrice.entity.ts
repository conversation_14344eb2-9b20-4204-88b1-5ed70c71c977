import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { ServiceEntity } from './service.entity'

@Entity('service_custom_price')
export class ServiceCustomPriceEntity extends BaseEntity {
  /** Số lượng */
  @Column({
    nullable: false,
    default: 0,
  })
  number: number

  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  /** Tên  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** <PERSON><PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** Kiểu dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: false,
    default: 'Number',
  })
  type: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unit: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currency: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  serviceId: string
  /** 1 công thức chỉ có 1 dịch vụ cha */
  @ManyToOne(() => ServiceEntity, (p) => p.customPrices)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>
}
