import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BankBranchEntity } from './bankBranch.entity'
import { SupplierBankEntity } from './supplierBank.entity'

/** Ngân hàng */
@Entity('bank')
export class BankEntity extends BaseEntity {
  /** Tên ngân hàng */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** Mã ngân hàng */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** <PERSON>ô tả */
  @Column({
    type: 'nvarchar',
    length: 4000,
    nullable: true,
  })
  description: string

  // n chi nhánh ngân hàng
  @OneToMany(() => BankBranchEntity, (e) => e.bank)
  bankBranchs: Promise<BankBranchEntity[]>

  // Tài khoản NH NCC
  @OneToMany(() => SupplierBankEntity, (e) => e.bank)
  supplierBanks: Promise<SupplierBankEntity[]>
}
