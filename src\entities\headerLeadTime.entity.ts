import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { TemplateLeadTimeEntity } from './templateLeadTime.entity'
import { PoLeadTimeEntity } from './poLeadTime.entity'

@Entity('header_lead_time')
export class HeaderLeadTimeEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: false,
  })
  code: string

  @OneToMany(() => TemplateLeadTimeEntity, (p) => p.headerLeadTimeId)
  templateLeadTimes: Promise<TemplateLeadTimeEntity[]>

  @OneToMany(() => PoLeadTimeEntity, (p) => p.headerLeadTimeId)
  poLeadTimes: Promise<PoLeadTimeEntity[]>
}
