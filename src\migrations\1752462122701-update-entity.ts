import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateEntity1752462122701 implements MigrationInterface {
  name = 'UpdateEntity1752462122701'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "business_template_group_plan" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cdbae8a9b3d5deb2615eedc4692" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_06b27fe3f350ef8c30a3a00f819" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "status" nvarchar(50), "plantId" uniqueidentifier, "businessTemplatePlanTypeId" uniqueidentifier, CONSTRAINT "PK_cdbae8a9b3d5deb2615eedc4692" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "business_template_group_plan" ADD CONSTRAINT "FK_9ede2e5b37fcb9f5d200b44cab0" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "business_template_group_plan" ADD CONSTRAINT "FK_fbc336f53a170500236cef8433e" FOREIGN KEY ("businessTemplatePlanTypeId") REFERENCES "business_template_plan_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_group_plan" DROP CONSTRAINT "FK_fbc336f53a170500236cef8433e"`)
    await queryRunner.query(`ALTER TABLE "business_template_group_plan" DROP CONSTRAINT "FK_9ede2e5b37fcb9f5d200b44cab0"`)
    await queryRunner.query(`DROP TABLE "business_template_group_plan"`)
  }
}
