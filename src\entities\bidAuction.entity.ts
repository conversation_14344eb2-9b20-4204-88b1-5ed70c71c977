import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyToOne, Join<PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { BidEntity } from './bid.entity'
import { BidAuctionSupplierEntity } from './bidAuctionSupplier.entity'
import { BidAuctionPriceEntity } from './bidAuctionPrice.entity'

@Entity('bid_auction')
export class BidAuctionEntity extends BaseEntity {
  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @Column({ type: 'bigint', nullable: true })
  timePlus: number

  @Column({ type: 'varchar', length: 250, nullable: true })
  timePlusType: string

  /** Thời điểm kết thúc */
  @Column({ nullable: true, type: 'datetime' })
  dateStartPlus: Date

  /** Thời điểm bắt đầu */
  @Column({ nullable: true, type: 'datetime' })
  dateStart: Date

  @Column({ type: 'bigint', nullable: true })
  timeApprove: number

  @Column({ type: 'bigint', nullable: true })
  step: number

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  fileUrl: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  timeApproveType: string

  /** Thời điểm kết thúc*/
  @Column({
    nullable: false,
    type: 'datetime',
  })
  endDate: Date

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.bidAuctions)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  /** Id của đấu giá cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId?: string
  /** Cha */
  @ManyToOne(() => BidAuctionEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: Promise<BidAuctionEntity>

  /** Con - 1 đấu giá sẽ có thể có nhiều đấu giá con */
  @OneToMany(() => BidAuctionEntity, (p) => p.parent)
  childs: Promise<BidAuctionEntity[]>

  @OneToMany(() => BidAuctionSupplierEntity, (p) => p.bidAuction)
  bidAuctionSupplier: Promise<BidAuctionSupplierEntity[]>

  @OneToMany(() => BidAuctionPriceEntity, (p) => p.bidAuction)
  bidAuctionPrice: Promise<BidAuctionPriceEntity[]>
}
