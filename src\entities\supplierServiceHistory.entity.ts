import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { SupplierServiceEntity } from './supplierService.entity'

/**
 * <PERSON><PERSON><PERSON> lại lịch sử chỉnh sửa điểm khi người dùng duyệt năng lực và muốn chỉnh sửa lại điểm này(userScore)
 */
@Entity('supplier_service_history')
export class SupplierServiceHistoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 200,
    nullable: false,
  })
  approver: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  dataJson: any

  /** SupplierServiceId */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierServiceId: string
  @ManyToOne(() => SupplierServiceEntity, (p) => p.supplierServiceHistories)
  @JoinColumn({ name: 'supplierServiceId', referencedColumnName: 'id' })
  supplierService: Promise<SupplierServiceEntity>
}
