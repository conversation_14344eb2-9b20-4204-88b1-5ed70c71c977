import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTable1750303124871 implements MigrationInterface {
    name = 'CreateTable1750303124871'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "FK_6691c9664e1adc1b068c9fe02f2"`);
        await queryRunner.query(`CREATE TABLE "reservation_norm_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a9c5044f2495508af07c26eec76" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3e7cbd93727925dfbfc5a13962b" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "code" varchar(250), "materialCode" varchar(255), "materialId" uniqueidentifier, "baseUnitNorm" float CONSTRAINT "DF_49bfe2c03d721430b2375ab142e" DEFAULT 0, "baseUnitQuantityRemind" float CONSTRAINT "DF_8a36df1f63f8b1da515d56ab309" DEFAULT 0, "unitId" uniqueidentifier, "norm" float CONSTRAINT "DF_b4190ce2d06986424703df4d8e5" DEFAULT 0, "quantityRemind" float CONSTRAINT "DF_a0ddd62d8bf0246dba60004ee5d" DEFAULT 0, "startDate" datetime, "expireDate" datetime, "reservationNormId" uniqueidentifier, "baseUnitId" uniqueidentifier, CONSTRAINT "PK_a9c5044f2495508af07c26eec76" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "uomAlternativeId"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "DF_7b82877095547e218d38397e4a3"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "normAlternative"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "year" float CONSTRAINT "DF_f13a520e8ddcf8b385dedf5d12e" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "reservation_norm_detail" ADD CONSTRAINT "FK_65ccc80c14a67f1e17cfd6d5a5e" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_norm_detail" ADD CONSTRAINT "FK_4a1f1f7b7cdcea407505da7a72d" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_norm_detail" ADD CONSTRAINT "FK_12aa0d22b91524b8772ba2692ef" FOREIGN KEY ("reservationNormId") REFERENCES "reservation_norm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_norm_detail" ADD CONSTRAINT "FK_15f68c9c3d6d63e66c25685f8ca" FOREIGN KEY ("baseUnitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_norm_detail" DROP CONSTRAINT "FK_15f68c9c3d6d63e66c25685f8ca"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm_detail" DROP CONSTRAINT "FK_12aa0d22b91524b8772ba2692ef"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm_detail" DROP CONSTRAINT "FK_4a1f1f7b7cdcea407505da7a72d"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm_detail" DROP CONSTRAINT "FK_65ccc80c14a67f1e17cfd6d5a5e"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "DF_f13a520e8ddcf8b385dedf5d12e"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "year"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "normAlternative" float`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD CONSTRAINT "DF_7b82877095547e218d38397e4a3" DEFAULT 0 FOR "normAlternative"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "uomAlternativeId" uniqueidentifier`);
        await queryRunner.query(`DROP TABLE "reservation_norm_detail"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD CONSTRAINT "FK_6691c9664e1adc1b068c9fe02f2" FOREIGN KEY ("uomAlternativeId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
