import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

/** Interface Tạo mới vùng bằng excel. */
export class BusinessPartnerGroupCreateByExcelDto {
  @ApiProperty({ description: 'Mã .' })
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty({ description: 'Tên .' })
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty({ description: 'Mô tả về .' })
  @IsOptional()
  @IsString()
  description: string

  @ApiProperty({ description: 'Mã bắt đầu .' })
  @IsOptional()
  startCode?: number

  @ApiProperty({ description: 'Mã kết thúc' })
  @IsOptional()
  endCode?: number
}
