import { MigrationInterface, QueryRunner } from 'typeorm'

export class BusinessTemplatePlan1753063948017 implements MigrationInterface {
  name = 'BusinessTemplatePlan1753063948017'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "shipmentFeeConditionTypeCompactId" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "shipmentFeeConditionTypeCompactCode" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "shipmentFeeConditionTypeCompactValue" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "currentcyId" varchar(150)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "currentcyId"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "shipmentFeeConditionTypeCompactValue"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "shipmentFeeConditionTypeCompactCode"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "shipmentFeeConditionTypeCompactId"`)
  }
}
