import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class ShipmentFeeConditionsListCreateDto {
  @ApiProperty({ description: 'Mã điều kiện phí list' })
  code: string

  @ApiProperty({ description: 'Tên điều kiện phí list' })
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty({ description: 'Mô tả', required: false })
  @IsString()
  @IsOptional()
  description?: string
}

export class ShipmentFeeConditionsListUpdateDto {
  @ApiProperty({ description: 'ID', required: true })
  @IsString()
  @IsOptional()
  id?: string

  @ApiProperty({ description: 'Mã điều kiện phí list', required: false })
  @IsString()
  @IsOptional()
  code?: string

  @ApiProperty({ description: 'Tên điều kiện phí list', required: false })
  @IsString()
  @IsOptional()
  name?: string

  @ApiProperty({ description: '<PERSON><PERSON> tả', required: false })
  @IsString()
  @IsOptional()
  description?: string
}
