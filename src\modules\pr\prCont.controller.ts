import { Controller, Get, Query } from '@nestjs/common'
import { PrService } from './pr.service'
import { ApiOperation, ApiTags } from '@nestjs/swagger'

@ApiTags('Pr')
@Controller('pr_cont')
export class PrContController {
  constructor(private readonly service: PrService) {}

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách PR theo id truyền vào' })
  @Get('find_pr_item')
  public async findPrItem(@Query('prId') prId: string) {
    return await this.service.findPrItem(prId)
  }
}
