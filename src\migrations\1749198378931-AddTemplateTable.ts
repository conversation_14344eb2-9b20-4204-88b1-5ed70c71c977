import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTemplateTable1749198378931 implements MigrationInterface {
  name = 'AddTemplateTable1749198378931'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "template" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_fbae2ac36bd9b5e1e793b957b7f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_174bbdfda9fae5387e6c897149e" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "name" nvarchar(255) NOT NULL, "link" nvarchar(255) NOT NULL, "type" nvarchar(255) NOT NULL, CONSTRAINT "PK_fbae2ac36bd9b5e1e793b957b7f" PRIMARY KEY ("id"))`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "template"`)
  }
}
