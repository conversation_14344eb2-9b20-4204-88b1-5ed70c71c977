import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { BusinessPlanTemplateEntity } from './businessPlanTemplate.entity'
import { BusinessPlanHistoryEntity } from './businessPlanHistory.entity'
import { BusinessPlanColValueEntity } from './businessPlanColValue.entity'
import { CurrencyEntity } from './currency.entity'
import { PaymentMethodEntity } from './paymentMethod.entity'
import { IncotermEntity } from './incoterm.entity'
import { MediaFileEntity } from './mediaFile.entity'
import { CompanyEntity } from './company.entity'
import { ContractEntity } from './contract.entity'
import { PlantEntity } from './plant.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { BusinessPlanSettingValueEntity } from './businessPlanSettingValue.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { PrEntity } from './pr.entity'
import { OfferEntity } from './offer.entity'
import { BidEntity } from './bid.entity'
import { BusinessPlanRfqEntity } from './businessPlanRfq.entity'

@Entity('business_plan')
export class BusinessPlanEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.businessPlans)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanTemplateId: string
  @ManyToOne(() => BusinessPlanTemplateEntity, (p) => p.businessPlans)
  @JoinColumn({ name: 'businessPlanTemplateId', referencedColumnName: 'id' })
  businessPlanTemplate: Promise<BusinessPlanTemplateEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  currencyFromId: string
  @ManyToOne(() => CurrencyEntity, (p) => p.businessPlanCurrencyFrom)
  @JoinColumn({ name: 'currencyFromId', referencedColumnName: 'id' })
  currencyFrom: Promise<CurrencyEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  currencyToId: string
  @ManyToOne(() => CurrencyEntity, (p) => p.businessPlanCurrencyTo)
  @JoinColumn({ name: 'currencyToId', referencedColumnName: 'id' })
  currencyTo: Promise<CurrencyEntity>

  /** Tỷ giá */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  exchangeRate: number

  /** Phương thức thanh toán */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentMethodId: string
  @ManyToOne(() => PaymentMethodEntity, (p) => p.businessPlan)
  @JoinColumn({ name: 'paymentMethodId', referencedColumnName: 'id' })
  paymentMethod: Promise<PaymentMethodEntity>

  /** Điều kiện giao hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermId: string
  @ManyToOne(() => IncotermEntity, (p) => p.businessPlan)
  @JoinColumn({ name: 'incotermId', referencedColumnName: 'id' })
  incoterm: Promise<IncotermEntity>

  @OneToMany(() => BidEntity, (p) => p.businessPlan)
  bids: Promise<BidEntity[]>

  /** incoterm Description */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  incotermDescription: string

  /** địa điểm giao hàng */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  deliveryAddress: string

  /**  Thời gian dự kiến về kho */
  @Column({
    type: 'datetime',
    nullable: true,
  })
  estimatedTime: Date

  /** Nguồn mua */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  purchasingSource: string

  /** Nguồn bán */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  sellingSource: string

  /** Nhà cung cấp trung gian */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  intermediarySupplier: string

  /** sloc */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  sloc: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string
  @ManyToOne(() => CompanyEntity, (p) => p.businessPlan)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  @OneToMany(() => BusinessPlanHistoryEntity, (p) => p.businessPlan)
  histories: Promise<BusinessPlanHistoryEntity[]>

  @OneToMany(() => BusinessPlanColValueEntity, (p) => p.businessPlan)
  businessPlanColValues: Promise<BusinessPlanColValueEntity[]>

  @OneToMany(() => MediaFileEntity, (p) => p.businessPlan)
  mediaFiles: Promise<MediaFileEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.businessPlan)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.businessPlan)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @OneToMany(() => RecommendedPurchaseEntity, (p) => p.businessPlan)
  recommendedPurchase: Promise<RecommendedPurchaseEntity[]>

  @OneToMany(() => BusinessPlanSettingValueEntity, (p) => p.businessPlan)
  businessPlanSettingValue: Promise<BusinessPlanSettingValueEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.businessPlan)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  @OneToMany(() => OfferEntity, (p) => p.businessPlan)
  offer: Promise<OfferEntity[]>

  @OneToMany(() => BusinessPlanRfqEntity, (p) => p.businessPlan)
  businessPlanRfq: Promise<BusinessPlanRfqEntity[]>
}
