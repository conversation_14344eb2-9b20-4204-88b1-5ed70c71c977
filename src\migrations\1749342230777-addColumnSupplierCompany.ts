import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnSupplierCompany1749342230777 implements MigrationInterface {
    name = 'AddColumnSupplierCompany1749342230777'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier_company" ADD "supplierSource" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier_company" ADD "isApproveActive" bit CONSTRAINT "DF_43b1401d49e8576e467bb0f696e" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier_company" ADD "requestUpdateStatus" varchar(100)`);
        await queryRunner.query(`ALTER TABLE "supplier_company" ADD "isHO" bit CONSTRAINT "DF_7d7e11c0ffe39d304bb689db51d" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier_company" DROP CONSTRAINT "DF_7d7e11c0ffe39d304bb689db51d"`);
        await queryRunner.query(`ALTER TABLE "supplier_company" DROP COLUMN "isHO"`);
        await queryRunner.query(`ALTER TABLE "supplier_company" DROP COLUMN "requestUpdateStatus"`);
        await queryRunner.query(`ALTER TABLE "supplier_company" DROP CONSTRAINT "DF_43b1401d49e8576e467bb0f696e"`);
        await queryRunner.query(`ALTER TABLE "supplier_company" DROP COLUMN "isApproveActive"`);
        await queryRunner.query(`ALTER TABLE "supplier_company" DROP COLUMN "supplierSource"`);
    }

}
