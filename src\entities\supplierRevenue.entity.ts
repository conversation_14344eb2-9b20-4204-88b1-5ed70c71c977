import { <PERSON><PERSON><PERSON>, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { Transform } from 'class-transformer'

/** <PERSON><PERSON>h thu nhà cung cấp trong các năm gần đây */
@Entity('supplier_revenue')
export class SupplierRevenueEntity extends BaseEntity {
  @Column({ type: 'datetime', nullable: true })
  @Transform(({ value }) => (value ? new Date(value) : null))
  year: Date

  // Doanh thu
  @Column({
    type: 'bigint',
    nullable: true,
    default: 0,
  })
  revenue: number

  /** Nhà cung cấp  */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.supplierRevenues)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>
}
