import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BusinessTemplatePlanEntity } from './businessTemplatePlan.entity'

@Entity('business_template_plan_cost')
export class BusinessTemplatePlanCostEntity extends BaseEntity {
  /* id của PAKD(BusinessTemplatePlan) */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessTemplatePlanId: string
  @ManyToOne(() => BusinessTemplatePlanEntity, (c) => c.businessTemplatePlanCost)
  @JoinColumn({ name: 'businessTemplatePlanId', referencedColumnName: 'id' })
  businessTemplatePlan: Promise<BusinessTemplatePlanEntity>

  /* tên chi phí  */
  @Column({
    type: 'varchar',
    // length: 255,
    nullable: true,
  })
  name: string
  /* tổng chi phí */
  @Column({ type: 'bigint', nullable: true })
  totalPrice: number
  // json

  /* json config bảng */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        return JSON.stringify(value)
      },
      from(value) {
        return value ? JSON.parse(value) : {}
      },
    },
  })
  lstRfqConfig: any
}
