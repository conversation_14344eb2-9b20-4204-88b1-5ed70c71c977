import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class BusinessPlanTemplateCreateColExcelDto {
  @ApiProperty({ description: 'Mã Template làm tròn cont.' })
  @IsNotEmpty()
  @IsString()
  businessPlanTemplateCode: string

  @ApiProperty({ description: 'Danh sách.' })
  lstData: BusinessPlanTemplateCreateColExcelListDto[]
}

export class BusinessPlanTemplateCreateColExcelListDto {
  @ApiProperty({ description: 'Mã cột làm tròn cont.' })
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty({ description: 'Tên cột làm tròn cont.' })
  @IsNotEmpty()
  @IsString()
  name: string

  fomular: string

  isSystemData: boolean

  dataMapping: string

  isEdited: boolean

  colType: string

  isRequired: boolean

  sort: number

  businessPlanTemplateCode: string

  type: string
}
