import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateColum1750929900119 implements MigrationInterface {
    name = 'CreateColum1750929900119'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "pr_item" ADD "roundedAmountCont" decimal(20,4) CONSTRAINT "DF_df2c98ca0787bffcd6c8f05a990" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD "supplierInquiries" decimal(20,4) CONSTRAINT "DF_496caa61b00202d6760b0713579" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD "totalQuantity" decimal(20,4) CONSTRAINT "DF_8aec302933049b30dc7141f34d6" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "DF_8aec302933049b30dc7141f34d6"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "totalQuantity"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "DF_496caa61b00202d6760b0713579"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "supplierInquiries"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "DF_df2c98ca0787bffcd6c8f05a990"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "roundedAmountCont"`);
    }

}
