import { Entity, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm'
import { BaseEntity } from './base.entity'
import { HeaderLeadTimeEntity } from './headerLeadTime.entity'
import { POEntity } from './po.entity'

@Entity('po_lead_time')
export class PoLeadTimeEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  code: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  headerLeadTimeId: string
  @ManyToOne(() => HeaderLeadTimeEntity, (p) => p.poLeadTimes)
  @JoinColumn({ name: 'headerLeadTimeId', referencedColumnName: 'id' })
  headerLeadTime: Promise<HeaderLeadTimeEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.poLeadTimes)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  @Column({
    nullable: true,
  })
  numberOfDay: number

  @Column({
    nullable: true,
    default: 0,
  })
  sort: number

  /** Công thức */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fomular: string

  @Column({
    nullable: true,
    default: false,
  })
  isSum: boolean

  /**Số ngày do nhà cung cấp điền */
  @Column({
    nullable: true,
    default: 0,
  })
  numberOfDaySupplier: number
}
