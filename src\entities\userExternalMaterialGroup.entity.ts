import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { UserEntity } from './user.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'

@Entity({ name: 'user_external_material_group' })
export class UserExternalMaterialGroupEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  userId: string
  @ManyToOne(() => UserEntity, (user) => user.userExGr)
  @JoinColumn({ name: 'userId', referencedColumnName: 'id' })
  user: Promise<UserEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.userExGr)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>
  externalMaterialGroupDetail: ExternalMaterialGroupEntity
}
