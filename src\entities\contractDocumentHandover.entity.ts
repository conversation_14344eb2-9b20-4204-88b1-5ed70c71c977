import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { ContractEntity, InboundEntity, MediaFileEntity, SupplierEntity } from '.'

/** <PERSON><PERSON> sách bàn giao chứng từ của contract*/
@Entity('contract_document_handover')
export class ContractDocumentHandoverEntity extends BaseEntity {
  /** <PERSON><PERSON> chứng từ */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Tiêu đề chứng từ */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: false,
  })
  name: string

  /** ghi chú */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  note: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.contracyDocumentHandovers)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.contractDocumentHandovers)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  @OneToMany(() => MediaFileEntity, (p) => p.contracyDocumentHandover)
  mediaFiles: Promise<MediaFileEntity[]>
}
