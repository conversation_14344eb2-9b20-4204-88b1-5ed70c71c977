import { MigrationInterface, QueryRunner } from 'typeorm'

export class TransPlan1750314963128 implements MigrationInterface {
  name = 'TransPlan1750314963128'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "transportation_plan" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_153235558780f63c1e8f260a566" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_949cd2bbca9b39cd19861335a4e" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "shipmentId" varchar(250) NOT NULL, "submitPriceOld" bigint, CONSTRAINT "PK_153235558780f63c1e8f260a566" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "transportation_plan_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_65aeb019211ae7f02012b163a01" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2c2b1e0cbb3d6af4ba7c748961a" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "transportationPlanId" varchar(255) NOT NULL, "shipmentConditionTypeId" uniqueidentifier NOT NULL, "shipmentConditionTypeTemplateId" uniqueidentifier, "price" bigint, "TransportationPlanId" uniqueidentifier, CONSTRAINT "PK_65aeb019211ae7f02012b163a01" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "transportation_plan_detail" ADD CONSTRAINT "FK_bac2363017c8bddfc73468a9d22" FOREIGN KEY ("transportationPlanId") REFERENCES "transportation_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "transportation_plan_detail" ADD CONSTRAINT "FK_6286126796a711a990c934616ea" FOREIGN KEY ("shipmentConditionTypeId") REFERENCES "shipment_condition_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "transportation_plan_detail" ADD CONSTRAINT "FK_418fd2af6b6c21b60b7ad7020c9" FOREIGN KEY ("shipmentConditionTypeTemplateId") REFERENCES "shipment_condition_type_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "transportation_plan_detail" DROP CONSTRAINT "FK_418fd2af6b6c21b60b7ad7020c9"`)
    await queryRunner.query(`ALTER TABLE "transportation_plan_detail" DROP CONSTRAINT "FK_6286126796a711a990c934616ea"`)
    await queryRunner.query(`ALTER TABLE "transportation_plan_detail" DROP CONSTRAINT "FK_bac2363017c8bddfc73468a9d22"`)
    await queryRunner.query(`DROP TABLE "transportation_plan_detail"`)
    await queryRunner.query(`DROP TABLE "transportation_plan"`)
  }
}
