import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../common/guards'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { FilterOneDto } from '../../dto/filterOne.dto'
import { BusinessPartnerGroupService } from './businessPartnerGroup.service'
import { BusinessPartnerGroupCreateDto, BusinessPartnerGroupUpdateDto, BusinessPartnerGroupCreateByExcelDto } from './dto'

@ApiBearerAuth()
@ApiTags('BusinessPartnerGroup')
@UseGuards(JwtAuthGuard)
@Controller('business_partner_group')
export class BusinessPartnerGroupController {
  constructor(private readonly service: BusinessPartnerGroupService) {}

  @ApiOperation({ summary: 'L<PERSON>y danh sách vùng' })
  @Post('find')
  public async find() {
    return await this.service.find()
  }

  @ApiOperation({ summary: 'Danh sách vùng phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới một vùng với dữ liệu được cung cấp.' })
  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: BusinessPartnerGroupCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin của vùng với dữ liệu được cung cấp.' })
  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: BusinessPartnerGroupUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái kích hoạt của vùng.' })
  @Post('update_active')
  async updateActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Tạo mới một vùng với danh sách dữ liệu danh được cung cấp.' })
  @Post('import_data')
  async importData(@CurrentUser() user: UserDto, @Body() data: BusinessPartnerGroupCreateByExcelDto[]) {
    return await this.service.importData(user, data)
  }

  @ApiOperation({ summary: 'Load data select' })
  @Post('load_data_select')
  async loadDataSelect(@CurrentUser() user: UserDto) {
    return await this.service.loadDataSelect(user)
  }
}
