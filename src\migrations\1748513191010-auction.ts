import { MigrationInterface, QueryRunner } from "typeorm";

export class Auction1748513191010 implements MigrationInterface {
    name = 'Auction1748513191010'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "auction" ADD "plantId" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "auction" ADD "purchasingOrgId" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "auction" ADD "purchasingGroupId" nvarchar(250)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "auction" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "auction" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "auction" DROP COLUMN "plantId"`);
    }

}
