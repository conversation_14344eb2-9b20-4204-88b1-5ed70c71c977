export const enumData = {
  Page: {
    pageIndex: 1,
    pageSize: 10,
    pageSizeMax: 10000,
    total: 0,
  },

  UserType: {
    Employee: { code: 'Employee', name: '<PERSON><PERSON><PERSON> viên' },
    Admin: { code: 'Admin', name: 'Admin' },
    Supplier: { code: 'Supplier', name: '<PERSON><PERSON><PERSON> cung cấp' },
  },
  BiddingType: {
    PRODUCT: { code: 'PRODUCT', name: '<PERSON><PERSON>u thầu mua hàng hóa' },
    SHIPPING: { code: 'SHIPPING', name: 'Đ<PERSON>u thầu tìm đơn vị vận chuyển' },
  },

  SupplierStatus: {
    AwaitingConfirmation: {
      code: 'AwaitingConfirmation',
      name: 'Chờ xác nhận',
      color: '#0063D8',
      bgColor: '#DCEEFF',
      borderColor: '#2A7DDF',
    },
    LegalReview: {
      code: 'LegalReview',
      name: '<PERSON><PERSON> đánh giá pháp lý',
      color: '#9254de',
      bgColor: '#f9f0ff',
      borderColor: '#b37feb',
    },
    CapacityReview: {
      code: 'CapacityReview',
      name: 'Đang đánh giá năng lực',
      color: '#9254de',
      bgColor: '#f9f0ff',
      borderColor: '#b37feb',
    },
    InReview: {
      code: 'InReview',
      name: 'Đang duyệt',
      color: '#0063D8',
      bgColor: '#DCEEFF',
      borderColor: '#2A7DDF',
    },
    ReCheck: {
      code: 'ReCheck',
      name: 'Đang kiểm tra lại đánh giá',
      color: '#fa8c16',
      bgColor: '#fff7e6',
      borderColor: '#ffd591',
    },
    Failed: {
      code: 'Failed',
      name: 'Không đạt',
      color: 'red',
      bgColor: '#fff1f0',
      borderColor: '#ffa39e',
    },
    Active: {
      code: 'Active',
      name: 'Hoạt động',
      color: '#0A915B',
      bgColor: '#DEF2E0',
      borderColor: '#0A915B',
    },
    Locked: {
      code: 'Locked',
      name: 'Đang khóa',
      color: '#8c8c8c',
      bgColor: '#f0f0f0',
      borderColor: '#d9d9d9',
    },
  },

  // Trạng thái Pháp lý của NCC
  SupplierLegalStatus: {
    DangDuyet: { code: 'DangDuyet', name: 'Đang duyệt', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    TuChoi: { code: 'TuChoi', name: 'Từ chối', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    DaDuyet: { code: 'DaDuyet', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  // Trạng thái LVMH của NCC
  SupplierServiceStatus: {
    ChoXacNhan: {
      code: 'ChoXacNhan',
      name: 'Chờ xác nhận',
      color: 'gray',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
      color: 'orange',
    },
    KhongDat: {
      code: 'KhongDat',
      name: 'Không đạt',
      color: 'red',
    },
    HoatDong: {
      code: 'HoatDong',
      name: 'Hoạt động',
      color: 'green',
    },
    DangKhoa: {
      code: 'DangKhoa',
      name: 'Đang khóa',
      color: 'darkgray',
    },
  },

  // Trạng thái năng lực
  SupplierServiceStatusCapacity: {
    DangDuyet: {
      code: 'DangDuyet',
      name: 'Đang duyệt',
      color: '#0063D8',
      bgColor: '#DCEEFF',
      borderColor: '#2A7DDF',
    },
    TuChoi: { code: 'TuChoi', name: 'Từ chối', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    DaDuyet: { code: 'DaDuyet', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    NgungHoatDong: { code: 'NgungHoatDong', name: 'Ngừng hoạt động', color: 'darkred' },
  },
  SupplierServiceExpertiseStatus: {
    DaThamDinh: { code: 'DaThamDinh', name: 'Đã thẩm định' },
    ChuaThamDinh: {
      code: 'ChuaThamDinh',
      name: 'Chưa thẩm định',
    },
    ChuaDangKy: {
      code: 'ChuaDangKy',
      name: 'Chưa đăng ký lĩnh vực kinh doanh',
    },
  },
  SupplierExpertiseStatus: {
    DangThamDinh: {
      code: 'DangThamDinh',
      name: 'Đang thẩm định',
    },
    DaThamDinh: { code: 'DaThamDinh', name: 'Đã thẩm định' },
    KhongDuyetQT2: { code: 'KhongDuyetQT2', name: 'Không duyệt Doanh nghiệp' },
  },
  SupplierExpertiseLawStatus: {
    ChuaThamDinh: {
      code: 'ChuaThamDinh',
      name: 'Chưa hoàn thành',
    },
    KhongThamDinh: {
      code: 'KhongThamDinh',
      name: 'Không yêu cầu thẩm định',
    },
    DaThamDinh: { code: 'DaThamDinh', name: 'Đã hoàn thành' },
  },
  SupplierExpertiseCapacityStatus: {
    ChuaThamDinh: {
      code: 'ChuaThamDinh',
      name: 'Chưa hoàn thành',
    },
    GuiDuyet: {
      code: 'GuiDuyet',
      name: 'Gửi duyệt',
    },
    KhongThamDinh: {
      code: 'KhongThamDinh',
      name: 'Không yêu cầu thẩm định',
    },
    DaThamDinh: { code: 'DaThamDinh', name: 'Đã hoàn thành' },
  },
  SupplierExpertiseDetailType: {
    Law: { code: 'Law', name: 'Thông tin pháp lý' },
    Capacity: { code: 'Capacity', name: 'Thông tin năng lực' },
  },
  SettingStringType: {
    address: 'address',
    paymentType: 'paymentType',
    company: 'company',
    masterBidGuarantee: 'masterBidGuarantee',
    unit: 'unit',
    currency: 'currency',
  },
  SettingStringClientType: {
    BannerName: { code: 'BannerName', name: '' },
    Footer1: { code: 'Footer1', name: '' },
    Footer2: { code: 'Footer2', name: '' },
    Footer3: { code: 'Footer3', name: '' },
  },
  BannerClientType: {
    Video: { code: 'Video', name: '' },
    Image: { code: 'Image', name: '' },
  },
  BannerClientPosition: {
    // Left: { code: 'Left', name: 'Bên trái' },
    // Right: { code: 'Right', name: 'Bên phải' },
    Top: { code: 'Top', name: 'Bên trên' },
  },
  /** Kiểu dữ liệu */
  DataType: {
    String: { code: 'String', name: 'Free Text' },
    Number: { code: 'Number', name: 'Số' },
    File: { code: 'File', name: 'File' },
    List: { code: 'List', name: 'Danh sách' },
    Date: { code: 'Date', name: 'Ngày giờ' },
    Address: { code: 'Address', name: 'Địa chỉ' },
    Km: { code: 'Km', name: 'Khoảng cách (km)' },
    Time: { code: 'Time', name: 'Thời gian di chuyển (giờ)' },
  },

  BidStatus: {
    GoiThauTam: {
      code: 'GoiThauTam',
      name: 'Gói thầu tạm',
      statusColor: '#3484e1',
      statusBorderColor: '#3484e1',
      statusBgColor: '#dceeff',
    },
    ChoDuyetGoiThauTam: {
      code: 'ChoDuyetGoiThauTam',
      name: 'Chờ duyệt gói thầu tạm',
      statusColor: '#eaa049',
      statusBorderColor: '#eaa049',
      statusBgColor: '#fdf7d8',
    },
    DangCauHinhGoiThau: {
      code: 'DangCauHinhGoiThau',
      name: 'Mới tạo',
    },
    DangChonNCC: {
      code: 'DangChonNCC',
      name: 'Đang chọn nhà cung cấp',
    },
    TuChoiGoiThau: {
      code: 'TuChoiGoiThau',
      name: 'Từ chối gói thầu',
    },
    DangDuyetGoiThau: {
      code: 'DangDuyetGoiThau',
      name: 'Đang duyệt gói thầu',
    },
    DangNhanBaoGia: {
      code: 'DangNhanBaoGia',
      name: 'Đang mời thầu',
      statusColor: '#3484e1',
      statusBorderColor: '#3484e1',
      statusBgColor: '#dceeff',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đã mở thầu',
      statusColor: '#eaa049',
      statusBorderColor: '#eaa049',
      statusBgColor: '#fdf7d8',
    },
    DangDuyetDanhGia: {
      code: 'DangDuyetDanhGia',
      name: 'Đang duyệt đánh giá thầu',
      statusColor: '#f68a7d',
      statusBorderColor: '#f68a7d',
      statusBgColor: '#f8e1e0',
    },
    HoanTatDanhGia: {
      code: 'HoanTatDanhGia',
      name: 'Hoàn tất đánh giá thầu',
      statusColor: '#0a915b',
      statusBorderColor: '#0a915b',
      statusBgColor: '#def2e0',
    },

    DangDamPhanGia: {
      code: 'DangDamPhanGia',
      name: 'Đang đàm phán giá',
      color: '#ffc107',
      statusColor: '#99e2f9',
      statusBorderColor: '#99e2f9',
      statusBgColor: '#e6fcf9',
    },
    DongDamPhanGia: {
      code: 'DongDamPhanGia',
      name: 'Hoàn tất đàm phán giá',
      color: '#1890ff',
      statusColor: '#0a915b',
      statusBorderColor: '#0a915b',
      statusBgColor: '#def2e0',
    },
    DangDauGia: {
      code: 'DangDauGia',
      name: 'Đang đấu giá',
      color: '#ffc107',
      statusColor: '#d7d097',
      statusBorderColor: '#d7d097',
      statusBgColor: '#fefce5',
    },
    DongDauGia: {
      code: 'DongDauGia',
      name: 'Hoàn tất đấu giá',
      color: '#1890ff',
      statusColor: '#0a915b',
      statusBorderColor: '#0a915b',
      statusBgColor: '#def2e0',
    },
    ChoXacNhan: { code: 'ChoXacNhan', name: 'Đang chờ NCC xác nhận', statusColor: '#eaa049', statusBorderColor: '#eaa049', statusBgColor: '#fdf7d8' },
    DongThau: {
      code: 'DongThau',
      name: 'Đang duyệt Doanh nghiệp thắng thầu',
      statusColor: '#eaa049',
      statusBorderColor: '#eaa049',
      statusBgColor: '#fdf7d8',
    },
    DuyetNCCThangThau: {
      code: 'DuyetNCCThangThau',
      name: 'Đã duyệt Doanh nghiệp thắng thầu',
      statusColor: '#0a915b',
      statusBorderColor: '#0a915b',
      statusBgColor: '#def2e0',
    },
    DangDuyetKetThucThau: {
      code: 'DangDuyetKetThucThau',
      name: 'Đang duyệt kết thúc thầu',
      statusColor: '#eaa049',
      statusBorderColor: '#eaa049',
      statusBgColor: '#fdf7d8',
    },
    HoanTat: { code: 'HoanTat', name: 'Hoàn tất', statusColor: '#0a915b', statusBorderColor: '#0a915b', statusBgColor: '#def2e0' },
    Huy: { code: 'Huy', name: 'Huỷ', statusColor: '#f68a7d', statusBorderColor: '#f68a7d', statusBgColor: '#f8e1e0' },
  },
  BidTechStatus: {
    ChoDuyet: {
      code: 'ChoDuyet',
      name: 'Chờ Duyệt',
    },
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },
    TuChoi: {
      code: 'DaTao',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidTradeStatus: {
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },

    GuiDuyet: {
      code: 'GuiDuyet',
      name: 'Đã gửi duyệt',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },
    TuChoi: {
      code: 'TuChoi',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidPriceStatus: {
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },
    GuiDuyet: {
      code: 'GuiDuyet',
      name: 'Đã gửi duyệt',
    },
    TuChoi: {
      code: 'DaTao',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidChooseSupplierStatus: {
    ChuaChon: {
      code: 'ChuaChon',
      name: 'Chưa chọn',
    },
    DangChon: {
      code: 'DangChon',
      name: 'Đang chọn',
    },
    DaChon: {
      code: 'DaChon',
      name: 'Đã chọn',
    },
    GuiDuyet: {
      code: 'GuiDuyet',
      name: 'Đã chọn',
    },
    TuChoi: {
      code: 'TuChoi',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidTechRateStatus: {
    ChuaTao: {
      code: 'ChuaTao',
      name: 'Chưa tạo',
    },
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },

    GuiDuyet: {
      code: 'GuiDuyet',
      name: 'Đã tạo',
    },
    TuChoi: {
      code: 'DaTao',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidTradeRateStatus: {
    ChuaTao: {
      code: 'ChuaTao',
      name: 'Chưa tạo',
    },
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },
    TuChoi: {
      code: 'DaTao',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidPriceRateStatus: {
    ChuaTao: {
      code: 'ChuaTao',
      name: 'Chưa tạo',
    },
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
    },
    TuChoi: {
      code: 'DaTao',
      name: 'Từ chối',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidResetPriceStatus: {
    ChuaTao: {
      code: 'ChuaTao',
      name: 'Chưa tạo',
      description: 'Mặc định khi tạo ra gói thầu',
    },
    DangTao: {
      code: 'DangTao',
      name: 'Đang tạo',
      description: 'Khi mới xác nhận hiệu chỉnh bảng giá (Chưa lưu)',
    },
    DaTao: {
      code: 'DaTao',
      name: 'Đã tạo',
      description: 'Khi đã xác nhận tạo xong bảng giá hiệu chỉnh (Lưu xong)',
    },
    KetThuc: {
      code: 'KetThuc',
      name: 'Kết thúc',
      description: 'Kết thúc nộp chào giá hiệu chỉnh',
    },
  },
  BidSupplierResetPriceStatus: {
    KhongYeuCau: {
      code: 'KhongYeuCau',
      name: 'Không yêu cầu',
      description: 'Mặc định khi tạo mời thầu',
    },
    YeuCauBoSung: {
      code: 'YeuCauBoSung',
      name: 'Yêu cầu bổ sung',
      description: 'Khi chọn Doanh nghiệp nộp bảng giá hiệu chỉnh',
    },
    DaBoSung: {
      code: 'DaBoSung',
      name: 'Đã bổ sung',
      description: 'Khi Doanh nghiệp đã nộp bổ sung bảng giá hiệu chỉnh',
    },
  },
  BidHistoryStatus: {
    SaoChepGoiThau: {
      code: 'SaoChepGoiThau',
      name: 'Sao chép gói thầu',
    },
    TaoGoiThauExcel: {
      code: 'TaoGoiThauExcel',
      name: 'Tạo thông tin chung cho gói thầu bằng excel',
    },
    TaoGoiThau: {
      code: 'TaoGoiThau',
      name: 'Tạo thông tin chung cho gói thầu',
    },
    SuaTaoGoiThau: {
      code: 'SuaTaoGoiThau',
      name: 'Sửa thông tin chung của gói thầu',
    },
    SuaTaoGoiThauSauDuyet: {
      code: 'SuaTaoGoiThauSauDuyet',
      name: 'Sửa thông tin chung của gói thầu sau duyệt',
    },
    YeuCauDuyetGoiThauTam: {
      code: 'YeuCauDuyetGoiThauTam',
      name: 'Yêu cầu duyệt gói thầu tạm',
    },
    TuChoiGoiThauTam: {
      code: 'TuChoiGoiThauTam',
      name: 'Yêu cầu kiểm tra lại thông tin chung của gói thầu tạm',
    },
    DuyetGoiThauTam: {
      code: 'DuyetGoiThauTam',
      name: 'Duyệt thông tin chung của gói thầu tạm',
    },
    DuyetThauNhanh: {
      code: 'DuyetThauNhanh',
      name: 'Duyệt thầu nhanh',
    },
    TaoKyThuat: {
      code: 'TaoKyThuat',
      name: 'Tạo thông tin kỹ thuật cho gói thầu',
    },
    TuChoiTaoKyThuat: {
      code: 'TuChoiTaoKyThuat',
      name: 'Từ chối thông tin kỹ thuật của gói thầu',
    },
    DuyetTaoKyThuat: {
      code: 'DuyetTaoKyThuat',
      name: 'Duyệt thông tin kỹ thuật của gói thầu',
    },
    TaoThuongMai: {
      code: 'TaoThuongMai',
      name: 'Tạo thông tin thương mại cho gói thầu',
    },
    TuChoiThuongMai: {
      code: 'TuChoiThuongMai',
      name: 'Từ chối thông tin thương mại cho gói thầu',
    },
    DuyetThuongMai: {
      code: 'DuyetThuongMai',
      name: 'Duyệt thông tin thương mại cho gói thầu',
    },
    TaoGia: {
      code: 'TaoGia',
      name: 'Tạo thông tin giá cho gói thầu',
    },
    DuyetGia: {
      code: 'DuyetGia',
      name: 'Duyệt thông tin hạng mục chào giá, cơ cấu giá cho gói thầu',
    },
    ChonNCC: {
      code: 'ChonNCC',
      name: 'Chọn nhà cung cấp mời tham gia thầu',
    },
    ChonLaiNCC: {
      code: 'ChonNCC',
      name: 'Chọn lại nhà cung cấp mời tham gia thầu',
    },
    GuiMPOLeader: {
      code: 'GuiMPOLeader',
      name: 'Gửi yêu cầu phê duyệt bảng chào giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu',
    },
    TuChoiGoiThau: {
      code: 'TuChoiGoiThau',
      name: 'Từ chối bảng chào giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu',
    },
    DuyetGoiThau: {
      code: 'DuyetGoiThau',
      name: 'Duyệt gói thầu',
    },
    NhanBaoGia: {
      code: 'NhanBaoGia',
      name: 'Nhận báo giá',
    },
    EmailNhacMoThauLan1: {
      code: 'EmailNhacMoThauLan1',
      name: 'Email nhắc mở thầu lần 1',
    },
    EmailNhacMoThauLan2: {
      code: 'EmailNhacMoThauLan2',
      name: 'Email nhắc mở thầu lần 2',
    },
    MoThau: {
      code: 'MoThau',
      name: 'Mở đánh giá',
    },
    DanhGiaKyThuat: {
      code: 'DanhGiaKyThuat',
      name: 'Đánh giá kỹ thuật',
    },
    TuChoiDanhGiaKyThuat: {
      code: 'TuChoiDanhGiaKyThuat',
      name: 'Từ chối đánh giá kỹ thuật',
    },
    DuyetDanhGiaKyThuat: {
      code: 'DuyetDanhGiaKyThuat',
      name: 'Duyệt đánh giá kỹ thuật',
    },
    DanhGiaThuongMai: {
      code: 'DanhGiaThuongMai',
      name: 'Đánh giá thương mại',
    },
    DanhGiaGia: {
      code: 'DanhGiaGia',
      name: 'Đánh giá giá',
    },
    TuChoiDanhGiaThuongMai: {
      code: 'TuChoiDanhGiaThuongMai',
      name: 'Từ chối đánh giá chào giá và thương mại',
    },
    DuyetDanhGiaThuongMai: {
      code: 'DuyetDanhGiaThuongMai',
      name: 'Duyệt đánh giá chào giá và thương mại',
    },
    HieuChinhBangGia: {
      code: 'HieuChinhBangGia',
      name: 'Hiệu chỉnh bảng giá',
    },
    HoanTatHieuChinhBangGia: {
      code: 'HoanTatHieuChinhBangGia',
      name: 'Hoàn tất hiệu chỉnh bảng giá',
    },
    KetThucNopChaoGiaHieuChinh: {
      code: 'KetThucNopChaoGiaHieuChinh',
      name: 'Kết thúc nộp chào giá hiệu chỉnh',
    },
    TaoDamPhanGia: {
      code: 'TaoDamPhanGia',
      name: 'Tạo đàm phán giá',
    },
    DongDamPhanGia: {
      code: 'DongDamPhanGia',
      name: 'Hoàn tất đàm phán giá',
    },
    TaoDauGia: { code: 'TaoDauGia', name: 'Tạo đấu giá' },
    DongDauGia: {
      code: 'DongDauGia',
      name: 'Hoàn tất đấu giá',
    },
    ThamDinh: {
      code: 'ThamDinh',
      name: 'Thẩm định',
    },
    DuyetThamDinh: {
      code: 'DuyetThamDinh',
      name: 'Duyệt thẩm định',
    },
    XacNhanNCCTrungThau: {
      code: 'PheDuyet',
      name: 'Chọn Doanh nghiệp thắng thầu',
    },
    PheDuyetNCCThangThau: { code: 'PheDuyetNCCThangThau', name: 'Phê duyệt Doanh nghiệp thắng thầu' },
    TuChoiNCCThangThau: { code: 'TuChoiNCCThangThau', name: 'Phê duyệt Doanh nghiệp thắng thầu' },
    YeuCauKiemTraLai: {
      code: 'YeuCauKiemTraLai',
      name: 'Yêu cầu đánh giá và chọn lại Doanh nghiệp thắng thầu',
    },
    GuiYeuCauPheDuyetKetThucThau: {
      code: 'GuiYeuCauPheDuyetKetThucThau',
      name: 'Gửi yêu cầu phê duyệt kết thúc thầu',
    },
    PheDuyetKetThucThau: { code: 'PheDuyetKetThucThau', name: 'Phê duyệt kết thúc thầu' },
    PheDuyetKetQua: { code: 'HoanTat', name: 'Phê duyệt kết quả' },
    Huy: { code: 'Huy', name: 'Huỷ' },
    YeuCauHuyGoiThau: { code: 'YeuCauHuyGoiThau', name: 'Yêu cầu hủy gói thầu' },
    XacNhanHuyGoiThau: { code: 'XacNhanHuyGoiThau', name: 'Xác nhận hủy gói thầu' },
    YeuCauDuyetThongTinKiThuat: {
      code: 'YeuCauDuyetThongTinKiThuat',
      name: 'Yêu cầu phê duyệt thông tin kĩ thuật',
    },
  },
  BidSupplierStatus: {
    DaDuocChon: {
      code: 'DaDuocChon',
      name: 'Đã được chọn tham gia gói thầu',
    },
    DaThongBaoMoiThau: {
      code: 'DaThongBaoMoiThau',
      name: 'Đã gửi thông báo mời thầu',
    },
    DaXacNhanKhongThamGiaThau: {
      code: 'DaXacNhanKhongThamGiaThau',
      name: 'Đã xác nhận không tham gia thầu',
    },
    DaXacNhanThamGiaThau: {
      code: 'DaXacNhanThamGiaThau',
      name: 'Đã xác nhận tham gia thầu',
    },
    DaHoanThanhBoSungHoSo: {
      code: 'DaHoanThanhBoSungHoSo',
      name: 'Đã hoàn thành bổ sung hồ sơ',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
    },
    DaDanhGia: {
      code: 'DaDanhGia',
      name: 'Đã đánh giá',
    },
  },
  BidSupplierFileStatus: {
    ChuaKiemTra: {
      code: 'ChuaKiemTra',
      name: 'Chưa kiểm tra',
    },
    HopLe: {
      code: 'HopLe',
      name: 'Hợp lệ',
    },
    KhongHopLe: {
      code: 'KhongHopLe',
      name: 'Không hợp lệ',
    },
  },
  BidSupplierTechStatus: {
    KhongXacNhan: {
      code: 'KhongXacNhan',
      name: 'Không xác nhận',
    },
    ChuaXacNhan: {
      code: 'ChuaXacNhan',
      name: 'Chưa xác nhận',
    },
    DangBoSung: {
      code: 'DangBoSung',
      name: 'Đang bổ sung',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
    },
    DaXacNhan: {
      code: 'DaXacNhan',
      name: 'Đã xác nhận',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidSupplierPriceStatus: {
    KhongXacNhan: {
      code: 'KhongXacNhan',
      name: 'Không xác nhận',
    },
    ChuaXacNhan: {
      code: 'ChuaXacNhan',
      name: 'Chưa xác nhận',
    },
    DangBoSung: {
      code: 'DangBoSung',
      name: 'Đang bổ sung',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
    },
    DaXacNhan: {
      code: 'DaXacNhan',
      name: 'Đã xác nhận',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidSupplierTradeStatus: {
    KhongXacNhan: {
      code: 'KhongXacNhan',
      name: 'Không xác nhận',
    },
    ChuaXacNhan: {
      code: 'ChuaXacNhan',
      name: 'Chưa xác nhận',
    },
    DangBoSung: {
      code: 'DangBoSung',
      name: 'Đang bổ sung',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
    },
    DaXacNhan: {
      code: 'DaXacNhan',
      name: 'Hoàn thành',
    },
    DaDuyet: {
      code: 'DaDuyet',
      name: 'Đã duyệt',
    },
  },
  BidRuleType: {
    MPO: {
      code: 'MPO',
      name: 'Thành viên phụ trách mua hàng',
      description: '1 nhân viên',
    },
    MPOLeader: {
      code: 'MPOLeader',
      name: 'Người duyệt nội dung mua hàng',
      description: '1 nhân viên',
    },
    Tech: {
      code: 'Tech',
      name: 'Thành viên phụ trách yêu cầu kỹ thuật',
      description: '1 nhân viên',
    },
    TechLeader: {
      code: 'TechLeader',
      name: 'Người duyệt yêu cầu kỹ thuật',
      description: '1 nhân viên',
    },
    Memmber: {
      code: 'Memmber',
      name: 'Các thành viên khác thuộc hội đồng xét thầu',
      description: 'nhiều nhân viên',
    },
    Other: {
      code: 'Other',
      name: 'Các thành viên khác',
      description: 'nhiều nhân viên',
    },

    EmpTech: {
      code: 'EmpTech',
      name: 'Các thành viên mở thầu trong hội thầu kĩ thuật',
      description: 'nhiều nhân viên',
    },
    EmpOther: {
      code: 'EmpOther',
      name: 'Các thành viên mở thầu trong hội đồng Giá, cơ cấu giá, ĐKTM',
      description: 'nhiều nhân viên',
    },
  },
  BidDealStatus: {
    DangDamPhan: { code: 'DangDamPhan', name: 'Đang đàm phán' },
    DongDamPhanGia: { code: 'DongDamPhanGia', name: 'Hoàn tất' },
  },
  BidDealSupplierStatus: {
    DangDamPhan: { code: 'DangDamPhan', name: 'Đang đàm phán' },
    DaGuiGiaMoi: { code: 'DaGuiGiaMoi', name: 'Đã gửi giá mới' },
    DaTuChoi: { code: 'DaTuChoi', name: 'Đã từ chối' },
  },

  BidAuctionStatus: {
    DangDauGia: { code: 'DangDauGia', name: 'Đang đấu giá' },
    DongDauGia: { code: 'DongDauGia', name: 'Hoàn tất' },
  },
  BidAuctionSupplierStatus: {
    DangDauGia: { code: 'DangDauGia', name: 'Chưa đấu giá' },
    DaDauGia: { code: 'DaDauGia', name: 'Đã đấu giá' },
  },
  EmailTemplate: {
    SendConfirmCode: {
      code: 'SendConfirmCode',
      name: 'Doanh nghiệp gửi mã xác nhận',
    },
    FinishEvaluation: {
      code: 'FinishEvaluation',
      name: 'Kết thúc thẩm định Doanh nghiệp',
    },
    UpdateBidSuccess: {
      code: 'UpdateBidSuccess',
      name: 'Chỉnh sửa thông tin chung của gói thầu thành công',
    },
    SupplierBidSuccess: {
      code: 'SupplierBidSuccess',
      name: 'Doanh nghiệp đã đấu thầu',
    },
    SendEmailBid: {
      code: 'SendEmailBid',
      name: 'Gửi thông báo nội bộ, thông báo Doanh nghiệp',
    },
  },
  DataHistoryTable: {
    Supplier: 'supplier_entity',
    SupplierCapacity: 'supplier_capacity_entity',
  },
  SQSMessageType: {
    Test: 'test',
    Email: 'email',
    Material: 'Material',
  },
  EmailStatus: {
    Success: {
      code: 'Success',
      name: 'Gửi email thành công',
    },
    Fail: {
      code: 'Fail',
      name: 'Gửi email thất bại',
    },
  },
  NotifyStatus: {
    ChuaDoc: { code: 'ChuaDoc', name: 'Chưa đọc' },
    DaDoc: { code: 'DaDoc', name: 'Đã đọc' },
  },
  ColType: {
    MPO: { code: 'MPO', name: 'Nhân viên' },
    Supplier: { code: 'Supplier', name: 'Nhà cung cấp' },
  },
  StatusServiceCapacity: {
    ChuaDuyet: { code: 'ChuaDuyet', name: 'Chưa duyệt' },
    GuiDuyet: { code: 'GuiDuyet', name: 'Đã gửi duyệt' },
    DaDuyet: { code: 'DaDuyet', name: 'Đã duyệt' },
  },

  // + Mới tạo: Xem, chỉnh sửa, gửi duyệt, hủy
  // + Chờ duyệt: Duyệt, xem, yêu cầu kiểm tra lại, chỉnh sửa
  // + Yêu cầu kiểm tra lại: Xem, Chỉnh sửa, gửi duyệt, hủy
  // + Hủy: Xem
  // + Chờ kích hoạt: Xem, chỉnh sửa
  // + Đang thực hiện: Xem, đóng hợp đồng
  // + Đóng:Xem

  ContractStatus: {
    TEMPORARY: { code: 'TEMPORARY', name: 'Lưu tạm', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAIT_APPROVE: { code: 'WAIT_APPROVE', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    REQUEST_RE_CHECK: { code: 'REQUEST_RE_CHECK', name: 'Yêu cầu kiểm tra lại', color: '#AA0808', bgColor: '#E9BFBF', borderColor: '#AA0808' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    WAIT_ACTIVE: { code: 'WAIT_ACTIVE', name: 'Chờ kích hoạt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    PROCESSING: { code: 'PROCESSING', name: 'Đang thực hiện', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    DONE: { code: 'DONE', name: 'Đóng', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  PORoleCode: {
    View: { code: 'VIEW', name: 'Xem', description: 'Quyền xem thông tin PO' },
    Edit: {
      code: 'EDIT',
      name: 'Chỉnh sửa',
      description: 'Quyền chỉnh sửa thông tin PO',
    },
    Confirm: {
      code: 'COMFIRM',
      name: 'Duyệt PO',
      description: 'Quyền duyệt  PO',
    },
    PurchaseOrderPayMent: {
      code: 'PURCHASEORDERPAYMENT',
      name: 'Thanh toán PO',
      description: 'Quyền thanh toán  PO',
    },
    Cancel: {
      code: 'CANCEL',
      name: 'Hủy',
      description: 'Quyền chỉnh sửa Hủy PO',
    },
  },
  /** Trạng thái ĐNTT của tiến độ */
  PaymentProgressStatus: {
    Unpaid: { code: 'UNPAIND', name: 'Chưa đề nghị thanh toán' },
    Partial: { code: 'PARTIAL', name: 'Đã đề nghị thanh toán một phần' },
    Paid: { code: 'PAID', name: 'Đã đề nghị thanh toán' },
  },

  ContractTypeAppendix: {
    // CHANGE_QUANTITY: { code: 'CHANGE_QUANTITY', name: 'Phụ lục thay đổi số lượng' },
    // CHANGE_PRICE: { code: 'CHANGE_PRICE', name: 'Phụ lục thay đổi đơn giá' },
    CHANGE_TIME: { code: 'CHANGE_TIME', name: 'Phụ lục thay đổi thời gian' },
    CHANGE_RULES: { code: 'CHANGE_RULES', name: 'Phụ lục thay đổi điều khoản' },
    CHANGE_OTHER: { code: 'CHANGE_OTHER', name: 'Phụ lục thay đổi khác' },
  },
  PurchaseOrderPayMent: {
    Unpaid: {
      code: 'UNPAIND',
      name: 'Chưa thanh toán',
      description: 'PO chưa được thanh toán',
    },
    SuggestPaid: { code: 'SUGGEST_PAID', name: 'Đã đề nghị thanh toán', description: 'PO đã được đề nghị thanh toán' },
    Partial: {
      code: 'PARTIAL',
      name: 'Thanh toán một phần',
      description: 'PO được thanh toán 1 phần',
    },
    Paid: {
      code: 'PAID',
      name: 'Đã thanh toán',
      description: 'PO đã được thanh toán',
    },
  },
  PurchaseOrderStatus: {
    HOLD: { code: 'HOLD', name: 'Held', description: 'Held ', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    PARK: { code: 'PARK', name: 'Parked', description: 'Parked ', color: 'darkorange', bgColor: '#F5CA89', borderColor: '#EFCE22' },
    SAVED: { code: 'SAVED', name: 'Saved', description: 'Saved', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    DELIVERYREFUSE: {
      code: 'DELIVERYREFUSE',
      name: 'NCC từ chối giao hàng',
      description: 'Nhà cung cấp từ chối giao hàng',
      color: 'orangered',
    },
    DELIVERY: {
      code: 'DELIVERY',
      name: 'NCC giao hàng',
      description: 'Nhà cung cấp xác nhận giao hàng',
      color: '#ff409c',
    },

    CHECK_AGAIN: {
      code: 'CHECK_AGAIN',
      name: 'Kiểm tra lại',
      description: 'Kiểm tra lại',
      color: 'orangered',
      bgColor: '#FCF0DD',
      borderColor: 'orangered',
    },
    WAITING_APPROVAL: {
      code: 'WAITING_APPROVAL',
      name: 'Chờ duyệt',
      description: 'Chờ duyệt',
      color: '#EFCE22',
      bgColor: '#FCF0DD',
      borderColor: '#F5CA89',
    },

    COMPLETE: { code: 'COMPLETE', name: 'Hoàn thành', description: 'PO đã hoàn tất', color: '#008000', bgColor: '#DEF2E0', borderColor: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', description: 'Hủy PO', color: '#cf1322', bgColor: '#ffa39e', borderColor: '#E11F1F' },
    CLOSED: { code: 'CLOSED', name: 'Đóng', description: 'Đóng', color: '#008000', bgColor: '#DEF2E0', borderColor: '#00AA25' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', description: 'Đã duyệt', color: '#008000', bgColor: '#DEF2E0', borderColor: '#00AA25' },
    REJECT: { code: 'REJECT', name: 'Từ chối', description: 'Từ chối', color: '#cf1322', bgColor: '#ffa39e', borderColor: '#E11F1F' },
  },
  ShipmentConditionType: {
    YEAR: {
      code: 'YEAR',
      name: 'Theo năm',
      description: 'Theo năm',
    },
    MONTH: {
      code: 'MONTH',
      name: 'Theo tháng',
      description: 'Theo tháng',
    },
    CANCEL: { code: 'CANCEL', name: 'Hủy' },
    REVERT: { code: 'REVERT', name: 'Gỡ từ chối duyệt' },
  },
  PRStatus: {
    H: {
      code: 'H',
      name: 'Lưu tạm',
      color: '#8898AA',
      bgColor: 'rgb(136 152 170 / 15%)',
      borderColor: '#8898AA',
      description: 'Held: Lưu tạm',
    },

    S: {
      code: 'S',
      name: 'saved',
      color: '#0063D8',
      bgColor: 'rgb(0 99 216 / 15%)',
      borderColor: '#0063D8',
      description: 'Saved: Sau khi nhấn Save PR thành công',
    },
    C_PR: { code: 'C_PR', name: 'Đóng', color: '#1D2D3E', bgColor: 'rgb(*********** / 15%)', borderColor: '#1D2D3E', description: 'PR lỗi' },
    C: { code: 'C', name: 'Hủy', color: '#AA0808', bgColor: 'rgb(170 8 8 / 15%)', borderColor: '#AA0808', description: 'Sau khi huỷ PR' },
    W_A: {
      code: 'W_A',
      name: 'Chờ duyệt',
      color: '#F3AF2B',
      bgColor: 'rgb(243 175 43 / 15%)',
      borderColor: '#F3AF2B',
      description: ' Sau khi tất cả các Line PR được đóng',
    },
    A: { code: 'A', name: 'Đã duyệt', color: '#0A915B', bgColor: 'rgb(10 145 91 / 15%)', borderColor: '#0A915B', description: 'PR đã được duyệt' },
    C_A: {
      code: 'C_A',
      name: 'Kiểm tra lại',
      color: '#FF49CC',
      bgColor: 'rgb(255 73 204 / 15%)',
      borderColor: '#FF49CC',
      description: ' PR có trạng thái này nếu cấp duyệt nhấn “Kiểm tra lại” thay vì nhấn “duyệt” hoặc “từ chối”',
    },
    R: {
      code: 'R',
      name: 'Từ chối duyệt',
      color: '#F80D53',
      bgColor: 'rgb(248 13 83 / 15%)',
      borderColor: '#F80D53',
      description: 'PR từ chối duyệt',
    },
  },

  ContractTypePo: {
    NonContract: {
      code: 'NONCONTRACT',
      name: 'Chọn không theo hợp đồng',
    },
    Contract: { code: 'CONTRACT', name: 'Chọn theo hợp đồng' },
  },

  SourceType: {
    Admin: { name: 'Quản Lý Admin', code: 'ADMIN' },
    Client: { name: 'Cổng Đấu Thầu', code: 'CLIENT' },
  },

  KPIRating: {
    Pass: { name: 'Đạt', code: 'PASS' },
    Fail: { name: 'Không đạt', code: 'FAIL' },
  },

  Evaluate: {
    A: { name: 'Điểm A', code: 'A' },
    B: { name: 'Điểm B', code: 'B' },
    C: { name: 'Điểm C', code: 'C' },
    D: { name: 'Điểm D', code: 'D' },
    E: { name: 'Điểm E', code: 'E' },
  },

  DatetimeQuarterly: {
    Q1: { code: 'Q1', name: 'Quý 1', value: 1 - 3 },
    Q2: { code: 'Q2', name: 'Quý 2', value: 4 - 6 },
    Q3: { code: 'Q3', name: 'Quý 3', value: 7 - 3 },
    Q4: { code: 'Q4', name: 'Quý 4', value: 10 - 12 },
  },

  DatetimeFilter: {
    Month: { code: 'MONTH', name: 'Chọn thời gian theo tháng' },
    Quarterly: { code: 'QUARTERLY', name: 'Chọn thời gian theo quý' },
    Year: { code: 'YEAR', name: 'Chọn thời gian theo năm' },
  },

  SchemeStatus: {
    MoiTao: { code: 'MoiTao', name: 'Mới tạo', color: 'darkblue' },
    DaDuyet: { code: 'DaDuyet', name: 'Đã duyệt', color: 'darkgreen' },
    Huy: { code: 'Huy', name: 'Huỷ', color: 'darkred' },
  },

  WarningType: {
    Purchare_Plan_Expiry: {
      code: 'PURCHARE_PLAN_EXPIRY',
      name: 'Cảnh Báo Khi Hết Hạn Mua Hàng Theo Kế Hoạch {0}',
      default: `
      <p>Kính gửi Anh/Chị:  <b>{0}</b>,</p>
      <br>
      <p>Yêu cầu mua hàng <b>[ {1} ]</b> với mã <b>[ {2} ]</b> đã quá hạn giao hàng theo kế hoạch nhưng vẫn chưa hoàn tất.</p>
      <p>Anh/Chị vui lòng kiểm tra lại thông tin.</p>
      <br>
      <p><i>Lưu ý: Cảnh báo này được gửi tự động từ Hệ Thống Đấu Thầu APE !</i></p>`,
    },
    Purchare_Plan_Over_Budget: {
      code: 'PURCHARE_PLAN_OVER_BUDGET',
      name: 'Cảnh Báo Vượt Ngân Sách Cho Kế Hoạch {0}',
      default: `
      <p>Kính gửi Anh/Chị:  <b>{0}</b>,</p>
      <br>
      <p>Yêu cầu mua hàng <b>[ {1} ]</b> với mã <b>[ {2} ]</b> đã vượt quá ngân sách kế hoạch.</p>
      <p>Ngân sách dự kiến <b>{3}</b> nhưng ngân sách mua hàng thực tế đã là <b>{4}</b> .</p>
      <p>Anh/Chị vui lòng kiểm tra lại thông tin.</p>
      <br>
      <p><i>Lưu ý: Cảnh báo này được gửi tự động từ Hệ Thống Đấu Thầu APE !</i></p>`,
    },
    Contract_Coming_Expiry: {
      code: 'CONTRACT_COMING_EXPIRY',
      name: 'Cảnh Báo Sắp Hết Hạn Hợp Đồng {0}',
      default: `
      <p>Kính gửi Anh/Chị:  <b>{0}</b>,</p>
      <br>
      <p>Hợp đồng <b>[ {1} ]</b> với mã <b>[ {2} ]</b> đã sắp tới ngày hết hạn nhưng vẫn chưa được gia hạn.</p>
      <p>Anh/Chị vui lòng kiểm tra lại thông tin.</p>
      <br>
      <p><i>Lưu ý: Cảnh báo này được gửi tự động từ Hệ Thống Đấu Thầu APE !</i></p>`,
    },
    Contract_Expiry_Payment: {
      code: 'CONTRACT_EXPIRY_PAYMENT',
      name: 'Cảnh Báo Hết Hạn Thanh Toán Hợp Đồng {0}',
      default: `
      <p>Kính gửi Anh/Chị:  <b>{0}</b>,</p>
      <br>
      <p>Hợp đồng <b>[ {1} ]</b> với mã <b>[ {2} ]</b> đã hết hạn thanh toán nhưng vẫn chưa được xử lý.</p>
      <p>Anh/Chị vui lòng kiểm tra lại thông tin.</p>
      <br>
      <p><i>Lưu ý: Cảnh báo này được gửi tự động từ Hệ Thống Đấu Thầu APE !</i></p>`,
    },
    Bid_Expiry_Setting_Evalution: {
      code: 'BID_EXPIRY_SETTING_EVALUTION',
      name: 'Cảnh Báo Sắp Hết Hạn {0} Gói Thầu {1}',
      default: `
      <p>Kính gửi Anh/Chị:  <b>{0}</b>,</p>
      <br>
      <p>Gói thầu <b>[ {1} ]</b> với mã <b>[ {2} ]</b> đã hết hạn thiết lập và đánh giá nhưng vẫn chưa được xử lý.</p>
      <p>Anh/Chị vui lòng kiểm tra lại thông tin.</p>
      <br>
      <p><i>Lưu ý: Cảnh báo này được gửi tự động từ Hệ Thống Đấu Thầu APE !</i></p>`,
    },
  },

  /** Loại data cần cảnh báo */
  DataWarningType: {
    PR_Plan: { code: 'PR_PLAN', name: 'Yêu Cầu Mua Hàng Theo Kế Hoạch' },
    PR_Aries: { code: 'PR_ARIES', name: 'Yêu Cầu Mua Hàng Phát Sinh' },
    Purchase_Plan: { code: 'PURCHASE_PLAN', name: 'Kế Hoạch Mua Hàng' },
    Contract: { code: 'CONTRACT', name: 'Hợp Đồng' },
    Bid: { code: 'BID', name: 'Gói Thầu' },
  },
  DeliveryDateStatus: {
    EQUAL: { code: 'EQUAL', name: 'Đúng ngày giao hàng', color: 'lightgreen' },
    MISS: { code: 'MISS', name: 'Trễ ngày giao hàng', color: 'lightcoral' },
    UP_COMMING: { code: 'UP_COMMING', name: 'Chuẩn bị đến ngày giao hàng', color: 'lightblue' },
  },
  /** Trạng thái phiếu xuất kho  */
  OutboundStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#2453F8' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: '#E11F1F' },
  },

  /** Trạng thái phiếu nhập kho  */
  InboundStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAITING: { code: 'WAITING', name: 'Chờ nhận hàng', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    IMPORTED: { code: 'IMPORTED', name: 'Đã nhập kho', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Đã huỷ', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
  },

  /** Trạng thái phiếu giao hàng  */
  ShipmentStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAITING: { code: 'WAITING', name: 'Chờ nhận', color: '#EFCE22', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    IMPORTED: { code: 'IMPORTED', name: 'Đã nhập kho', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Đã huỷ', color: '#E11F1F', bgColor: '#ffa39e', borderColor: '#E11F1F' },
  },

  /** Trạng thái shipment type  */
  ShipmentTypeStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAITING: { code: 'WAITING', name: 'Chờ nhận', color: '#EFCE22', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    IMPORTED: { code: 'IMPORTED', name: 'Đã nhập kho', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Đã huỷ', color: '#E11F1F', bgColor: '#ffa39e', borderColor: '#E11F1F' },
  },

  /** Trạng thái shipment route  */
  ShipmentRouteStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAITING: { code: 'WAITING', name: 'Chờ nhận', color: '#EFCE22', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    IMPORTED: { code: 'IMPORTED', name: 'Đã nhập kho', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Đã huỷ', color: '#E11F1F', bgColor: '#ffa39e', borderColor: '#E11F1F' },
  },

  /** Trạng thái phiếu giao hàng  */
  ShipmentCostStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAITING: { code: 'WAITING', name: 'Chờ nhận', color: '#EFCE22', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    IMPORTED: { code: 'IMPORTED', name: 'Đã nhập kho', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Đã huỷ', color: '#E11F1F', bgColor: '#ffa39e', borderColor: '#E11F1F' },
  },

  /** Trạng thái loại phiếu giao hàng  */
  ShipmentCostTypeStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAITING: { code: 'WAITING', name: 'Chờ nhận', color: '#EFCE22', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    IMPORTED: { code: 'IMPORTED', name: 'Đã nhập kho', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Đã huỷ', color: '#E11F1F', bgColor: '#ffa39e', borderColor: '#E11F1F' },
  },

  /** Trạng thái loại phiếu giao hàng (procedure)  */
  ShipmentCostTypeGroupConditionStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAITING: { code: 'WAITING', name: 'Chờ nhận', color: '#EFCE22', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    IMPORTED: { code: 'IMPORTED', name: 'Đã nhập kho', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Đã huỷ', color: '#E11F1F', bgColor: '#ffa39e', borderColor: '#E11F1F' },
  },

  /** Trạng thái loại phiếu giao hàng (procedure)  */
  ShipmentCostTypeCondition: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAITING: { code: 'WAITING', name: 'Chờ nhận', color: '#EFCE22', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    IMPORTED: { code: 'IMPORTED', name: 'Đã nhập kho', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Đã huỷ', color: '#E11F1F', bgColor: '#ffa39e', borderColor: '#E11F1F' },
  },

  /** Loại shipment  */
  ShipmentType: {
    PURCHASE_ORDER: { code: 'PURCHASE_ORDER', name: 'Shipment Đơn hàng mua', description: 'Có tham chiếu danh sách IB', color: '#2453F8' },
    COMMERCIAL: { code: 'COMMERCIAL', name: 'Shipment thương mại', description: 'Không tham chiếu IB hoặc OD', color: '#00AA25' },
  },

  /** Loại shipment  */
  ShipmentCostType: {
    PURCHASE_ORDER: { code: 'PURCHASE_ORDER', name: 'Shipment Đơn hàng mua', description: 'Có tham chiếu danh sách IB', color: '#2453F8' },
    SALE_ORDER: { code: 'SALE_ORDER', name: 'Shipment Đơn hàng bán', description: 'Có tham chiếu danh sách OD', color: '#2453F8' },
    COMMERCIAL: { code: 'COMMERCIAL', name: 'Shipment thương mại', description: 'Không tham chiếu IB hoặc OD', color: '#00AA25' },
  },

  /** Shipment Route  */
  ShipmentRoute: {
    ROUTE_01: { code: 'ROUTE_01', name: 'Đường 01', color: '#2453F8' },
    ROUTE_02: { code: 'ROUTE_02', name: 'Đường 02', color: '#2453F8' },
    ROUTE_03: { code: 'ROUTE_03', name: 'Đường 03', color: '#2453F8' },
  },

  /** Shipping type  */
  ShippingType: {
    '01': { code: '01', name: 'TRUNK', color: '#2453F8' },
    '03': { code: '03', name: 'RAIL', color: '#2453F8' },
    '04': { code: '04', name: 'SEA', color: '#2453F8' },
    '05': { code: '05', name: 'AIR', color: '#2453F8' },
  },

  /** Trạng thái phiếu kiểm kho  */
  CheckInventoryStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#2453F8' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: '#E11F1F' },
  },
  AuctionSupplierStatus: {
    NEW: { code: 'NEW', name: 'Chưa đến hạn', color: '#2453F8', statusBorderColor: '#76aceb', statusBgColor: '#dceeff', statusColor: '#76aceb' },
    DangDauGia: {
      code: 'DangDauGia',
      name: 'Đang đấu giá',
      color: '#2453F8',
      statusBorderColor: '#76aceb',
      statusBgColor: '#dceeff',
      statusColor: '#76aceb',
    },

    REJECT: { code: 'REJECT', name: 'Từ chối', color: '#E11F1F', statusBorderColor: '#E11F1F', statusBgColor: '#fde3e6', statusColor: '#E11F1F' },
    WAITING: {
      code: 'WAITING',
      name: 'Chờ xác nhận',
      color: 'darkgreen',
      statusBorderColor: '#f1c187',
      statusBgColor: '#fff7e4',
      statusColor: '#f1c187',
    },
    APROVE: { code: 'APROVE', name: 'Xác nhận', color: '#00AA25', statusBorderColor: '#85c6d4', statusBgColor: '#e0f9fb', statusColor: '#85c6d4' },
  },

  /** Trạng thái đấu giá  */
  AuctionStatus: {
    TEMP: { code: 'TEMP', name: 'Đấu giá tạm', color: '#2453F8', statusBorderColor: '#76aceb', statusBgColor: '#dceeff', statusColor: '#76aceb' },
    SEND_TEMP: {
      code: 'SEND_TEMP',
      name: 'Đấu giá tạm',
      color: '#2453F8',
      statusBorderColor: '#76aceb',
      statusBgColor: '#dceeff',
      statusColor: '#76aceb',
    },

    NEW: { code: 'NEW', name: 'Mới tạo', color: '#2453F8', statusBorderColor: '#76aceb', statusBgColor: '#dceeff', statusColor: '#76aceb' },
    DOING: {
      code: 'DOING',
      name: 'Đang đấu giá',
      color: 'darkgreen',
      statusBorderColor: '#f1c187',
      statusBgColor: '#fff7e4',
      statusColor: '#f1c187',
    },
    DONE: { code: 'DONE', name: 'Kết thúc', color: '#00AA25', statusBorderColor: '#85c6d4', statusBgColor: '#e0f9fb', statusColor: '#85c6d4' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: '#E11F1F', statusBorderColor: '#E11F1F', statusBgColor: '#fde3e6', statusColor: '#E11F1F' },
  },

  /** Các cách tính điểm */
  PriceScoreCalculateWay: {
    SumScore: { code: 'SumScore', name: 'Độ lệch chuẩn theo tổng điểm' },
    SumPrice: { code: 'SumPrice', name: 'Độ lệch chuẩn theo tổng thành tiền' },
    SumUnitPrice: { code: 'SumUnitPrice', name: 'Độ lệch chuẩn theo tổng đơn giá' },
  },
  /** Trạng thái đơn Po  */
  PoOrder: {
    REVICED: { code: 'REVICED', name: 'Đã nhận đơn', color: '#2453F8' },
    DOING: { code: 'DOING', name: 'Đang sản xuất', color: 'darkgreen' },
    EXPECT_COMPLETE: { code: 'EXPECT_COMPLETE', name: 'Dự kiến hoàn thành', color: '#00AA25' },
    INVENTORY: { code: 'INVENTORY', name: 'Đang tồn kho', color: '#E11F1F' },
    WAIT_REPLY: { code: 'WAIT_REPLY', name: 'Chờ NCC phản hồi', color: '#f1c187' },
    COMPLETE: { code: 'COMPLETE', name: 'Hoàn thành', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  /** ENUM (type PercentageType in ConditionType) */
  PercentageType: {
    AMOUNT: { code: 'AMOUNT', name: 'Amount' },
    PERCENTAGE: { code: 'PERCENTAGE', name: 'Percentage' },
    FIXED_AMOUNT: { code: 'FIXED_AMOUNT', name: 'Fixed amount' },
  },
  /** ENUM (type headerItemType in ConditionType) */
  headerItemType: {
    HEADER: { code: 'HEADER', name: 'Mức chung(Header)' },
    ITEM: { code: 'ITEM', name: 'Mức từng mặt hàng(Item)' },
  },
  role: {
    TGD: { code: 'TGD', name: 'Tổng giám đốc' },
    GDK: { code: 'GDK', name: 'Giám đốc' },
    TPH: { code: 'TPH', name: 'Trưởng phòng' },
    NVI: { code: 'NVI', name: 'Nhân viên' },
    PPH: { code: 'PPH', name: 'Phó phòng' },
    GSA: { code: 'GSA', name: 'Giám sát' },
    TTR: { code: 'TTR', name: 'Tổ trưởng' },
  },
  PRType: {
    ZPR1: {
      code: 'ZPR1',
      name: 'Yêu cầu mua nguyên vật liệu, vật tư phục vụ sản xuất (từ MRP)',
      description:
        'Yêu cầu mua vật tư – hàng hóa sinh ra từ nhu cầu hoạch định vật tư bên bộ phận sản xuất sẽ được tạo tự động trên hệ thống SAP thông qua chức năng hoạch định nhu cầu vật tư',
      From: 1000000000,
      To: 1999999999,
    },
    ZPR2: {
      code: 'ZPR2',
      name: 'Yêu cầu mua hàng vật tư phục vụ bảo trì (Từ PM)',
      description: 'Yêu cầu mua vật liệu, phụ tùng, công cụ cho bảo trì được thông qua chức năng tính nhu cầu vật tư cho bảo trì',
      From: 2000000000,
      To: 2999999999,
    },
    ZPR3: {
      code: 'ZPR3',
      name: 'Yêu cầu mua đầu cơ',
      description: 'Yêu cầu mua bổ sung lượng đầu cơ',
      From: 3000000000,
      To: 3999999999,
    },
    ZPR4: {
      code: 'ZPR4',
      name: 'Yêu cầu mua hàng thủ công',
      description:
        'Yêu cầu mua hàng hóa, vật phẩm khuyến mãi, mua tài sản, máy móc thiết bị, vật tư xây dựng, công cụ dụng cụ, vật tư thí nghiệm, thuê dịch vụ sửa chữa, xây dựng, quảng cáo, marketing,.. sẽ được tạo thủ công trên hệ thống',
      From: 4000000000,
      To: 4999999999,
    },
    ZPR5: {
      code: 'ZPR5',
      name: 'Yêu cầu mua hàng từ SO',
      description: 'Yêu cầu mua vật tư – hàng hóa sinh ra từ nhu cầu bán hàng bên bộ phận bán hàng',
      From: 5000000000,
      To: 5999999999,
    },
    ZPR6: {
      code: 'ZPR6',
      name: 'Yêu cầu mua hàng tích hợp',
      description: 'Yêu cầu mua vật tư – hàng hóa sinh ra từ tích hợp',
      From: 6000000000,
      To: 6999999999,
    },
    ZPR7: {
      code: 'ZPR7',
      name: 'YC mua DV CT',
      description: 'Yêu cầu mua dịch vụ - chương trình',
      From: 4000000000,
      To: 4999999999,
    },
  },

  Quotation: {
    AN: { code: 'AN', name: 'Thư hỏi giá/ báo giá', From: 6000000000, To: 6099999999 },
    ZBH: { code: 'ZBH', name: 'Thư hỏi giá BH', From: 6000000000, To: 6099999999 },
  },

  PRAsyncStatus: {
    NEW: { code: 'NEW', name: 'Chưa gửi Async' },
    SENDED: { code: 'SENDED', name: 'Đã gửi Async' },
    SUCCESS: { code: 'SUCCESS', name: 'Async thành công' },
    ERROR: { code: 'ERROR', name: 'Async lỗi' },
  },

  /** Trạng thái ngân sách */
  BudgetStatus: {
    WAIT_EPAY: {
      code: 'WAIT_EPAY',
      name: 'Chờ duyệt',
      color: '#E48617',
      backgroundColor: 'rgb(243 210 36 / 15%)',
      statusBorderColor: '#E48617',
    }, //  PR đã được tạo lệnh đề xuất điều chỉnh ngân sách trước khi được đồng bộ về PMS và đang chờ duyệt
    // WAIT_SEND_APPROVE: { code: 'WAIT_SEND_APPROVE', name: 'Chờ gửi duyệt NS', color: '#364ed1', backgroundColor: '#acb8fc' }, // PR ở trạng thái Held/Parked, đã đề xuất điều chỉnh ngân sách, đang chờ gửi duyệt
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', backgroundColor: 'rgb(24 144 255 / 15%)', statusBorderColor: '#0063D8' }, // PR ở trạng thái Chờ duyệt, đã đề xuất điều chỉnh ngân sách, đã gửi duyệt
    REJECT: { code: 'REJECT', name: 'Từ chối', color: '#F6489C', backgroundColor: 'rgb(246 72 156 / 15%)', statusBorderColor: '#F6489C' }, // Người duyệt từ chối ở PMS hoặc từ chối ở ePayment (Trường hợp đề xuất điều chỉnh ngân sách ở ePayment)
    APPROVED: { code: 'APPROVED', name: ' Đã duyệt', color: '#0A915B', backgroundColor: 'rgb(176 245 192 / 15%)', statusBorderColor: '#0A915B' }, // Người duyệt phê duyệt ở PMS hoặc phê duyệt ở ePayment (Trường hợp đề xuất điều chỉnh ngân sách ở ePayment)
  },

  PRStatusItem: {
    NotEdited: { code: 'NotEdited', name: 'Not Edited' },
    Edited: { code: 'Edited', name: 'Edited' },
  },

  BlockStatusItem: {
    NotBlocked: { code: 'NotBlocked', name: 'Not Blocked' },
    BlockByRequester: { code: 'BlockByRequester', name: 'Block By Requester' },
  },

  ApproveStatus: {
    Active: { code: 'Active', name: 'Active' },
    InRelease: { code: 'InRelease', name: 'In Release' },
    ReleaseRefused: { code: 'ReleaseRefused', name: 'Release Refused' },
    ReleaseCompleted: { code: 'ReleaseCompleted', name: 'Release Completed' },
  },
  FlowCode: {
    ZPR1: { code: 'ZPR1', name: 'Yêu cầu mua nguyên vật liệu, vật tư phục vụ sản xuất (từ MRP)' },
    ZPR2: { code: 'ZPR2', name: 'Yêu cầu mua hàng vật tư phục vụ bảo trì (Từ PM)' },
    ZPR3: { code: 'ZPR3', name: 'Yêu cầu mua đầu cơ' },
    ZPR4: { code: 'ZPR4', name: 'Yêu cầu mua hàng thủ công' },
    ZPR5: { code: 'ZPR5', name: 'Yêu cầu mua hàng từ SO' },
    ZPR6: { code: 'ZPR6', name: 'Yêu cầu mua hàng tích hợp' },
    ZPR7: { code: 'ZPR7', name: 'YC mua DV CT' },
    SPL: { code: 'SPL', name: 'Quyền duyệt pháp lý' }, // Quyền duyệt pháp lý
    SNL: { code: 'SNL', name: 'Quyền duyệt năng lực' }, // Quyền duyệt năng lực
    SAP_CODE: { code: 'SAP_CODE', name: 'Supplier Number' }, // Quyền duyệt sap code
    RUSL: { code: 'RUSL', name: 'Quyền duyệt yêu cầu chỉnh sửa pháp lý' }, // Quyền duyệt chỉnh sửa yêu cầu pháp lý
    RUSC: { code: 'RUSC', name: 'Quyền duyệt yêu cầu chỉnh sửa năng lực' }, // Quyền duyệt chỉnh sửa yêu cầu năng lực
    LS: { code: 'LS', name: 'Quyền duyệt yêu cầu khóa / mở khóa nhà cung cấp' }, // Quyền duyệt chỉnh sửa yêu cầu năng lực
    LSS: { code: 'LSS', name: 'Quyền duyệt yêu cầu khóa / mở khóa lĩnh vực mua hàng của nhà cung cấp' },
    LSMH: { code: 'LSMH', name: 'Đánh giá lịch sử mua hàng' },
    SUPPLIER_UPGRADE: { code: 'SUPPLIER_UPGRADE', name: 'Nâng cấp nhà cung cấp' },
    TTNLNCC: { code: 'TTNLNCC', name: 'Template đánh giá NCC' },
    BID: { code: 'BID', name: 'Quyền duyệt gói thầu tạm' },
    AUCTION: { code: 'AUCTION', name: 'Quyền duyệt đấu giá' },
    OFFER: { code: 'OFFER', name: 'Quyền duyệt báo giá' },
    BIDTECH: { code: 'BIDTECH', name: 'Quyền duyệt thông tin thiết lập yêu cầu kĩ thuật' },
    BIDTRADE: { code: 'BIDTRADE', name: 'Quyền duyệt thông tin thiết điều kiện thương mại' },
    BIDPRICE: { code: 'BIDPRICE', name: 'Quyền duyệt thông tin thiết lập HMCG, CCG' },
    BID_CHOOSE_SUPPLIER: { code: 'BID_CHOOSE_SUPPLIER', name: 'Quyền duyệt danh sách nhà cung cấp mời thầu' },
    OFFER_CHOOSE_SUPPLIER: { code: 'OFFER_CHOOSE_SUPPLIER', name: 'Quyền duyệt danh sách nhà cung cấp mời chào giá' },
    EVALUATE_RESULT_CAPACITY: { code: 'EVALUATE_RESULT_CAPACITY', name: 'Quyền duyệt kết quả đánh giá năng lực' },
    EVALUATE_RESULT_TRADE: { code: 'EVALUATE_RESULT_TRADE', name: 'Quyền duyệt kết quả đánh giá, chào giá, cơ cấu giá, điều kiện thương mại' },
    SUPPLIER_WIN_BID: { code: 'SUPPLIER_WIN_BID', name: 'Quyền duyệt nhà cung cấp thắng thầu' },
    SUPPLIER_WIN_OFFER: { code: 'SUPPLIER_WIN_OFFER', name: 'Quyền duyệt nhà cung cấp thắng chào giá' },
    FINISH_BID: { code: 'FINISH_BID', name: 'Gửi yêu cầu phê duyệt kết thúc thầu' },
    CONTRACT: { code: 'CONTRACT', name: 'Quyền duyệt hợp đồng' },
    CONTRACT_APPENDIX: { code: 'CONTRACT_APPENDIX', name: 'Quyền duyệt phụ lục hợp đồng' },
    CONTRACT_INSPECTION: { code: 'CONTRACT_INSPECTION', name: 'Quyền duyệt nghiệm thu hợp đồng' },
    PO: { code: 'PO', name: 'Quyền duyệt PO' },
    BUSINESSPLAN: { code: 'BUSINESSPLAN', name: 'Quyền duyệt phương án kinh doanh' },
    RECOMMENDEDPURCHASE: { code: 'RECOMMENDEDPURCHASE', name: 'Quyền đề nghị mua hàng' },
    OFFERPRICE: { code: 'OFFERPRICE', name: 'Bảng giá chào giá' },
    OFFERTRADE: { code: 'OFFERTRADE', name: 'DKTM chào giá' },
    COMPLAINT: { code: 'COMPLAINT', name: 'Khiếu nại' },
    PAYMENT: { code: 'PAYMENT', name: 'Quyền duyệt hồ sơ thanh toán' },
    PLAN_SITE_ASSESSMENT: { code: 'PLAN_SITE_ASSESSMENT', name: 'Quyền duyệt kế hoạch đánh giá hiện trường' },
    SITE_ASSESSMENT: { code: 'SITE_ASSESSMENT', name: 'Quyền duyệt đánh giá hiện trường' },
    RESERVATION: { code: 'RESERVATION', name: 'Nhu cầu sử dụng' },
    SUPPLIER_POTENTIAL: { code: 'SUPPLIER_POTENTIAL', name: 'Quyền duyệt nhà cung cấp tiềm năng' },
    SHIPMENT_YEAR: { code: 'SHIPMENT_YEAR', name: 'Cấu hình bảng giá shipment theo năm' },
    SHIPMENT_MONTH: { code: 'SHIPMENT_MONTH', name: 'Cấu hình bảng giá shipment theo tháng' },
    SHIPMENT_PLAN: { code: 'SHIPMENT_PLAN', name: 'Phương án vận chuyển' },
    REQUEST_QUOTE: { code: 'REQUEST_QUOTE', name: 'Yêu cầu báo giá' },
    BUSINESS_PLAN_TEMPLATE: { code: 'BUSINESS_PLAN_TEMPLATE', name: 'PAKD' },
    APPROVED_RFQ: { code: 'APPROVED_RFQ', name: 'Quyền duyệt RFQ' },
  },

  sapCode: {
    Z01: { code: 'Z01', name: 'Z01', groupName: 'Nhóm nội địa', start: 1000000, end: 1999999 },
    Z02: { code: 'Z02', name: 'Z02', groupName: 'Nhóm nội địa HT', start: 2000000, end: 2999999 },
    Z03: { code: 'Z03', name: 'Z03', groupName: 'Nhóm XN khẩu', start: 3000000, end: 3999999 },
    Z04: { code: 'Z04', name: 'Z04', groupName: 'Nhóm nội bộ', start: 4000000, end: 4999999 },
    Z05: { code: 'Z05', name: 'Z05', groupName: 'Nhóm địa chỉ GH', start: 5000000, end: 5999999 },
    Z06: { code: 'Z06', name: 'Z06', groupName: 'Nhóm khác', start: 6000000, end: 6999999 },
    Z07: { code: 'Z07', name: 'Z07', groupName: 'Nhóm nhân viên', start: 7000000, end: 7999999 },
    Z08: { code: 'Z08', name: 'Z08', groupName: 'Nhóm phòng ban', start: 300000000, end: 999999999 },
    Z09: { code: 'Z09', name: 'Z09', groupName: 'Nhóm vãng lai', start: 9000000, end: 909999 },
    Z88: { code: 'Z88', name: 'Z88', groupName: 'Nhóm nội bộ', start: 8000000, end: 8999999 },
    Z91: { code: 'Z91', name: 'Z91', groupName: 'Nhóm chành/BB', start: 9100000, end: 9199999 },
  },

  APPROVE_TYPE: {
    DONE: { code: 'DONE', name: 'Đã duyệt hết' },
    NOT_DONE: { code: 'NOT_DONE', name: 'Chưa duyệt hết' },
  },

  RATE_TYPE: {
    TECH: { code: 'TECH', name: 'Kỹ thuật' },
    PRICE: { code: 'PRICE', name: 'Giá ' },
    TRADE: { code: 'TRADE', name: 'DKTM' },
  },

  PRSouceType: {
    PMS: { code: 'PMS', name: 'Tạo từ PMS' },
    SAP: { code: 'SAP', name: 'Đồng bộ từ SAP' },
  },

  supplierType: {
    NEW: { code: 'NEW', name: 'Mới tạo' },
    POTENTIAL: { code: 'POTENTIAL', name: 'Nhà cung cấp tiềm năng' },
    OFFICIAL: { code: 'OFFICIAL', name: 'Nhà cung cấp chính thức' },
    TRANSIENT: { code: 'TRANSIENT', name: 'Nhà cung cấp vãng lai' },
  },

  SupplierNumberSettingAction: {
    View: {
      code: 'View',
      name: 'Xem',
      description: 'Quyền xem thông tin số nhà cung cấp',
    },
    Mutation: {
      code: 'Mutation',
      name: 'Tạo mới, nhập dữ liệu, điều chỉnh',
      description: 'Quyền xem, tạo mới, nhập dữ liệu, điều chỉnh thông tin số nhà cung cấp',
    },
  },

  SupplierNumberAprovalStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    IMPORTING: { code: 'IMPORTING', name: 'Đang nhập liệu', color: '#99e2f9', bgColor: '#e6fcf9', borderColor: '#99e2f9' },
    PENDING: { code: 'PENDING', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    REQUEST_APPROVE: { code: 'REQUEST_APPROVE', name: 'Gửi duyệt', color: '#f756a4', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    RECHECK: { code: 'RECHECK', name: 'Kiểm tra lại', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
  },

  SupplierNumberSyncStatus: {
    SYNCED: { code: 'SYNCED', name: 'Đã đồng bộ', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    NOT_SYNC: { code: 'NOT_SYNC', name: 'Chưa đồng bộ', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
  },

  RequestUpdateStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAIT_APPROVE: { code: 'WAIT_APPROVE', name: 'Đang duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    CANCEL: { code: 'CANCEL', name: 'Từ chối', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    DESTROY: { code: 'DESTROY', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
  },

  SupplierUpdateType: {
    Law: { code: 'Law', name: 'Thông tin pháp lý' },
    Capacity: { code: 'Capacity', name: 'Thông tin năng lực' },
    Lock_Supplier: { code: 'Lock_Supplier', name: 'Mở/khóa nhà cung cấp' },
    Lock_Supplier_Service: { code: 'Lock_Supplier_Service', name: 'Mở/khóa LVKD của nhà cung cấp' },
    Supplier_Upgrade: { code: 'Supplier_Upgrade', name: 'Nâng cấp nhà cung cấp' },
  },

  OfferSupplierStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    TEMP_SAVE: { code: 'TEMP_SAVE', name: 'Lưu nháp', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    SEND: { code: 'SEND', name: 'Đã gửi', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  OfferStatus: {
    MoiTao: { code: 'MoiTao', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    ChoDuyet: { code: 'ChoDuyet', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    DaDuyet: { code: 'DaDuyet', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    HoanTatCauHinh: { code: 'HoanTatCauHinh', name: 'Hoàn tất cấu hình', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    DangCauHinhGoiThau: {
      code: 'DangCauHinhGoiThau',
      name: 'Đang cấu hình',
      color: '#ED9A1F',
      bgColor: '#FCF0DD',
      borderColor: '#F5CA89',
    },
    DangDamPhanGia: {
      code: 'DangDamPhanGia',
      name: 'Đang đàm phán giá',
      color: '#ffc107',
      statusColor: '#99e2f9',
      statusBorderColor: '#99e2f9',
      statusBgColor: '#e6fcf9',
    },
    DongThau: {
      code: 'DongThau',
      name: 'Đang duyệt Doanh nghiệp thắng đấu giá',
      statusColor: '#eaa049',
      statusBorderColor: '#eaa049',
      statusBgColor: '#fdf7d8',
    },
    HoanTatDanhGia: {
      code: 'HoanTatDanhGia',
      name: 'Hoàn tất đánh giá thầu',
      statusColor: '#0a915b',
      statusBorderColor: '#0a915b',
      statusBgColor: '#def2e0',
    },
    DongDamPhanGia: {
      code: 'DongDamPhanGia',
      name: 'Hoàn tất đàm phán giá',
      color: '#1890ff',
      statusColor: '#0a915b',
      statusBorderColor: '#0a915b',
      statusBgColor: '#def2e0',
    },
    DaCongKhai: { code: 'DaCongKhai', name: 'Đã công khai', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    NopBaoGia: { code: 'NopBaoGia', name: 'NCC nộp báo giá', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    DanhGiaNCC: { code: 'DanhGiaNCC', name: 'Đang đánh giá NCC', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    ChoDuyetKetQua: { code: 'ChoDuyetKetQua', name: 'Chờ duyệt kết quả', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    Huy: { code: 'Huy', name: 'Huỷ', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
  },

  ReferenceType: {
    PR: { code: 'PR', name: 'Tham chiếu từ PR' },
    BusinessPlan: { code: 'BusinessPlan', name: 'Tham chiếu từ kế hoạch kinh doanh ' },
    Quote: { code: 'Quote', name: 'Tham chiếu từ báo giá' },
  },

  ValuationType: {
    Null: { code: 'Null', name: 'Null' },
    V15X: { code: 'V15X', name: 'V15X' },
    V150: { code: 'V150', name: 'V150' },
    V155: { code: 'V155', name: 'V155' },
  },

  ZTCODE: {
    ME51N: { code: 'ME51N', name: 'ME51N' },
    ME52N: { code: 'ME52N', name: 'ME52N' },
    ME54N: { code: 'ME54N', name: 'ME54N' },
    ME41: { code: 'ME41', name: 'ME41' },
    ME31K: { code: 'ME31K', name: 'ME31K' },
    ME21N: { code: 'ME21N', name: 'ME21N' },
    VL31N: { code: 'VL31N', name: 'VL31N' },
    VT01N: { code: 'VT01N', name: 'VT01N' },
    VI01: { code: 'VI01', name: 'VI01' },
  },

  SiteAssessmentStatus: {
    WAIT_EVALUATE: { code: 'WAIT_EVALUATE', name: 'Chờ đánh giá', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    CONSIDERING: { code: 'CONSIDERING', name: 'Đang đánh giá', color: '#EFCE22', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    APPROVING: { code: 'APPROVING', name: 'Đang duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    REQUEST_RE_CHECK: { code: 'REQUEST_RE_CHECK', name: 'Đang kiểm tra lại đánh giá', color: '#EFCE22', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    COMPLETE: { code: 'COMPLETE', name: 'Hoàn thành', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  SupplierReplyStatus: {
    REPLIED: { code: 'REPLIED', name: 'Đã trả lời', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    NOT_REPLY: { code: 'NOT_REPLY', name: 'Chưa trả lời', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    NOT_REQUIRED: {
      code: 'NOT_REQUIRED',
      name: 'Không yêu cầu trả lời',
      color: '#6E6E6E',
      bgColor: '#EEEEEE',
      borderColor: '#CCCCCC',
    },
  },

  HistoryPurchase: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    COMPLETE: { code: 'COMPLETE', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    EVALUATING: { code: 'EVALUATING', name: 'Đang đánh giá', color: '#ED9A1F', bgColor: '#efffa8', borderColor: '#ED9A1F' },
    CANCEL: { code: 'CANCEL', name: 'Từ chối', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    REVIEW: { code: 'REVIEW', name: 'Kiểm tra lại', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    PENDING: { code: 'PENDING', name: 'Chờ duyệt', color: '#f756a4', bgColor: '#fff1f0', borderColor: '#ffa39e' },
  },
  SupplierUpgradeStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    CANCEL: { code: 'CANCEL', name: 'Từ chối', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' }, // từ chối duyệt
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    PENDING: { code: 'PENDING', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    DROP: { code: 'DROP', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' }, // hủy lệnh nâng cấp
  },
  RoleType: {
    CRUD_PR: { code: 'CRUD_PR', name: 'Tạo sửa xem PR', dbCol: ['prType', 'poType'] },
    APPROVE_PR: { code: 'APPROVE_PR', name: 'Duyệt PR', dbCol: ['prType', 'poType'] },
    CRUD_PO: { code: 'CRUD_PO', name: 'Tạo sửa xem PO', dbCol: ['prType', 'poType'] },
    APPROVE_PO: { code: 'APPROVE_PO', name: 'Duyệt PO', dbCol: ['prType', 'poType'] },
  },

  RequestUpdateStatusSupplier: {
    ACTIVE: { code: 'ACTIVE', name: 'Đang hoạt động', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    DISCONTINUED: { code: 'DISCONTINUED', name: 'Khóa NCC', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    WAIT_APPROVE_DISCONTINUED: {
      code: 'WAIT_APPROVE_DISCONTINUED',
      name: 'Chờ duyệt khóa NCC',
      color: '#ED9A1F',
      bgColor: '#FCF0DD',
      borderColor: '#F5CA89',
    },
    WAIT_APPROVE_ACTIVE: {
      code: 'WAIT_APPROVE_ACTIVE',
      name: 'Chờ duyệt mở khóa NCC',
      color: '#0A915B',
      bgColor: '#DEF2E0',
      borderColor: '#0A915B',
    },
  },

  RequestUpdateStatusSupplierService: {
    ACTIVE: { code: 'ACTIVE', name: 'Đang hoạt động', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    DISCONTINUED: { code: 'DISCONTINUED', name: 'Khóa LVKD', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    WAIT_APPROVE_DISCONTINUED: {
      code: 'WAIT_APPROVE_DISCONTINUED',
      name: 'Chờ duyệt khóa LVKD',
      color: '#ED9A1F',
      bgColor: '#FCF0DD',
      borderColor: '#F5CA89',
    },
    WAIT_APPROVE_ACTIVE: {
      code: 'WAIT_APPROVE_ACTIVE',
      name: 'Chờ duyệt mở khóa LVKD',
      color: '#0A915B',
      bgColor: '#DEF2E0',
      borderColor: '#0A915B',
    },
  },

  PurchasingSource: {
    HO: { code: 'HO', name: 'HO mua hàng' },
    OS: { code: 'OS', name: 'Site tự mua hàng' },
  },

  TemplateSupplierType: {
    SCENE: { code: 'SCENE', name: 'Hiện trường' },
    PURCHASEHISTORY: { code: 'PURCHASEHISTORY', name: 'Lịch sử mua hàng' },
  },

  SupplierType: {
    NEW: { code: 'NEW', name: 'Mới tạo' },
    POTENTIAL: { code: 'POTENTIAL', name: 'Tiềm năng' },
    OFFICIAL: { code: 'OFFICIAL', name: 'Chính thức' },
  },

  RatingType: {
    WEAK: { code: 'WEAK', name: 'Yếu' },
    MEDIUM: { code: 'MEDIUM', name: 'Trung bình' },
    RATHER: { code: 'RATHER', name: 'Khá' },
    GOOD: { code: 'GOOD', name: 'Tốt' },
    VERY_GOOD: { code: 'VERY_GOOD', name: 'Rất tốt' },
  },
  AuctionType: {
    BID: { code: 'BID', name: 'Gói thầu' },
    PR: { code: 'PR', name: 'PR' },
    MatGroup: { code: 'MatGroup', name: 'Khá' },
    File: { code: 'File', name: 'File đính kèm' },
  },

  AuctionOutsideType: {
    BID: { code: 'BID', name: 'Gói thầu' },
    PR: { code: 'PR', name: 'PR' },
    OFFER: { code: 'OFFER', name: 'báo giá' },
  },

  QuickPriceType: {
    PR: { code: 'PR', name: 'PR' },
    ExMatGr: { code: 'ExMatGr', name: 'External material group' },
  },

  /**Loại khóa/mở khóa */
  LockType: {
    LOCK: { code: 'LOCK', name: 'Khóa' },
    UNLOCK: { code: 'UNLOCK', name: 'Mở Khóa' },
  },

  /**Nguồn tham chiếu dùng cho contract */
  REFERENCE_SOURCE: {
    PR: { code: 'PR', name: 'PR' },
    BID: { code: 'BID', name: 'Đấu thầu' },
    AUCTION: { code: 'AUCTION', name: 'Đấu giá' },
    RECOMMENDED_PURCHASE: { code: 'RECOMMENDED_PURCHASE', name: 'Đề nghị mua hàng' },
  },

  ContractInspectionStatus: {
    TEMPORARY: { code: 'TEMPORARY', name: 'Lưu tạm', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    COMPLETE: { code: 'COMPLETE', name: 'Hoàn thành nghiệm thu', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
  },

  /**Nguồn tham chiếu dùng cho PO */
  ReferenceSourcePo: {
    CONTRACT: { code: 'CONTRACT', name: 'Hợp đồng' },
    BID: { code: 'BID', name: 'Đấu thầu' },
    AUCTION: { code: 'AUCTION', name: 'Đấu giá' },
    PR: { code: 'PR', name: 'PR' },
    RECOMMENDED_PURCHASE: { code: 'RECOMMENDED_PURCHASE', name: 'Đề nghị mua hàng' },
    SHIPMENT_COST: { code: 'SHIPMENT_COST', name: 'Shipment cost' },
  },

  PrItemClosed: {
    H: { code: 'H', name: 'hold', color: '#0063D8', bgColor: '', borderColor: '#0063D8' },
    X: { code: 'X', name: 'Closed', color: 'red', bgColor: '', borderColor: '#2A7DDF' },
    L: { code: 'L', name: 'Delete', color: 'red', bgColor: '', borderColor: '#2A7DDF' },
  },

  CompanyLevel: {
    SUBSIDIARY: { code: 'SUBSIDIARY', name: 'công ty con', color: '#0063D8', bgColor: '', borderColor: '#0063D8' },
    GROUP: { code: 'X', name: 'group', color: 'red', bgColor: '', borderColor: '#2A7DDF' },
  },

  BillStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    PROCESS: { code: 'PROCESS', name: 'Đang xử lý', color: 'green', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    WAIT_CONFIRM: { code: 'WAIT_CONFIRM', name: 'Chờ xác nhận', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    CONFIRMED: { code: 'CONFIRMED', name: 'Đã xác nhận', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  BillPaymentStatus: {
    NEW: { code: 'NEW', name: 'Chưa thanh toán', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    PAID: { code: 'PAID', name: 'Đã thanh toán', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  /**Dùng cho phân quyền */
  Platform: {
    WEB_ADMIN: { code: 'WEB_ADMIN', name: 'Web admin' },
    APP: { code: 'APP', name: 'App' },
  },

  ContractAppendixStatus: {
    TEMPORARY: { code: 'TEMPORARY', name: 'Lưu tạm', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAIT_APPROVE: { code: 'WAIT_APPROVE', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    REQUEST_RE_CHECK: { code: 'REQUEST_RE_CHECK', name: 'Yêu cầu kiểm tra lại', color: '#AA0808', bgColor: '#E9BFBF', borderColor: '#AA0808' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    WAIT_ACTIVE: { code: 'WAIT_ACTIVE', name: 'Chờ kích hoạt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    PROCESSING: { code: 'PROCESSING', name: 'Đang thực hiện', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    DONE: { code: 'DONE', name: 'Đóng', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  /** Loại dề xuất (ngân sách) */
  ProposeBudgetType: {
    FIRST_PERIOD: { code: 'FIRST_PERIOD', name: 'Điều chỉnh đầu kỳ' },
    DURING_PERIOD: { code: 'DURING_PERIOD', name: 'Phát sinh trong kỳ' },
    TRANSFER_BUDGET: { code: 'TRANSFER_BUDGET', name: 'Điều chuyển ngân sách' },
    LAST_BUDGET: { code: 'FINAL_BUDGET', name: 'Điều chỉnh cuối kỳ' },
  },

  /** Loại chứng từ (ngân sách) */
  ReceiptBudgetType: {
    PR: { code: 'PR', name: 'PR' },
    PO: { code: 'PO', name: 'PO' },
  },

  /** Cơ sở điều chỉnh (ngân sách) */
  BaseAdjust: {
    QUANTITY: { code: 'QUANTITY', name: 'Do lượng' },
    PRICE: { code: 'PRICE', name: 'Do giá' },
    BOTH: { code: 'BOTH', name: 'Cả hai' },
  },

  /** Loại nghiệm thu */
  contractInspectionType: {
    Q: { code: 'Q', name: 'Nghiệm thu theo số lượng hợp đồng' },
    P: { code: 'P', name: 'Nghiệm thu theo số lượng thanh toán' },
  },

  /** Loại đấu thầu */
  BidType: {
    PLAN: { code: 'PLAN', name: 'Thầu theo kế hoạch' },
    SURVEY: { code: 'SURVEY', name: 'Thầu khảo sát' },
    SHIPMENT: { code: 'SHIPMENT', name: 'Thầu theo shipment' },
  },

  ComplaintType: {
    Q2: { code: 'Q2', name: 'Vendor Complaint - Khiếu nại nhà cung cấp' },
  },

  ReferenceComplaintType: {
    PO: { code: 'PO', name: 'Theo PO' },
    CONTRACT: { code: 'CONTRACT', name: 'Theo hợp đồng' },
  },

  CodingComplaint: {
    KT_QM: { code: 'KT_QM', name: 'Các vẫn đề lượng NCR' },
    MEASURE: { code: 'MEASURE', name: 'Đáng giá kết quả giám sát' },
    QM: { code: 'QM', name: 'Problem Detail' },
    VW1: { code: 'VW1', name: 'Production' },
    VW2: { code: 'VW2', name: 'Organization' },
    VW3: { code: 'VW3', name: 'Salte and distribution' },
    VW4: { code: 'VW4', name: 'Layout changes' },
    YESNO: { code: 'YESNO', name: 'Đánh giá kết quả kiểm tra ngoại quan' },
  },

  ChoiceComplaintKTQM: {
    C01: { code: 'C01', name: 'Thiếu chứng từ' },
    C02: { code: 'C02', name: 'Sai số lượng hàng hóa' },
    C03: { code: 'C03', name: 'Chất lượng săn phẩm' },
    C04: { code: 'C04', name: 'Thời gian giao hàng/Tiến độ' },
    C05: { code: 'C05', name: 'Dịch vụ bán hàng' },
    C06: { code: 'C06', name: 'Dịch vụ bảo hành' },
    C07: { code: 'C07', name: 'Dịch vụ vận chuyển' },
    C08: { code: 'C08', name: 'Sử dụng sai so với HDSD' },
    C09: { code: 'C09', name: 'Bảo quản sai so với MSDS' },
    C10: { code: 'C10', name: 'Sai chủng loại hàng hóa' },
    C11: { code: 'C11', name: 'Khác' },
    C12: { code: 'C12', name: 'Hàng hóa hết hạng sử dụng' },
    C13: { code: 'C13', name: 'KH không còn nhu cầu sử dụng' },
    C14: { code: 'C14', name: 'KH mất khả năng thanh toán' },
  },

  ChoiceComplaintMeasure: {
    C0001: { code: 'C0001', name: 'Bình thường ' },
    C0002: { code: 'C0002', name: 'Có nguy cơ' },
    C0003: { code: 'C0003', name: 'Vuọt quá giá trị cho phép' },
    C0004: { code: 'C0004', name: 'Cực kì nguy hiểm' },
  },

  ChoiceComplaintQM: {
    C1: { code: 'C1', name: 'Problem notification' },
    C2: { code: 'C2', name: 'Danger note' },
    C3: { code: 'C3', name: 'Quality activity' },
  },
  ChoiceComplaintVW1: {
    C0001: { code: 'C0001', name: 'Assembly Processing' },
    C0002: { code: 'C0002', name: 'Control' },
  },
  ChoiceComplaintVW2: {
    C0001: { code: 'C0001', name: 'Structure' },
    C0002: { code: 'C0002', name: 'Schedule' },
  },
  ChoiceComplaintVW3: {
    C0001: { code: 'C0001', name: 'Division 1' },
    C0002: { code: 'C0002', name: 'Division 2' },
    C0003: { code: 'C0003', name: 'Division 3' },
  },
  ChoiceComplaintVW4: {
    C0001: { code: 'C0001', name: 'Genaral' },
  },
  ChoiceComplaintYESNO: {
    NO: { code: 'NO', name: 'Không đạt' },
    YES: { code: 'YES', name: 'Đạt' },
  },

  ComplaintStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAIT_APPROVE: { code: 'WAIT_APPROVE', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    CHECK_AGAIN: { code: 'CHECK_AGAIN', name: 'Yêu cầu kiểm tra lại', color: 'orangered', bgColor: '#FCF0DD', borderColor: 'orangered' },
    WAIT_NCC_CONFIRMATION: {
      code: 'WAIT_NCC_CONFIRMATION',
      name: 'Chờ NCC xác nhận',
      color: '#A74AE1',
      bgColor: '#F0E0FA',
      backgroundColor: '#A74AE1',
    },
    SUPPLIER_AGREES: { code: 'SUPPLIER_AGREES', name: 'NCC đồng ý', color: '#008000', bgColor: '#DEF2E0', borderColor: '#00AA25' },
    SUPPLIER_REFUSED: { code: 'SUPPLIER_REFUSED', name: 'NCC từ chối', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
  },

  /**Khắc phục khiếu nại */
  CodeGroup: {
    TM_NCC: { code: 'TM_NCC', name: 'TM_NCC' },
    TC_NCC: { code: 'TC_NCC', name: 'TC_NCC' },
    TT_NCC: { code: 'TT_NCC', name: 'TT_NCC' },
  },

  TaskGroup: {
    T007: { code: '007', name: '007' },
    T010: { code: '010', name: '010' },
    T009: { code: '009', name: '009' },
  },

  /** Ngăn ngừa */
  ActivityCode: {
    TM_MUA: { code: 'TM_MUA', name: 'TM_MUA' },
    TC_MUA: { code: 'TC_MUA', name: 'TC_MUA' },
    TT_MUA: { code: 'TT_MUA', name: 'TT_MUA' },
  },

  ActivityGroup: {
    T001: { code: '001', name: '001' },
    T003: { code: '003', name: '003' },
    T007: { code: '007', name: '007' },
  },

  /** Nguồn tham chiếu hóa đơn */
  referencesInvoice: {
    C: { code: 'C', name: 'Theo hợp đồng' },
    P: { code: 'P', name: 'Theo PO' },
  },

  SettingString: {
    KES_URL: {
      code: 'KES_URL',
      name: 'Đường dẫn trang KES',
      valueString: 'https://ktg-pms-client-kes-dev.apetechs.co',
      isDeleted: false,
      type: 'Supplier',
    },
    KTG_URL: {
      code: 'KTG_URL',
      name: 'Đường dẫn trang KTG',
      valueString: 'https://ktg-pms-client-dev.apetechs.co',
      isDeleted: false,
      type: 'Supplier',
    },

    VAT: {
      code: 'VAT',
      name: 'Thuế giá trị gia tăng',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },

    IMPORT_TAX_PERCENT: {
      code: 'IMPORT_TAX_PERCENT',
      name: '% Thuế nhập khẩu',
      value: 5,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },

    SELF_DEFENSE_TAX_PERCENT: {
      code: 'SELF_DEFENSE_TAX_PERCENT',
      name: '% Thuế tự vệ',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },

    CORPORATE_INCOME_TAX_PERCENT: {
      code: 'CORPORATE_INCOME_TAX_PERCENT',
      name: '% Thuế thu nhập doanh nghiệp',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },

    NUMBER_LOAN_MONTHS: {
      code: 'NUMBER_LOAN_MONTHS',
      name: 'số tháng vay',
      value: 12,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },

    INTEREST_EXPENSE_PERCENT: {
      code: 'INTEREST_EXPENSE_PERCENT',
      name: '% Chi phí lãi vay',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },

    PPR_SERVICE_FEE: { code: 'PPR_SERVICE_FEE', name: 'Phí dịch vụ PPR ', value: 10, isDeleted: false, type: 'DynamicConfiguration' },
    RECEIVING_COST_WHEN_PURCHASING_PERCENT: {
      code: 'RECEIVING_COST_WHEN_PURCHASING_PERCENT',
      name: 'CP tiếp nhận khi mua hàng %',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },
    SHIPPING_COST_WHEN_PURCHASING_PERCENT: {
      code: 'SHIPPING_COST_WHEN_PURCHASING_PERCENT',
      name: 'CP vận chuyển khi mua hàng %',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },
    SHIPPING_AND_HANDLING_WHEN_SELLING_PERCENT: {
      code: 'SHIPPING_AND_HANDLING_WHEN_SELLING_PERCENT',
      name: 'CP vận chuyển bốc xếp khi bán %',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },
    MANAGEMENT_COST_PERCENT: {
      code: 'MANAGEMENT_COST_PERCENT',
      name: 'CP quản lý %',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },

    BANKING_COST_PERCENT: {
      code: 'BANKING_COST_PERCENT',
      name: 'Chi phí Ngân hàng %',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },
    INSURANCE_COST_PERCENT: {
      code: 'INSURANCE_COST_PERCENT',
      name: 'Chi phí Bảo hiểm %',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },
    WARRANTY_COST_PERCENT: {
      code: 'WARRANTY_COST_PERCENT',
      name: 'Chi phí Bảo hành %',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },
    BONUS_COST_PERCENT: {
      code: 'BONUS_COST_PERCENT',
      name: 'Chi phí thưởng %',
      value: 10,
      isDeleted: false,
      type: 'DynamicConfiguration',
    },
  },

  SettingStringTypeDynamic: {
    string: {
      code: 'string',
      name: 'string',
    },
    number: {
      code: 'number',
      name: 'number',
    },
    DynamicConfiguration: {
      code: 'DynamicConfiguration',
      name: 'DynamicConfiguration',
    },
  },
  OrderType: {
    PRODUCT: { code: 'PRODUCT', name: 'Chào giá mua hàng hóa' },
    SHIPPING: { code: 'SHIPPING', name: 'Chào giá tìm đơn vị vận chuyển' },
  },

  PriceListType: {
    INBOUND_SHIPMENT: { code: 'INBOUND_SHIPMENT', name: 'Bảng giá cho Inbound Shipment Cost' },
  },

  PaymentStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0064D9', bgColor: '#99C1F0', borderColor: '#0064D9' },
    CHECKING: { code: 'CHECKING', name: 'Đang kiểm tra hồ sơ', color: '#BA066C', bgColor: '#EEC1DA', borderColor: '#BA066C' },
    WAIT_APPROVE: { code: 'WAIT_APPROVE', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    CONFIRMED: { code: 'CONFIRMED', name: 'Đã xác nhận', color: '#BA066C', bgColor: '#EEC1DA', borderColor: '#BA066C' },
    REQUEST_CONFIRM: { code: 'REQUEST_CONFIRM', name: 'Yêu cầu xác nhận lại', color: '#BA066C', bgColor: '#EEC1DA', borderColor: '#BA066C' },
    PAYING: { code: 'PAYING', name: 'Đang thanh toán', color: '#D8A800', bgColor: '#ddcc8c', borderColor: '#D8A800' },
    PAID: { code: 'PAID', name: 'Đã thanh toán', color: '#006D6A', bgColor: '#F9F2D9', borderColor: '#006D6A' },
    REQUEST_RECHECK: { code: 'REQUEST_RECHECK', name: 'Yêu cầu kiểm tra lại', color: '#AA0808', bgColor: '#E9BFBF', borderColor: '#AA0808' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', description: 'Đã duyệt', color: '#008000', bgColor: '#DEF2E0', borderColor: '#00AA25' },
  },

  /* type nhân viên (EMPLOYEE) dùng để phân quyền chứ không có hiển thị trên cây nên 
  thêm type nhân viên để phục vụ cho lúc thêm mới nhân viên và chỉnh sửa nhân viên 
  dành cho chức năng phân quyền tính năng 
   */
  OrganizationalType: {
    COMPANY: { code: 'COMPANY', name: 'Công ty', entityName: 'CompanyEntity', lstDataName: 'listOfDataCompany', nameShow: '(Công ty)' },
    BLOCK: { code: 'BLOCK', name: 'Khối', entityName: 'BlockEntity', lstDataName: 'listOfDataBlock', nameShow: '(Khối)' },
    DEPARTMENT: {
      code: 'DEPARTMENT',
      name: 'Phòng ban',
      entityName: 'DepartmentEntity',
      lstDataName: 'listOfDataDepartment',
      nameShow: '(Phòng ban)',
    },
    PART: { code: 'PART', name: 'Bộ phận', entityName: 'PartEntity', lstDataName: 'listOfDataPart', nameShow: '(Bộ phận)' },
    POSITION: { code: 'POSITION', name: 'Vị trí', entityName: 'PositionEntity', lstDataName: 'listOfDataPosition', nameShow: '(Vị trí)' },
    EMPLOYEE: { code: 'EMPLOYEE', name: 'Nhân viên', entityName: 'EmployeeEntity', lstDataName: 'listOfDataEmployee', nameShow: '(Nhân viên)' },
  },

  RoundUpContCol: {
    SafeInventory: { code: 'SafeInventory', name: 'Tồn kho an toàn', value: 1 },
    QuantityInStock: { code: 'QuantityInStock', name: 'Số lượng tồn kho ', value: 2 },
    QuantityDemandedInMonthX: { code: 'QuantityDemandedInMonthX', name: 'Số lượng nhu cầu tháng X', value: 3 },
    NumberOfunfinishedPRsAndPOsOfMonthX: { code: 'NumberOfunfinishedPRsAndPOsOfMonthX', name: 'Số lượng PR + PO dang dở của tháng X ', value: 4 },
    InventoryOfMonthX: { code: 'InventoryOfMonthX', name: 'Tồn cuối tháng X', value: 5 },
    QuantityDemandedInMonthX1: { code: 'QuantityDemandedInMonthX1', name: 'Số lượng nhu cầu tháng X + 1', value: 6 },
    NumberOfunfinishedPRsAndPOsOfMonthN1: { code: 'NumberOfunfinishedPRsAndPOsOfMonthN1', name: 'Số lượng PR + PO dang dở tháng N + 1', value: 7 },
    InventoryOfMonthX1: { code: 'InventoryOfMonthX1', name: 'Tồn cuối tháng X + 1', value: 8 },
    QuantityDemandedInMonthX2: { code: 'QuantityDemandedInMonthX2', name: 'Số lượng nhu cầu tháng X + 2', value: 9 },
    NumberOfunfinishedPRsAndPOsOfMonthN2: { code: 'NumberOfunfinishedPRsAndPOsOfMonthN2', name: 'Số lượng PR + PO dang dở tháng N + 2', value: 10 },
    InventoryOfMonthX2: { code: 'InventoryOfMonthX2', name: 'Tồn cuối tháng X + 2', value: 11 },
    QuantityDemandedInMonthX3: { code: 'QuantityDemandedInMonthX3', name: 'Số lượng nhu cầu tháng X + 3', value: 12 },
    NumberOfunfinishedPRsAndPOsOfMonthN3: { code: 'NumberOfunfinishedPRsAndPOsOfMonthN3', name: 'Số lượng PR + PO dang dở tháng N + 3', value: 13 },
    PRMRPQuantity: { code: 'PRMRPQuantity', name: 'Số lượng PR MRP', value: 14 },
    DeliveryDate: { code: 'DeliveryDate', name: 'Ngày giao hàng', value: 15 },
    MinInventoryInQuarterX: { code: 'MinInventoryInQuarterX', name: 'Tồn kho min quý X ', value: 16 },
    AverageSalesMonthOfYearX: { code: 'AverageSalesMonthOfYearX', name: 'Trung bình bán/ tháng của năm X', value: 17 },
    AverageSalesPlanMonthRegisteredInYearY: {
      code: 'AverageSalesPlanMonthRegisteredInYearY',
      name: 'Trung bình kế hoạch bán/ tháng đăng ký năm Y ',
      value: 18,
    },
    NumberMachinesBox: { code: 'NumberMachinesBox', name: 'Số máy / thùng ', value: 19 },
    Long: { code: 'Long', name: 'Dài', value: 20 },
    Width: { code: 'Width', name: 'Rộng', value: 21 },
    High: { code: 'High', name: 'Cao', value: 22 },
  },

  TimeType: {
    Min: { code: 'Min', name: 'Phút', suffix: 'm' },
    Hours: { code: 'Hours', name: 'Giờ', suffix: 'd' },
    Day: { code: 'Day', name: 'Ngày', suffix: 'h' },
    Month: { code: 'Month', name: 'Tháng', suffix: 'M' },
    Year: { code: 'Year', name: 'Năm', suffix: 'y' },
  },

  DataMapping: {
    Database: { code: 'Database', name: 'Lấy dữ liệu từ thiết lập' },
    DynamicSetup: { code: 'DynamicSetup', name: 'Thiết lập động' },
  },

  RoundUpContTemplateStatus: {
    New: { code: 'New', name: 'Mới tạo', color: '#0063D8', backgroundColor: 'rgb(24 144 255 / 15%)', statusBorderColor: '#0063D8' }, // PR ở trạng thái Chờ duyệt, đã đề xuất điều chỉnh ngân sách, đã gửi duyệt
    Active: { code: 'Active', name: ' Đang hoạt động', color: '#0A915B', backgroundColor: 'rgb(176 245 192 / 15%)', statusBorderColor: '#0A915B' }, // Người duyệt phê duyệt ở PMS hoặc phê duyệt ở ePayment (Trường hợp đề xuất điều chỉnh ngân sách ở ePayment)
    UnActive: { code: 'UnActive', name: 'Ngưng hoạt động', color: '#F6489C', backgroundColor: 'rgb(246 72 156 / 15%)', statusBorderColor: '#F6489C' }, // Người duyệt từ chối ở PMS hoặc từ chối ở ePayment (Trường hợp đề xuất điều chỉnh ngân sách ở ePayment)
  },

  ShipmentConfigTemplate: {
    New: { code: 'New', name: 'Mới tạo', color: '#0063D8', backgroundColor: 'rgb(24 144 255 / 15%)', statusBorderColor: '#0063D8' }, // PR ở trạng thái Chờ duyệt, đã đề xuất điều chỉnh ngân sách, đã gửi duyệt
    Active: { code: 'Active', name: ' Đang hoạt động', color: '#0A915B', backgroundColor: 'rgb(176 245 192 / 15%)', statusBorderColor: '#0A915B' }, // Người duyệt phê duyệt ở PMS hoặc phê duyệt ở ePayment (Trường hợp đề xuất điều chỉnh ngân sách ở ePayment)
    Cancel: { code: 'Cancel', name: 'Ngưng hoạt động', color: '#F6489C', backgroundColor: 'rgb(246 72 156 / 15%)', statusBorderColor: '#F6489C' }, // Người duyệt từ chối ở PMS hoặc từ chối ở ePayment (Trường hợp đề xuất điều chỉnh ngân sách ở ePayment)
  },

  RoundUpContTemplateType: {
    KG: { code: 'KG', name: 'Theo KG' },
    CBM: { code: 'CBM', name: 'Theo CBM' },
  },

  RoundUpContStatus: {
    Draft: {
      code: 'Draft',
      name: 'Nháp',
      color: '#A67C00',
      backgroundColor: 'rgba(255, 223, 128, 0.15)',
      statusBorderColor: '#A67C00',
    },
    New: {
      code: 'New',
      name: 'Mới tạo',
      color: '#1890FF',
      backgroundColor: 'rgba(24, 144, 255, 0.15)',
      statusBorderColor: '#1890FF',
    },
    Cancel: {
      code: 'Cancel',
      name: 'Hủy',
      color: '#F5222D',
      backgroundColor: 'rgba(245, 34, 45, 0.15)',
      statusBorderColor: '#F5222D',
    },
    Doing: {
      code: 'Doing',
      name: 'Đang thực hiện',
      color: '#13C2C2',
      backgroundColor: 'rgba(19, 194, 194, 0.15)',
      statusBorderColor: '#13C2C2',
    },
    Complete: {
      code: 'Complete',
      name: 'Hoàn thành',
      color: '#52C41A',
      backgroundColor: 'rgba(82, 196, 26, 0.15)',
      statusBorderColor: '#52C41A',
    },
  },

  BusinessPlanTemplateStatus: {
    New: { code: 'New', name: 'Mới tạo', color: '#0063D8', backgroundColor: 'rgb(24 144 255 / 15%)', statusBorderColor: '#0063D8' }, // PR ở trạng thái Chờ duyệt, đã đề xuất điều chỉnh ngân sách, đã gửi duyệt
    Active: { code: 'Active', name: ' Đang hoạt động', color: '#0A915B', backgroundColor: 'rgb(176 245 192 / 15%)', statusBorderColor: '#0A915B' }, // Người duyệt phê duyệt ở PMS hoặc phê duyệt ở ePayment (Trường hợp đề xuất điều chỉnh ngân sách ở ePayment)
    UnActive: { code: 'UnActive', name: 'Ngưng hoạt động', color: '#F6489C', backgroundColor: 'rgb(246 72 156 / 15%)', statusBorderColor: '#F6489C' }, // Người duyệt từ chối ở PMS hoặc từ chối ở ePayment (Trường hợp đề xuất điều chỉnh ngân sách ở ePayment)
  },

  BusinessPlanCol: {
    QuantityInStock: { code: 'QuantityInStock', name: 'Số lượng tồn kho', value: 1 },
    QuantityInRoad: { code: 'QuantityInRoad', name: 'Số lượng hàng đi đường', value: 2 },
    UnitPricePurchases: { code: 'UnitPricePurchases', name: 'Đơn giá mua hàng đi đường', value: 3 },
    ValueGoodInRoad: { code: 'ValueGoodInRoad', name: 'Trị giá hàng đi đường', value: 4 },
    UnitPurchasePrice: { code: 'UnitPurchasePrice', name: 'Đơn giá mua hàng về', value: 5 },
    QuantityArrived: { code: 'QuantityArrived', name: 'Số lượng hàng về', value: 6 },
    UnitPriceQuote: { code: 'UnitPriceQuote', name: 'Đơn giá báo giá', value: 7 },
    BiddingPackagePrice: { code: 'BiddingPackagePrice', name: 'Đơn giá gói thầu', value: 8 },
    ExchangeValue: { code: 'ExchangeValue', name: 'Tỷ giá Quy đổi ', value: 9 },
  },

  RecommendedPurchaseTemplateStatus: {
    New: { code: 'New', name: 'Mới tạo', color: '#0063D8', backgroundColor: 'rgb(24 144 255 / 15%)', statusBorderColor: '#0063D8' }, // PR ở trạng thái Chờ duyệt, đã đề xuất điều chỉnh ngân sách, đã gửi duyệt
    Active: { code: 'Active', name: ' Đang hoạt động', color: '#0A915B', backgroundColor: 'rgb(176 245 192 / 15%)', statusBorderColor: '#0A915B' }, // Người duyệt phê duyệt ở PMS hoặc phê duyệt ở ePayment (Trường hợp đề xuất điều chỉnh ngân sách ở ePayment)
    UnActive: { code: 'UnActive', name: 'Ngưng hoạt động', color: '#F6489C', backgroundColor: 'rgb(246 72 156 / 15%)', statusBorderColor: '#F6489C' }, // Người duyệt từ chối ở PMS hoặc từ chối ở ePayment (Trường hợp đề xuất điều chỉnh ngân sách ở ePayment)
  },

  RecommendedPurchaseCol: {
    QuantityRequestedForQuote: { code: 'QuantityRequestedForQuote', name: 'Số lượng yêu cầu báo giá', value: 1 },
    NumberOfBiddingRequests: { code: 'NumberOfBiddingRequests', name: 'Số lượng yêu cầu đấu thầu', value: 2 },
    MostRecentPurchasePrice: { code: 'MostRecentPurchasePrice', name: 'Đơn giá mua gần nhất', value: 3 },
    MostRecentlyPurchasedQuantity: { code: 'MostRecentlyPurchasedQuantity', name: 'Số lượng mua gần nhất', value: 4 },
    QuantityInStock: { code: 'QuantityInStock', name: 'Số lượng tồn kho', value: 5 },
    UnitPriceOfInventory: { code: 'UnitPriceOfInventory', name: 'Đơn giá tồn kho', value: 6 },
  },

  /** Loại khai báo chi phí dùng cho shipment cost*/
  dataTypeDeclaration: {
    DeliveryItem: { code: 'DeliveryItem', name: 'Delivery item' },
    HUGroup: { code: 'HUGroup', name: 'HU Group' },
    ShipmentCostItem: { code: 'ShipmentCostItem', name: 'Shipment cost item' },
  },

  BusinessPlanStatus: {
    Draft: { code: 'Draft', name: ' Bản nháp', color: 'rgb(69 10 145)', backgroundColor: 'white', statusBorderColor: 'rgb(69 10 145)' },
    New: { code: 'New', name: 'Mới tạo', color: '#0063D8', backgroundColor: 'rgb(24 144 255 / 15%)', statusBorderColor: '#0063D8' },
    Wait: {
      code: 'Wait',
      name: 'Chờ duyệt',
      color: 'rgb(216 211 0)',
      backgroundColor: 'rgb(222 255 24 / 15%)',
      statusBorderColor: ' rgb(216 207 0)',
    },
    Approved: { code: 'Approved', name: ' Đã duyệt', color: '#0A915B', backgroundColor: 'rgb(176 245 192 / 15%)', statusBorderColor: '#0A915B' }, // Người duyệt phê duyệt ở PMS hoặc phê duyệt ở ePayment (Trường hợp đề xuất điều chỉnh ngân sách ở ePayment)
    Cancel: {
      code: 'Cancel',
      name: 'Đã hủy',
      color: 'rgb(246 72 72)',
      backgroundColor: 'rgb(246 72 72 / 15%)',
      statusBorderColor: 'rgb(255 0 0)',
    },
    CheckAgain: {
      code: 'CheckAgain',
      name: 'Yêu cầu kiểm tra lại',
      color: '#F6489C',
      backgroundColor: 'rgb(246 72 156 / 15%)',
      statusBorderColor: '#F6489C',
    },
  },

  PurchaseKpi: {
    MONTH: { code: 'MONTH', name: 'Tháng' },
    PRECIOUS: { code: 'PRECIOUS', name: 'Quý' },
    YEAR: { code: 'YEAR', name: 'Năm' },
  },

  KpiStatus: {
    NOT_DECENTRALIZATION: { code: 'NOT_DECENTRALIZATION', name: 'Chưa phân quyền', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    DECENTRALIZATION: { code: 'DECENTRALIZATION', name: 'Đã phân quyền', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  SRCTYPE: {
    SYSTEM: { code: 'SYSTEM', name: 'Hệ thống ' },
    MANUAL: { code: 'MANUAL', name: 'Người dùng nhập' },
  },

  TypeOf: {
    CURRENCY: { code: 'CURRENCY', name: 'Tiền tệ' },
    PERCENT: { code: 'PERCENT', name: 'Phần trăm' },
  },

  RoleTake: {
    USER: { code: 'USER', name: 'user' },
    EMPLOYEE: { code: 'EMPLOYEE ', name: 'nhân viên' },
  },

  RoleData: {
    // Child
    Person: { code: 'Person', name: 'user' },
    Child: { code: 'Child', name: 'nhân viên' },
    AllCompany: { code: 'AllCompany', name: 'nhân viên' },
    All: { code: 'All', name: 'nhân viên' },
  },

  TicketEvaluationKpiStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    EVALUATING: { code: 'EVALUATING', name: 'Đang đánh giá', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    REVIEWED: { code: 'REVIEWED', name: 'Đã đánh giá', color: '#EFCE22', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    WAIT_CONFIRMATION: {
      code: 'WAIT_CONFIRMATION',
      name: 'Chờ xác nhận',
      color: 'rgb(216 211 0)',
      bgColor: 'rgb(222 255 24 / 15%)',
      borderColor: ' rgb(216 207 0)',
    },
    CHECK_AGAIN: {
      code: 'CHECK_AGAIN',
      name: 'Yêu cầu kiểm tra lại',
      color: '#F6489C',
      bgColor: 'rgb(246 72 156 / 15%)',
      borderColor: '#F6489C',
    },
    COMPLETED: { code: 'COMPLETED', name: 'Hoàn tất', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  Month: {
    LAST_MONTH: { code: 'LAST_MONTH', name: 'Ngày cuối cùng của tháng ' },
    SPECIFIC_DATE: { code: 'SPECIFIC_DATE', name: 'Ngày cụ thể ' },
  },
  Quarter: {
    LAST_QUARTER: { code: 'LAST_QUARTER', name: 'Ngày cuối cùng của quý' },
    SPECIFIC_DATE: { code: 'SPECIFIC_DATE', name: 'Ngày cụ thể ' },
  },
  Year: {
    LAST_YEAR: { code: 'LAST_YEAR', name: 'Ngày cuối cùng của năm ' },
    SPECIFIC_DATE: { code: 'SPECIFIC_DATE', name: 'Ngày cụ thể ' },
  },

  RoleSupplierNumber: {
    BusinessPartnerWrite: { code: 'BusinessPartnerWrite', name: 'Nhập liệu role Business Partner' },
    BusinessPartnerEdit: { code: 'BusinessPartnerEdit', name: 'Điều chỉnh role Business Partner' },
    SupplierWrite: { code: 'SupplierWrite', name: 'Nhập liệu role Supplier' },
    SupplierEdit: { code: 'SupplierEdit', name: 'Điều chỉnh role Supplier' },
    FISupplierWrite: { code: 'FISupplierWrite', name: 'Nhập liệu role FI Supplier' },
    FISupplierEdit: { code: 'FISupplierEdit', name: 'Điều chỉnh role FI Supplier' },
  },
  RoleEnum: {
    // Thiết lập
    Country: { name: 'Quốc gia', path: '/setting/country', code: 'Country' },
    Region: { name: 'Tỉnh thành', path: '/setting/region', code: 'Region' },
    ServiceTemplate: { name: 'Template mua hàng', path: '/setting/service-template', code: 'ServiceTemplate' },
    SupplierTemplate: { name: 'Template đánh giá NCC', path: '/setting/supplier-template', code: 'SupplierTemplate' },
    RoundUpContTemplate: { name: 'Template Làm Tròn Cont', path: '/setting/round-up-cont-template', code: 'RoundUpContTemplate' },
    BusinessPlanTemplate: { name: 'Template PAKD', path: '/setting/business-plan-template', code: 'BusinessPlanTemplate' },
    RecommendedPurchaseTemplate: { name: 'Template ĐNMH', path: '/setting/recommended-purchase-template', code: 'RecommendedPurchaseTemplate' },
    PriceList: { name: 'Thiết lập bảng giá PO', path: '/setting/price-list', code: 'PriceList' },
    PurchaseKpi: { name: 'Template KPI mua hàng', path: '/purchase-kpi/purchase-kpi', code: 'PurchaseKpi' },
    Procedure: { name: 'Procedure', path: '/setting/procedure', code: 'Procedure' },
    ConditionTypeMaster: { name: 'Condition type', path: '/setting/condition-type-master', code: 'ConditionTypeMaster' },
    SupplierSchema: { name: 'Supplier Schema', path: '/setting/supplier-schema', code: 'SupplierSchema' },
    PurchasingOrgSchema: { name: 'Purchasing Org Schema', path: '/setting/purchasing-org-schema', code: 'PurchasingOrgSchema' },
    Schema: { name: 'Bảng Schema PO ', path: '/setting/schema', code: 'Schema' },
    Company: { name: 'Danh sách công ty', path: '/setting/company', code: 'Company' },
    Plant: { name: 'Danh sách nhà máy', path: '/setting/plant', code: 'Plant' },
    Block: { name: 'Danh sách khối', path: '/setting/block', code: 'Block' },
    Department: { name: 'Danh sách phòng ban', path: '/setting/department', code: 'Department' },
    Part: { name: 'Danh sách bộ phận', path: '/setting/part', code: 'Part' },
    Position: { name: 'Danh sách vị trí', path: '/setting/position', code: 'Position' },
    PurchasingOrg: { name: 'Tổ chức mua hàng', path: '/setting/purchasing-org', code: 'PurchasingOrg' },
    PurchasingGroup: { name: 'Nhóm mua hàng', path: '/setting/purchasing-group', code: 'PurchasingGroup' },
    Currency: { name: 'Tiền tệ', path: '/setting/currency', code: 'Currency' },
    Incoterm: { name: 'Điều kiện thương mại', path: '/setting/incoterm', code: 'Incoterm' },
    PaymentTerm: { name: 'Thời hạn thanh toán', path: '/setting/payment-term', code: 'PaymentTerm' },
    PaymentMethod: { name: 'Phương thức thanh toán', path: '/setting/payment-method', code: 'PaymentMethod' },
    PlanningGroup: { name: 'Dòng tiền', path: '/setting/planning-group', code: 'PlanningGroup' },
    ShareholderCategory: { name: 'Danh mục cổ đông', path: '/setting/shareholder-category', code: 'ShareholderCategory' },
    CostCenter: { name: 'Phòng ban - bộ phận', path: '/setting/cost-center', code: 'CostCenter' },
    BusinessPartnerGroupr: { name: 'Nhóm đối tác kinh doánh', path: '/setting/business-partner-group', code: 'BusinessPartnerGroupr' },
    Employee: { name: 'Nhân viên', path: '/setting/employee', code: 'Employee' },
    BusinessType: { name: 'Loại hình doanh nghiệp', path: '/setting/business-typpe', code: 'BusinessType' },
    BillLookup: { name: 'Tra cứu hóa đơn', path: '/setting/bill-lookup', code: 'BillLookup' },

    PermissionAdditional: { name: 'Phân quyền bổ sung', path: '/setting/permission-additional', code: 'PermissionAdditional' },
    FlowApprove: { name: 'Chiến lược duyệt', path: '/setting/flow-approve', code: 'FlowApprove' },
    Uom: { name: 'UOM', path: '/setting/uom', code: 'Uom' },
    MaterialType: { name: 'Material type', path: '/setting/material-type', code: 'MaterialType' },
    ExternalMaterialGroup: { name: 'External Material Group', path: '/setting/external-material-group', code: 'ExternalMaterialGroup' },
    MatGroup: { name: 'Material Group', path: '/setting/mat-group', code: 'MatGroup' },
    Material: { name: 'Material', path: '/pr/material', code: 'Material' },
    Service: { name: 'Lĩnh vực mua hàng', path: '/setting/service', code: 'Service' },
    Asset: { name: 'Asset', path: '/setting/asset', code: 'Asset' },
    ShipmentRoute: { name: 'Shipment route', path: '/shipment/shipment-route', code: 'ShipmentRoute' },
    ShipmentCostType: { name: 'Shipment cost type', path: '/shipment/shipment-cost-type', code: 'ShipmentCostType' },
    Bank: { name: 'Ngân hàng', path: '/setting/bank', code: 'Bank' },
    BankBranch: { name: 'Chi nhánh ngân hàng', path: '/setting/bank-branch', code: 'BankBranch' },
    GlAccount: { name: 'GL account', path: '/setting/gl-account', code: 'GlAccount' },
    Order: { name: 'Order', path: '/setting/order', code: 'Order' },
    MasterBidForm: { name: 'Hình thức đấu thầu', path: '/setting/master-bid-form', code: 'MasterBidForm' },
    MasterBidGuaranteeForm: { name: 'Bảo lãnh dự thầu', path: '/setting/master-bid-guarantee-form', code: 'MasterBidGuaranteeForm' },
    EmailTemplate: { name: 'Template email', path: '/setting/email-template', code: 'EmailTemplate' },
    FaqCategory: { name: 'Danh mục FAQ', path: '/setting/faq-category', code: 'FaqCategory' },
    Faq: { name: 'FAQ', path: '/setting/faq', code: 'Faq' },
    LanguageKey: { name: 'Thiết lập ngôn ngữ', path: '/setting/language-key', code: 'LanguageKey' },
    CompanyAddress: { name: 'Địa chỉ', path: '/setting/company-address', code: 'CompanyAddress' },
    SettingString: { name: 'Thiết lập động', path: '/setting/setting-string', code: 'SettingString' },
    // Nhà cung cấp
    ManagementSupSupplier: { code: 'ManagementSupSupplier', name: 'Nhà cung cấp', path: '/bid/supplier-manage' },
    SupplierCapacity: { name: 'Pháp lý/Năng Lực', code: 'SupplierCapacity', path: '/bid/supplier-capacity' },
    SupplierNumberSettingRole: { name: 'Phân quyền nhập liệu', code: 'SupplierNumberSettingRole', path: '/bid/supplier-number-setting-role' },
    SupplierCreateCode: { name: 'Tạo mã SAP', path: '/bid/supplier-create-code', code: 'SupplierCreateCode' },
    ApproveRequestUpdateLawSupplier: { name: 'Pháp lý', path: '/bid/approve-request-update-law-supplier', code: 'ApproveRequestUpdateLawSupplier' },
    ApproveRequestUpdateCapacitySupplier: {
      name: 'Năng lực',
      path: '/bid/approve-request-update-capacity-supplier',
      code: 'ApproveRequestUpdateCapacitySupplier',
    },
    ApproveRequestActiveSupplierService: {
      name: 'Mở/khóa LVKD',
      path: '/bid/approve-request-active-supplier-service',
      code: 'ApproveRequestActiveSupplierService',
    },
    ApproveRequestActiveSupplier: { name: 'Mở/khóa NCC', path: '/bid/approve-request-active-supplier', code: 'ApproveRequestActiveSupplier' },
    SupplierUpgrade: { name: 'Nâng cấp nhà cung cấp', path: '/bid/supplier-upgrade', code: 'SupplierUpgrade' },
    SiteAssessment: { name: 'Hiện trường', path: '/bid/site-assessment', code: 'SiteAssessment' },
    EvaluationHistoryPurchase: { name: 'Lịch sử mua hàng', path: '/bid/evaluation-history-purchase', code: 'EvaluationHistoryPurchase' },
    // Quản trị mua hàng
    BidNew: { name: 'Tạo gói thầu', path: '/bid/bid-new', code: 'BidNew' },
    BidRate: { name: 'Đánh giá gói thầu', path: '/bid/bid-rate', code: 'BidRate' },
    BidInfo: { name: 'Truy vấn gói thầu', path: '/bid/bid-info', code: 'BidInfo' },
    BidNewSurvey: { name: 'Gói thầu khảo sát', path: '/bid/bid-new-survey', code: 'BidNewSurvey' },
    PriceQuoteList: { name: 'Yêu cầu báo giá', path: '/price-quote/list', code: 'PriceQuoteList' },
    Auction: { name: 'Đấu giá', path: '/bid/auction', code: 'Auction' },
    BusinessPlan: { name: 'Phương án kinh doanh', path: '/business-plan', code: 'BusinessPlan' },
    RecommendedPurchase: { name: 'Đề nghị mua hàng', path: '/recommended-purchase', code: 'RecommendedPurchase' },
    RoundUpCont: { name: 'Làm tròn cont', path: '/round-up-cont', code: 'RoundUpCont' },
    Contract: { name: 'Hợp đồng', path: '/contract/contract', code: 'Contract' },
    ContractAppendix: { name: 'Phụ lục hợp đồng', path: '/contract/contract', code: 'ContractAppendix' },
    PO: { name: 'PO', path: '/contract/po', code: 'PO' },
    Inbound: { name: 'Danh sách Inbound', path: '/inbound/inbound', code: 'Inbound' },
    ShipmentSchedule: { name: 'Lịch giao hàng', path: '/inbound/shipment-schedule', code: 'ShipmentSchedule' },
    Bill: { name: 'Hóa đơn', path: '/bill', code: 'Bill' },
    Payment: { name: 'Thanh toán', path: '/payment', code: 'Payment' },
    Complaint: { name: 'Danh sách khiếu nại', path: '/complaint/complaint', code: 'Complaint' },
    Shipment: { name: 'Shipment', path: '/shipment/shipment', code: 'Shipment' },
    ShipmentCost: { name: 'Shipment cost', path: '/shipment/shipment-cost', code: 'ShipmentCost' },
    Budget: { name: 'Đề xuất điều chỉnh ngân sách', path: '/budget', code: 'Budget' },
    TicketEvaluationKpi: { name: 'Phiếu đánh giá KPI mua hàng', path: '/purchase-kpi/ticket-evaluation-kpi', code: 'TicketEvaluationKpi' },

    // Yêu cầu mua hàng
    PurchaseRequestList: { name: 'Yêu cầu mua hàng', path: '/pr/pr', code: 'PurchaseRequestList' },
    PlanSiteAssessment: { name: 'Kế hoạch đánh giá hiện trường', path: '/bid/plan-site-assessment', code: 'PlanSiteAssessment' },
  },

  SupplierNumberSettingRolePermission: {
    Supplier: {
      code: 'Supplier',
      name: 'Nhà cung cấp',
      View: { code: 'SupplierView', name: 'em role Supplier' },
      Write: { code: 'SupplierWrite', name: 'Nhập liệu role Supplier' },
      Edit: { code: 'SupplierEdit', name: 'Điều chỉnh role Supplier' },
    },
    BusinessPartner: {
      code: 'BusinessPartner',
      name: 'BusinessPartner',
      View: { code: 'BusinessPartnerView', name: 'Xem role Business Partner' },
      Write: { code: 'BusinessPartnerWrite', name: 'Nhập liệu role Business Partner' },
      Edit: { code: 'BusinessPartnerEdit', name: 'Điều chỉnh role Business Partner' },
    },
    FISupplier: {
      code: 'FISupplier',
      name: 'FI Supplier',
      View: { code: 'FISupplierView', name: 'Xem role FI Supplier' },
      Write: { code: 'FISupplierWrite', name: 'Nhập liệu role FI Supplier' },
      Edit: { code: 'FISupplierEdit', name: 'Điều chỉnh role FI Supplier' },
    },
  },

  BankGroupType: {
    VIETTINBANK: { code: 'VIETTINBANK', name: 'Ngân hàng Viettinbank' },
    REMAINING_BANKS: { code: 'REMAINING_BANKS', name: 'Các ngân hàng còn lại' },
  },

  SupplierPotentialUpgradeStatus: {
    New: { code: 'New', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    SentSupplier: { code: 'SentSupplier', name: 'Đã gửi NCC', color: '#FF49CC', bgColor: '#FFCDF1', borderColor: '#FF5BD0' },
    SupplierRespond: { code: 'SupplierRespond', name: 'NCC đã phản hồi', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    ReCheck: { code: 'ReCheck', name: 'Yêu cầu kiểm tra lại', color: '#556B81', bgColor: '#E5E9EC', borderColor: '#556B81' },
    WaitSapCode: { code: 'WaitSapCode', name: 'Chờ tạo mã SAP', color: '#0D16F8', bgColor: '#DBDCFE', borderColor: '#0D16F8' },
    WaitApprove: { code: 'WaitApprove', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    Approved: { code: 'Approved', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    NoResponse: { code: 'NoResponse', name: 'Chưa phản hồi', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
  },

  ReferenceSource: {
    BusinessPlan: { code: 'BusinessPlan', name: 'Phương án kinh doanh' },
    RecommendedPurchase: { code: 'RecommendedPurchase', name: 'Đề nghị mua hàng' },
  },

  ReferenceSourceRecommendedPurchase: {
    BusinessPlan: { code: 'BusinessPlan', name: 'Phương án kinh doanh' },
    Bid: { code: 'Bid', name: 'Đấu thầu' },
    Quote: { code: 'Quote', name: 'Báo giá' },
    ShipmentCost: { code: 'ShipmentCost', name: 'Shipment Cost' },
  },

  paymentType: {
    N: { code: 'N', name: 'Thông thường' },
    A: { code: 'A', name: 'Tạm ứng' },
  },

  /**Nguồn tạo nhà cung cấp */
  supplierSource: {
    PORTAL: { code: 'PORTAL', name: 'Supplier Portal' },
    ADMIN: { code: 'ADMIN', name: 'Admin' },
  },

  PlanSiteAssessmentStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAIT_APPROVE: { code: 'WAIT_APPROVE', name: 'Đang duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    REQUEST_RE_CHECK: { code: 'REQUEST_RE_CHECK', name: 'Yêu cầu kiểm tra lại', color: '#EFCE22', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    DOING: { code: 'DOING', name: 'Đang thực hiện', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    COMPLETE: { code: 'COMPLETE', name: 'Hoàn thành', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  /* lọa của luồng duyệt */
  FlowAppoveType: {
    None: { code: 'None', name: 'Mặc định', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    LackOfFunds: { code: 'LackOfFunds', name: 'Thiếu ngân sách', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
  },

  BusinessTransaction: {
    PR: { code: 'RMBA' },
    DNMH: { code: 'RMBA' },
    PO: { code: 'RMBE' },
  },

  RequestUpdateSupplierType: {
    Law: { code: 'Law', name: 'Thông tin chung' },
    Capacity: { code: 'Capacity', name: 'Lĩnh vực kinh doanh' },
  },

  Reservation: {
    IT: { code: 'IT', name: 'Công nghệ thông tin' },
    VPP: { code: 'VPP', name: 'Văn phòng phẩm' },
    OTHER: { code: 'OTHER', name: 'Khác' },
  },

  ReservationTime: {
    MONTH: { code: 'MONTH', name: 'Tháng' },
    QUARTERLY: { code: 'QUARTERLY', name: 'Quý' },
    YEAR: { code: 'YEAR', name: 'Năm' },
  },

  ShipmentPlanNumberType: {
    Shipment: { code: 'Shipment', name: 'Shipment' },
    Manual: { code: 'Manual', name: 'Chọn tay' },
  },

  ShipmentPriceStatus: {
    NEW: { code: 'NEW', name: 'Mới' },
    IN_PROGRESS: { code: 'IN_PROGRESS', name: 'Đang duyệt' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt' },
    CANCEL: { code: 'CANCEL', name: 'Từ chối duyệt' },
    REVERT: { code: 'REVERT', name: 'Gỡ từ chối duyệt' },
  },

  ShipmentPlanStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', statusColor: '#0063D8', statusBgColor: '#DCEEFF', statusBorderColor: '#2A7DDF' },
    IN_PROGRESS: { code: 'IN_PROGRESS', name: 'Đang duyệt', statusColor: '#ED9A1F', statusBgColor: '#FCF0DD', statusBorderColor: '#F5CA89' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', statusColor: '#0A915B', statusBgColor: '#DEF2E0', statusBorderColor: '#0A915B' },
    CANCEL: { code: 'CANCEL', name: 'Từ chối duyệt', statusColor: '#F80D53', statusBgColor: 'rgb(248 13 83 / 15%)', statusBorderColor: '#F80D53' },
    REVERT: { code: 'REVERT', name: 'Gỡ từ chối duyệt' },
  },
  ReservationStatus: {
    N: { code: 'N', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    W_A: { code: 'W_A', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    C: { code: 'C', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    A: { code: 'A', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },

    R: {
      code: 'R',
      name: 'Từ chối duyệt',
      color: '#F80D53',
      bgColor: 'rgb(248 13 83 / 15%)',
      borderColor: '#F80D53',
      description: 'PR từ chối duyệt',
    },

    C_PR: { code: 'C_PR', name: 'Đóng', color: '#1D2D3E', bgColor: 'rgb(*********** / 15%)', borderColor: '#1D2D3E', description: '' },
  },

  ReservationType: {
    HangHoa: { code: 'HangHoa', name: 'Hàng hóa' },
    DichVu: { code: 'DichVu', name: 'Dịch vụ' },
    TongHop: { code: 'TongHop', name: 'Tổng hợp' },
  },

  MessageType: {
    ERROR: { code: 'ERROR', name: 'Lỗi' },
    SUCCESS: { code: 'SUCCESS', name: 'Thành công' },
  },

  CriteriaType: {
    LAW: { code: 'LAW', name: 'Tiêu chí đánh giá pháp lý' },
    CAPACITY: { code: 'CAPACITY', name: 'Tiêu chí đánh giá năng lực' },
  },
  BizType: {
    MANUFACTURING: { code: 'MANUFACTURING', name: 'Sản xuất' },
    TRADING: { code: 'TRADING', name: 'Thương mại hàng hóa' },
    SERVICE: { code: 'SERVICE', name: 'Dịch vụ' },
    OTHER: { code: 'OTHER', name: 'Khác' },
  },

  DataTypeSiteAssessment: {
    STRING: { code: 'STRING', name: 'Kí tự' },
    NUMBER: { code: 'NUMBER', name: 'Số' },
    LIST: { code: 'LIST', name: 'Danh sách' },
    FILE: { code: 'FILE', name: 'File' },
  },

  LegalEntity: {
    HO: { code: 'HO', name: 'Pháp nhân HO' },
    SITE: { code: 'SITE', name: 'Site đứng pháp nhân' },
  },

  MappingField: {
    /**KG */
    prItemId: { code: 'prItemId', name: 'ID sản phẩm' },
    productCode: { code: 'productCode', name: 'Mã sản phẩm' },
    productName: { code: 'productName', name: 'Tên sản phẩm' },
    unitCode: { code: 'unitCode', name: 'ĐVT' },
    // quantityRound: { code: 'quantityRound', name: 'Số lượng làm tròn cont' },
    safetyStock: { code: 'safetyStock', name: 'Tồn kho an toàn' },
    stockQuantity: { code: 'stockQuantity', name: 'Số lượng tồn kho' },
    demandQtyN: { code: 'demandQtyN', name: 'SL nhu cầu tháng N (Ngày hiện tại - 01.N.24)' },
    prAndPoOpenQtyN: { code: 'prAndPoOpenQtyN', name: 'PR+ PO dở dang Tháng N.24' },
    // closingStockN: { code: 'closingStockN', name: 'Tồn cuối tháng N.24' },
    demandQtyN1: { code: 'demandQtyN1', name: 'SL nhu cầu N+1.24' },
    prAndPoOpenQtyN1: { code: 'prAndPoOpenQtyN1', name: 'PR+PO dở dang N+1.24' },
    closingStockN1: { code: 'closingStockN1', name: 'Tồn cuối N+1.24' },
    demandQtyN2: { code: 'demandQtyN2', name: 'SL nhu cầu N+2.24' },
    prAndPoOpenQtyN2: { code: 'prAndPoOpenQtyN2', name: 'PR+PO dở dang N+2.24' },
    quantity: { code: 'quantity', name: 'Số lượng PR MRP' },
    demandQtyN3: { code: 'demandQtyN3', name: 'SL nhu cầu N+3.24' },
    prAndPoOpenQtyN3: { code: 'prAndPoOpenQtyN3', name: 'PR+PO dở dang N+3.24' },

    // roundedContQtyCT: { code: 'roundedContQtyCT', name: 'Số lượng bổ sung để tròn cont (CT)' },
    // roundedContQtyDC: { code: 'roundedContQtyDC', name: 'Số lượng bổ sung để tròn cont (DC)' },
    // supplierReturnQty: { code: 'supplierReturnQty', name: 'Số lượng hỏi hàng NCC' },
    // closingStockN2: { code: 'closingStockN2', name: 'Tồn kho cuối N+2' },
    // closingStockRatioN2: { code: 'closingStockRatioN2', name: 'Tỷ lệ tồn kho cuối N+2' },
    // salesRatioN2: { code: 'salesRatioN2', name: 'Tỷ lệ bán hàng N+2' },

    /**BCM */
    unitsPerCarton: { code: 'unitsPerCarton', name: 'Số máy/thùng' },
    cartonCount: { code: 'cartonCount', name: 'Số thùng' },
    length: { code: 'length', name: 'Dài' },
    width: { code: 'width', name: 'Rộng' },
    height: { code: 'height', name: 'Cao' },
    cbmPerSku: { code: 'cbmPerSku', name: 'CBM/SKU' },
    // mrpPrQuantity: { code: 'mrpPrQuantity', name: 'Số lượng PR MRP' },
    // convertedCbmMrpPr: { code: 'convertedCbmMrpPr', name: 'CBM quy đổi của PR MRP' },
    // orderedCbm: { code: 'orderedCbm', name: 'CBM đặt hàng' },
  },
  Acccate: {
    A: {
      code: 'A',
      name: 'Asset',
      description: 'Asset',
    },
    B: {
      code: 'B',
      name: 'MTS prod./sales ord.',
      description: 'MTS prod./sales ord.',
    },
    C: {
      code: 'C',
      name: 'Sales order',
      description: 'Sales order',
    },
    D: {
      code: 'D',
      name: 'Indiv.cust./project',
      description: 'Indiv.cust./project',
    },
    E: {
      code: 'E',
      name: 'Ind. cust. w. KD-CO',
      description: 'Ind. cust. w. KD-CO',
    },
    F: {
      code: 'F',
      name: 'Order',
      description: 'Order',
    },
    G: {
      code: 'G',
      name: 'MTS prod./project',
      description: 'MTS prod./project',
    },
    K: {
      code: 'K',
      name: 'Cost center',
      description: 'Cost center',
    },
    M: {
      code: 'M',
      name: 'Ind. cust. w/o KD-CO',
      description: 'Ind. cust. w/o KD-CO',
    },
    N: {
      code: 'N',
      name: 'Network',
      description: 'Network',
    },
    P: {
      code: 'P',
      name: 'Project',
      description: 'Project',
    },
    Q: {
      code: 'Q',
      name: 'Proj. make-to-order',
      description: 'Proj. make-to-order',
    },
    T: {
      code: 'T',
      name: 'All new aux.acc.ass.',
      description: 'All new aux.acc.ass.',
    },
    U: {
      code: 'U',
      name: 'Unknown',
      description: 'Unknown',
    },
    X: {
      code: 'X',
      name: 'All aux.acct.assgts.',
      description: 'All aux.acct.assgts.',
    },
    Z: {
      code: 'Z',
      name: 'Returnable Packaging',
      description: 'Returnable Packaging',
    },
  },

  ItemCategory: {
    K: {
      code: 'K',
      name: 'Consignment',
      description: 'Consignment',
    },
    L: {
      code: 'L',
      name: 'Subcontracting',
      description: 'Subcontracting',
    },
    S: {
      code: 'S',
      name: 'Third-party',
      description: 'Third-party',
    },
    U: {
      code: 'U',
      name: 'Stock transfer',
      description: 'Stock transfer',
    },
    D: {
      code: 'D',
      name: 'Service',
      description: 'Service',
    },
    E: {
      code: 'E',
      name: 'Enhanced Limit',
      description: 'Enhanced Limit',
    },
    C: {
      code: 'C',
      name: 'Stock prov.by cust.',
      description: 'Stock prov.by cust.',
    },
    P: {
      code: 'P',
      name: 'Return.trans.pack.',
      description: 'Return.trans.pack.',
    },
  },

  OutputField: {
    prItemId: { code: 'prItemId', name: 'ID sản phẩm' },
    roundedContQtyDC: { code: 'roundedContQtyDC', name: 'Số lượng bổ sung để tròn cont (DC)' },
    supplierReturnQty: { code: 'supplierReturnQty', name: 'Số lượng hỏi hàng NCC' },
  },

  /**PR dùng cho làm tròn cont */
  PRTypeCont: {
    SUMMARY: { code: 'SUMMARY', name: 'PR Tổng hợp' },
    NORMAL: { code: 'NORMAL', name: 'PR Thường' },
  },

  /** enum Leadtime */
  LeadTime: {
    DNHM: { code: 'DNHM', name: 'Đề nghị mua hàng' },
    HD: { code: 'HD', name: 'Hồ sơ, HĐ, thanh toán' },
    TGNCCCBNL: { code: 'TGNCCCBNL', name: '"TG NCC chuẩn bị NL' },
    NCCSX: { code: 'NCCSX', name: 'NCC sản xuất' },
    TGNCCSX: { code: 'TGNCCSX', name: 'TG từ NCC sx xong đến cảng NCC' },
    TGVCNCCDENC: { code: 'TGVCNCCDENC', name: 'TGVC từ cảng NCC - cảng VN' },
    TGTUCANGTOIKHOKT: { code: 'TGTUCANGTOIKHOKT', name: 'TGVC từ cảng VN - kho KT' },
    KTCLNK: { code: 'KTCLNK', name: 'Kiểm tra CL+NK' },
    TLTMH: { code: 'TLTMH', name: 'Tổng Leadtime mua hàng' },
    TLTKH: { code: 'TLTKH', name: 'Tổng Leadtime kéo hàng' },
  },

  PurchasePlanType: {
    // Hàng mua mới
    NEW: { code: 'NEW', name: 'Hàng mua mới' }, // Hàng mua mới
    // Hàng mua quốc tế
    INTERNATIONAL: { code: 'INTERNATIONAL', name: 'Hàng mua quốc tế' }, // Hàng mua quốc tế
  },

  PurposeQuote: {
    NVL: { code: 'NVL', name: 'Chào giá mua hàng hóa/NVL' },
    ORDER: { code: 'ORDER', name: 'Chào giá mua dịch vụ' },
    ASSET: { code: 'ASSET', name: 'Chào giá mua tài sản' },
    TRANSPORT: { code: 'TRANSPORT', name: 'Chào giá mua dịch vụ vận chuyển' },
  },

  TypeQuote: {
    UNEXPECTED: { code: 'Unexpected', name: 'Báo giá đột xuất' },
    PERIODIC: { code: 'PERIODIC', name: 'Báo giá định kỳ' },
  },

  QuotationPeriod: {
    DAY: { code: 'Ngày', name: 'Ngày (Báo giá sẽ được tạo lại theo ngày)' },
    WEEK: { code: 'WEEK', name: 'Tuần (Báo giá sẽ được tạo vào T2 mỗi tuần)' },
    MONTH: { code: 'MONTH', name: 'Tháng (Báo giá sẽ được tạo vào ngày đầu tiên của tháng)' },
    YEAR: { code: 'YEAR', name: 'Năm (Báo giá sẽ được tạo vào ngày 01/01)' },
  },
  /**Nguồn tham chiếu dùng cho contract */
  ReferenceQuote: {
    PR: { code: 'PR', name: 'Tham chiếu từ PR' },
    PR_TOTAL: { code: 'PR_TOTAL', name: 'Tham chiếu từ PR tổng hợp' },
    PAVC: { code: 'PAVC', name: 'Tham chiếu từ Shipment/PAVC' },
    NO: { code: 'NO', name: 'Không tham chiếu' },
  },

  QuotationForm: {
    PUBLIC: { code: 'PUBLIC', name: 'Công khai' },
    NCC: { code: 'NCC', name: 'Chọn NCC từ danh sách' },
  },

  RequestStatus: {
    T: { code: 'T', name: 'Lưu tạm', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    N: { code: 'N', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    W_A: { code: 'W_A', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    C: { code: 'C', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    A: { code: 'A', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    R: {
      code: 'R',
      name: 'Từ chối duyệt',
      color: '#F80D53',
      bgColor: 'rgb(248 13 83 / 15%)',
      borderColor: '#F80D53',
      description: 'PR từ chối duyệt',
    },

    C_PR: { code: 'C_PR', name: 'Đóng', color: '#1D2D3E', bgColor: 'rgb(*********** / 15%)', borderColor: '#1D2D3E', description: '' },
  },

  SupplierRequestStatus: {
    XacNhan: {
      code: 'XacNhan',
      name: 'Xác nhận tham gia',
      color: 'orange',
    },
    TuChoi: {
      code: 'TuChoi',
      name: 'Từ chối tham gia',
      color: 'red',
    },

    ChoXacNhan: {
      code: 'ChoXacNhan',
      name: 'Chờ xác nhận',
      color: 'orange',
    },
  },

  OptionYesNo: {
    Allowed: { code: 'Allowed', name: 'Cho phép' },
    NotAllowed: { code: 'NotAllowed', name: 'Không cho phép' },
  },

  OptionYesNoEn: {
    Allowed: { code: 'Allowed', name: 'Allowed' },
    NotAllowed: { code: 'NotAllowed', name: 'Not Allowed' },
  },

  FreightPaymentTerm: {
    Prepaid: { code: 'Prepaid', name: 'Cước phí trả trước' },
    Collect: { code: 'Collect', name: 'Cước phí trả sau' },
  },

  FreightPaymentTermEn: {
    Prepaid: { code: 'Prepaid', name: 'Freight Prepaid' },
    Collect: { code: 'Collect', name: 'Freight Collect' },
  },

  ContractType: {
    Normal: { code: 'Normal', name: 'Hợp đồng thường' },
    FrameWork: { code: 'FrameWork', name: 'Hợp đồng nguyên tắc' },
  },

  IncotermVersion: {
    IncotermVersion2000: { code: 'IncotermVersion2000', name: '2000' },
    IncotermVersion2010: { code: 'IncotermVersion2010', name: '2010' },
    IncotermVersion2020: { code: 'IncotermVersion2020', name: '2020' },
  },

  BusinessTemplatePlanStatus: {
    N: { code: 'N', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    W_A: { code: 'W_A', name: 'Đang duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    A: { code: 'A', name: 'Đã duyệt', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    R: {
      code: 'R',
      name: 'Từ chối',
      color: '#F80D53',
      bgColor: 'rgb(248 13 83 / 15%)',
      borderColor: '#F80D53',
      description: 'PR từ chối',
    },
    C_PR: { code: 'C_PR', name: 'Đóng', color: '#1D2D3E', bgColor: 'rgb(*********** / 15%)', borderColor: '#1D2D3E', description: '' },
  },
  BusinessTemplatePlanRef: {
    /* PAVC */
    PAVC: { code: 'PAVC', name: 'Phương án vận chuyên' },
    // nhập tay
    Input: { code: 'Input', name: 'Nhập tay' },
  },
  CostType: {
    MasterData: { code: 'MasterData', name: 'Master data' },
    Input: { code: 'Input', name: 'Nhập tay' },
    Vanilla: { code: 'Vanilla', name: 'Giữ nguyên' },
    Overview: { code: 'Overview', name: 'Từ Overview' },
  },

  AccountAssignment: {
    A: {
      code: 'A',
      name: 'Asset',
      description: 'Asset',
    },
    B: {
      code: 'B',
      name: 'MTS prod./sales ord.',
      description: 'MTS prod./sales ord.',
    },
    C: {
      code: 'C',
      name: 'Sales order',
      description: 'Sales order',
    },
    D: {
      code: 'D',
      name: 'Indiv.cust./project',
      description: 'Indiv.cust./project',
    },
    E: {
      code: 'E',
      name: 'Ind. cust. w. KD-CO',
      description: 'Ind. cust. w. KD-CO',
    },
    F: {
      code: 'F',
      name: 'Order',
      description: 'Order',
    },
    G: {
      code: 'G',
      name: 'MTS prod./project',
      description: 'MTS prod./project',
    },
    K: {
      code: 'K',
      name: 'Cost center',
      description: 'Cost center',
    },
    M: {
      code: 'M',
      name: 'Ind. cust. w/o KD-CO',
      description: 'Ind. cust. w/o KD-CO',
    },
    N: {
      code: 'N',
      name: 'Network',
      description: 'Network',
    },
    P: {
      code: 'P',
      name: 'Project',
      description: 'Project',
    },
    Q: {
      code: 'Q',
      name: 'Proj. make-to-order',
      description: 'Proj. make-to-order',
    },
    T: {
      code: 'T',
      name: 'All new aux.acc.ass.',
      description: 'All new aux.acc.ass.',
    },
    U: {
      code: 'U',
      name: 'Unknown',
      description: 'Unknown',
    },
    X: {
      code: 'X',
      name: 'All aux.acct.assgts.',
      description: 'All aux.acct.assgts.',
    },
    Z: {
      code: 'Z',
      name: 'Returnable Packaging',
      description: 'Returnable Packaging',
    },
  },
  CostConfigType: {
    Overview: { code: 'Overview', name: 'Overview' },
    Detail: { code: 'Detail', name: 'Detail' },
  },
  BusinessTemplatePlanCostType: {
    Master: { code: 'Master', name: 'Master data', value: 1 },
    Hand: { code: 'Hand', name: 'Chọn tay', value: 2 },
    Raw: { code: 'Raw', name: 'Gốc', value: 3 },
    Overview: { code: 'Overview', name: 'Từ Overview', value: 4 },
  },
}
