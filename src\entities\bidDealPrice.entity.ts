import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { BidDealEntity } from './bidDeal.entity'
import { BidPriceEntity } from './bidPrice.entity'

@Entity('bid_deal_price')
export class BidDealPriceEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  /** Số lượng đàm phán */
  @Column({
    nullable: false,
    default: 0,
  })
  number: number

  /** Gi<PERSON> tốt nhất đang chào */
  @Column({
    type: 'float',
    nullable: true,
  })
  bestPrice: number

  /** Giá tốt nhất đã trúng thầu */
  @Column({
    type: 'float',
    nullable: true,
  })
  bestPriceHistory: number

  /** Gi<PERSON> tốt nhất đang mua */
  @Column({
    type: 'float',
    nullable: true,
  })
  bestPriceCurrent: number

  /** <PERSON><PERSON><PERSON> mong muốn đàm phán */
  @Column({
    type: 'float',
    nullable: true,
  })
  suggestPrice: number

  @Column({
    type: 'float',
    nullable: true,
  })
  maxPrice: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidDealId: string
  @ManyToOne(() => BidDealEntity, (p) => p.bidDealPrices)
  @JoinColumn({ name: 'bidDealId', referencedColumnName: 'id' })
  bidDeal: Promise<BidDealEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidDealPrices)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>

  // @OneToMany(
  //   () => BidDealSupplierPriceValueEntity,
  //   p => p.bidDealPrice,
  // )
  // bidDealSupplierPriceValue: Promise<BidDealSupplierPriceValueEntity[]>
}
