import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateScore1749122585035 implements MigrationInterface {
  name = 'UpdateScore1749122585035'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier_score" ALTER COLUMN "supplierId" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier_score" ALTER COLUMN "supplierId" varchar(255) NOT NULL`)
  }
}
