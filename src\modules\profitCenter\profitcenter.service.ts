/*
https://docs.nestjs.com/providers#services
*/

import { Body, Injectable, NotFoundException } from '@nestjs/common'
import { ProfitCenterRepository } from '../../repositories/profiCenter.repository'
import { UserDto } from '../../dto'
import { ProfitCenterCreationDto } from './dto/profitCenterCreationDto'
import { ERROR_CODE_TAKEN, ERROR_DUPLICATE_DATA, ERROR_NOT_FOUND_DATA, UPDATE_SUCCESS } from '../../constants'
import { ProfitCenterUpdateDto } from './dto/profitCenterUpdateDto'

@Injectable()
export class ProfitCenterService {
  constructor(private repo: ProfitCenterRepository) {}

  async addNewProfitCenter(user: UserDto, data: ProfitCenterCreationDto) {
    const existed = await this.repo.find({
      where: { code: data.code, isDeleted: false },
      select: { id: true, code: true, description: true },
      order: { code: 'ASC' },
    })

    if (existed.length > 0) {
      throw new Error(ERROR_DUPLICATE_DATA)
    }

    const newProfitCenter = {
      ...data,
      createdBy: user.id,
      updatedBy: user.id,
    }

    return await this.repo.insert(newProfitCenter)
  }

  async getAllProfitCenters() {
    return await this.repo.findAndCount({ where: { isDeleted: false }, order: { createdAt: 'ASC' } })
  }

  async updateProfitCenter(user: UserDto, data: ProfitCenterUpdateDto) {
    const existingProfitCenter = await this.repo.findOne({
      where: {
        id: data.id,
        // isDeleted: false,
      },
    })
    if (!existingProfitCenter) {
      throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    }

    // Update ProfitCenter
    const updateData = {
      ...data,
      updatedBy: user.id,
      updatedAt: new Date(),
    }

    await this.repo.update(data.id, updateData)

    // Return the updated ProfitCenter
    return { message: UPDATE_SUCCESS }
  }

  async updateActiveProfitCenter(user, id: string) {
    const profitCenter = await this.repo.findOne({ where: { id: id }, select: { id: true, isDeleted: false } })
    if (!profitCenter) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(id, { isDeleted: true, updatedBy: user.id })

    return { message: UPDATE_SUCCESS }
  }
}
