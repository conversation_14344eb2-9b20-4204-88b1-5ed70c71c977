import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ComplaintLineItemEntity } from './complaintLineItem.entity'
import { ContractEntity } from './contract.entity'
import { FactorySupplierEntity } from './factorySupplier.entity'
import { MaterialEntity } from './material.entity'
import { MaterialGroupEntity } from './materialGroup.entity'
import { MaterialStorageLocationEntity } from './materialStorageLocation.entity'
import { PlantEntity } from './plant.entity'
import { UomEntity } from './uom.entity'

/** item của hợp đồng */
@Entity({ name: 'contract_item' })
export class ContractItemEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** material */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.contractItems)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** Hàng hóa */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  shortText: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.contractItems)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  /** Quantity/Số lượng */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  /** Số lượng còn lại */
  @Column({
    nullable: true,
    default: 0,
  })
  restQuantity: number

  /** Số lượng hợp đồng */
  @Column({
    nullable: true,
    default: 0,
  })
  contractQuantity: number

  /**Unit Price/Đơn giá */
  @Column({
    nullable: true,
  })
  price: number

  /**Amount/Thành tiền*/
  @Column({
    type: 'float',
    nullable: true,
  })
  totalPrice: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  itemCode: string

  /** contract*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.contractItems)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  /**Unit of Measure/ĐVT  */
  @Column({
    nullable: true,
  })
  unitName: string

  /** DS khiếu nại - line item */
  @OneToMany(() => ComplaintLineItemEntity, (p) => p.contractItem)
  complaintLineItems: Promise<ComplaintLineItemEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchasePrId: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  itemNo: string

  /** Lot/Lô hàng */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  lotNumber: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  acccate: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  category: string

  /** material group */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string
  @ManyToOne(() => MaterialGroupEntity, (p) => p.contractItems)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  assetCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  orderCode: string

  /** Nhà máy */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.contractItems)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  storageLocation: string

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  ounCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  ounId: string
  @ManyToOne(() => UomEntity, (p) => p.contractItemUons)
  @JoinColumn({ name: 'ounId', referencedColumnName: 'id' })
  oun: Promise<UomEntity>

  /**Hệ số quy đổi */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  quantityAlternative: number

  /**Thành tiền VND*/
  @Column({
    type: 'float',
    nullable: true,
  })
  totalPriceVND: number

  /**Mã thuế */
  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: true,
  })
  taxCode: string

  /**Thuế */

  @Column({
    nullable: true,
    default: 0,
  })
  taxRate: number

  /**Thành tiền sau thuế*/
  @Column({
    type: 'float',
    nullable: true,
  })
  totalPriceAfterTax: number

  /**Origin/Xuất xứ */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  origin: string

  /**Manufacturer/Nhà máy */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  manufacturer: string

  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  underDeliveryTolerance: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  overDeliveryTolerance: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  stockType: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  valuationType: string

  /**Specification/Tiêu chuẩn*/
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  technicalSpec: string

  /**Ngày giao hàng trễ nhất */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  latestDeliveryDate: Date

  /**Tolerance/Dung sai*/
  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: true,
  })
  toleranceRange: string

  /** Vị trí kho hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialStorageLocationId: string
  @ManyToOne(() => MaterialStorageLocationEntity, (p) => p.contractItems)
  @JoinColumn({ name: 'materialStorageLocationId', referencedColumnName: 'id' })
  materialStorageLocation: Promise<MaterialStorageLocationEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  factorySupplierId: string
  @ManyToOne(() => FactorySupplierEntity, (p) => p.contractItems)
  @JoinColumn({ name: 'factorySupplierId', referencedColumnName: 'id' })
  factorySupplier: Promise<FactorySupplierEntity>
}
