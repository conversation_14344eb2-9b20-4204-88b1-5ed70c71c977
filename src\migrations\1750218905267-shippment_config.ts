import { MigrationInterface, QueryRunner } from "typeorm";

export class ShippmentConfig1750218905267 implements MigrationInterface {
    name = 'ShippmentConfig1750218905267'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "shipment_condition_type_template" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b8e63353b48b5da8f758f00774c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c92a4d28311add7cf748824a583" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "shipmentConditionTypeId" uniqueidentifier, "dateFrom" datetime, "dateTo" datetime, "price" bigint, "conditionCodeCompact" nvarchar(max), "conditionIdCompact" nvarchar(max), CONSTRAINT "PK_b8e63353b48b5da8f758f00774c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shipment_condition_type_template_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_35143530db3e9238f11dd2e5575" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ed824283a20ff703bbde8b0fbca" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "shipmentConditionTypeTemplateId" uniqueidentifier, "dateFrom" datetime, "dateTo" datetime, "price" bigint, "conditionCompact" nvarchar(max), CONSTRAINT "PK_35143530db3e9238f11dd2e5575" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD CONSTRAINT "FK_921be70527c79231a155369ac01" FOREIGN KEY ("shipmentConditionTypeId") REFERENCES "shipment_condition_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" ADD CONSTRAINT "FK_78472ab0359c9e4f3166aa7dff2" FOREIGN KEY ("shipmentConditionTypeTemplateId") REFERENCES "shipment_condition_type_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" DROP CONSTRAINT "FK_78472ab0359c9e4f3166aa7dff2"`);
        await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP CONSTRAINT "FK_921be70527c79231a155369ac01"`);
        await queryRunner.query(`DROP TABLE "shipment_condition_type_template_value"`);
        await queryRunner.query(`DROP TABLE "shipment_condition_type_template"`);
    }

}
