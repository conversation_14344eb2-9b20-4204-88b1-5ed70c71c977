import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PlantEntity } from './plant.entity'
import { EmployeeEntity } from './employee.entity'
import { SupplierEntity } from './supplier.entity'
import { PurchasingGroupEntity } from './purchasingGroup.entity'
import { ComplaintDepartmentEntity } from './complaintDepartment.entity'
import { POEntity } from './po.entity'
import { ContractEntity } from './contract.entity'
import { ComplaintLineItemEntity } from './complaintLineItem.entity'
import { ComplaintItemEntity } from './complaintItem.entity'
import { ComplaintItemCargoEntity } from './complaintItemCargo.entity'
import { ComplaintEmployeeEntity } from './complaintEmployee.entity'
import { ComplaintFixEntity } from './complaintFix.entity'
import { ComplaintPreventionEntity } from './complaintPrevention.entity'
import { ComplaintHandlingPlanEntity } from './complaintHandlingPlan.entity'
import { ComplaintChatEntity } from './complaintChat.entity'
import { PurchasingOrgEntity } from './purchasingOrg.entity'
import { ComplaintNotifyEntity } from './complaintNotify.entity'

/** Khiếu nại */
@Entity('complaint')
export class ComplaintEntity extends BaseEntity {
  /** Số phiếu khiếu nại */
  @Column({ type: 'varchar', length: 250, nullable: false })
  code: string

  /** Tên phiếu khiếu nại */
  @Column({ type: 'varchar', length: 250, nullable: false })
  name: string

  /**Loại khiếu lại) */
  @Column({ type: 'varchar', length: 30, nullable: true })
  complaintType: string

  /** Nguồn tham chiếu, enum ReferenceComplaintType */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  complaintReferenceType: string

  /** Chứng từ tham chiếu */
  @Column({
    type: 'varchar',
    length: '250',
    nullable: true,
  })
  referenceSource: string

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Chứng từ tham chiếu theo Po*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.complaints)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  /** Chứng từ tham chiếu theo hợp đồng*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.complaints)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  /** Chứng từ nhập kho */
  @Column({
    type: 'varchar',
    length: '250',
    nullable: true,
  })
  warehouseReceipt: string

  /** Plant */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (c) => c.complaints)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /** Thời gian nhập kho */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  warehouseDate: Date

  /** Chứng từ kiểm tra chất lượng */
  @Column({
    type: 'varchar',
    length: '250',
    nullable: true,
  })
  qualityInspection: string

  /** Organization */
  @Column({
    type: 'varchar',
    length: '250',
    nullable: true,
  })
  organization: string

  /** purchasingOrg */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string
  @ManyToOne(() => PurchasingOrgEntity, (c) => c.complaints)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  /** purchasingGroup */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string
  @ManyToOne(() => PurchasingGroupEntity, (c) => c.complaints)
  @JoinColumn({ name: 'purchasingGroupId', referencedColumnName: 'id' })
  purchasingGroup: Promise<PurchasingGroupEntity>

  /** Ngày tạo */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  createDate: Date

  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (c) => c.complaints)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (c) => c.complaints)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Ghi chú  */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  description: string

  /** Gửi nhà cung cấp ? */
  @Column({
    nullable: true,
    default: false,
  })
  isSendSupplier: boolean

  /** Tiêu đề phương án xử lý */
  @Column({ type: 'varchar', length: 250, nullable: true })
  title: string

  /** Mô tả phương án xử lý*/
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  note: string

  /** File đính kèm phương án xử lý*/
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  attachedFile: string

  /** Xác nhận phương án xử lý */
  @Column({
    nullable: true,
    default: false,
  })
  isConfirm: boolean

  /** ds Khiếu nại - phòng ban */
  @OneToMany(() => ComplaintDepartmentEntity, (p) => p.complaint)
  complaintDepartments: Promise<ComplaintDepartmentEntity[]>

  /** ds Khiếu nại - nhân viên */
  @OneToMany(() => ComplaintEmployeeEntity, (p) => p.complaint)
  complaintEmployees: Promise<ComplaintEmployeeEntity[]>

  /** ds Khiếu nại - line item */
  @OneToMany(() => ComplaintLineItemEntity, (p) => p.complaint)
  complaintLineItems: Promise<ComplaintLineItemEntity[]>

  /** ds item */
  @OneToMany(() => ComplaintItemEntity, (p) => p.complaint)
  complaintItems: Promise<ComplaintItemEntity[]>

  /** ds xử lý hàng hóa */
  @OneToMany(() => ComplaintItemCargoEntity, (p) => p.complaint)
  complaintItemCargos: Promise<ComplaintItemCargoEntity[]>

  /** Khắc phục khiếu nại */
  @OneToMany(() => ComplaintFixEntity, (p) => p.complaint)
  complaintFixs: Promise<ComplaintFixEntity[]>

  /** Phòng ngừa khiếu nại */
  @OneToMany(() => ComplaintPreventionEntity, (p) => p.complaint)
  complaintPreventions: Promise<ComplaintPreventionEntity[]>

  /** Phương án xử lý */
  @OneToMany(() => ComplaintHandlingPlanEntity, (p) => p.complaint)
  complaintHandlingPlans: Promise<ComplaintHandlingPlanEntity[]>

  /** Chat */
  @OneToMany(() => ComplaintChatEntity, (p) => p.complaint)
  complaintChats: Promise<ComplaintChatEntity[]>

  /** Thông báo khiếu nại */
  @OneToMany(() => ComplaintNotifyEntity, (p) => p.complaint)
  complaintNotifys: Promise<ComplaintNotifyEntity[]>
}
