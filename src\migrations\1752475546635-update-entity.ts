import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateEntity1752475546635 implements MigrationInterface {
    name = 'UpdateEntity1752475546635'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "business_plan_template_cost_list" ADD "businessTemplatePlanId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_cost_list" ADD "businessTemplatePlan" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_cost_list" ADD CONSTRAINT "FK_d923444cdf9695ea13a5135433d" FOREIGN KEY ("businessTemplatePlan") REFERENCES "business_template_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "business_plan_template_cost_list" DROP CONSTRAINT "FK_d923444cdf9695ea13a5135433d"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_cost_list" DROP COLUMN "businessTemplatePlan"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_cost_list" DROP COLUMN "businessTemplatePlanId"`);
    }

}
