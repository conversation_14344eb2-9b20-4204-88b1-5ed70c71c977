import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm'
import { InboundEntity, MaterialEntity, ShipmentEntity } from '.'

/** DS Items */
@Entity('shipment_item')
export class ShipmentItemEntity extends BaseEntity {
  /** Shipment */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentId: string
  @ManyToOne(() => ShipmentEntity, (p) => p.shipmentItems)
  @JoinColumn({ name: 'shipmentId', referencedColumnName: 'id' })
  shipment: Promise<ShipmentEntity>

  /** Material */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.shipmentItems)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** Số lượng */
  @Column({
    nullable: true,
    type: 'int',
  })
  quantityInput: number

  /** Số lượng dự kiến */
  @Column({
    nullable: true,
    type: 'int',
  })
  quantityDocuments: number

  /** Số kiện */
  @Column({
    nullable: true,
    type: 'int',
  })
  packageQuantity: number

  /** Material */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  inboundId: string
  @ManyToOne(() => InboundEntity, (p) => p.shipmentItems)
  @JoinColumn({ name: 'inboundId', referencedColumnName: 'id' })
  inbound: Promise<InboundEntity>
}
