import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PermissionViewEntity } from './permissionView.entity'
import { GroupPermissionEntity } from './groupPermission.entity'

@Entity('permission_to_group')
export class PermissionToGroupEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  permissionViewId: string

  @ManyToOne(() => PermissionViewEntity, (p) => p.permissionToGroups)
  @JoinColumn({ name: 'permissionViewId', referencedColumnName: 'id' })
  permissionView: Promise<PermissionViewEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  groupPermissionId: string

  @ManyToOne(() => GroupPermissionEntity, (p) => p.permissionToGroups)
  @JoinColumn({ name: 'groupPermissionId', referencedColumnName: 'id' })
  groupPermission: Promise<GroupPermissionEntity>
}
