import { Controller, UseGuards, Post, Body, Put, Param, Request } from '@nestjs/common'
import { JwtAuthGuard } from '../common/guards'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { AuctionAddSupplier, AuctionCancel, AuctionCreate, AuctionSubmit, AuctionUpdate } from './dto'
import { AuctionService } from './auction.service'

import { ACCEPT_LANGUAGE } from '../../constants'
import { Request as IRequest } from 'express'

@ApiBearerAuth()
@ApiTags('Auction')
@UseGuards(JwtAuthGuard)
@Controller('auction')
export class AuctionController {
  constructor(private readonly service: AuctionService) {}

  @ApiOperation({ summary: 'Lấy ds phiên đấu giá' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { status?: string; supplierId?: string }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds phiên đấu giá' })
  @Post('approve')
  public async approve(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.approve(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds phiên đấu giá' })
  @Post('reject')
  public async reject(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.reject(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết đấu giá' })
  @Post('find_detail')
  public async findDetail(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    return await this.service.findDetail(data, user)
  }

  @ApiOperation({ summary: 'Lấy ds PR của các gói phiên đấu giá được chọn' })
  @Post('find_auction_pr')
  public async findAuctionPr(@CurrentUser() user: UserDto, @Body() data: { ltsAuctionId: [] }) {
    return await this.service.findAuctionPr(user, data)
  }

  @ApiOperation({ summary: 'Tạo đấu giá' })
  @Post('create_data')
  public async createData(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.createData(data, user)
  }

  @ApiOperation({ summary: 'Chỉnh sửa đấu giá' })
  @Post('update_data')
  public async updateData(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.updateData(data, user)
  }

  @ApiOperation({ summary: 'Mời thêm NCC' })
  @Post('add_supplier')
  public async addSupplier(@Body() data: AuctionAddSupplier, @CurrentUser() user: UserDto) {
    return await this.service.addSupplier(data, user)
  }

  @ApiOperation({ summary: 'Hủy đấu giá' })
  @Post('cancel_data')
  public async cancelData(@Body() data: AuctionCancel, @CurrentUser() user: UserDto) {
    return await this.service.cancelData(data, user)
  }

  @ApiOperation({ summary: 'DS đấu giá có phân trang' })
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.pagination(data, user)
  }

  @ApiOperation({ summary: 'Gửi yêu cầu phê duyệt gói thầu tạm' })
  @Put('send_mpoleader_check_auction/:id')
  public async sendMpoleaderCheckBid(@CurrentUser() user: UserDto, @Param('id') id: string) {
    return await this.service.sendMpoleaderCheckBid(user, id)
  }

  @ApiOperation({ summary: 'Duyệt gói thầu tạm' })
  @Put('mpoleader_accept_auction/:id')
  public async mpoleaderAcceptBid(@CurrentUser() user: UserDto, @Param('id') id: string) {
    return await this.service.mpoleaderAcceptBid(user, id)
  }

  //#region CLIENT

  @ApiOperation({ summary: 'Thông tin đấu giá của NCC' })
  @Post('get_detail')
  public async findDetailAuctionSupplier(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    return await this.service.findDetailAuctionSupplier(data, user)
  }

  @ApiOperation({ summary: 'Nộp đấu giá' })
  @Post('submit_data')
  public async submitData(@Body() data: any, @CurrentUser() user: UserDto) {
    return await this.service.submitData(data, user)
  }

  @ApiOperation({ summary: 'Ds nhà cung cấp thắng đấu giá' })
  @Post('load_aution_by_supplier')
  public async loadAutionBySupplier(@CurrentUser() user: UserDto, @Body() data: { autionId: string }, @Request() req: IRequest) {
    return await this.service.loadAutionBySupplier(user, data, req.headers[ACCEPT_LANGUAGE])
  }

  //#endregion
}
