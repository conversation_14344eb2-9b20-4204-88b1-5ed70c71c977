import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { IncotermConversionRepository, IncotermRepository } from '../../repositories'
import { IncotermConversionController } from './incotermConversion.controller'
import { IncotermConversionService } from './incotermConversion.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([IncotermConversionRepository, IncotermRepository])],
  controllers: [IncotermConversionController],
  providers: [IncotermConversionService],
})
export class IncotermConversionModule {}
