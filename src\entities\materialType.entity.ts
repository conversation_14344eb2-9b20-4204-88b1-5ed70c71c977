import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { MaterialEntity } from './material.entity'

@Entity('material_type')
export class MaterialTypeEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /**Tên hiện thị ở supplier portal khi load sidebar */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  portalName: string

  @OneToMany(() => ExternalMaterialGroupEntity, (p) => p.materialType)
  externalMaterialGroups: Promise<ExternalMaterialGroupEntity[]>

  @OneToMany(() => MaterialEntity, (p) => p.materialType)
  materials: Promise<MaterialEntity[]>
}
