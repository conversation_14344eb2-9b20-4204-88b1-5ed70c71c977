import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PlantEntity } from './plant.entity'
import { BusinessTemplatePlanTypeEntity } from './businessTemplatePlanType.entity'
import { BusinessTemplatePlanEntity } from './businessTemplatePlan.entity'

@Entity('business_template_group_plan')
export class BusinessTemplateGroupPlanEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  /*  plantId*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (c) => c.businessTemplateGroupPlan)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /*  Loại template*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessTemplatePlanTypeId: string
  @ManyToOne(() => BusinessTemplatePlanTypeEntity, (c) => c.businessTemplateGroupPlan)
  @JoinColumn({ name: 'businessTemplatePlanTypeId', referencedColumnName: 'id' })
  businessTemplatePlanType: Promise<BusinessTemplatePlanTypeEntity>

  @OneToMany(() => BusinessTemplatePlanEntity, (p) => p.businessTemplateGroupPlan)
  businessTemplatePlan: Promise<BusinessTemplatePlanEntity[]>
}
