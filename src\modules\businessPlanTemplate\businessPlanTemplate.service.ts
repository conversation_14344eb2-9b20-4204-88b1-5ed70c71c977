import { Injectable, NotAcceptableException } from '@nestjs/common'
import { BusinessPlanRepository, BusinessPlanTemplateColRepository, BusinessPlanTemplateRepository, EmployeeRepository } from '../../repositories'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'

import {
  CREATE_SUCCESS,
  DELETE_SUCCESS,
  enumData,
  ERROR_INVALID_FOMULAR,
  ERROR_NOT_FOUND_DATA,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../constants'
import { BusinessPlanHistoryEntity, BusinessPlanTemplateColEntity, BusinessPlanTemplateEntity } from '../../entities'
import { Between, Equal, FindOptionsWhere, In, Like, Not } from 'typeorm'
import { v4 as uuidv4 } from 'uuid'
import { coreHelper } from '../../helpers'
import * as moment from 'moment'
import {
  BusinessPlanTemplateColUpdateDto,
  BusinessPlanTemplateCreateColDto,
  BusinessPlanTemplateCreateColExcelDto,
  BusinessPlanTemplateCreateDto,
  BusinessPlanTemplateUpdateDto,
} from './dto'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'

@Injectable()
export class BusinessPlanTemplateService {
  constructor(
    private repo: BusinessPlanTemplateRepository,
    private employeeRepo: EmployeeRepository,
    private businessPlanTemplateColRepo: BusinessPlanTemplateColRepository,
    private businessPlanRepo: BusinessPlanRepository,
    private organizationalPositionService: OrganizationalPositionService,
  ) {}

  async createData(user: UserDto, data: BusinessPlanTemplateCreateDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BusinessPlanTemplateEntity)
      const historyRepo = trans.getRepository(BusinessPlanHistoryEntity)
      const businessPlanTemplateColRepo = trans.getRepository(BusinessPlanTemplateColEntity)
      const checkCode = await repo.exists({ where: { code: data.code } })
      if (checkCode) throw new Error(`Mã đã tồn tại. Vui lòng kiểm tra lại`)
      const newBusinessPlanTemplateEntity = new BusinessPlanTemplateEntity()
      newBusinessPlanTemplateEntity.code = data.code
      newBusinessPlanTemplateEntity.id = uuidv4()
      newBusinessPlanTemplateEntity.name = data.name
      newBusinessPlanTemplateEntity.companyId = user.companyId
      newBusinessPlanTemplateEntity.description = data.description
      newBusinessPlanTemplateEntity.status = enumData.BusinessPlanTemplateStatus.New.code
      newBusinessPlanTemplateEntity.createdAt = new Date()
      newBusinessPlanTemplateEntity.employeeId = user.employeeId
      newBusinessPlanTemplateEntity.createdBy = user.id
      newBusinessPlanTemplateEntity.type = data.type
      await repo.insert(newBusinessPlanTemplateEntity)

      const lstCol = await businessPlanTemplateColRepo.find({ where: { businessPlanTemplateCode: data.code } })
      if (lstCol.length > 0) {
        for (let item of lstCol) {
          item.businessPlanTemplateId = newBusinessPlanTemplateEntity.id
          item.updatedAt = new Date()
          item.updatedBy = user.id
          await businessPlanTemplateColRepo.save(item)
        }
      }

      const his = new BusinessPlanHistoryEntity()
      his.businessPlanTemplateId = newBusinessPlanTemplateEntity.id
      his.createdByName = user.username
      his.createdAt = new Date()
      his.createdBy = user.id
      his.description =
        'Tài Khoản [' + user.username + '] vừa tạo mới Template phương án kinh doanh với mã [' + newBusinessPlanTemplateEntity.code + '] '
      await historyRepo.insert(his)
    })
    return { message: CREATE_SUCCESS }
  }

  async updateData(user: UserDto, data: BusinessPlanTemplateUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(`phương án kinh doanh không tồn tại. Vui lòng kiểm tra lại`)
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BusinessPlanTemplateEntity)
      const historyRepo = trans.getRepository(BusinessPlanHistoryEntity)
      const businessPlanTemplateColRepo = trans.getRepository(BusinessPlanTemplateColEntity)

      entity.code = data.code
      entity.name = data.name
      entity.companyId = user.companyId
      entity.description = data.description
      entity.status = enumData.RoundUpContTemplateStatus.New.code
      entity.updatedAt = new Date()
      entity.updatedBy = user.id
      entity.type = data.type
      await repo.save(entity)
      const lstCol = await businessPlanTemplateColRepo.find({ where: { businessPlanTemplateCode: data.code } })
      if (lstCol.length > 0) {
        for (let item of lstCol) {
          item.businessPlanTemplateId = entity.id
          item.updatedAt = new Date()
          item.updatedBy = user.id
          await businessPlanTemplateColRepo.save(item)
        }
      }
      const his = new BusinessPlanHistoryEntity()
      his.businessPlanTemplateId = entity.id
      his.createdByName = user.username
      his.createdAt = new Date()
      his.createdBy = user.id
      his.description = 'Tài Khoản [' + user.username + '] vừa cập nhật Template phương án kinh doanh với mã [' + entity.code + '] '
      await historyRepo.insert(his)
    })
    return { message: CREATE_SUCCESS }
  }

  async genCode() {
    const curDate = new Date()
    const fd = coreHelper.getFirstDayOfMonth(curDate)
    const ld = coreHelper.getLastDayOfMonth(curDate)

    let count = await this.repo.count({
      where: { createdAt: Between(fd, ld) },
    })

    const month = moment(curDate).format('MM')
    const year = moment(curDate).format('YY')

    let code: string
    let isDuplicate: boolean

    do {
      let countText = `000${count + 1}`
      countText = countText.slice(countText.length - 4, countText.length)
      code = `TLPAKD${month}${year}${countText}`

      // Kiểm tra mã đã tồn tại
      isDuplicate = await this.repo.exists({
        where: { code },
      })

      if (isDuplicate) {
        count++
      }
    } while (isDuplicate)

    return { code }
  }

  async createDataTemplateCol(user: UserDto, data: BusinessPlanTemplateCreateColDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BusinessPlanTemplateColEntity)
      const checkCode = await repo.exists({ where: { code: data.code, businessPlanTemplateCode: data.businessPlanTemplateCode } })
      if (checkCode) throw new Error(`Mã đã tồn tại. Vui lòng kiểm tra lại`)

      if (data.fomular?.length > 0) {
        const lstField = await this.getPriceColAll(data.businessPlanTemplateCode)
        const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
        if (!isValidFomular) throw new NotAcceptableException(ERROR_INVALID_FOMULAR)
      }

      const newEntity = new BusinessPlanTemplateColEntity()
      newEntity.code = data.code
      newEntity.id = uuidv4()
      newEntity.name = data.name
      newEntity.companyId = user.companyId
      newEntity.name = data.name
      newEntity.colType = data.colType
      newEntity.dataMapping = data.dataMapping
      newEntity.fomular = data.fomular
      newEntity.isEdited = data.isEdited
      newEntity.businessPlanTemplateCode = data.businessPlanTemplateCode
      newEntity.sort = data.sort
      newEntity.isSystemData = data.isSystemData
      newEntity.isRequired = data.isRequired
      newEntity.createdAt = new Date()
      newEntity.createdBy = user.id
      newEntity.type = data.type
      await repo.insert(newEntity)
    })
    return { message: CREATE_SUCCESS }
  }

  async getPriceColAll(code: string) {
    return await this.businessPlanTemplateColRepo.find({
      where: { businessPlanTemplateCode: code, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })
  }

  async deleteDataTemplateCol(data: FilterOneDto) {
    const checkItem = await this.businessPlanTemplateColRepo.findOne({ where: { id: data.id } })
    if (!checkItem) throw new Error(`Item không tồn tại. Vui lòng kiểm tra lại`)

    if (checkItem.businessPlanTemplateId) {
      const checkExist = await this.businessPlanRepo.findOne({
        where: { businessPlanTemplateId: checkItem.businessPlanTemplateId, isDeleted: false },
      })
      if (checkExist) throw new Error(`Item trong template này đã được sử dụng. Vui lòng kiểm tra lại`)
    }

    await this.businessPlanTemplateColRepo.delete({ id: checkItem.id })
    return { message: DELETE_SUCCESS }
  }

  async deleteAllDataTemplateCol(data: FilterOneDto) {
    const checkItem = await this.businessPlanTemplateColRepo.findOne({ where: { businessPlanTemplateCode: data.code } })
    if (checkItem) {
      if (checkItem.businessPlanTemplateId) {
        const checkExist = await this.businessPlanRepo.findOne({
          where: { businessPlanTemplateId: checkItem.businessPlanTemplateId, isDeleted: false },
        })
        if (checkExist) throw new Error(`Item trong template này đã được sử dụng. Vui lòng kiểm tra lại`)
      }

      await this.businessPlanTemplateColRepo.delete({ businessPlanTemplateCode: data.code })
    }

    return { message: DELETE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: FindOptionsWhere<BusinessPlanTemplateEntity> = {}
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.BusinessPlanTemplate.code)
    if (dataRs.type === enumData.RoleData.All.code) {
      /* do nothing */
    }
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.status) whereCon.status = data.where.status
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.type) whereCon.type = data.where.type
    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC', code: 'DESC' },
      skip: data.skip,
      take: data.take,
    })

    if (res[0].length == 0) return [[], 0]

    const dictUser: any = {}
    {
      const lstId = coreHelper.selectDistinct(res[0], 'createdBy')
      const lstUser: any = await this.employeeRepo.find({
        where: { userId: In(lstId), isDeleted: false },
        select: { id: true, name: true, userId: true },
      })
      lstUser.forEach((c) => (dictUser[c.userId] = c))
    }

    const dictUserUpdate: any = {}
    {
      const lstId = coreHelper.selectDistinct(res[0], 'updatedBy')
      const lstUser: any = await this.employeeRepo.find({
        where: { userId: In(lstId), isDeleted: false },
        select: { id: true, name: true, userId: true },
      })
      lstUser.forEach((c) => (dictUserUpdate[c.userId] = c))
    }

    for (const item of res[0]) {
      if (item.updatedBy) {
        item.employeeName = dictUserUpdate[item.updatedBy]?.name
      } else {
        item.employeeName = dictUser[item.createdBy]?.name
      }
      item.statusName = enumData.BusinessPlanTemplateStatus[item.status].name
      item.statusColor = enumData.BusinessPlanTemplateStatus[item.status].color
      item.statusBgColor = enumData.BusinessPlanTemplateStatus[item.status].backgroundColor
      item.statusBorderColor = enumData.BusinessPlanTemplateStatus[item.status].statusBorderColor
    }
    return res
  }

  async findDetail(data: FilterOneDto) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: { histories: true, businessPlanTemplateCols: true },
      order: { histories: { createdAt: 'DESC' } },
    })
    res.lstHistory = res.__histories__
    res.lstTemplateCols = res.__businessPlanTemplateCols__
    res.statusName = enumData.BusinessPlanTemplateStatus[res.status].name
    res.statusColor = enumData.BusinessPlanTemplateStatus[res.status].color
    res.statusBgColor = enumData.BusinessPlanTemplateStatus[res.status].backgroundColor
    res.statusBorderColor = enumData.BusinessPlanTemplateStatus[res.status].statusBorderColor

    delete res.__histories__
    delete res.__businessPlanTemplateCols__
    return res
  }

  async findTemplateCol(data: FilterOneDto) {
    const res: any = await this.businessPlanTemplateColRepo.find({
      where: { businessPlanTemplateCode: Like(`%${data.code}%`) },
      order: { sort: 'ASC' },
    })
    if (res.length === 0) return []
    for (let item of res) {
      item.typeName = enumData.DataType[item.type]?.name || ''
      if (item.colType === enumData.DataMapping.Database.code) {
        item.dataMappingName = enumData.BusinessPlanCol[item.dataMapping]?.name
      } else {
        item.dataMappingName = enumData.SettingString[item.dataMapping]?.name
      }
    }
    return res
  }

  async updateDataTemplateCol(user: UserDto, data: BusinessPlanTemplateColUpdateDto) {
    const entity = await this.businessPlanTemplateColRepo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(`Dữ liệu không tồn tại. Vui lòng kiểm tra lại`)

    if (data.fomular?.length > 0) {
      const lstField = await this.getPriceColAll(data.businessPlanTemplateCode)
      const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
      if (!isValidFomular) throw new NotAcceptableException(ERROR_INVALID_FOMULAR)
    }

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BusinessPlanTemplateColEntity)
      const checkCode = await repo.findOne({
        where: { code: data.code, id: Not(Equal(data.id)), businessPlanTemplateCode: data.businessPlanTemplateCode },
      })
      if (checkCode) throw new Error(`Mã đã tồn tại. Vui lòng kiểm tra lại`)
      entity.code = data.code
      entity.name = data.name
      entity.companyId = user.companyId
      entity.colType = data.colType
      entity.dataMapping = data.dataMapping
      entity.fomular = data.fomular
      entity.isEdited = data.isEdited
      entity.businessPlanTemplateCode = data.businessPlanTemplateCode
      entity.sort = data.sort
      entity.isSystemData = data.isSystemData
      entity.isRequired = data.isRequired
      entity.createdAt = new Date()
      entity.createdBy = user.id
      entity.type = data.type
      await repo.save(entity)
    })
    return { message: UPDATE_SUCCESS }
  }

  async createDataTemplateColList(user: UserDto, data: BusinessPlanTemplateCreateColExcelDto) {
    if (data.lstData.length === 0) throw new NotAcceptableException(` Vui lòng nhập 1 dòng dử liệu.`)
    const dictRound: any = {}
    {
      const lstCol: any = await this.businessPlanTemplateColRepo.find({
        where: { businessPlanTemplateCode: Like(`%${data.businessPlanTemplateCode}%`) },
      })
      for (const item of lstCol) dictRound[item.code] = item
    }

    for (let item of data.lstData) {
      if (dictRound[item.code]) throw new Error(` Mã cột đã tồn tại. Vui lòng kiểm tra lại`)
    }
    let lstTask: any = []
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BusinessPlanTemplateColEntity)
      for (let item of data.lstData) {
        if (item.fomular?.length > 0) {
          const lstField = await this.getPriceColAll(data.businessPlanTemplateCode)
          const isValidFomular = await coreHelper.checkFomular(item.fomular, lstField)
          if (!isValidFomular) throw new NotAcceptableException(ERROR_INVALID_FOMULAR)
        }
        const newEntity = new BusinessPlanTemplateColEntity()
        newEntity.code = item.code
        newEntity.id = uuidv4()
        newEntity.name = item.name
        newEntity.companyId = user.companyId
        newEntity.name = item.name
        newEntity.colType = item.colType
        newEntity.dataMapping = item.dataMapping
        newEntity.fomular = item.fomular
        newEntity.isEdited = item.isEdited
        newEntity.businessPlanTemplateCode = data.businessPlanTemplateCode
        newEntity.sort = item.sort
        newEntity.isSystemData = item.isSystemData
        newEntity.isRequired = item.isRequired
        newEntity.createdAt = new Date()
        newEntity.createdBy = user.id
        newEntity.type = item.type
        lstTask.push(newEntity)
      }

      const lstChild = coreHelper.splitArrayByParameters(lstTask, 2000)
      for (const chunk of lstChild) {
        await trans.insert(BusinessPlanTemplateColEntity, chunk)
      }
    })
    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật trạng thái kích hoạt của. */
  async updateUnActive(user: UserDto, id: string) {
    const entity = await this.repo.findOne({
      where: { id: id },
      select: { id: true, isDeleted: true, code: true, status: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    const checkRoundUp = await this.businessPlanRepo.findOne({
      where: {
        businessPlanTemplateId: entity.id,
        isDeleted: false,
        status: In([
          enumData.BusinessPlanStatus.New.code,
          enumData.BusinessPlanStatus.Draft.code,
          enumData.BusinessPlanStatus.CheckAgain.code,
          enumData.BusinessPlanStatus.Wait.code,
        ]),
      },
    })
    if (checkRoundUp) throw new Error(`Phương án kinh doanh ở trạng thái không phù hợp. Vui lòng kiểm tra lại`)

    await this.repo.update(id, {
      status: enumData.BusinessPlanTemplateStatus.UnActive.code,
      updatedBy: user.id,
      updatedAt: new Date(),
    })

    await this.businessPlanTemplateColRepo.update(
      { businessPlanTemplateCode: entity.code },
      {
        isDeleted: true,
        updatedBy: user.id,
        updatedAt: new Date(),
      },
    )

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /** Cập nhật trạng thái kích hoạt của. */
  async updateActive(user: UserDto, id: string) {
    const entity = await this.repo.findOne({
      where: { id: id },
      select: { id: true, isDeleted: true, code: true, status: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    const checkRoundUp = await this.businessPlanRepo.findOne({ where: { businessPlanTemplateId: entity.id, isDeleted: false } })
    let status = enumData.BusinessPlanTemplateStatus.Active.code
    if (!checkRoundUp) {
      status = enumData.BusinessPlanTemplateStatus.New.code
    }

    await this.repo.update(id, {
      status: status,
      updatedBy: user.id,
      updatedAt: new Date(),
    })

    await this.businessPlanTemplateColRepo.update(
      { businessPlanTemplateCode: entity.code },
      {
        isDeleted: false,
        updatedBy: user.id,
        updatedAt: new Date(),
      },
    )

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async find(data: { status?: string }) {
    const whereCon: any = { isDeleted: false }
    if (data.status) {
      if (typeof data.status == 'string') {
        whereCon.status = data.status
      } else {
        whereCon.status = In(data.status)
      }
    }
    const res: any = await this.repo.find({
      where: whereCon,
      order: { createdAt: 'DESC' },
    })

    return res
  }

  async findListTemplateCol(data: FilterOneDto) {
    const res: any = await this.businessPlanTemplateColRepo.find({
      where: { businessPlanTemplateId: Like(`%${data.id}%`), isDeleted: false },
      order: { sort: 'ASC' },
    })
    if (res.length === 0) return []
    for (let item of res) {
      item.typeName = enumData.DataType[item.type]?.name || ''
      if (item.colType === enumData.DataMapping.Database.code) {
        item.dataMappingName = enumData.BusinessPlanCol[item.dataMapping]?.name
      } else {
        item.dataMappingName = enumData.SettingString[item.dataMapping]?.name
      }
    }
    return res
  }
}
