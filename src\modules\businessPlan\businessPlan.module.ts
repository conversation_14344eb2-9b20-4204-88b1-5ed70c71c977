import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import {
  BusinessPlanHistoryRepository,
  BusinessPlanRepository,
  BusinessPlanTemplateColRepository,
  CompanyRepository,
  ContractRepository,
  CurrencyRepository,
  EmployeeRepository,
  ExternalMaterialGroupRepository,
  PermissionApproveRepository,
  PlantRepository,
  PrItemRepository,
  PrRepository,
  SettingStringRepository,
} from '../../repositories'
import { BussinessPlanController } from './businessPlan.controller'
import { BussinessPlanService } from './businessPlan.service'
import { IncotermRepository } from '../../repositories/incoterm.repository'
import { FlowApproveModule } from '../flowApprove/flowApprove.module'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BusinessPlanRepository,
      CompanyRepository,
      PlantRepository,
      PrRepository,
      ExternalMaterialGroupRepository,
      EmployeeRepository,
      PrItemRepository,
      PermissionApproveRepository,
      BusinessPlanTemplateColRepository,
      BusinessPlanHistoryRepository,
      CurrencyRepository,
      ContractRepository,
      IncotermRepository,
      SettingStringRepository,
    ]),
    FlowApproveModule,
    OrganizationalPositionModule,
  ],
  controllers: [BussinessPlanController],
  providers: [BussinessPlanService],
})
export class BussinessPlanModule {}
