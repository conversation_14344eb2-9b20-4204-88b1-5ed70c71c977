import { MigrationInterface, QueryRunner } from "typeorm";

export class AddReasonRecheckSiteAssessment1749953701139 implements MigrationInterface {
    name = 'AddReasonRecheckSiteAssessment1749953701139'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "site_assessment" ADD "reasonRecheck" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "site_assessment" ADD "userRequestRecheck" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "site_assessment" DROP COLUMN "userRequestRecheck"`);
        await queryRunner.query(`ALTER TABLE "site_assessment" DROP COLUMN "reasonRecheck"`);
    }

}
