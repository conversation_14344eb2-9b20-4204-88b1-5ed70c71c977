import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTableCost1748484579772 implements MigrationInterface {
    name = 'AddTableCost1748484579772'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "cost" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9457483cde444b1dd32aacb24bb" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b54fd070d7632814f74483caefe" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" nvarchar(250), "description" varchar(max) NOT NULL, "materialGroupId" uniqueidentifier, "cost" float, CONSTRAINT "PK_9457483cde444b1dd32aacb24bb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "cost" ADD CONSTRAINT "FK_5cc26b46f8ab84e9364f30fa49a" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cost" DROP CONSTRAINT "FK_5cc26b46f8ab84e9364f30fa49a"`);
        await queryRunner.query(`DROP TABLE "cost"`);
    }

}
