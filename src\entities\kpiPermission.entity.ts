import { Column, <PERSON>tity, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { KpiEntity } from './kpi.entity'
import { KpiPermissionPositionEntity } from './kpiPermissionPosition.entity'

/**  template phân quyền kpi */
@Entity('kpi_permission')
export class KpiPermissionEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** Có bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** Cách tính điểm theo loại càng cao càng tốt */
  @Column({
    nullable: false,
    default: true,
  })
  isCalUp: boolean

  /** Kiểu dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  percent: number

  /** % điều kiện đạt tỉ trọng */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentRule: number

  /** % điều kiện liệt tỉ trọng khi tính theo chiều giảm dần */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentDownRule: number

  /** Cấp độ */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** Mô tả */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => KpiPermissionEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: KpiPermissionEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => KpiPermissionEntity, (p) => p.parent)
  childs: Promise<KpiPermissionEntity[]>

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Giá trị nhỏ nhất */
  @Column({
    nullable: true,
  })
  requiredMin: number

  /** Thuộc tính của tiêu chí thể hệ Doanh nghiệp sẽ được highlight màu xanh nếu đạt giá trị X khi xếp hạng năng lực */
  @Column({
    nullable: false,
    default: false,
  })
  isHighlight: boolean

  @Column({
    nullable: true,
  })
  hightlightValue: number

  /** id kpi mua hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  kpiId: string
  @ManyToOne(() => KpiEntity, (p) => p.kpiPermissions)
  @JoinColumn({ name: 'kpiId', referencedColumnName: 'id' })
  kpi: Promise<KpiEntity>
}
