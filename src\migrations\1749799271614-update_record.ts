import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateRecord1749799271614 implements MigrationInterface {
  name = 'UpdateRecord1749799271614'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "site_assessment" ADD "count" bigint`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "site_assessment" DROP COLUMN "count"`)
  }
}
