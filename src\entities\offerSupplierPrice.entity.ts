import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { OfferSupplierEntity } from './offerSupplier.entity'
import { OfferPriceEntity } from './offerPrice.entity'

/** <PERSON><PERSON>n nộp giá cuối của <PERSON> nghiệ<PERSON> */
@Entity('offer_supplier_price')
export class OfferSupplierPriceEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerSupplierId: string
  @ManyToOne(() => OfferSupplierEntity, (p) => p.offerSupplierPrices)
  @JoinColumn({ name: 'offerSupplierId', referencedColumnName: 'id' })
  offerSupplier: Promise<OfferSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerPriceId: string
  @ManyToOne(() => OfferPriceEntity, (p) => p.offerSupplierPrices)
  @JoinColumn({ name: 'offerPriceId', referencedColumnName: 'id' })
  offerPrice: Promise<OfferPriceEntity>

  /** Tên hạng mục */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  offerPriceName: string

  /** Cấp độ */
  @Column({
    nullable: true,
    default: 1,
  })
  offerPriceLevel: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string

  /** Ngày nộp chào giá */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  submitDate: Date

  /** Hình thức nộp giá: 0,1,2 tương ứng chào giá, đàm phán, đấu giá */
  @Column({
    nullable: true,
  })
  submitType: number

  /** Số lượng */
  @Column({
    nullable: true,
  })
  number: number

  /** Đơn giá */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  unitPrice: number

  /** Thành tiền (doanh thu theo hạng mục = Số lượng*Đơn giá ) */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  price: number
}
