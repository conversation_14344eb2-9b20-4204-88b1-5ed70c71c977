import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeLength1750833090405 implements MigrationInterface {
  name = 'ChangeLength1750833090405'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "shipmentFeeConditionTypeCompactId"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "shipmentFeeConditionTypeCompactId" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "shipmentFeeConditionTypeCompactCode"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "shipmentFeeConditionTypeCompactCode" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "shipmentFeeConditionTypeCompactValue"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "shipmentFeeConditionTypeCompactValue" varchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "shipmentFeeConditionTypeCompactValue"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "shipmentFeeConditionTypeCompactValue" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "shipmentFeeConditionTypeCompactCode"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "shipmentFeeConditionTypeCompactCode" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "shipmentFeeConditionTypeCompactId"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "shipmentFeeConditionTypeCompactId" varchar(255)`)
  }
}
