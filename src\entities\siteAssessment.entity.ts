import { BeforeInsert, <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { PlantEntity } from './plant.entity'
import { ServiceEntity } from './service.entity'
import { CriteriaSiteAssessmentEntity, FactorySupplierEntity } from '.'
import { PlanSiteAssessmentEntity } from './planSiteAssessment.entity'

/** Phiếu đánh giá hiện trường */
@Entity('site_assessment')
export class SiteAssessmentEntity extends BaseEntity {
  @Column({
    type: 'bigint',
    nullable: true,
  })
  count: number

  // @BeforeInsert()
  // async setCountOnInsert() {
  //   // Lấy tổng số bản ghi hiện tại trong bảng site_assessment
  //   const repo = (this.constructor as typeof SiteAssessmentEntity).getRepository()
  //   const total = await repo.count()
  //   this.count = total + 1
  // }

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Ng<PERSON>y đánh giá */
  @Column({
    nullable: false,
    type: 'date',
  })
  dateAssessment: Date

  /** Hạn trả lời của nhà cung cấp */
  @Column({
    nullable: true,
    type: 'date',
  })
  deadlineSupplierReply: Date

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  status: string

  /** Trạng thái trả lời của NCC */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  supplierReplyStatus: string

  /** Có được gửi đến NCC hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isSendSupplier: boolean

  /** Đã được nhấn gửi đến nhà cung cấp */
  @Column({
    nullable: false,
    default: false,
  })
  isApprovedSent: boolean

  @Column({
    type: 'nvarchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @Column({
    type: 'nvarchar',
    length: 4000,
    nullable: true,
  })
  comment: string

  @Column({
    type: 'nvarchar',
    length: 4000,
    nullable: true,
  })
  suggestion: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.siteAssessments)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  factorySupplierId: string
  @ManyToOne(() => FactorySupplierEntity, (p) => p.siteAssessments)
  @JoinColumn({ name: 'factorySupplierId', referencedColumnName: 'id' })
  factorySupplier: Promise<FactorySupplierEntity>

  /**lưu id nhân viên là trưởng đoàn    */
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  listEmployeeId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.siteAssessments)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.siteAssessments)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  /** DS tiêu chí */
  @OneToMany(() => CriteriaSiteAssessmentEntity, (p) => p.siteAssessment)
  criteriaSiteAssessments: Promise<CriteriaSiteAssessmentEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantSiteAssessmentId: string
  @ManyToOne(() => PlanSiteAssessmentEntity, (p) => p.siteAssessments)
  @JoinColumn({ name: 'plantSiteAssessmentId', referencedColumnName: 'id' })
  plantSiteAssessment: Promise<PlanSiteAssessmentEntity>

  @Column({
    nullable: true,
  })
  reasonRecheck: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  userRequestRecheck: string
}
