import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { RoundUpContEntity } from './roundUpCont.entity'

@Entity('round_up_cont_history')
export class RoundUpContHistoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  createdByName: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  roundUpContId: string
  @ManyToOne(() => RoundUpContEntity, (p) => p.histories)
  @JoinColumn({ name: 'roundUpContId', referencedColumnName: 'id' })
  roundUpCont: Promise<RoundUpContEntity>

  /** Trạng thái hiện tại */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  description: string
}
