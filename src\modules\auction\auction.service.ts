import { Injectable, NotAcceptableException } from '@nestjs/common'
import { ACTION_SUCCESS, CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_SUCCESS, enumData } from '../../constants'
import { Between, In, IsNull, Like, Not } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { AuctionHistoryRepository, AuctionRepository, AuctionSupplierPriceRepository, AuctionSupplierRepository } from '../../repositories'
import { AuctionAddSupplier, AuctionCancel } from './dto'
import { AuctionEntity, AuctionHistoryEntity, AuctionSupplierEntity } from '../../entities'
import { EmailService } from '../email/email.service'
import * as moment from 'moment'
import { nanoid } from 'nanoid'
import { AuctionSupplierPriceEntity } from '../../entities/auctionSupPrice.entity'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'
import { FlowApproveService } from '../flowApprove/flowApprove.service'
import { apiHelper } from '../../helpers'
@Injectable()
export class AuctionService {
  constructor(
    private readonly repo: AuctionRepository,
    private readonly emailService: EmailService,
    private readonly auctionSupplierRepo: AuctionSupplierRepository,
    private readonly auctionHistoryRepo: AuctionHistoryRepository,
    private readonly auctionSupplierPriceRepository: AuctionSupplierPriceRepository,
    private organizationalPositionService: OrganizationalPositionService,
    private flowService: FlowApproveService,
  ) {}

  //#region ADMIN
  /**Danh sách phiên đấu giá */
  async find(user: UserDto, data: { status?: string; supplierId?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    if (data.status) whereCon.status = data.status
    if (data.supplierId) {
      whereCon.auctionSuppliers = { supplierId: data.supplierId }
    }
    const res: any = await this.repo.find({
      where: whereCon,
      relations: { pr: true, auctionSupplierPrice: true, auctionSuppliers: true },
    })

    return res
  }

  async findAuctionPr(user: UserDto, data: { ltsAuctionId: [] }) {
    const res: any = await this.repo.find({
      where: { id: In(data.ltsAuctionId), isDeleted: false },
      relations: { pr: true },
    })
    const result = []
    for (const auction of res) {
      let prId: any[] = []
      let prCode: any[] = []
      let allPrIds: any[] = []
      if (auction.prId) {
        prId = auction.prId
          .split(',')
          .map((id: string) => id.trim())
          .filter((id) => id !== '')
        prCode = auction.__pr__?.code ? [auction.__pr__.code] : []
      }
      allPrIds = Array.from(new Set([...prId]))
      result.push({
        allPrIds,
        prCode,
      })
    }

    return result
  }

  /** Chi tiết đấu giá */
  public async findDetail(data: { id: string }, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    /* tìm ra bid auction mới nhất của gói thầu bằng id bid truyền vào */

    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: { bid: true, auctionSupplierPrice: true, auctionSuppliers: { supplier: true }, auctionHistorys: true },
      order: { auctionSuppliers: { submitPrice: 'ASC' }, auctionHistorys: { createdAt: 'DESC' } },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    res.bidName = res.__bid__?.name
    delete res.__bid__
    res.lstHistory = res.__auctionHistorys__ || []
    delete res.__auctionHistorys__
    res.lstSupplier = res.__auctionSuppliers__ || []
    res.lstSup = []
    res.lstItem = res.__auctionSupplierPrice__ || []
    let rank = 1
    let isFistReject = false
    for (const item of res.lstSupplier) {
      item.id = item.id
      item.rank = rank
      rank++
      item.supplierName = item.__supplier__.name
      item.isChosen = true
      item.code = item.__supplier__.code
      item.createdAt = item.__supplier__.createdAt
      item.statusName = enumData.AuctionSupplierStatus[item.status]?.name
      item.statusBorderColor = enumData.AuctionSupplierStatus[item.status]?.statusBorderColor
      item.statusBgColor = enumData.AuctionSupplierStatus[item.status]?.statusBgColor
      item.statusColor = enumData.AuctionSupplierStatus[item.status]?.statusColor

      item.showAction = false
      if (item.rank == 1 && item.status === enumData.AuctionSupplierStatus.WAITING.code) item.showAction = true
      if (enumData.AuctionSupplierStatus.APROVE.code === item.status) {
        isFistReject = false
      }
      if (isFistReject) {
        item.showAction = true
        isFistReject = false
      }
      if (enumData.AuctionSupplierStatus.REJECT.code === item.status) {
        isFistReject = true
      }
      res.lstSup.push(item)
      delete item.__supplier__
    }
    delete res.__auctionSuppliers__
    res.statusName = enumData.AuctionStatus[res.status]?.name
    // res.step = Number(res.step)
    return res
  }

  public async approve(user: UserDto, data: { id: string }) {
    const sup = this.auctionSupplierRepo.find({ where: { id: data.id } })
    if (!sup) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    await this.auctionSupplierRepo.update(data.id, { status: enumData.AuctionSupplierStatus.APROVE.code })

    return { message: 'Chấp nhận thành công' }
  }
  public async reject(user: UserDto, data: { id: string }) {
    const sup = this.auctionSupplierRepo.find({ where: { id: data.id } })
    if (!sup) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    await this.auctionSupplierRepo.update(data.id, { status: enumData.AuctionSupplierStatus.REJECT.code })

    return { message: 'Từ chối thành công' }
  }

  /** DS đấu giá có phân trang */
  public async pagination(data: PaginationDto, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCon: any = {}
    /**Phân quyền data,  quyền duyệt */
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.Auction.code, [enumData.FlowCode.AUCTION.code])
    if (dataRs.whereApprove?.length > 0) whereCon[dataRs.propertiesApprove] = In(dataRs.whereApprove)
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where.title) whereCon.title = Like(`%${data.where.title}%`)
    if (data.where.bidId) whereCon.bidId = data.where.bidId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where?.dateStart) {
      whereCon.dateStart = Between(
        moment(data.where.dateStart).format('YYYY-MM-DD 00:00:00'),
        moment(data.where.dateStart).format('YYYY-MM-DD 23:59:59'),
      )
    }
    if (data.where?.dateEnd) {
      whereCon.dateEnd = Between(moment(data.where.dateEnd).format('YYYY-MM-DD 00:00:00'), moment(data.where.dateEnd).format('YYYY-MM-DD 23:59:59'))
    }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { bid: true, auctionSuppliers: true },
      order: { createdAt: 'DESC' },
    })

    if (res[0].length == 0) return res
    for (const item of res[0]) {
      item.numSup = 0
      if (item.__auctionSuppliers__) {
        item.numSup = item.__auctionSuppliers__.length
      }
      //#region Xử lý trạng thái
      item.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
        lsType: [enumData.FlowCode.OFFER.code, enumData.FlowCode.AUCTION.code],
      })
      item.canApprove = item.objPermissionApprove[enumData.FlowCode.AUCTION.code] || false

      /** Chưa đến hạn? */
      item.isShowChoiseSup = item.status === enumData.AuctionStatus.DONE.code
      item.statusName = enumData.AuctionStatus[item.status]?.name
      item.statusBorderColor = enumData.AuctionStatus[item.status]?.statusBorderColor
      item.statusBgColor = enumData.AuctionStatus[item.status]?.statusBgColor
      item.statusColor = enumData.AuctionStatus[item.status]?.statusColor
      //#endregion

      item.bidName = item.__bid__?.name
      delete item.__bid__
    }

    return res
  }

  async sendMpoleaderCheckBid(user: UserDto, auction: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    let flowType: string
    flowType = enumData.FlowCode.AUCTION.code
    await this.flowService.setRoleRule(user, {
      targetId: auction,
      entityName: AuctionEntity.name,
      flowType: flowType,
      // plantId: user.orgCompanyId,
      // purchasingGroupId: user.orgCompanyId,
      // purchasingOrgId: user.orgCompanyId,
    })

    await this.repo.update(auction, { status: enumData.AuctionStatus.SEND_TEMP.code, updatedBy: user.id })

    return { message: 'Đã gửi yêu cầu phê duyệt ' }
  }

  /** Kiểm tra quyền phê duyệt gói thầu tạm */

  /** MPOLeader phê duyệt gói thầu tạm */
  async mpoleaderAcceptBid(user: UserDto, bidId: string, isMobile?: boolean) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: bidId,
      entityName: AuctionEntity.name,
      type: enumData.FlowCode.AUCTION.code,
      // plantId: user.orgCompanyId,
      // purchasingGroupId: user.orgCompanyId,
      // purchasingOrgId: user.orgCompanyId,
    })
    if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
      return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    } else {
      await this.repo.update(bidId, { status: enumData.AuctionStatus.NEW.code, updatedBy: user.id })
      // Bid History
      const bidSupplier = await this.auctionSupplierRepo.find({ where: { auctionId: bidId } })
      const listBidSupplier = bidSupplier.map((e) => e.supplierId)
      //send mail nội bộ
      this.emailService.ThongBaoDaTaoGoiThau(bidId)
      await this.emailService.ThongBaoNccThamGiaDauGiaNhanh(bidId, listBidSupplier)
    }
    return { message: 'Đã phê duyệt thành công.' }
  }

  /** Tạo đấu giá */
  public async createData(data: any, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const auctionNew = new AuctionEntity()
    auctionNew.code = nanoid()
    /* Đấu giá nhanh theo  */
    auctionNew.bidAutionType = data.bidAutionType
    /* Tiêu đề */
    auctionNew.title = data.title
    /* Thời điểm bắt đầu */
    auctionNew.dateStart = new Date(data.dateStart)
    /* Thời điểm kết thúc */
    auctionNew.dateEnd = new Date(data.dateEnd)
    /* Thời điểm bắt đầu thời gian cộng thêm */
    auctionNew.dateStartPlus = new Date(data.dateStartPlus)
    /* Thời gian cộng thêm khi có NCC nộp giá mới */
    auctionNew.timePlus = data.timePlus
    /* Thời gian cộng thêm khi có NCC nộp giá mới - t */
    auctionNew.timePlusType = data.timePlusType
    /* Nhà cung cấp trúng đấu giá xác nhận kết quả trong vòng */
    auctionNew.timeApprove = data.timeApprove
    /* Nhà cung cấp trúng đấu giá xác nhận kết quả trong vòng - t */
    auctionNew.timeApproveType = data.timeApproveType
    auctionNew.isShowDetailEmail = data.isShowDetailEmail
    auctionNew.isShowDetailClient = data.isShowDetailClient
    auctionNew.isShowDetailSMS = data.isShowDetailSMS
    /* Bước giá */
    auctionNew.step = data.step
    auctionNew.status = enumData.AuctionStatus.TEMP.code
    /* Đơn vị tiền tệ */
    auctionNew.currency = data.currency
    /* Plant */
    auctionNew.plantId = data.plantId
    /*purchasingOrgId*/
    auctionNew.purchasingOrgId = data.purchasingOrgId
    /*purchasingGroupId */
    auctionNew.purchasingGroupId = data.purchasingGroupId
    /* Hiển thị số lượng NCC tham gia */
    auctionNew.isShowSup = data.isShowSup
    /* Gói thầu */
    if (data.bidId) auctionNew.bidId = data.bidId
    /* PR */
    if (data.prId) auctionNew.prId = data.prId
    /* External MatGroup */
    if (data.externalMaterialGroupId) auctionNew.externalMaterialGroupId = data.externalMaterialGroupId
    auctionNew.createdBy = user.id
    auctionNew.companyId = data.companyId
    const auction = await this.repo.save(auctionNew)

    /* Tạo ra danh sách item của item lọai đi theo PR & Bid */
    for (const item of data.lstItem) {
      const bitItem = new AuctionSupplierPriceEntity()
      bitItem.quantity = item.quantity
      bitItem.maxPrice = item.maxPrice
      bitItem.unitId = item.unitId
      bitItem.name = item.name
      bitItem.belongAuction = true
      bitItem.auctionId = auction.id
      if (data.bidAutionType === enumData.AuctionType.BID.code) {
        bitItem.prItemId = item?.prItemId
        bitItem.bidItemId = item.id
      }
      if (data.bidAutionType === enumData.AuctionType.PR.code) {
        bitItem.prItemId = item.id
      }
      if (data.bidAutionType === enumData.AuctionType.MatGroup.code) {
        bitItem.exMatGroupId = data.externalMaterialGroupId
      }
      bitItem.companyId = user.companyId
      bitItem.createdBy = user.id
      await this.auctionSupplierPriceRepository.insert(bitItem)
    }

    for (const item of data.dataPrices) {
      const bitItem = new AuctionSupplierPriceEntity()
      bitItem.quantity = item.quantity
      bitItem.maxPrice = item.maxPrice
      bitItem.unitName = item.unitName
      bitItem.name = item.name
      bitItem.belongAuction = false
      bitItem.isMaterial = true
      bitItem.auctionId = auction.id
      if (data.bidAutionType === enumData.AuctionType.BID.code) {
        bitItem.prItemId = item?.prItemId
        bitItem.bidItemId = item.id
      }
      if (data.bidAutionType === enumData.AuctionType.PR.code) {
        bitItem.prItemId = item.id
      }
      if (data.bidAutionType === enumData.AuctionType.MatGroup.code) {
        bitItem.exMatGroupId = data.externalMaterialGroupId
      }

      bitItem.companyId = user.companyId
      bitItem.createdBy = user.id
      await this.auctionSupplierPriceRepository.insert(bitItem)
    }

    /* tạo ra danh sách nhà cung cấp */

    for (const supplierId of data.lstSup) {
      if (supplierId.isChosen) {
        const auctionSupplierNew = new AuctionSupplierEntity()
        auctionSupplierNew.companyId = user.companyId
        auctionSupplierNew.auctionId = auction.id
        auctionSupplierNew.supplierId = supplierId.id
        auctionSupplierNew.createdBy = user.id
        const newAuction = await this.auctionSupplierRepo.save(auctionSupplierNew)
        /* Tạo ra danh sách item của NCC */
        for (const item of data.dataPrices) {
          const bitItem = new AuctionSupplierPriceEntity()
          bitItem.quantity = item.quantity
          bitItem.maxPrice = item.maxPrice
          bitItem.unitId = item.unitId
          bitItem.name = item.name
          bitItem.unitName = item.unitName
          bitItem.belongAuction = false
          bitItem.auctionSupplierId = newAuction.id
          if (data.bidAutionType === enumData.AuctionType.BID.code) {
            bitItem.prItemId = item?.prItemId
            bitItem.bidItemId = item.id
          }
          if (data.bidAutionType === enumData.AuctionType.PR.code) {
            bitItem.prItemId = item.id
          }
          if (data.bidAutionType === enumData.AuctionType.MatGroup.code) {
            bitItem.exMatGroupId = data.externalMaterialGroupId
          }
          bitItem.companyId = user.companyId
          bitItem.createdBy = user.id
          await this.auctionSupplierPriceRepository.insert(bitItem)
        }
      }
    }

    const historyNew = new AuctionHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.auctionId = auction.id
    historyNew.createdBy = user.id
    historyNew.description = `Nhân viên ${user.username} tạo phiên đấu giá`
    await this.auctionHistoryRepo.insert(historyNew)

    //send mail cho các Doanh nghiệp
    // await this.emailService.ThongBaoNccThamGiaDauGiaNhanh(auction.id)

    return { message: CREATE_SUCCESS }
  }

  /** Chỉnh sửa đấu giá */
  public async updateData(data: any, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const auction = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!auction) throw new Error(ERROR_NOT_FOUND_DATA)

    const isCancel = auction.status == enumData.AuctionStatus.CANCEL.code
    if (isCancel) throw new Error(`Phiên đấu giá đã hủy, không được chỉnh sửa!`)

    /** Chưa đến hạn? */
    const isNotYet = new Date(auction.dateStart).getTime() > new Date().getTime()
    if (!isNotYet) throw new Error(`Chỉ được chỉnh sửa khi phiên đấu giá chưa đến hạn!`)

    /* Đấu giá nhanh theo  */
    auction.bidAutionType = data.bidAutionType
    /* Tiêu đề */
    auction.title = data.title
    /* Thời điểm bắt đầu */
    auction.dateStart = new Date(data.dateStart)
    /* Thời điểm kết thúc */
    auction.dateEnd = new Date(data.dateEnd)
    /* Thời điểm bắt đầu thời gian cộng thêm */
    auction.dateStartPlus = new Date(data.dateStartPlus)
    /* Thời gian cộng thêm khi có NCC nộp giá mới */
    auction.timePlus = data.timePlus
    /* Thời gian cộng thêm khi có NCC nộp giá mới - t */
    auction.timePlusType = data.timePlusType
    /* Nhà cung cấp trúng đấu giá xác nhận kết quả trong vòng */
    auction.timeApprove = data.timeApprove
    /* Nhà cung cấp trúng đấu giá xác nhận kết quả trong vòng - t */
    auction.timeApproveType = data.timeApproveType
    /* Bước giá */
    auction.step = data.step
    /* Đơn vị tiền tệ */
    auction.currency = data.currency
    /* Hiển thị số lượng NCC tham gia */
    auction.isShowSup = data.isShowSup
    // await this.repo.update(auction.id, auction)

    /* Cập nhật cho item */
    for (const item of data.lstItem) {
      await this.auctionSupplierPriceRepository.update(item.id, { quantity: item.quantity, maxPrice: item.maxPrice, unitId: item.unitId })
    }
    /* cập nhật lại cho supplier */
    for (const supplierId of data.lstSup) {
      for (const item of data.lstItem) {
        const current = await this.auctionSupplierPriceRepository.findOne({
          where: { auctionSupplier: { supplierId: supplierId.id }, name: item.name },
        })
        if (current) {
          await this.auctionSupplierPriceRepository.update(current.id, { quantity: item.quantity, maxPrice: item.maxPrice, unitId: item.unitId })
        }
      }
    }

    const historyNew = new AuctionHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.auctionId = auction.id
    historyNew.createdBy = user.id
    historyNew.description = `Nhân viên ${user.username} chỉnh sửa phiên đấu giá`
    await this.auctionHistoryRepo.insert(historyNew)

    //send mail cho các Doanh nghiệp
    // await this.emailService.ThongBaoNccCapNhatDauGiaNhanh(auction.id)

    return { message: UPDATE_SUCCESS }
  }

  /** Mời thêm NCC */
  public async addSupplier(data: AuctionAddSupplier, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const auction = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!auction) throw new Error(ERROR_NOT_FOUND_DATA)

    const isCancel = auction.status == enumData.AuctionStatus.CANCEL.code
    if (isCancel) throw new Error(`Phiên đấu giá đã hủy, không được chỉnh sửa!`)

    /** Chưa đến hạn? */
    const isNotYet = new Date(auction.dateStart).getTime() > new Date().getTime()
    if (!isNotYet) throw new Error(`Chỉ được chỉnh sửa khi phiên đấu giá chưa đến hạn!`)

    if (!data.lstSupplierId || data.lstSupplierId.length == 0) throw new Error('Vui lòng chọn NCC mời thêm!')

    for (const supplierId of data.lstSupplierId) {
      const auctionSupplierNew = new AuctionSupplierEntity()
      auctionSupplierNew.companyId = user.companyId
      auctionSupplierNew.auctionId = auction.id
      auctionSupplierNew.supplierId = supplierId
      auctionSupplierNew.createdBy = user.id
      await this.auctionSupplierRepo.insert(auctionSupplierNew)
    }

    const historyNew = new AuctionHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.auctionId = auction.id
    historyNew.createdBy = user.id
    historyNew.description = `Nhân viên ${user.username} mời thêm ${data.lstSupplierId.length} NCC`
    await this.auctionHistoryRepo.insert(historyNew)

    //send mail cho các Doanh nghiệp đc mời thêm
    await this.emailService.ThongBaoNccThamGiaDauGiaNhanh(auction.id, data.lstSupplierId)

    return { message: ACTION_SUCCESS }
  }

  /** Hủy đấu giá */
  public async cancelData(data: AuctionCancel, user: UserDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const auction = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!auction) throw new Error(ERROR_NOT_FOUND_DATA)

    const isCancel = auction.status == enumData.AuctionStatus.CANCEL.code
    if (isCancel) throw new Error(`Phiên đấu giá đã hủy trước đó!`)

    /** Chưa đến hạn? */
    const isNotYet = new Date(auction.dateStart).getTime() > new Date().getTime()
    if (!isNotYet) throw new Error(`Chỉ được hủy khi phiên đấu giá chưa đến hạn!`)

    await this.repo.update(auction.id, { status: enumData.AuctionStatus.CANCEL.code, updatedBy: user.id, updatedAt: new Date() })

    const historyNew = new AuctionHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.auctionId = auction.id
    historyNew.createdBy = user.id
    historyNew.description = `Nhân viên ${user.username} hủy phiên đấu giá`
    await this.auctionHistoryRepo.insert(historyNew)

    //send mail cho các Doanh nghiệp
    await this.emailService.ThongBaoNccHuyDauGiaNhanh(auction.id)

    return { message: ACTION_SUCCESS }
  }

  //#endregion

  //#region CLIENT

  /** Chi tiết đấu giá */
  public async findDetailAuctionSupplier(data: { id: string }, user: UserDto) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const res: any = await this.auctionSupplierRepo.findOne({
      where: { auctionId: data.id, supplierId: user.supplierId },
      // where: { auctionId: data.id },

      relations: { auction: { bid: true } },
    })

    const lstSup: any = await this.auctionSupplierRepo.find({
      where: { auctionId: data.id },
    })
    if (!res) throw new Error(`Không tìm thấy phiên đấu giá, hoặc bạn không được mời tham gia phiên đấu giá này!`)
    if (res.status == enumData.AuctionStatus.CANCEL.code) {
      throw new Error(`Phiên đấu giá này đã bị hủy!`)
    }
    if (res.status == enumData.AuctionStatus.DONE.code) {
      throw new Error(`Phiên đấu giá này đã kết thúc!`)
    }

    res.title = res.__auction__.title
    res.dateStart = res.__auction__.dateStart
    res.dateEnd = res.__auction__.dateEnd
    res.price = res.__auction__.price
    res.minPrice = res.__auction__.minPrice
    res.description = res.__auction__.description

    if (res.__auction__.bidId) {
      res.bidId = res.__auction__.bidId
      res.bidName = res.__auction__.__bid__?.name

      const domainObj = await apiHelper.getDomain(res.companyId)
      res.bidUrl = `${domainObj?.clientUrl}/detail?bidid=${res.bidId}"`
    }

    res.numSupplier = lstSup.length || 0
    res.lstItem = await res.auctionSupplierPrice
    delete res.__auction__

    return res
  }

  /** NCC đấu giá */
  public async submitData(data: any, user: UserDto) {
    //#region Check auction
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const auctionSupplier = await this.auctionSupplierRepo.findOne({
      where: { auctionId: data.auctionId, supplierId: user.supplierId, isDeleted: false },
    })
    if (!auctionSupplier) throw new Error(`Bạn không được mời tham gia phiên đấu giá này!`)

    const auction = await auctionSupplier.auction
    const dateNow = new Date()
    if (auction.status == enumData.AuctionStatus.CANCEL.code) {
      throw new Error(`Phiên đấu giá này đã bị hủy!`)
    }
    if (auction.dateStart.getTime() > dateNow.getTime()) {
      throw new Error(`Phiên đấu giá này chưa bắt đầu!`)
    }
    if (auction.dateEnd.getTime() < dateNow.getTime()) {
      throw new Error(`Phiên đấu giá này đã kết thúc!`)
    }
    if (auctionSupplier.submitPrice && +auctionSupplier.submitPrice <= +data.submitPrice) {
      throw new Error(`Giá phải nhỏ hơn lần đấu giá trước!`)
    }

    /* nếu đến thời gian thì cộng thêm */
    if (dateNow > auction.dateStartPlus) {
      if (auction.dateEnd && auction.timePlusType) {
        const newDateEnd = moment(new Date(auction.dateEnd)).add(auction.timePlus, enumData.TimeType[auction.timePlusType].suffix)
        const newDayEnd = new Date(newDateEnd.toDate())
        /* update ngày lên nè */
        await this.repo.update(auction.id, { dateEnd: newDayEnd })
      }
      /* cộng thêm dựa vào số đăng ký */
      /*  */
    }

    //#endregion
    await this.auctionSupplierRepo.update(auctionSupplier.id, {
      submitPrice: data.submitPrice,
      submitPriceOld: auctionSupplier.submitPrice,
      submitDate: dateNow,
    })
    // Tính lại rank
    await this.reRank(data.auctionId)

    const historyNew = new AuctionHistoryEntity()
    historyNew.companyId = user.companyId
    historyNew.auctionId = auction.id
    historyNew.createdBy = user.id
    historyNew.description = `Nhà cung cấp ${user.username} đấu giá với số tiền ${data.submitPrice}`
    await this.auctionHistoryRepo.insert(historyNew)

    return { message: CREATE_SUCCESS }
  }

  private async reRank(auctionId: string) {
    const lstAuctionSupplier = await this.auctionSupplierRepo.find({
      where: { auctionId, isDeleted: false, submitPrice: Not(IsNull()) },
      order: { submitPrice: 'ASC' },
      select: { id: true, submitPrice: true, rank: true },
    })
    let rank = 1
    for (const item of lstAuctionSupplier) {
      await this.auctionSupplierRepo.update(item.id, { rank: rank })
      if (rank == 1) {
        await this.repo.update(auctionId, { minPrice: item.submitPrice })
      }
      rank++
    }
  }

  async loadAutionBySupplier(user: UserDto, data: { autionId: string }, lan?: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const ltsAutionSupplier: any = await this.repo.find({
      where: { id: data.autionId, companyId: user.companyId, isDeleted: false },
      relations: { auctionSuppliers: { supplier: { banks: true } } },
    })

    const res = []
    for (const item of ltsAutionSupplier) {
      const filteredBidSuppliers = item.__auctionSuppliers__.filter((bidSupplier: any) => bidSupplier.isWinner === true)
      for (const item1 of filteredBidSuppliers) {
        if (item1.__supplier__) {
          res.push(item1.__supplier__)
        }
      }
    }
    return await this.repo.translate(res, lan)
  }

  /**Danh sách phiên đấu giá dùng cho client*/
  async findClient(user: UserDto, data: { status?: string }) {
    if (!user.supplierId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCon: any = { companyId: user.companyId, isDeleted: false }
    if (data.status) whereCon.status = data.status
    const res: any = await this.repo.find({
      where: whereCon,
      relations: { pr: true, auctionSupplierPrice: true },
    })

    return res
  }
  //#endregion
}
