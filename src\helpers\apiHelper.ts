import { HttpService } from '@nestjs/axios'
import { catchError, delay, lastValueFrom, of, retry, throwError } from 'rxjs'
import {
  AssetDto,
  BudgetDto,
  BudgetInfoDto,
  CancelReservationDto,
  ChangeStatusDto,
  GetStockDto,
  PostBudgetDto,
  PostReservationDto,
} from '../modules/sap/dto'
import { PrSapDto, PrSapFindDto } from '../modules/pr/dto'
import { Buffer } from 'buffer'
import { HttpException, HttpStatus, UnauthorizedException } from '@nestjs/common'
import axios from 'axios'
import { parseStringPromise } from 'xml2js'
import { TemplateConfigDto } from '../modules/templateConfig/dto/templateConfigDetail.dto'
class ApiHelper {
  constructor(private httpService: HttpService) {}

  private async processCallAPiHelperBasicAuth(url: string, data: any) {
    const username = process.env.API_USERNAME
    const password = process.env.API_PASSWORD
    const basicAuth = Buffer.from(`${username}:${password}`).toString('base64')

    const request = this.httpService.post(`${url}`, data, {
      headers: { Authorization: `Basic ${basicAuth}`, 'Content-Type': 'application/json' },
    })

    // Chờ kết quả của request
    return lastValueFrom(request)
  }

  /**  Gửi thông tin mã tài sản từ SAP --> PMS */
  async getListAsset(data: AssetDto) {
    try {
      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/getlistasset`, data)

      return res.data.MT_GET_LIST_ASSET_RP_S.header
    } catch (error) {
      throw new Error(error)
    }
  }

  /** Gửi thông tin PR ZPR1, ZPR2, ZPR5 từ SAP --> PMS */
  async loadListPR(data: PrSapFindDto) {
    try {
      let params = {
        company: data.company,
        prsap: data.prsap,
        puorg: data.puorg,
        pgroup: data.pgroup,
        prtype: data.prtype,
        plant: data.plant,
        option: data.option,
      }
      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/get_pr_info`, params)
      return res.data.MT_GET_PR_INFO_RP_S.list
    } catch (error) {
      throw new Error(error)
    }
  }

  /**  Gửi thông tin mã IO từ SAP --> PMS */
  async getListIO(data: AssetDto) {
    try {
      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/PMS/GetListIO`, data)
      return res.data.MT_GET_LIST_IO_RP_S.result
    } catch (error) {
      throw new Error(error)
    }
  }

  /**  Gửi thông tin giá ngân sách kế hoạch từ SAP --> PMS */
  async getListPriceBudget(data: BudgetDto) {
    try {
      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/getlistpricebudget`, data)
      return res.data.MT_GET_LIST_PRICE_BUDGET_RP_S.data
    } catch (error) {
      throw new Error(error)
    }
  }

  /**  Post PR từ PMS --> SAP */
  async postPrFromPMS(data: PrSapDto) {
    try {
      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/post_pr`, data)
      return res.data.MT_POST_PR_RP_S
    } catch (error) {
      throw new Error(error)
    }
  }

  /**   Post trạng thái PR PO Contract từ PMS --> SAP */
  async changeStatus(data: ChangeStatusDto) {
    try {
      let params = {
        company: data.company,
        type: data.type,
        docsap: data.docsap,
        relstate: data.relstate,
      }

      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/post_change_status`, params)
      return res.data.MT_POST_CHANGE_STATUS_RP_S
    } catch (error) {
      throw new Error(error)
    }
  }

  /**  Thông tin ngân sách từ SAP -> PMS */
  async getBudgetInfo(data: BudgetInfoDto) {
    try {
      let params: any = {
        bus_trans: data.bus_trans,
        prtype: data.prtype,
        potype: data.potype,
        company: data.company,
        pugrp: data.pugrp,
        plant: data.plant,
        sku: data.sku,
        acccate: data.acccate,
        deli_date: data.deli_date,
        coa: data.coa,
        costcenter: data.costcenter,
        order: data.order,
        asset: data.asset,
        sub_asset: data.sub_asset,
      }

      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/getbudgetinfo`, params)
      return res.data.MT_GET_BUDGET_INFO_RP_S.data
    } catch (error) {
      throw new Error(error)
    }
  }

  /** Lấy URL từ request */
  public getDomain(req: Request) {
    const header: any = req?.headers
    let domain = header?.origin
    if (!domain) throw new UnauthorizedException('Không xác định domain truy cập! (code: REQUEST_DOMAIN_ERROR)')
    return domain
  }

  public async downloadFileAsBase64(fileUrl: string): Promise<string> {
    try {
      // Gửi yêu cầu HTTP để tải file
      const response = await lastValueFrom(this.httpService.get(fileUrl, { responseType: 'arraybuffer' }))
      // Chuyển đổi buffer thành base64
      const base64Data = Buffer.from(response.data).toString('base64')
      return base64Data
    } catch (error) {
      console.error('Error downloading file:', error.message)
      throw new Error('Unable to download file')
    }
  }

  /**  Đồng bộ ngân sách từ PSM sang SAP */
  async postBudgetFromPMSToSAP(data: PostBudgetDto) {
    try {
      let params: any = {
        pms_doc: data.pms_doc,
        company: data.company,
        budget_period: data.budget_period,
        fiscal_year: data.fiscal_year,
        docdate: data.docdate,
        paymnt_budget: data.paymnt_budget,
      }

      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/postbudget`, params)

      return res.data.MT_POST_BUDGET_RP_S
    } catch (error) {
      throw new Error(error)
    }
  }
  /* Post RFQ PMS => SAP */
  async postRFQ(data: PostBudgetDto) {
    try {
      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/postrfq`, data)

      return res.data
    } catch (error) {
      throw new Error(error)
    }
  }

  async postResultRFQ(data: PostBudgetDto) {
    try {
      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/postresultrfq`, data)
      return res.data
    } catch (error) {
      throw new Error(error)
    }
  }
  /** POST CHANGE STATUS */
  async postChangePR(data: PrSapDto) {
    try {
      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/post_change_pr`, data)
      return res.data.MT_POST_CHANGE_PR_RP_S
    } catch (error) {
      throw new Error(error)
    }
  }

  async getExchangeRates() {
    try {
      const response = await axios.get(process.env.VCB_EXCHANGE, {
        responseType: 'text', // nhận raw text (XML)
      })

      // Parse XML sang JSON
      const result = await parseStringPromise(response.data, { explicitArray: false })

      // Cấu trúc XML trả về có thể như sau (ví dụ):
      // result.TyGiaExchangerates.TyGiaExchangerate (mảng các phần tử tỷ giá)

      const rates = result.TyGiaExchangerates.TyGiaExchangerate

      // rates là mảng hoặc 1 object (nếu chỉ 1 phần tử), xử lý chuẩn hóa
      const normalizedRates = Array.isArray(rates) ? rates : [rates]

      // Ví dụ mỗi phần tử có các trường như:
      // MaNgoaiTe (mã ngoại tệ)
      // MuaTienMat (giá mua tiền mặt)
      // MuaChuyenKhoan (giá mua chuyển khoản)
      // Ban (giá bán)

      return normalizedRates.map((rate) => ({
        currencyCode: rate.MaNgoaiTe,
        buyCash: rate.MuaTienMat,
        buyTransfer: rate.MuaChuyenKhoan,
        sell: rate.Ban,
      }))
    } catch (error) {
      throw new HttpException('Failed to fetch or parse exchange rates', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  private async processCallAPiHelperNoAuth(url: string, data: any) {
    const request = this.httpService
      .post(`${url}`, data, {
        headers: { 'Content-Type': 'application/json' },
      })
      .pipe(
        retry({
          count: 3, // Retry tối đa 3 lần
          delay: (error, retryCount) => {
            return of(error).pipe(delay(1000)) // Delay 1s giữa mỗi lần retry
          },
        }),
        catchError((error) => {
          return throwError(() => error.response || error)
        }),
      )
    return lastValueFrom(request)
  }
  private async processCallGetAPiHelperNoAuth(url: string, params?: Record<string, any>) {
    const request = this.httpService
      .get(url, {
        headers: { 'Content-Type': 'application/json' },
        params,
      })
      .pipe(
        retry({
          count: 3, // Retry tối đa 3 lần
          delay: (error, retryCount) => {
            return of(error).pipe(delay(1000)) // Delay 1s giữa mỗi lần retry
          },
        }),
        catchError((error) => {
          return throwError(() => error.response || error)
        }),
      )
    return lastValueFrom(request)
  }

  async postTemplateConfig(data: TemplateConfigDto) {
    try {
      let res: any = await this.processCallAPiHelperNoAuth(`${process.env.KTG_DOC_DEV}/api/templates/create`, data)
      return res.data
    } catch (error) {
      throw new Error(error)
    }
  }
  async getTemplateConfig(data: any) {
    try {
      let res: any = await this.processCallGetAPiHelperNoAuth(`${process.env.KTG_DOC_DEV}/api/templates/list`, data)
      return res.data
    } catch (error) {
      throw new Error(error)
    }
  }

  async getTemplateConfigDetail(code: string) {
    try {
      let res: any = await this.processCallGetAPiHelperNoAuth(`${process.env.KTG_DOC_DEV}/api/templates/detail`, { code: code })
      return res.data
    } catch (error) {
      throw new Error(error)
    }
  }

  async getGroupTemplate() {
    try {
      let res: any = await this.processCallGetAPiHelperNoAuth(`${process.env.KTG_DOC_DEV}/api/template-group/list`)
      return res.data
    } catch (error) {
      throw new Error(error)
    }
  }

  async postUpdateTemplateConfig(data: TemplateConfigDto) {
    try {
      let res: any = await this.processCallAPiHelperNoAuth(`${process.env.KTG_DOC_DEV}/api/templates/update`, data)
      return res.data
    } catch (error) {
      throw new Error(error)
    }
  }

  /**  Post Reservation từ PMS --> SAP */
  async postReservation(data: PostReservationDto) {
    try {
      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/post_reservation`, data)
      return res.data.MT_POST_RESERVATION_RP_S
    } catch (error) {
      throw new Error(error)
    }
  }

  async postChangeReservation(data: CancelReservationDto) {
    try {
      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/post_change_reservation`, data)
      return res.data.MT_POST_CHANGE_RESERVATION_RP_S
    } catch (error) {
      throw new Error(error)
    }
  }

  async getStock(data: GetStockDto) {
    try {
      let res: any = await this.processCallAPiHelperBasicAuth(`${process.env.HOST_KTG}/pms/get_stock`, data)
      return res.data.MT_GET_STOCK_RP_S
    } catch (error) {
      throw new Error(error)
    }
  }
}

export const apiHelper = new ApiHelper(new HttpService())
