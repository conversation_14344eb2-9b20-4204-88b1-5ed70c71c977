import { <PERSON>umn, <PERSON>ti<PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { POEntity } from './po.entity'
import { ServiceEntity } from './service.entity'
import { MaterialEntity } from './material.entity'
import { InboundItemEntity, UomEntity } from '.'
import { ComplaintLineItemEntity } from './complaintLineItem.entity'
import { PoProductPriceListEntity } from './poProductPriceList.entity'
import { PoPrEntity } from './poPr.entity'

//** PO detail */
@Entity({ name: 'po_product' })
export class POProductEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.products)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.products)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  note: string

  /** Số lượng chứng từ */
  @Column({
    nullable: true,
  })
  quantityDocuments: number

  /** Số lượng đã nhập*/
  @Column({
    nullable: true,
  })
  quantityInput: number

  /** Số lượng lên PO*/
  @Column({
    nullable: true,
  })
  quantityPo: number

  /** Số lượng nghiệm thu */
  @Column({
    nullable: true,
  })
  quantityAcceptances: number

  @Column({
    nullable: true,
  })
  price: number

  @Column({
    nullable: true,
  })
  priceNew: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  itemCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.poProducts)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  /** Số lượng còn lại */
  @Column({
    nullable: true,
    default: 0,
  })
  restQuantity: number

  /**Total price */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  totalPrice: number

  /**Short text */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  shortText: string

  /** Có xác nhận giá mới hay không */
  @Column({
    nullable: true,
    default: false,
  })
  isConfirm: boolean

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.poProducts)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  /** DS inbounds */
  @OneToMany(() => InboundItemEntity, (p) => p.poProduct)
  inboundItems: Promise<InboundItemEntity[]>

  /** DS khiếu nại - line item */
  @OneToMany(() => ComplaintLineItemEntity, (p) => p.poItem)
  complaintLineItems: Promise<ComplaintLineItemEntity[]>

  @OneToMany(() => PoProductPriceListEntity, (p) => p.poProduct)
  poProductPriceList: Promise<PoProductPriceListEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchasePrId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  poPrId: string
  // @ManyToOne(() => PoPrEntity, (p) => p.products)
  // @JoinColumn({ name: 'poPrId', referencedColumnName: 'id' })
  // poPr: Promise<PoPrEntity>

  // @Column({
  //   type: 'varchar',
  //   nullable: true,
  // })
  // materialGroupId: string

  // @ManyToOne(() => MaterialGroupEntity, (p) => p.products)
  // @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  // materialGroup: Promise<MaterialGroupEntity>

  //#region thông tin ngân sách sap trả về cho từng line item
  /**Quỹ data: FUND1 */
  @Column({
    length: 'max',
    nullable: true,
  })
  fund: string

  /**Mã phòng ban data: F3C00E0000 */
  @Column({
    length: 'max',
    nullable: true,
  })
  fp: string

  /** Tên phòng ban data: PHÒNG HÀNH CHÍNH NHÂN SỰ
   */
  @Column({
    length: 'max',
    nullable: true,
  })
  fpname: string

  /**Mã trung tâm ngân sách data: F3C00E0000 */
  @Column({
    length: 'max',
    nullable: true,
  })
  fc: string

  /**Tên trung tâm ngân sách: data */
  @Column({
    length: 'max',
    nullable: true,
  })
  fcname: string

  /**Mã hạn mục ngân sách data: CGL020601000 */
  @Column({
    length: 'max',
    nullable: true,
  })
  ci: string

  /** Tên hạn mục ngân sách data: Cphí chứng nhận, gia hạn đăng kiểm sản phẩm */
  @Column({
    length: 'max',
    nullable: true,
  })
  ciname: string

  /**Kỳ ngân sách data: T02 */
  @Column({
    length: 'max',
    nullable: true,
  })
  budgetperiod: string

  /**Ngân sách cho từng item(available_amount) data: 10.000.000  */
  @Column({
    nullable: true,
    default: 0,
  })
  budget: number

  //#endregion
}
