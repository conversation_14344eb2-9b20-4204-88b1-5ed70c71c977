import { TicketEvaluationKpiDetailEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'

@Entity('ticket_evaluation_kpi_list_detail')
export class TicketEvaluationKpiListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    nullable: true,
  })
  value: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  ticketEvaluationKpiDetailId: string
  @ManyToOne(() => TicketEvaluationKpiDetailEntity, (p) => p.ticketEvaluationKpiListDetails)
  @JoinColumn({ name: 'ticketEvaluationKpiDetailId', referencedColumnName: 'id' })
  ticketEvaluationKpiDetail: Promise<TicketEvaluationKpiDetailEntity>
}
