import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, JoinColumn, ManyToOne } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { FlowApproveEntity } from './flowApprove.entity'
import { CompanyEntity } from './company.entity'
import { BlockEntity, PlantEntity, PrEntity } from '.'
import { ComplaintDepartmentEntity } from './complaintDepartment.entity'
import { ComplaintItemCargoEntity } from './complaintItemCargo.entity'
import { TicketEvaluationKpiEmployeeEntity } from './ticketEvaluationKpiEmployee.entity'
import { ReservationNormEntity } from './reservationNorm.entity'
import { ReservationEntity } from './reservation.entity'

@Entity('department')
export class DepartmentEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  companyCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string
  @ManyToOne(() => CompanyEntity, (p) => p.departments)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  plantCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.departments)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @OneToMany(() => FlowApproveEntity, (p) => p.departments)
  flowApprovies: Promise<FlowApproveEntity[]>

  @OneToMany(() => EmployeeEntity, (p) => p.department)
  employee: Promise<EmployeeEntity[]>

  // @OneToMany(() => PositionEntity, (p) => p.department)
  // positions: Promise<PositionEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  blockId: string
  @ManyToOne(() => BlockEntity, (p) => p.departments)
  @JoinColumn({ name: 'blockId', referencedColumnName: 'id' })
  block: Promise<BlockEntity>

  @OneToMany(() => ComplaintDepartmentEntity, (p) => p.department)
  complaintDepartments: Promise<ComplaintDepartmentEntity[]>

  @OneToMany(() => ComplaintItemCargoEntity, (p) => p.department)
  complaintItemCargos: Promise<ComplaintItemCargoEntity[]>

  /** ds phiếu đánh giá KPI và NV */
  @OneToMany(() => TicketEvaluationKpiEmployeeEntity, (p) => p.department)
  ticketEvaluationKpiEmployees: Promise<TicketEvaluationKpiEmployeeEntity[]>

  @OneToMany(() => PrEntity, (p) => p.department)
  pr: Promise<PrEntity[]>

  @OneToMany(() => ReservationNormEntity, (p) => p.department)
  reservationNorms: Promise<ReservationNormEntity[]>

  @OneToMany(() => ReservationEntity, (p) => p.department)
  reservations: Promise<ReservationEntity[]>
}
