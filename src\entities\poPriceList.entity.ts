import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ProcedureEntity } from './procedure.entity'
import { CurrencyEntity } from './currency.entity'
import { MasterConditionTypeEntity } from './masterConditionType.entity'
import { PoProductPriceListEntity } from './poProductPriceList.entity'

/** <PERSON><PERSON>u hình bảng giá PO */
@Entity('po_price_list')
export class PoPriceListEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  procedureId: string

  @ManyToOne(() => ProcedureEntity, (p) => p.poPriceList)
  @JoinColumn({ name: 'procedureId', referencedColumnName: 'id' })
  procedure: Promise<ProcedureEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  masterConditionTypeId: string

  @ManyToOne(() => MasterConditionTypeEntity, (p) => p.poPriceList)
  @JoinColumn({ name: 'masterConditionTypeId', referencedColumnName: 'id' })
  masterConditionType: Promise<MasterConditionTypeEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  conditionTypeCode: string

  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  conditionTypeName: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  currencyId: string

  @ManyToOne(() => CurrencyEntity, (p) => p.poPriceList)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  /** Loại cấu hình cho Amount */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  amountType: string

  /** Công thức */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  amountFormula: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  amountCode: string

  /** Loại cấu hình cho Amount */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  conditionValueType: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  conditionValueCode: string

  /** Công thức */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  conditionValueFormula: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  per: string

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  percent: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  conditionUnit: string

  /** Nguồn tạo */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  srcType: string

  /** Nguồn tạo */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  typeOf: string

  @Column({
    nullable: true,
  })
  stepNumber: number

  @Column({
    nullable: true,
  })
  counter: number

  @Column({
    nullable: true,
  })
  fromStep: number

  @Column({
    nullable: true,
  })
  toStep: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  accountKey: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  acctKeyAccruals: string

  @Column({
    nullable: true,
  })
  cndnAmount: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  qtyCode: string

  @OneToMany(() => PoProductPriceListEntity, (p) => p.poPriceList)
  poProductPriceList: Promise<PoProductPriceListEntity[]>
}
