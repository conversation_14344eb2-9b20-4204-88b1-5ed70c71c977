import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { SupplierEntity } from './supplier.entity'
import { ComplaintEntity } from './complaint.entity'
import { ComplaintChatEntity } from './complaintChat.entity'

/** Thông báo khiếu nại */
@Entity('complaint_notify')
export class ComplaintNotifyEntity extends BaseEntity {
  /** Là thông báo mới */
  @Column({
    nullable: false,
    default: false,
  })
  isNew: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.complaintNotifys)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.complaintNotifys)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  complaintId: string
  @ManyToOne(() => ComplaintEntity, (p) => p.complaintNotifys)
  @JoinColumn({ name: 'complaintId', referencedColumnName: 'id' })
  complaint: Promise<ComplaintEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  complaintChatId: string
  @ManyToOne(() => ComplaintChatEntity, (p) => p.complaintNotifys)
  @JoinColumn({ name: 'complaintChatId', referencedColumnName: 'id' })
  complaintChat: Promise<ComplaintChatEntity>

  /** trạng thái đọc */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string
}
