import { MigrationInterface, QueryRunner } from 'typeorm'

export class BusinessTemplatePlanExchange1752723800683 implements MigrationInterface {
  name = 'BusinessTemplatePlanExchange1752723800683'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" DROP COLUMN "fromCurrencyId"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" ADD "fromCurrencyId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" DROP COLUMN "toCurrencyId"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" ADD "toCurrencyId" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" DROP COLUMN "toCurrencyId"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" ADD "toCurrencyId" varchar(10)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" DROP COLUMN "fromCurrencyId"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" ADD "fromCurrencyId" varchar(10)`)
  }
}
