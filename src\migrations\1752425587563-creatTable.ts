import { MigrationInterface, QueryRunner } from "typeorm";

export class CreatTable1752425587563 implements MigrationInterface {
    name = 'CreatTable1752425587563'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "cost_setting" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_bf0b090b2fc9ad2d0047e4a6c33" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_de677f8144b4df0541ee0226fef" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "code" nvarchar(50), "name" nvarchar(1000), "description" varchar(max), CONSTRAINT "PK_bf0b090b2fc9ad2d0047e4a6c33" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "cost" ADD "costSettingId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "cost" ADD CONSTRAINT "FK_a0026bd485d49caf10fca7994da" FOREIGN KEY ("costSettingId") REFERENCES "cost_setting"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cost" DROP CONSTRAINT "FK_a0026bd485d49caf10fca7994da"`);
        await queryRunner.query(`ALTER TABLE "cost" DROP COLUMN "costSettingId"`);
        await queryRunner.query(`DROP TABLE "cost_setting"`);
    }

}
