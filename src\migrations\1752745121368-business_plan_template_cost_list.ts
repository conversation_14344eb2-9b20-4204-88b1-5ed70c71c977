import { MigrationInterface, QueryRunner } from 'typeorm'

export class BusinessPlanTemplateCostList1752745121368 implements MigrationInterface {
  name = 'BusinessPlanTemplateCostList1752745121368'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_plan_template_cost_list" ADD "typeConfig" varchar(50)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_plan_template_cost_list" DROP COLUMN "typeConfig"`)
  }
}
