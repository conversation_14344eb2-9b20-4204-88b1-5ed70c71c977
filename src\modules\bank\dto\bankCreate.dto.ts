import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class BankCreateDto {
  @ApiProperty({ description: 'Mã Ngân hàng' })
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> hàng' })
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty({ description: '<PERSON>ô tả Ngân hàng' })
  @IsOptional()
  @IsString()
  description?: string
}
