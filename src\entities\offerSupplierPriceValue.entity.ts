import { Entity, Column, Jo<PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { OfferSupplierEntity } from './offerSupplier.entity'
import { OfferPriceEntity } from './offerPrice.entity'

/** Bảng chào giá nhanh */
@Entity('offer_supplier_price_value')
export class OfferSupplierPriceValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerSupplierId: string
  @ManyToOne(() => OfferSupplierEntity, (p) => p.offerSupplierPrices)
  @JoinColumn({ name: 'offerSupplierId', referencedColumnName: 'id' })
  offerSupplier: Promise<OfferSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerPriceId: string
  @ManyToOne(() => OfferPriceEntity, (p) => p.offerSupplierPrices)
  @JoinColumn({ name: 'offerPriceId', referencedColumnName: 'id' })
  offerPrice: Promise<OfferPriceEntity>

  /** Tên hạng mục */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  offerPriceName: string

  /** Cấp độ */
  @Column({
    nullable: true,
    default: 1,
  })
  offerPriceLevel: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unit: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currency: string

  /** Tên  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string

  /** Ngày nộp chào giá */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  submitDate: Date

  /** Hình thức nộp giá: 0,1,2 tương ứng chào giá, đàm phán, đấu giá */
  @Column({
    nullable: true,
  })
  submitType: number

  /** Số lượng */
  @Column({
    nullable: true,
  })
  number: number

  /** Đơn giá */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  unitPrice: number

  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  /** Thành tiền (doanh thu theo hạng mục = Số lượng*Đơn giá ) */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  price: number
}
