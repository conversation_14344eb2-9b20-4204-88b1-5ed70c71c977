import { ShipmentCostEntity, ShipmentCostPriceEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToOne } from 'typeorm'
import { ShipmentCostStageEntity } from './shipmentCostStage.entity'

/** Thông tin shipment cost stage cost: chi phí bảng kê của shipment cost*/
@Entity('shipment_cost_stage_cost')
export class ShipmentCostStageCostEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  conditionType: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @Column({
    nullable: true,
  })
  amount: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  crcy: string

  @Column({
    nullable: true,
  })
  per: number

  @Column({
    nullable: true,
  })
  conditionValue: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  curr: string

  @Column({
    nullable: true,
  })
  cConDe: number

  @Column({
    nullable: true,
  })
  numCCo: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentCostId: string
  @ManyToOne(() => ShipmentCostEntity, (p) => p.shipmentCostStageCosts)
  @JoinColumn({ name: 'shipmentCostId', referencedColumnName: 'id' })
  shipmentCost: Promise<ShipmentCostEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentCostStageId: string
  @ManyToOne(() => ShipmentCostStageEntity, (p) => p.shipmentCostStageCosts)
  @JoinColumn({ name: 'shipmentCostStageId', referencedColumnName: 'id' })
  shipmentCostStage: Promise<ShipmentCostStageEntity>

  @Column({
    nullable: false,
    default: false,
  })
  isCheck: boolean

  /**Giá lấy từ nguồn tham chiếu đấu giá shipment */
  @Column({
    nullable: true,
  })
  priceReference: number

  /** Mối liên hệ với template bảng giá chung*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentCostPriceId: string

  @OneToOne(() => ShipmentCostPriceEntity, (p) => p.shipmentCostStageCost)
  @JoinColumn({ name: 'shipmentCostPriceId', referencedColumnName: 'id' })
  shipmentCostPrice: Promise<ShipmentCostPriceEntity>

  /**Sử dụng giá từ nguồn tham chiếu lấy từ đấu giá shipment */
  @Column({
    nullable: false,
    default: false,
  })
  isUsePrice: boolean
}
