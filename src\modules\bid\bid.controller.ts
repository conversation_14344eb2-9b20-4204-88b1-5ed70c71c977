import { Controller, UseGuards, Post, Body, Get, Param, Put, Req, Request } from '@nestjs/common'
import { BidService } from './bid.service'
import { JwtAuthGuard } from '../common/guards'
import { CurrentUser } from '../common/decorators'
import { PaginationDto, UserDto } from '../../dto'
import {
  SupplierCreateTechItemDto,
  SupplierCreateTradeItemDto,
  SupplierCreatePriceItemDto,
  SupplierCreateCustomPriceItemDto,
  BidUpdateSettingDto,
  BidCreateDto,
  BidUpdateDto,
} from './dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import {
  BidCustomPriceCreateDto,
  BidCustomPriceUpdateDto,
  BidPriceColCreateDto,
  BidPriceColUpdateDto,
  BidPriceCreateDto,
  BidPriceUpdateDto,
  BidTechCreateDto,
  BidTechUpdateDto,
  BidTradeCreateDto,
  BidTradeUpdateDto,
} from './dto2'
import { BidTechTradeService } from './bidTechTrade.service'
import { BidPriceSupService } from './bidPriceSup.service'
import { BidClientService } from './bidClient.service'
import { BidCreateService } from './bidCreate.service'
import { BidRoleService } from './bidRole.service'
import { ACCEPT_LANGUAGE } from '../../constants'
import { Request as IRequest } from 'express'

@ApiBearerAuth()
@ApiTags('Bid')
@UseGuards(JwtAuthGuard)
@Controller('bids')
export class BidController {
  constructor(
    private readonly service: BidService,
    private readonly bidTechTradeService: BidTechTradeService,
    private readonly bidPriceSupService: BidPriceSupService,

    private readonly bidClientService: BidClientService,
    private readonly bidCreateService: BidCreateService,
    private readonly bidRoleService: BidRoleService,
  ) {}

  //#region get data

  @ApiOperation({ summary: 'Lấy ds gói thầu' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { status?: string; supplierId?: string }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds gói thầu' })
  @Post('find_with_ex_matgr')
  public async findWithExmat(@CurrentUser() user: UserDto, @Body() data: { exMatGrId?: string }) {
    return await this.service.findWithExmat(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds gói thầu theo shipment và biddingTypeCode ' })
  @Post('find_with_shipment')
  public async findWithShipment(@CurrentUser() user: UserDto, @Body() data: { shipmentId?: string; biddingTypeCode?: string }) {
    return await this.service.findWithShipment(user, data)
  }

  @ApiOperation({ summary: 'Lấy chi tiết gói thầu khi chỉnh sửa' })
  @Post('find_detail_edit')
  public async findDetailEdit(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetailEdit(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp được mời thầu' })
  @Post('find_bid_supplier')
  public async findBidSupplier(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.findBidSupplier(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds service của gói thầu' })
  @Post('find_bid_service')
  public async findBidService(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.findBidService(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds PR của các gói thầu được chọn' })
  @Post('find_bid_pr')
  public async findBidPr(@CurrentUser() user: UserDto, @Body() data: { ltsBidId: [] }) {
    return await this.service.findBidPr(user, data)
  }
  @ApiOperation({ summary: 'Lấy ds PR của các gói thầu được chọn' })
  @Post('load_bid_by_pr')
  public async loadBidByPr(@CurrentUser() user: UserDto, @Body() data: { ltsBidId: any[] }) {
    return await this.service.loadBidByPr(user, data)
  }
  @ApiOperation({ summary: 'Lấy ds NCC của các gói thầu được chọn' })
  @Post('load_bid_by_supplier')
  public async loadBidBySupplier(@CurrentUser() user: UserDto, @Body() data: { bidId: string }, @Request() req: IRequest) {
    return await this.service.loadBidBySupplier(user, data, req.headers[ACCEPT_LANGUAGE])
  }

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp khi nộp thầu trang admin' })
  @Post('load_bid_supplier')
  public async loadBidSupplier(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadBidSupplier(user, data)
  }

  @ApiOperation({ summary: 'Gửi email nội bộ và NCC được mời tham gia thầu' })
  @Post('send_email_bid')
  public async sendEmailBid(
    @CurrentUser() user: UserDto,
    @Body() data: { bidId: string; lstEmployeeId: string[]; lstSupplierId: string[]; emailContent: string },
  ) {
    return await this.service.sendEmailBid(user, data)
  }

  @ApiOperation({ summary: 'DS gói thầu phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'DS gói thầu phân trang' })
  @Post('list_requirement_stack')
  public async getListRequirementBiddingStack(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.biddingRequirement(user)
  }
  //#region createBidMibile

  @ApiOperation({ summary: 'DS gói thầu phân trang' })
  @Post('template_pagination')
  public async templatePagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.templatePagination(user, data)
  }

  @ApiOperation({ summary: 'DS gói thầu phân trang' })
  @Post('price_requirement_pagination')
  public async priceRequirementPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.priceRequirementPagination(user, data)
  }

  @ApiOperation({ summary: 'DS gói thầu phân trang' })
  @Post('template_evaluation_price_pagination')
  public async evaluationPricePagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.evaluationPricePagination(user, data)
  }

  @ApiOperation({ summary: 'DS gói thầu phân trang' })
  @Post('winning_contractor_pagination')
  public async winningContractorPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.winningContractorPagination(user, data)
  }

  @ApiOperation({ summary: 'DS gói thầu phân trang' })
  @Post('evaluation_info_pagination')
  public async evaluationInfoPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.evaluationInfoPagination(user, data)
  }

  @ApiOperation({ summary: 'DS gói thầu phân trang' })
  @Post('info_requirement_pagination')
  public async infoRequirementPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.infoRequirementPagination(user, data)
  }

  @ApiOperation({ summary: 'Lấy trạng thái gói thầu' })
  @Get('get_bid_status/:id')
  public async getBidStatus(@CurrentUser() user: UserDto, @Param('id') id: string) {
    return await this.service.getBidStatus(user, id)
  }
  //#endregion

  //#endregion

  //#region createBid

  @ApiOperation({ summary: 'Tạo gói thầu' })
  @Post('create_bid')
  public async createBid(@CurrentUser() user: UserDto, @Body() data: BidCreateDto) {
    return await this.bidCreateService.createBid(user, data)
  }

  @ApiOperation({ summary: 'Copy gói thầu' })
  @Post('copy_bid')
  public async copyBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.bidCreateService.copyBid(user, data.bidId)
  }

  @ApiOperation({ summary: 'Chỉnh sửa thông tin gói thầu' })
  @Post('update_bid')
  public async updateBid(@CurrentUser() user: UserDto, @Body() data: BidUpdateDto) {
    return await this.bidCreateService.updateBid(user, data)
  }

  @ApiOperation({ summary: 'Tạo gói thầu bằng excel' })
  @Post('import_bid')
  public async importBids(@CurrentUser() user: UserDto, @Body() data: { lstData: any[] }) {
    return await this.bidCreateService.importBids(user, data)
  }

  @ApiOperation({ summary: 'Yêu cầu hủy gói thầu' })
  @Put('request_delete_bid/:id')
  public async requestDeleteBid(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidCreateService.requestDeleteBid(user, bidId)
  }

  @ApiOperation({ summary: 'Xác nhận hủy gói thầu' })
  @Put('delete_bid/:id')
  public async deleteBid(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidCreateService.deleteBid(user, bidId)
  }

  @ApiOperation({ summary: 'Gửi yêu cầu phê duyệt gói thầu tạm' })
  @Put('send_mpoleader_check_bid/:id')
  public async sendMpoleaderCheckBid(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidRoleService.sendMpoleaderCheckBid(user, bidId)
  }

  @ApiOperation({ summary: 'Duyệt gói thầu tạm' })
  @Put('mpoleader_accept_bid/:id')
  public async mpoleaderAcceptBid(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidRoleService.mpoleaderAcceptBid(user, bidId)
  }

  @ApiOperation({ summary: 'Duyệt gói thầu tạm' })
  @Post('mpoleader_accept_bid_post')
  public async mpoleaderAcceptBidPost(@CurrentUser() user: UserDto, @Body() data: { bidId: string; comment: string }) {
    return await this.bidRoleService.mpoleaderAcceptBidAdapter(user, data)
  }

  @ApiOperation({ summary: 'Chào giá cạnh tranh cho gói thầu tạm' })
  @Put('mpoleader_accept_bid_quick/:id')
  public async mpoleaderAcceptBidQuick(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidRoleService.mpoleaderAcceptBidQuick(user, bidId)
  }

  @ApiOperation({ summary: 'Từ chối gói thầu tạm' })
  @Put('mpoleader_reject_bid/:id')
  public async mpoleaderRejectBid(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidRoleService.mpoleaderRejectBid(user, bidId)
  }

  @ApiOperation({ summary: 'Duyệt gói thầu tạm' })
  @Post('mpoleader_reject_bid_post')
  public async mpoleaderAcceptBidAdapter(@CurrentUser() user: UserDto, @Body() data: { bidId: string; comment: string }) {
    return await this.bidRoleService.mpoleaderAcceptBidAdapter(user, data)
  }

  @ApiOperation({ summary: 'Chỉnh sửa thông tin khác của gói thầu ' })
  @Post('update_setting_rate')
  public async updateSettingRate(@CurrentUser() user: UserDto, @Body() data: BidUpdateSettingDto) {
    return await this.bidRoleService.updateSettingRate(user, data)
  }

  //#endregion

  //#region bidTech

  @ApiOperation({ summary: 'Tải template kỹ thuật từ Item' })
  @Post('load_tech')
  public async loadTech(@CurrentUser() user: UserDto, @Body() data: { id: string; templateId: string }) {
    return await this.bidTechTradeService.loadTech(user, data.id, data.templateId)
  }

  @ApiOperation({ summary: 'Lưu template kỹ thuật cho gói thầu' })
  @Post('create_tech/:id')
  public async createTech(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { noteTech?: string }) {
    return await this.bidTechTradeService.createTech(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lấy ds các tiêu chí kỹ thuật cho gói thầu' })
  @Get('get_tech/:id')
  public async getTech(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidTechTradeService.getTech(user, bidId)
  }

  @ApiOperation({ summary: 'Gửi yêu cầu phê duyệt thiết lập yêu cầu kĩ thuật ' })
  @Put('bid_send_check_tech/:id')
  public async sendCheckBidTech(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidTechTradeService.sendCheckBidTech(user, bidId)
  }

  @ApiOperation({ summary: 'Duyệt thiết lập yêu cầu kỹ thuật của gói thầu ' })
  @Post('tech_accept/:id')
  public async techAccept(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { comment?: string }) {
    return await this.bidTechTradeService.techAccept(user, bidId, data)
  }

  @ApiOperation({ summary: 'Từ chối thiết lập yêu cầu kỹ thuật của gói thầu' })
  @Post('tech_reject/:id')
  public async techReject(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { comment?: string }) {
    return await this.bidTechTradeService.techReject(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lấy data cbb tiêu chí cấp 1' })
  @Get('tech_get_data/:bidid')
  public async techGetData(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.bidTechTradeService.techGetData(user, bidId)
  }

  @ApiOperation({ summary: 'Tạo mới tiêu chí kỹ thuật' })
  @Post('tech_create_data')
  public async techCreateData(@CurrentUser() user: UserDto, @Body() data: BidTechCreateDto) {
    return await this.bidTechTradeService.techCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật tiêu chí kỹ thuật' })
  @Post('tech_update_data')
  public async techUpdateData(@CurrentUser() user: UserDto, @Body() data: BidTechUpdateDto) {
    return await this.bidTechTradeService.techUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Xóa tiêu chí kỹ thuật' })
  @Post('tech_delete_data')
  public async techDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidTechTradeService.techDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xoá tất cả tiêu chí kỹ thuật' })
  @Post('tech_deleteall_data')
  public async techDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidTechTradeService.techDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Import excel kỹ thuật' })
  @Post('tech_import/:bidId')
  public async tech_import(
    @CurrentUser() user: UserDto,
    @Param('bidId') bidId: string,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.bidTechTradeService.tech_import(user, bidId, data)
  }

  @ApiOperation({ summary: 'Danh sách lựa chọn cho tiêu chí kiểu List' })
  @Get('tech_listdetail_list/:id')
  public async bidTechListDetail_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.bidTechTradeService.bidTechListDetail_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo lựa chọn cho tiêu chí kiểu List' })
  @Post('tech_listdetail_create_data')
  public async bidTechListDetail_create_data(@CurrentUser() user: UserDto, @Body() data: { bidTechId: string; name: string; value: number }) {
    return await this.bidTechTradeService.bidTechListDetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật lựa chọn cho tiêu chí kiểu List' })
  @Post('tech_listdetail_update_data')
  public async bidTechListDetail_update_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; bidTechId: string; name: string; value: number },
  ) {
    return await this.bidTechTradeService.bidTechListDetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa lựa chọn cho tiêu chí kiểu List' })
  @Post('tech_listdetail_delete_data')
  public async bidTechListDetail_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidTechTradeService.bidTechListDetail_delete_data(user, data.id)
  }

  //#endregion

  //#region bidTrade

  @ApiOperation({ summary: 'Tải template ĐKTM từ Item' })
  @Post('load_trade')
  public async loadTrade(@CurrentUser() user: UserDto, @Body() data: { id: string; templateId: string }) {
    return await this.bidTechTradeService.loadTrade(user, data.id, data.templateId)
  }

  @ApiOperation({ summary: 'Lưu template ĐKTM cho gói thầu' })
  @Post('create_trade/:id')
  public async createTrade(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { noteTrade: string }) {
    return await this.bidTechTradeService.createTrade(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lưu template ĐKTM cho gói thầu' })
  @Post('send_trade/:id')
  public async sendTrade(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { noteTrade: string }) {
    return await this.bidTechTradeService.sendTrade(user, bidId, data)
  }

  @ApiOperation({ summary: 'Duyệt thiết lập điều kiện thương mại của gói thầu' })
  @Post('trade_accept/:id')
  public async tradeAccept(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { noteTrade?: string; noteMPOLeader?: string }) {
    return await this.bidTechTradeService.tradeAccept(user, bidId, data)
  }

  @ApiOperation({ summary: 'Duyệt thiết lập điều kiện thương mại của gói thầu' })
  @Post('trade_reject/:id')
  public async tradeReject(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: any) {
    return await this.bidTechTradeService.tradeReject(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lấy thiết lập ĐKTM của gói thầu' })
  @Get('get_trade/:id')
  public async getTrade(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidTechTradeService.getTrade(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy data cbb tiêu chí cấp 1' })
  @Get('trade_get_data/:bidid')
  public async tradeGetData(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.bidTechTradeService.tradeGetData(user, bidId)
  }

  @ApiOperation({ summary: 'Tạo thêm ĐKTM' })
  @Post('trade_create_data')
  public async tradeCreateData(@CurrentUser() user: UserDto, @Body() data: BidTradeCreateDto) {
    return await this.bidTechTradeService.tradeCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật ĐKTM' })
  @Post('trade_update_data')
  public async tradeUpdateData(@CurrentUser() user: UserDto, @Body() data: BidTradeUpdateDto) {
    return await this.bidTechTradeService.tradeUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Xoá ĐKTM' })
  @Post('trade_delete_data')
  public async tradeDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidTechTradeService.tradeDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xoá tất cả ĐKTM' })
  @Post('trade_deleteall_data')
  public async tradeDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidTechTradeService.tradeDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Import excel ĐKTM' })
  @Post('trade_import/:bidId')
  public async trade_import(
    @CurrentUser() user: UserDto,
    @Param('bidId') bidId: string,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.bidTechTradeService.trade_import(user, bidId, data)
  }

  @ApiOperation({ summary: 'Danh sách lựa chọn cho tiêu chí kiểu List' })
  @Get('trade_listdetail_list/:id')
  public async bidTradeListDetail_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.bidTechTradeService.bidTradeListDetail_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo lựa chọn cho tiêu chí kiểu List' })
  @Post('trade_listdetail_create_data')
  public async bidTradeListDetail_create_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; bidTradeId: string; name: string; value: number },
  ) {
    return await this.bidTechTradeService.bidTradeListDetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật lựa chọn cho tiêu chí kiểu List' })
  @Post('trade_listdetail_update_data')
  public async bidTradeListDetail_update_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; bidTradeId: string; name: string; value: number },
  ) {
    return await this.bidTechTradeService.bidTradeListDetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa lựa chọn cho tiêu chí kiểu List' })
  @Post('trade_listdetail_delete_data')
  public async bidTradeListDetail_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidTechTradeService.bidTradeListDetail_delete_data(user, data.id)
  }

  //#endregion

  //#region bidPrice

  @ApiOperation({ summary: 'Lấy các hạng mục giá của gói thầu' })
  @Post('price_find')
  public async price_find(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.bidPriceSupService.price_find(user, data)
  }

  @ApiOperation({ summary: 'Tải các hạng mục chào giá từ template Item' })
  @Get('load_price/:id')
  public async loadPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidPriceSupService.loadPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Tải các hạng mục chào giá từ template Item' })
  @Get('load_price_service/:id')
  public async loadPriceService(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidPriceSupService.loadPriceItem(user, bidId)
  }

  @ApiOperation({ summary: 'Tải các hạng mục cơ cấu giá từ template Item' })
  @Get('load_customprice/:id')
  public async loadCustomPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidPriceSupService.loadCustomPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lưu template chào giá cho gói thầu' })
  @Post('create_price/:id')
  public async createPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { notePrice: string }) {
    return await this.bidPriceSupService.createPrice(user, bidId, data)
  }

  @ApiOperation({ summary: 'Duyệt thiết lập  hạng mục cơ cấu, cơ cấu giá của gói thầu' })
  @Post('price_accept/:id')
  public async priceAccept(@CurrentUser() user: UserDto, @Param('id') bidId: string, @Body() data: { notePrice?: string }) {
    return await this.bidPriceSupService.priceAccept(user, bidId, data)
  }

  @ApiOperation({ summary: 'Lấy template chào giá cho gói thầu' })
  @Get('get_price/:id')
  public async getPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidPriceSupService.getPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Xác nhận cấu hình lại bảng giá' })
  @UseGuards(JwtAuthGuard)
  @Put('reset_price/:id')
  public async resetPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidPriceSupService.resetPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lưu cấu hình bảng giá' })
  @UseGuards(JwtAuthGuard)
  @Post('save_reset_price/:id')
  public async saveResetPrice(
    @CurrentUser() user: UserDto,
    @Param('id') bidId: string,
    @Body()
    data: {
      lstSupplierId: string[]
      resetPriceEndDate: Date
      isRequireFilePriceDetail: boolean
      isRequireFileTechDetail: boolean
    },
  ) {
    return await this.bidPriceSupService.saveResetPrice(user, bidId, data)
  }

  @ApiOperation({ summary: 'Danh sách ncc đã nộp chào giá hiệu chỉnh' })
  @UseGuards(JwtAuthGuard)
  @Get('bid_supplier_join_reset_price/:bidid')
  public async bidSupplierJoinResetPrice(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.bidPriceSupService.bidSupplierJoinResetPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Kết thúc nộp chào giá hiệu chỉnh' })
  @UseGuards(JwtAuthGuard)
  @Put('end_reset_price/:id')
  public async endResetPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidPriceSupService.endResetPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy template cơ cấu giá của gói thầu' })
  @Get('get_customprice/:id')
  public async getCustomPrice(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidPriceSupService.getCustomPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Tạo hạng mục giá mới' })
  @Post('price_create_data')
  public async priceCreateData(@CurrentUser() user: UserDto, @Body() data: BidPriceCreateDto) {
    return await this.bidPriceSupService.priceCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hạng mục giá' })
  @Post('price_update_data')
  public async priceUpdateData(@CurrentUser() user: UserDto, @Body() data: BidPriceUpdateDto) {
    return await this.bidPriceSupService.priceUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Xóa hạng mục giá' })
  @Post('price_delete_data')
  public async priceDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidPriceSupService.priceDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả hạng mục giá của gói thầu' })
  @Post('price_deleteall_data')
  public async priceDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidPriceSupService.priceDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Lưu công thức tính đơn giá' })
  @Post('setting_fomular')
  public async setting_fomular(@CurrentUser() user: UserDto, @Body() data: { id: string; fomular: string }) {
    return await this.bidPriceSupService.setting_fomular(user, data)
  }

  @ApiOperation({ summary: 'Setup cách tính điểm giá của Item gói thầu' })
  @Post('setting_way_cal_score_price')
  public async saveSettingPriceCalWay(@CurrentUser() user: UserDto, @Body() data: { id: string; wayCalScorePrice: string }) {
    return await this.bidPriceSupService.saveSettingPriceCalWay(user, data)
  }

  @ApiOperation({ summary: 'Import excel chào giá' })
  @Post('price_import/:bidId')
  public async price_import(
    @CurrentUser() user: UserDto,
    @Param('bidId') bidId: string,
    @Body() data: { lstDataTable1: any[]; lstDataTable2: any[] },
  ) {
    return await this.bidPriceSupService.price_import(user, bidId, data)
  }

  @ApiOperation({ summary: 'Tạo hạng mục cơ cấu giá mới' })
  @Post('customprice_create_data')
  public async customPriceCreateData(@CurrentUser() user: UserDto, @Body() data: BidCustomPriceCreateDto) {
    return await this.bidPriceSupService.customPriceCreateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hạng mục cơ cấu giá' })
  @Post('customprice_update_data')
  public async customPriceUpdateData(@CurrentUser() user: UserDto, @Body() data: BidCustomPriceUpdateDto) {
    return await this.bidPriceSupService.customPriceUpdateData(user, data)
  }

  @ApiOperation({ summary: 'Xóa hạng mục cơ cấu giá' })
  @Post('customprice_delete_data')
  public async customPriceDeleteData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidPriceSupService.customPriceDeleteData(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả hạng mục cơ cấu giá của Item gói thầu' })
  @Post('customprice_deleteall_data')
  public async customPriceDeleteAllData(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidPriceSupService.customPriceDeleteAllData(user, data.id)
  }

  @ApiOperation({ summary: 'Import template cơ cấu giá Item gói thầu' })
  @Post('customprice_import/:bidId')
  public async custompriceImport(@CurrentUser() user: UserDto, @Param('bidId') bidId: string, @Body() data: { lstData: any[] }) {
    return await this.bidPriceSupService.custompriceImport(user, bidId, data)
  }

  @ApiOperation({ summary: 'Danh sách cột động Item' })
  @Get('price_col_list/:id')
  public async bidPriceCol_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.bidPriceSupService.bidPriceCol_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo cột động Item' })
  @Post('price_col_create_data')
  public async bidPriceCol_create_data(@CurrentUser() user: UserDto, @Body() data: BidPriceColCreateDto) {
    return await this.bidPriceSupService.bidPriceCol_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật cột động Item' })
  @Post('price_col_update_data')
  public async bidPriceCol_update_data(@CurrentUser() user: UserDto, @Body() data: BidPriceColUpdateDto) {
    return await this.bidPriceSupService.bidPriceCol_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa cột động Item' })
  @Post('price_col_delete_data')
  public async bidPriceCol_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidPriceSupService.bidPriceCol_delete_data(user, data.id)
  }

  @ApiOperation({ summary: 'Xóa tất cả cột động Item gói thầu' })
  @Post('price_col_delete_all_data')
  public async bidPriceCol_delete_all_data(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.bidPriceSupService.bidPriceCol_delete_all_data(user, data.bidId)
  }

  @ApiOperation({ summary: 'Danh sách thông tin mở rộng của hạng mục chào giá' })
  @Get('price_listdetail_list/:id')
  public async bidPriceListDetail_list(@CurrentUser() user: UserDto, @Param('id') id: any) {
    return await this.bidPriceSupService.bidPriceListDetail_list(user, id)
  }

  @ApiOperation({ summary: 'Tạo thông tin mở rộng của hạng mục chào giá' })
  @Post('price_listdetail_create_data')
  public async bidPriceListDetail_create_data(
    @CurrentUser() user: UserDto,
    @Body() data: { bidPriceId: string; name: string; type: string; value: string },
  ) {
    return await this.bidPriceSupService.bidPriceListDetail_create_data(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin mở rộng của hạng mục chào giá' })
  @Post('price_listdetail_update_data')
  public async bidPriceListDetail_update_data(
    @CurrentUser() user: UserDto,
    @Body() data: { id: string; bidPriceId: string; name: string; type: string; value: string },
  ) {
    return await this.bidPriceSupService.bidPriceListDetail_update_data(user, data)
  }

  @ApiOperation({ summary: 'Xóa thông tin mở rộng của hạng mục chào giá' })
  @Post('price_listdetail_delete_data')
  public async bidPriceListDetail_delete_data(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.bidPriceSupService.bidPriceListDetail_delete_data(user, data.id)
  }

  //#endregion

  //#region bidChooseSupplier

  @ApiOperation({ summary: 'Lấy danh sách NCC mời tham gia thầu' })
  @Post('load_supplier_invite')
  public async loadSupplierInvite(
    @CurrentUser() user: UserDto,
    @Body() data: { bidId: string; supplierName?: string; lstStatus?: string[]; typeGetData: number },
  ) {
    return await this.bidPriceSupService.loadSupplierInvite(user, data)
  }

  @ApiOperation({ summary: 'Chọn NCC mời tham gia thầu' })
  @Post('bid_choose_supplier')
  public async bidChooseSupplier(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstData: any[] }) {
    return await this.bidPriceSupService.bidChooseSupplier(user, data)
  }

  @ApiOperation({ summary: 'Chọn lại NCC mời tham gia thầu' })
  @Put('bid_rechoose_supplier/:id')
  public async bidRechooseSupplier(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidPriceSupService.bidRechooseSupplier(user, bidId)
  }

  @ApiOperation({ summary: 'Gửi yêu cầu phê duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu' })
  @Put('bid_send_mpoleader_check/:id')
  public async bidSendMpoleaderCheck(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.bidPriceSupService.bidSendMpoleaderCheck(user, bidId)
  }
  //#endregion

  //#region accept all

  @ApiOperation({ summary: 'Duyệt tất cả (bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu)' })
  @Post('accept_all')
  public async acceptAll(@CurrentUser() user: UserDto, @Body() data: { id: string; comment: string }) {
    return await this.bidPriceSupService.acceptAll(user, data)
  }

  @ApiOperation({ summary: 'Từ chối tất cả (bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu)' })
  @Post('reject_all')
  public async rejectAll(@CurrentUser() user: UserDto, @Body() data: { id: string; comment: string }) {
    return await this.bidPriceSupService.rejectAll(user, data)
  }

  //#endregion

  //#region open bid

  @ApiOperation({ summary: 'Load ds Doanh nghiệp khi mở thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('load_bid_supplier_open_bid')
  public async loadBidSupplierOpenBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadBidSupplierOpenBid(user, data)
  }

  @ApiOperation({ summary: 'Xác nhận mở thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('open_bid')
  public async openBid(
    @CurrentUser() user: UserDto,
    @Body()
    data: {
      bidId: string
      lstData: any[]
      lstEmployeeId: string[]
      lstEmployeeTech: any[]
      lstEmployeeOther: any[]
      isMemberScore: boolean
      isOtherCouncil: boolean
      isPersonalScoreVisible: boolean
    },
  ) {
    return await this.service.openBid(user, data)
  }

  //#endregion

  //#region Chức năng admin nộp hồ sơ cho Doanh nghiệp

  @ApiOperation({ summary: 'Admin nộp hồ sơ thầu Item cho NCC' })
  @Post('bidding_from_admin_createBidSupplier')
  public async biddingFromAdmin_createBidSupplier(
    @CurrentUser() user: UserDto,
    @Body()
    data: {
      bidId: string
      supplierId: string
      techInfo: SupplierCreateTechItemDto[]
      tradeInfo: SupplierCreateTradeItemDto[]
      priceShipmentInfo: any[]
      priceInfo: SupplierCreatePriceItemDto[]
      customPriceInfo: SupplierCreateCustomPriceItemDto[]
    },
  ) {
    return await this.bidClientService.createBidSupplierFromAdmin(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds hồ sơ kỹ thuật Item của NCC' })
  @Post('bidding_from_admin_loadDataBidTech')
  public async biddingFromAdmin_loadDataBidTech(@CurrentUser() user: UserDto, @Body() data: { bidId: string; supplierId?: string }) {
    return await this.bidClientService.loadDataBidTech(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds hồ sơ ĐKTM Item của NCC' })
  @Post('bidding_from_admin_loadDataBidTrade')
  public async biddingFromAdmin_loadDataBidTrade(@CurrentUser() user: UserDto, @Body() data: { bidId: string; supplierId?: string }) {
    return await this.bidClientService.loadDataBidTrade(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds bảng chào giá Item của NCC' })
  @Post('bidding_from_admin_loadDataBidPrice')
  public async biddingFromAdmin_loadDataBidPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; supplierId?: string }) {
    return await this.bidClientService.loadDataBidPrice(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds cơ cấu giá Item của NCC' })
  @Post('bidding_from_admin_loadDataBidCustomPrice')
  public async biddingFromAdmin_loadDataBidCustomPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; supplierId?: string }) {
    return await this.bidClientService.loadDataBidCustomPrice(user, data)
  }

  @ApiOperation({ summary: 'Danh sách bid chờ duyệt' })
  @Post('waiting_approve')
  public async bidRequirement(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.bidRequirement(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds bid supplier theo gói thầu' })
  @Post('load_bid_supplier_from_bid')
  public async loadBidSupplierFromBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadBidSupplierFromBid(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds đấu giá shipment' })
  @Post('load_supplier_shipment_value')
  public async loadSupplierShipmentValue(@CurrentUser() user: UserDto, @Body() data: { bidSupplierId: string }) {
    return await this.service.loadSupplierShipmentValue(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách item của gói thầu' })
  @Post('load_list_item_from_bid')
  public async loadListitemFromBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadListitemFromBid(user, data)
  }

  @Post('update-api')
  public async updateRfqToSap() {
    return await this.service.updateRfqResultToSap()
  }

  //#endregion
}
