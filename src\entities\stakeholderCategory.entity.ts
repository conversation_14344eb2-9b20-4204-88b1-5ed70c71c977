import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierNumberRequestApproveEntity } from '.'

/** <PERSON>h mục cổ đông
 */
@Entity('stakeholder_category')
export class StakeholderCategoryEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.stakeholderCategory)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>
}
