import { Entity, Column, <PERSON>To<PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierServiceEntity } from './supplierService.entity'

/** Nhà xưởng và thiết bị ở lĩnh vực kinh doanh */
@Entity('supplier_service_factory')
export class SupplierServiceFactoryEntity extends BaseEntity {
  /**  Tên */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  name: string

  /**Tổng diện tích */
  @Column({
    type: 'float',
    nullable: true,
  })
  totalAreaM2: number

  /**Số giờ làm việc trung bình */
  @Column({
    type: 'float',
    nullable: true,
  })
  avgWorkHour: number

  /**Diện tích văn phòng */
  @Column({
    type: 'float',

    nullable: true,
  })
  officeAreaM2: number

  /**Tổng diện tích nhà xưởng sản xuất */
  @Column({
    type: 'float',

    nullable: true,
  })
  productionAreaM2: number

  /**Diện tích nhà kho nguyên liệu (m2) */
  @Column({
    type: 'float',

    nullable: true,
  })
  rawMaterialAreaM2: number

  /**Diện tích nhà kho thành phẩm (m2)*/
  @Column({
    type: 'float',
    nullable: true,
  })
  finishedGoodsAreaM2: number

  /** Lĩnh vực kinh doanh NCC  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierServiceId: string
  @ManyToOne(() => SupplierServiceEntity, (p) => p.serviceFacilities)
  @JoinColumn({ name: 'supplierServiceId', referencedColumnName: 'id' })
  supplierService: Promise<SupplierServiceEntity>
}
