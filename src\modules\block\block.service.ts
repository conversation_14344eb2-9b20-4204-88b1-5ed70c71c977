import { Injectable } from '@nestjs/common'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { BlockRepository, DepartmentRepository } from '../../repositories'
import {
  CREATE_SUCCESS,
  enumData,
  ERROR_CODE_TAKEN,
  ERROR_NOT_FOUND_DATA,
  IMPORT_SUCCESS,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../constants'
import { BlockEntity } from '../../entities'
import { In, Like } from 'typeorm'
import { coreHelper } from '../../helpers'
import { BlockCreateDto, BlockUpdateDto, BlockExcelDto, AddDepartmentBlockDto } from './dto'
import { UpdateCompanyBlockDto } from './dto/updateCompanyBlock.dto'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'

@Injectable()
export class BlockService {
  constructor(
    private readonly repo: BlockRepository,
    private departmentRepo: DepartmentRepository,
    private organizationalPositionService: OrganizationalPositionService,
  ) {}

  public async find(user: UserDto, data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    if (data.companyId) whereCon.companyId = data.companyId
    return await this.repo.find({ where: whereCon, order: { createdAt: 'DESC' } })
  }

  async findOne(data: FilterOneDto) {
    const whereCon: any = { isDeleted: false }
    if (data.id) whereCon.id = data.id
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    return await this.repo.findOne({ where: whereCon })
  }

  public async createData(user: UserDto, data: BlockCreateDto) {
    const objCheckCode = await this.repo.findOne({ where: { code: Like(`%${data.code}%`) }, select: { id: true } })
    if (objCheckCode) throw new Error(ERROR_CODE_TAKEN)

    const block = new BlockEntity()
    block.name = data.name
    block.code = data.code
    block.description = data.description
    block.createdBy = user.id
    block.createdAt = new Date()
    await this.repo.insert(block)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: BlockUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.description = data.description
    entity.updatedBy = user.id
    entity.updatedAt = new Date()
    await entity.save()

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.Block.code)
    if (dataRs.type === enumData.RoleData.All.code) {
      /* do nothing */
    }
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.plantId) whereCon.plantId = data.where.plantId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.companyId) whereCon.companyId = data.where.companyId
    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { company: true, plant: true },
      order: { code: 'DESC', createdAt: 'DESC' },
    })
    if (res[0].length == 0) return [[], 0]
    for (const item of res[0]) {
      item.companyName = item?.__company__?.name
      item.companyCode = item?.__company__?.code
      item.plantName = item?.__plant__?.name
      item.plantCode = item?.__plant__?.code
      delete item.__company__
      delete item.__plant__
    }
    return res
  }

  public async updateIsDelete(data: { id: string }, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async findDetail(user: UserDto, data: FilterOneDto) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: { plant: true, company: true },
    })
    res.companyName = res?.__company__?.name
    res.companyCode = res?.__company__?.code
    res.plantName = res?.__plant__?.name
    res.plantCode = res?.__plant__?.code
    delete res.__company__
    delete res.__plant__
    return res
  }

  async createDataExcel(user: UserDto, data: BlockExcelDto[]) {
    if (data.length < 1) throw new Error('Vui lòng điền ít nhất 1 dòng dữ liệu!')
    {
      const dublicateArayCode = coreHelper.findDuplicates(data, 'code')
      if (dublicateArayCode.length > 0) throw new Error(`Danh sách mã khối trùng nhau ${dublicateArayCode.toString()}`)
    }

    //lọc lấy danh sách code của khối
    const codes = data.map(function (obj) {
      return obj.code
    })

    // tìm ra những mã bị trùng dưới data base
    const dupCount = await this.repo.find({ where: { code: In(codes) } })
    if (dupCount.length > 0) {
      const dupCode = data.map(function (obj) {
        return obj.code
      })
      throw new Error(`Danh sách mã khối trùng nhau [${dupCode.toString()}]`)
    }
    // const dictBlock: any = {}
    // {
    //   const lstCode = coreHelper.selectDistinct(data, 'code')
    //   const lstBlock: any = await this.repo.find({ where: { code: In(lstCode), isDeleted: false }, select: { id: true, code: true } })
    //   lstBlock.forEach((c) => (dictBlock[c.code] = c))
    // }

    // for (let item of data) {
    //   if (dictBlock[item.code]) throw new Error(`Khối [${item.code}] đã tồn tại . Vui lòng kiểm tra lại`)
    // }
    const lstTask = []
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BlockEntity)
      for (let item of data) {
        item.code = item.code.trim().toUpperCase()
        const newBlock = new BlockEntity()
        newBlock.code = item.code
        newBlock.name = item.name
        newBlock.description = item.description
        newBlock.createdBy = user.id
        newBlock.createdAt = new Date()
        const entity = repo.create(newBlock)
        lstTask.push(entity)
      }
      const lstChild = coreHelper.splitArrayByParameters(lstTask, 2000)
      for (const chunk of lstChild) {
        await trans.insert(BlockEntity, chunk)
      }
    })
    return { message: IMPORT_SUCCESS }
  }

  /** Hàm cập nhật companyId */
  public async updateCompanyBlock(user: UserDto, data: UpdateCompanyBlockDto) {
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BlockEntity)

      const lstBlock = await repo.find({
        where: { id: In(data.lstBlockId), isDeleted: false },
        select: { id: true },
      })

      const lstBlockId = lstBlock.map((c: any) => c.id)
      let lstPartIdNew = data.lstBlockId.filter((c) => !lstBlockId.includes(c))
      lstPartIdNew = [...new Set(lstPartIdNew)]

      for (const blockId of lstBlockId) {
        const updateData = {
          companyId: data.companyId,
          updatedBy: user.id,
          updatedAt: new Date(),
        }
        await repo.update(blockId, updateData)
      }

      return { message: UPDATE_SUCCESS }
    })
  }

  /** Thêm phòng ban công ty */
  async addDepartmentCompany(user: UserDto, data: AddDepartmentBlockDto) {
    const block = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!block) throw new Error(ERROR_NOT_FOUND_DATA + ' [ công ty ] ')

    const lstDepartment = await this.departmentRepo.find({ where: { id: In(data.departmentIds), isDeleted: false } })
    if (!lstDepartment.length) throw new Error(ERROR_NOT_FOUND_DATA + ' [ phòng ban ] ')

    // for (const department of lstDepartment) {
    //   if (department.companyId) throw new Error(`Phòng ban [${department.name}] đã có thuộc công ty`)
    // }

    const lstDepartmentId = lstDepartment.map((department) => department.id)

    await this.departmentRepo.update({ id: In(lstDepartmentId) }, { blockId: data.id, updatedBy: user.id, updatedAt: new Date() })
    return { message: UPDATE_SUCCESS }
  }

  /** Xóa phòng ban công ty */
  async removeDepartmentCompany(user: UserDto, data: AddDepartmentBlockDto) {
    const block = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!block) throw new Error(ERROR_NOT_FOUND_DATA + ' [ công ty ] ')

    const lstDepartment = await this.departmentRepo.find({ where: { id: In(data.departmentIds), blockId: data.id, isDeleted: false } })
    if (!lstDepartment.length) throw new Error(ERROR_NOT_FOUND_DATA + ' [ phòng ban ] ')

    const lstDepartmentId = lstDepartment.map((department) => department.id)
    await this.departmentRepo.update({ id: In(lstDepartmentId) }, { blockId: null, updatedBy: user.id, updatedAt: new Date() })

    return { message: UPDATE_SUCCESS }
  }
}
