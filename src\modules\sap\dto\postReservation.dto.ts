import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator'

export class PostReservationDetailDto {
  @IsString()
  @IsNotEmpty()
  item: string

  @IsString()
  @IsNotEmpty()
  sku: string

  @IsNumber()
  quan: number

  @IsString()
  @IsNotEmpty()
  unit: string

  @IsString()
  @IsNotEmpty()
  plant: string

  @IsString()
  @IsOptional()
  sloc?: string

  @IsString()
  @IsOptional()
  batch?: string

  @IsString()
  @IsNotEmpty()
  requirement_date: string

  @IsString()
  @IsOptional()
  item_text?: string
}

export class PostReservationDto {
  @IsString()
  @IsNotEmpty()
  company: string

  @IsString()
  @IsNotEmpty()
  ncsd_pms: string

  @IsString()
  @IsNotEmpty()
  base_date: string

  @IsString()
  @IsNotEmpty()
  mvt: string

  @IsString()
  @IsNotEmpty()
  plant: string

  @IsString()
  @IsNotEmpty()
  coa: string

  @IsString()
  @IsOptional()
  goods_recipient?: string

  @IsString()
  @IsOptional()
  cost_center?: string

  @IsString()
  @IsOptional()
  io?: string

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PostReservationDetailDto)
  detail: PostReservationDetailDto[]
}
