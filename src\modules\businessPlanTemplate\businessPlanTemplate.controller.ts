import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../common/guards'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { CurrentUser } from '../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import {
  BusinessPlanTemplateCreateDto,
  BusinessPlanTemplateUpdateDto,
  BusinessPlanTemplateCreateColDto,
  BusinessPlanTemplateColUpdateDto,
  BusinessPlanTemplateCreateColExcelDto,
} from './dto'
import { BusinessPlanTemplateService } from './businessPlanTemplate.service'
@ApiBearerAuth()
@ApiTags('BusinessPlanTemplate')
@UseGuards(JwtAuthGuard)
@Controller('business_plan_template')
export class BusinessPlanTemplateController {
  constructor(private readonly service: BusinessPlanTemplateService) {}

  @ApiOperation({ summary: 'Tạo mới một dữ liệu.' })
  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: BusinessPlanTemplateCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật một dữ liệu.' })
  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: BusinessPlanTemplateUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Sinh mã' })
  @Post('gen_code')
  async genCode() {
    return this.service.genCode()
  }

  @ApiOperation({ summary: 'Tạo mới một cột dữ liệu động.' })
  @Post('create_data_template_col')
  async createDataTemplateCol(@CurrentUser() user: UserDto, @Body() data: BusinessPlanTemplateCreateColDto) {
    return await this.service.createDataTemplateCol(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới một cột dữ liệu động.' })
  @Post('update_data_template_col')
  async updateDataTemplateCol(@CurrentUser() user: UserDto, @Body() data: BusinessPlanTemplateColUpdateDto) {
    return await this.service.updateDataTemplateCol(user, data)
  }

  @ApiOperation({ summary: 'Xóa 1 dòng dữ liệu.' })
  @Post('delete_data_template_col')
  async deleteDataTemplateCol(@Body() data: FilterOneDto) {
    return await this.service.deleteDataTemplateCol(data)
  }

  @ApiOperation({ summary: 'Xóa tất cả dữ liệu.' })
  @Post('delete_all_data_template_col')
  async deleteAllDataTemplateCol(@Body() data: FilterOneDto) {
    return await this.service.deleteAllDataTemplateCol(data)
  }

  @ApiOperation({ summary: 'Danh sách' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết' })
  @Post('find_detail')
  public async findDetail(@Body() data: FilterOneDto) {
    return await this.service.findDetail(data)
  }

  @ApiOperation({ summary: 'Chi tiết' })
  @Post('find_template_col')
  public async findTemplateCol(@Body() data: FilterOneDto) {
    return await this.service.findTemplateCol(data)
  }

  @ApiOperation({ summary: 'Tạo mới một cột dữ liệu động.' })
  @Post('create_data_template_col_list')
  async createDataTemplateColList(@CurrentUser() user: UserDto, @Body() data: BusinessPlanTemplateCreateColExcelDto) {
    return await this.service.createDataTemplateColList(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái ngưng hoạt động.' })
  @Post('update_un_active')
  async updateUnActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateUnActive(user, data.id)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động .' })
  @Post('update_active')
  async updateActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Lấy danh sách yêu cầu chỉnh sửa thông tin nhà cung cấp' })
  @UseGuards(JwtAuthGuard)
  @Post('find')
  public async find(@Body() data: { status?: string }) {
    return await this.service.find(data)
  }

  @ApiOperation({ summary: 'Chi tiết' })
  @Post('find_list_template_col')
  public async findListTemplateCol(@Body() data: FilterOneDto) {
    return await this.service.findListTemplateCol(data)
  }
}
