import { MigrationInterface, QueryRunner } from "typeorm";

export class ReservationNorm1749662431313 implements MigrationInterface {
    name = 'ReservationNorm1749662431313'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "plantId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "startDate" datetime`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "expireDate" datetime`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "totalEmployee" bigint`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD CONSTRAINT "FK_fbd6117ac7f9fbaf5350bc8fc96" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: Query<PERSON>unner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "FK_fbd6117ac7f9fbaf5350bc8fc96"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "totalEmployee"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "expireDate"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "startDate"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "plantId"`);
    }

}
