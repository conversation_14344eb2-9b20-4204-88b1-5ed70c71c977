import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnSupplierNumber1751509693027 implements MigrationInterface {
    name = 'AddColumnSupplierNumber1751509693027'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "setting_role" ADD "orgGroupCompanyId" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "setting_role" DROP COLUMN "orgGroupCompanyId"`);
    }

}
