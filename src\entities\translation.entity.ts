import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm'

@Entity()
export class TranslationEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  language: string

  /** <PERSON><PERSON>n bảng */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  keyTable: string

  /** Tên cột */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  keyCol: string

  /** Id của dòng dữ liệu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  idMap: string

  /** Giá trị */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  value: string
}
