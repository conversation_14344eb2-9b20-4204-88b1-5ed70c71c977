import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, Index, ManyToOne, JoinColumn } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
import { PlantEntity } from './plant.entity'
import { BusinessTemplatePlanExchangeEntity } from './businessTemplatePlanExchange.entity'
import { BusinessTemplatePlanTypeEntity } from './businessTemplatePlanType.entity'
import { IncotermEntity } from './incoterm.entity'
import { BusinessPlanEntity } from './businessPlan.entity'
import { ShipmentPlanEntity } from './shipmentPlan.entity'
import { BusinessTemplatePlanCostEntity } from './businessTemplatePlanCost.entity'
import { BusinessTemplateGroupPlanEntity } from './businessTemplateGroupPlan.entity'
import { BusinessPlanTemplateCostListEntity } from './businessTemplatePlanCostList.entity'

/** Thông tin loại hình doanh nghiệp*/
@Entity('business_template_plan')
export class BusinessTemplatePlanEntity extends BaseEntity {
  /* id của group */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessTemplateGroupPlanId: string
  @ManyToOne(() => BusinessTemplateGroupPlanEntity, (c) => c.businessTemplatePlan)
  @JoinColumn({ name: 'businessTemplateGroupPlanId', referencedColumnName: 'id' })
  businessTemplateGroupPlan: Promise<BusinessTemplateGroupPlanEntity>

  /** Mã */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  code: string

  /** name */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  name: string

  /**Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /* loại tham chiếu */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  referenceType: string

  /*  plantId*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (c) => c.businessTemplatePlan)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /*  Loại template*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessTemplatePlanTypeId: string
  @ManyToOne(() => BusinessTemplatePlanTypeEntity, (c) => c.businessTemplatePlans)
  @JoinColumn({ name: 'businessTemplatePlanTypeId', referencedColumnName: 'id' })
  businessTemplatePlanType: Promise<BusinessTemplatePlanTypeEntity>

  // số tháng vay(number )
  @Column({
    type: 'int',
    nullable: true,
  })
  loanTerm: number

  //currency
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  currency: string

  @OneToMany(() => BusinessTemplatePlanExchangeEntity, (p) => p.businessTemplatePlan)
  businessTemplatePlanExchange: Promise<BusinessTemplatePlanExchangeEntity[]>

  /* incoterm quy đổi */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermId: string
  @ManyToOne(() => IncotermEntity, (c) => c.businessTemplatePlans)
  @JoinColumn({ name: 'incotermId', referencedColumnName: 'id' })
  incoterm: Promise<IncotermEntity>

  /* id PAVC */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPlanId: string

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  shipmentFeeConditionTypeCompactId: string
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  shipmentFeeConditionTypeCompactCode: string
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  shipmentFeeConditionTypeCompactValue: string

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  currentcyId: string

  /* id phương án PAVC */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentNumberPlanId: string
  @ManyToOne(() => ShipmentPlanEntity, (c) => c.businessTemplatePlans)
  @JoinColumn({ name: 'shipmentPlanId', referencedColumnName: 'id' })
  shipmentPlan: Promise<ShipmentPlanEntity>

  /* id PAVC */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPlanIncotermId: string

  /* id PAVC */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPlanPriceId: string

  /* giá shipment */
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  shipmentPlanPrice: number

  /* json config bảng */
  /* json config bảng */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        return JSON.stringify(value)
      },
      from(value) {
        return value ? JSON.parse(value) : {}
      },
    },
  })
  configTable: any

  @OneToMany(() => BusinessTemplatePlanCostEntity, (p) => p.businessTemplatePlan)
  businessTemplatePlanCost: Promise<BusinessTemplatePlanCostEntity[]>

  @OneToMany(() => BusinessPlanTemplateCostListEntity, (p) => p.businessTemplatePlan)
  businessPlanTemplateCostLists: Promise<BusinessPlanTemplateCostListEntity[]>
}
