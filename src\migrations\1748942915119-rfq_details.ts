import { MigrationInterface, QueryRunner } from "typeorm";

export class RfqDetails1748942915119 implements MigrationInterface {
    name = 'RfqDetails1748942915119'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "rfq_details" ADD "isFinal" bit CONSTRAINT "DF_9a25a9054538d54acc9897c9627" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "rfq_details" DROP CONSTRAINT "DF_9a25a9054538d54acc9897c9627"`);
        await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "isFinal"`);
    }

}
