import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { MaterialEntity } from './material.entity'
import { PrItemEntity } from './prItem.entity'

@Entity('product_hierarchy')
export class ProductHierarchyEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 250, nullable: true })
  name: string

  @Column({ type: 'varchar', length: 250, nullable: false })
  code: string

  // Cấp bậc của dịch vụ (1-2-3-4)
  @Column({ nullable: false })
  level: number

  // Kiểm tra xem dịch vụ này có phải là dịch vụ cuối cùng không
  @Column({ nullable: false, default: false })
  isLast: boolean

  @Column({ type: 'varchar', nullable: true })
  parentId: string
  // 1 dịch vụ chỉ có 1 dịch vụ cha
  @ManyToOne(() => ProductHierarchyEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: Promise<ProductHierarchyEntity>

  // 1 dịch vụ có thể có nhiều dịch vụ con
  @OneToMany(() => ProductHierarchyEntity, (p) => p.parent)
  childs: Promise<ProductHierarchyEntity[]>

  @OneToMany(() => MaterialEntity, (p) => p.productHierarchy)
  material: Promise<MaterialEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.productHierarchy)
  prItems: Promise<PrItemEntity[]>
}
