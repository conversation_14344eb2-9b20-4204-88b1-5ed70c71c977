import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColoumnSupplier1749450310766 implements MigrationInterface {
    name = 'AddColoumnSupplier1749450310766'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier_company" ADD "type" nvarchar(30)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "isFirstRegister" bit CONSTRAINT "DF_4c314adf551dc527befa1aa3796" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP CONSTRAINT "DF_4c314adf551dc527befa1aa3796"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "isFirstRegister"`);
        await queryRunner.query(`ALTER TABLE "supplier_company" DROP COLUMN "type"`);
    }

}
