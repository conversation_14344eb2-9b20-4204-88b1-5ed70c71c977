import { ApiProperty } from '@nestjs/swagger'
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class ShipmentConfigTemplateCreateDto {
  @ApiProperty({ description: 'Tên config template' })
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty({ description: 'người tạo' })
  employeeId?: string

  @ApiProperty({ description: 'Mô tả', required: false })
  @IsString()
  @IsOptional()
  description?: string

  @ApiProperty({ description: 'Trạng thái', required: false })
  @IsString()
  @IsOptional()
  status?: string

  @ApiProperty({ description: 'Danh sách detail', required: false })
  lstShipmentConfigTemplateDetail?: ShipmentConfigTemplateDetailCreateDto[]
}

export class ShipmentConfigTemplateDetailCreateDto {
  @ApiProperty({ description: 'Id condition type', required: false })
  @IsString()
  @IsOptional()
  id: string

  @ApiProperty({ description: 'Mã condition type', required: false })
  @IsString()
  @IsOptional()
  code?: string

  @ApiProperty({ description: 'Tên condition type', required: false })
  @IsString()
  @IsOptional()
  name?: string

  @ApiProperty({ description: 'Mô tả', required: false })
  @IsString()
  @IsOptional()
  description?: string

  @ApiProperty({ description: 'Id idDetail', required: false })
  idDetail: string
}

export class ShipmentConfigTemplateUpdateDto {
  @ApiProperty({ description: 'Id condition type', required: false })
  id: string

  @ApiProperty({ description: 'Mã condition type', required: false })
  @IsString()
  @IsOptional()
  code?: string

  @ApiProperty({ description: 'Tên condition type', required: false })
  @IsString()
  @IsOptional()
  name?: string

  @ApiProperty({ description: 'người tạo' })
  employeeId?: string

  @ApiProperty({ description: 'Mô tả', required: false })
  @IsString()
  @IsOptional()
  description?: string

  @ApiProperty({ description: 'Danh sách detail', required: false })
  lstShipmentConfigTemplateDetail?: ShipmentConfigTemplateDetailCreateDto[]
}
