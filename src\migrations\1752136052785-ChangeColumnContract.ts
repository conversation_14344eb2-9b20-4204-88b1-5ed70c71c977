import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeColumnContract1752136052785 implements MigrationInterface {
  name = 'ChangeColumnContract1752136052785'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_98f3974fd55029e29719e687824"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractAmountAfterTax"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_d5fa3315444e5b64afecb3e3dbf"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractAmountBeforeTaxInVND"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_52d0b64f1307dd7fd0c32bc4c5f"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractAmountAfterTaxInVND"`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractValueAfterTax" bigint CONSTRAINT "DF_7bfefed0d5604051a5f97151698" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractValueBeforeTaxInVND" bigint CONSTRAINT "DF_bd5ea893d3d160e68942f122222" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractValueAfterTaxInVND" bigint CONSTRAINT "DF_24d5b0461c905b71195e43482dd" DEFAULT 0`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_24d5b0461c905b71195e43482dd"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractValueAfterTaxInVND"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_bd5ea893d3d160e68942f122222"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractValueBeforeTaxInVND"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_7bfefed0d5604051a5f97151698"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractValueAfterTax"`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractAmountAfterTaxInVND" bigint`)
    await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "DF_52d0b64f1307dd7fd0c32bc4c5f" DEFAULT 0 FOR "contractAmountAfterTaxInVND"`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractAmountBeforeTaxInVND" bigint`)
    await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "DF_d5fa3315444e5b64afecb3e3dbf" DEFAULT 0 FOR "contractAmountBeforeTaxInVND"`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractAmountAfterTax" bigint`)
    await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "DF_98f3974fd55029e29719e687824" DEFAULT 0 FOR "contractAmountAfterTax"`)
  }
}
