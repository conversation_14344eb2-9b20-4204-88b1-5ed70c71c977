import { MigrationInterface, QueryRunner } from 'typeorm'

export class ShipmentPlan1752898621517 implements MigrationInterface {
  name = 'ShipmentPlan1752898621517'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_plan" ADD "acceptedValueId" varchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_plan" DROP COLUMN "acceptedValueId"`)
  }
}
