import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { AuctionEntity } from './auction.entity'
import { BaseEntity } from './base.entity'

/** <PERSON><PERSON>ch sử <PERSON>u giá */
@Entity({ name: 'auction_history' })
export class AuctionHistoryEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: false })
  auctionId: string
  @ManyToOne(() => AuctionEntity, (p) => p.auctionHistorys)
  @JoinColumn({ name: 'auctionId', referencedColumnName: 'id' })
  auction: Promise<AuctionEntity>

  /** <PERSON><PERSON> chú thêm */
  @Column({ type: 'varchar', length: 250, nullable: true })
  description: string
}
