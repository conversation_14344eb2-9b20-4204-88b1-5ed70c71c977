import { Module } from '@nestjs/common'
import { ClientWebService } from './clientWeb.service'
import { ClientWebController } from './clientWeb.controller'
import { BidDetailModule } from '../bidDetail/bidDetail.module'
import { BidModule } from '../bid/bid.module'
import { SupplierModule } from '../supplier/supplier.module'
import { BidDealModule } from '../bidDeal/bidDeal.module'
import { BidAuctionModule } from '../bidAuction/bidAuction.module'
import { FaqCategoryModule } from '../faqCategory/faqCategory.module'
import { FaqModule } from '../faq/faq.module'
import { POModule } from '../po/po.module'
import { PaymentProgressModule } from '../paymentProgress/paymentProgress.module'
import { OfferModule } from '../offer/offer.module'
import { ContractModule } from '../contract/contract.module'
import { PrModule } from '../pr/pr.module'
import { ContractAppendixModule } from '../contractAppendix/contractAppendix.module'
import { InboundModule } from '../inbound/inbound.module'
import { OfferDealModule } from '../offerDeal/offerDeal.module'
import { ExternalMaterialGroupModule } from '../externalMaterialGroup/externalMaterialGroup.module'
import { AuctionModule } from '../auction/auction.module'
import { RecommendedPurchaseModule } from '../recommendedPurchase/recommendedPurchase.module'
import { CompanyModule } from '../company/company.module'
import { PaymentMethodModule } from '../paymentMethod/paymentMethod.module'
import { PaymentTermModule } from '../paymentTerm/paymentTerm.module'
import { CurrencyModule } from '../curency/currency.module'

@Module({
  imports: [
    BidModule,
    BidDetailModule,
    PaymentProgressModule,
    BidDealModule,
    OfferDealModule,
    BidAuctionModule,
    SupplierModule,
    FaqModule,
    FaqCategoryModule,
    POModule,
    OfferModule,
    ContractModule,
    PrModule,
    ContractAppendixModule,
    InboundModule,
    ExternalMaterialGroupModule,
    AuctionModule,
    SupplierModule,
    RecommendedPurchaseModule,
    CompanyModule,
    PaymentMethodModule,
    PaymentTermModule,
    CurrencyModule,
  ],
  controllers: [ClientWebController],
  providers: [ClientWebService],
})
export class ClientWebModule {}
