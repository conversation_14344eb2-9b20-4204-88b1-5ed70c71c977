import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

/** Điều kiện giá mua */
@Entity('condition_type')
export class ConditionTypeEntity extends BaseEntity {
  /** Mã điều kiện giá mua */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Tên điều kiện giá mua */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** Mô tả điều kiện giá mua */
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  description: string

  /**
   * type enum PercentageType
   * tỉ lệ %
   *  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  percentageType: string
  /**
   * type enum HeaderItemType
   * mức chung(header), mức từng mặt hàng(item)
   *  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  headerItemType: string
}
