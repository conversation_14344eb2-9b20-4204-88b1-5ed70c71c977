import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateScore1749122804854 implements MigrationInterface {
  name = 'UpdateScore1749122804854'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "comment" ALTER COLUMN "subId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "comment" ALTER COLUMN "entityName" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "comment" ALTER COLUMN "employeeId" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "comment" ALTER COLUMN "employeeId" varchar(255) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "comment" ALTER COLUMN "entityName" varchar(255) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "comment" ALTER COLUMN "subId" varchar(255) NOT NULL`)
  }
}
