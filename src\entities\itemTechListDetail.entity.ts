import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ItemTechEntity } from './itemTech.entity'

/** <PERSON><PERSON><PERSON> hình <PERSON> của yêu cầu kỹ thuật của vật tư trong PR */
@Entity('item_tech_list_detail')
export class ItemTechListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  itemTechId: string
  @ManyToOne(() => ItemTechEntity, (p) => p.itemTechListDetails)
  @JoinColumn({ name: 'itemTechId', referencedColumnName: 'id' })
  itemTech: Promise<ItemTechEntity>
}
