import { BaseEntity } from './base.entity'
import { Entity, Column, Join<PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { EmployeeEntity } from './employee.entity'

@Entity('permission_approve')
export class PermissionApproveEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  targetId: string

  @Column({
    length: 50,
    nullable: false,
  })
  entityName: string

  @Column({
    length: 50,
    nullable: false,
  })
  type: string

  /** Thứ hạng */
  @Column({ type: 'int', nullable: true })
  level?: number

  @Column({
    length: 50,
    nullable: false,
  })
  approveType: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  employeePositionId: string

  @Column({
    nullable: true,
    default: false,
  })
  mustApproveAll: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  departmentCode: string

  @Column({
    nullable: true,
    default: false,
  })
  approved: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isEmployee: boolean

  // Từ chối
  @Column({
    nullable: true,
    default: false,
  })
  reject: boolean

  /** <PERSON><PERSON> chú của luồng duyệt */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  comment: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  acceptedValueId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.permissionAapproves)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>
}
