import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyToOne, JoinC<PERSON>umn } from 'typeorm'
import { ServiceSceneEntity } from './serviceScene.entity'

@Entity('service_scene_list_detail')
export class ServiceSceneListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  serviceSceneId: string
  @ManyToOne(() => ServiceSceneEntity, (p) => p.serviceSceneListDetails)
  @JoinColumn({ name: 'serviceSceneId', referencedColumnName: 'id' })
  serviceScene: Promise<ServiceSceneEntity>
}
