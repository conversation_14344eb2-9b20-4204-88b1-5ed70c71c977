import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { enumData } from '../constants'
import { AuctionHistoryEntity } from './auctionHistory.entity'
import { AuctionSupplierEntity } from './auctionSupplier.entity'
import { AuctionSupplierPriceEntity } from './auctionSupPrice.entity'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { ContractEntity } from './contract.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { POEntity } from './po.entity'
import { PrEntity } from './pr.entity'

/** Đấu giá */
@Entity({ name: 'auction' })
export class AuctionEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  @Column({ type: 'varchar', length: 500, nullable: true })
  title: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  bidAutionType: string

  /** Giá khởi điểm */
  @Column({ type: 'bigint', nullable: true })
  price: number

  @Column({ type: 'bigint', nullable: true })
  submitPrice: number

  /** Thời điểm kết thúc */
  @Column({ nullable: true, type: 'datetime' })
  dateStartPlus: Date

  @Column({ type: 'bigint', nullable: true })
  timePlus: number

  @Column({ type: 'varchar', length: 250, nullable: true })
  timePlusType: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  currency: string

  @Column({ type: 'bigint', nullable: true })
  timeApprove: number

  @Column({ type: 'bigint', nullable: true })
  step: number

  @Column({ type: 'varchar', length: 250, nullable: true })
  fileUrl: string

  @Column({ type: 'varchar', length: 250, nullable: true })
  timeApproveType: string

  @Column({ type: 'varchar', nullable: true })
  prId?: string
  @ManyToOne(() => PrEntity, (p) => p.auctions)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr?: Promise<PrEntity>

  @Column({ type: 'varchar', nullable: true })
  externalMaterialGroupId?: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.auctions)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  exMatGroup?: Promise<ExternalMaterialGroupEntity>

  @Column({
    nullable: true,
    default: false,
  })
  isShowSup: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isShowDetailClient: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isShowDetailEmail: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isShowDetailSMS: boolean

  /** Giá thấp nhất */
  @Column({ type: 'bigint', nullable: true })
  minPrice?: number

  /** Thời điểm bắt đầu */
  @Column({ nullable: true, type: 'datetime' })
  dateStart: Date

  /** Thời điểm kết thúc */
  @Column({ nullable: true, type: 'datetime' })
  dateEnd: Date

  /** Trạng thái */
  @Column({ type: 'varchar', length: 10, nullable: true, default: enumData.AuctionStatus.NEW.code })
  status: string

  /** Ghi chú */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  description?: string

  /** Plant */
  @Column({ type: 'nvarchar', length: 250, nullable: true })
  plantId?: string

  /** purchasingOrgId */
  @Column({ type: 'nvarchar', length: 250, nullable: true })
  purchasingOrgId?: string

  /** purchasingGroupId */
  @Column({ type: 'nvarchar', length: 250, nullable: true })
  purchasingGroupId?: string

  /** Số lượng NCC tham gia */
  @Column({ nullable: true, default: 0 })
  numSupplier: number

  /** Gói thầu */
  @Column({ type: 'varchar', nullable: true })
  bidId?: string
  @ManyToOne(() => BidEntity, (p) => p.auctions)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid?: Promise<BidEntity>

  /** Các NCC tham gia */
  @OneToMany(() => AuctionSupplierEntity, (p) => p.auction)
  auctionSuppliers: Promise<AuctionSupplierEntity[]>

  /** Lịch sử */
  @OneToMany(() => AuctionHistoryEntity, (p) => p.auction)
  auctionHistorys: Promise<AuctionHistoryEntity[]>

  @OneToMany(() => AuctionSupplierPriceEntity, (p) => p.auction)
  auctionSupplierPrice: Promise<AuctionSupplierPriceEntity[]>

  /** Po */
  @OneToMany(() => POEntity, (p) => p.auction)
  pos: Promise<POEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.auction)
  contracts: Promise<ContractEntity[]>
}
