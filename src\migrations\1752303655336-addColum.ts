import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColum1752303655336 implements MigrationInterface {
  name = 'AddColum1752303655336'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "orderCode" varchar(500)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "orderName" varchar(500)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "assetCode" nvarchar(500)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "assetDesc" nvarchar(500)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "assetDesc"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "assetCode"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "orderName"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "orderCode"`)
  }
}
