import { MediaFileEntity, ShipmentCostEntity, ShipmentCostTypeEntity, ShipmentRouteEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany, Index } from 'typeorm'
import { ShipmentPoEntity } from './shipmentPo.entity'
import { ShipmentInboundEntity } from './shipmentInbound.entity'
import { ShipmentItemEntity } from './shipmentItem.entity'
import { ShipmentContainerEntity } from './shipmentContainer.entity'
import { ShipmentStageEntity } from './shipmentStage.entity'

/** Thông tin shipment*/
@Entity('shipment')
export class ShipmentEntity extends BaseEntity {
  /**Trạng thái duyệt */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Số shipment */
  @Index({ unique: true })
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Số shipment number */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  shipmentNumber: string

  /** Shipment type */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  shipmentType: string

  /** Route Id*/
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  routeId: string

  /** Shipping type */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  shippingType: string

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  phone: string

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  pwdAgent: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  carRegNoInternal: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  carRegNoExternal: string

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  driverPhoneExternal: string

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  driverPhoneInternal: string

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  driverNameExternal: string

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  driverNameInternal: string

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  identityDriver: string

  @Column({
    type: 'varchar',
    length: 2000,
    nullable: true,
  })
  fileUrl: string

  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  fileName: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  /**Shipment Route */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentRouteId: string
  @ManyToOne(() => ShipmentRouteEntity, (p) => p.shipments)
  @JoinColumn({ name: 'shipmentRouteId', referencedColumnName: 'id' })
  shipmentRoute: Promise<ShipmentRouteEntity>

  /**Shipment cost type*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentCostTypeId: string
  @ManyToOne(() => ShipmentCostTypeEntity, (p) => p.shipments)
  @JoinColumn({ name: 'shipmentCostTypeId', referencedColumnName: 'id' })
  shipmentCostType: Promise<ShipmentCostTypeEntity>

  @OneToMany(() => ShipmentCostEntity, (p) => p.shipment)
  shipmentCosts: Promise<ShipmentCostEntity[]>

  @OneToMany(() => ShipmentPoEntity, (p) => p.shipment)
  shipmentPos: Promise<ShipmentPoEntity[]>

  @OneToMany(() => ShipmentInboundEntity, (p) => p.shipment)
  shipmentInbounds: Promise<ShipmentInboundEntity[]>

  @OneToMany(() => ShipmentItemEntity, (p) => p.shipment)
  shipmentItems: Promise<ShipmentItemEntity[]>

  @OneToMany(() => ShipmentContainerEntity, (p) => p.shipment)
  shipmentContainers: Promise<ShipmentContainerEntity[]>

  @OneToMany(() => MediaFileEntity, (p) => p.shipment)
  mediaFiles: Promise<MediaFileEntity[]>

  @OneToMany(() => ShipmentStageEntity, (p) => p.shipment)
  stages: Promise<ShipmentStageEntity[]>
}
