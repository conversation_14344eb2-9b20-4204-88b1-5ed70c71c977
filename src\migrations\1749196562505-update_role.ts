import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateRole1749196562505 implements MigrationInterface {
  name = 'UpdateRole1749196562505'
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "viewPermission" nvarchar(max)`)
  }
  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "viewPermission"`)
  }
}
