import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { TemplateSiteAssessmentItemEntity } from './templateSiteAssessmentItem.entity'

/**Template đ<PERSON>h giá hiện trường */
@Entity('template_site_assessment')
export class TemplateSiteAssessmentEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  status: string

  // các nội dung đánh giá hiện trường
  @OneToMany(() => TemplateSiteAssessmentItemEntity, (p) => p.templateSiteAssessmentId)
  templateSiteAssessments: Promise<TemplateSiteAssessmentItemEntity[]>
}
