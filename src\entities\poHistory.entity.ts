import { Entity, Column, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { POEntity } from './po.entity'
import { SupplierEntity } from './supplier.entity'

/** <PERSON><PERSON><PERSON> sử PO */
@Entity('po_history')
export class POHistoryEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /** Trạng thái hiện tại */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  statusCurrent: string

  /** Trạng thái chuyển đổi */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  statusConvert: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.poHistorys)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.poHistorys)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.poHistorys)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  description: string
}
