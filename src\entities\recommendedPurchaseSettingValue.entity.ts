import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { SettingStringEntity } from './settingString.entity'
import { RecommendedPurchaseTemplateColEntity } from './recommendedPurchaseTemplateCol.entity'

@Entity('recommended_purchase_setting_value')
export class RecommendedPurchaseSettingValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  settingStringId: string
  @ManyToOne(() => SettingStringEntity, (p) => p.recommendedPurchaseSettingValue)
  @JoinColumn({ name: 'settingStringId', referencedColumnName: 'id' })
  settingString: Promise<SettingStringEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseId: string
  @ManyToOne(() => RecommendedPurchaseEntity, (p) => p.recommendedPurchaseSettingValue)
  @JoinColumn({ name: 'recommendedPurchaseId', referencedColumnName: 'id' })
  recommendedPurchase: Promise<RecommendedPurchaseEntity>

  /** value */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  value: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  settingCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseTemplateColId: string
  @ManyToOne(() => RecommendedPurchaseTemplateColEntity, (p) => p.recommendedPurchaseSettingValue)
  @JoinColumn({ name: 'recommendedPurchaseTemplateColId', referencedColumnName: 'id' })
  recommendedPurchaseTemplateCol: Promise<RecommendedPurchaseTemplateColEntity>
}
