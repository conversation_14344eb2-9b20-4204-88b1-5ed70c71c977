import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BusinessPlanEntity, BusinessPlanTemplateEntity } from '.'

@Entity('business_plan_history')
export class BusinessPlanHistoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  createdByName: string

  /** Trạng thái hiện tại */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanTemplateId: string
  @ManyToOne(() => BusinessPlanTemplateEntity, (p) => p.histories)
  @JoinColumn({ name: 'businessPlanTemplateId', referencedColumnName: 'id' })
  businessPlanTemplate: Promise<BusinessPlanTemplateEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanId: string
  @ManyToOne(() => BusinessPlanEntity, (p) => p.histories)
  @JoinColumn({ name: 'businessPlanId', referencedColumnName: 'id' })
  businessPlan: Promise<BusinessPlanEntity>
}
