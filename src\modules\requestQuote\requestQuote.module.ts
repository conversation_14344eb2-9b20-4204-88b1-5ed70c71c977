import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import {
  ActionLogRepository,
  CompanyRepository,
  IncotermRepository,
  MaterialGroupRepository,
  PaymentTermConversionRepository,
  PaymentTermRepository,
  PlantRepository,
  PrRepository,
  RequestQuoteRepository,
  RequestQuoteSupplierRepository,
} from '../../repositories'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'
import { RequestQuoteController } from './requestQuote.controller'
import { RequestQuoteService } from './requestQuote.service'
import { FlowApproveModule } from '../flowApprove/flowApprove.module'
import { EmailModule } from '../email/email.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      RequestQuoteRepository,
      PrRepository,
      CompanyRepository,
      PlantRepository,
      ActionLogRepository,
      RequestQuoteSupplierRepository,
      IncotermRepository,
      PaymentTermRepository,
      MaterialGroupRepository,
      PaymentTermConversionRepository,
    ]),
    OrganizationalPositionModule,
    FlowApproveModule,
    EmailModule,
  ],
  controllers: [RequestQuoteController],
  providers: [RequestQuoteService],
  exports: [RequestQuoteService],
})
export class RequestQuoteModule {}
