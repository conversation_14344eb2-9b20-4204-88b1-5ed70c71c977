import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinC<PERSON>umn } from 'typeorm'
import { ServicePriceEntity } from './servicePrice.entity'

@Entity('service_price_list_detail')
export class ServicePriceListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  servicePriceId: string
  @ManyToOne(() => ServicePriceEntity, (p) => p.servicePriceListDetails)
  @JoinColumn({ name: 'servicePriceId', referencedColumnName: 'id' })
  servicePrice: Promise<ServicePriceEntity>
}
