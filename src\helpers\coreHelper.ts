import { enumData, enumLanguage } from '../constants'
import { create, all } from 'mathjs'
const config = {}
const math = create(all, config) as any
import * as moment from 'moment'
import { customAlphabet } from 'nanoid'
import { TranslationEntity } from '../entities/translation.entity'
import { Raw } from 'typeorm'

class CoreHelper {
  // public newDateTZ() {
  //   var targetTime = new Date()
  //   var timeZoneFromDB = -7.0 //time zone value from database
  //   //get the timezone offset from local time in minutes
  //   var tzDifference = timeZoneFromDB * 60 + targetTime.getTimezoneOffset()
  //   //convert the offset to milliseconds, add to targetTime, and make a new Date
  //   var offsetTime = new Date(targetTime.getTime() + tzDifference * 60 * 1000)
  //   return offsetTime
  // }

  public newDateTZ() {
    const d = new Date()
    const offset = 7
    // convert to msec
    // add local time zone offset
    // get UTC time in msec
    const utc = d.getTime() + d.getTimezoneOffset() * 60000

    // create new Date object for different city
    // using supplied offset
    const nd = new Date(utc + 3600000 * offset)
    return nd
  }

  public dateToString(d: Date) {
    const offset = 7
    // convert to msec
    // add local time zone offset
    // get UTC time in msec
    const utc = d.getTime() + d.getTimezoneOffset() * 60000

    // create new Date object for different city
    // using supplied offset
    const nd = new Date(utc + 3600000 * offset)

    return moment(nd).format('DD/MM/YYYY HH:mm')
  }

  public stringInject(str: String, data: any) {
    if (typeof str === 'string' && data instanceof Array) {
      return str.replace(/({\d})/g, function (i: any) {
        return data[i.replace(/{/, '').replace(/}/, '')]
      })
    } else if (typeof str === 'string' && data instanceof Object) {
      if (Object.keys(data).length === 0) {
        return str
      }

      for (var key in data) {
        return str.replace(/({([^}]+)})/g, function (i) {
          var key = i.replace(/{/, '').replace(/}/, '')
          if (!data[key]) {
            return i
          }

          return data[key]
        })
      }
    } else if ((typeof str === 'string' && data instanceof Array === false) || (typeof str === 'string' && data instanceof Object === false)) {
      return str
    } else {
      return false
    }
  }

  //#region tính điểm giá
  public async callScore(list: any, scoreDLC: number) {
    // Tính điểm
    let scorePrice = 0
    let minValue = 100000000
    let lstValue = []
    //Tìm minValue và lưu điểm tạm
    for (const item of list) {
      const bidPrice = await item.bidPrice
      if (bidPrice && bidPrice.parentId === null && bidPrice.type === enumData.DataType.Number.code && item.value && `${item.value}`.trim() != '') {
        const temp = await this.calScorePriceItem(bidPrice.percent, `${item.value}`)
        if (temp < minValue) {
          minValue = temp
        }
        lstValue.push(temp)
        item.score = temp
      }
    }

    const dlc = this.calDLC(lstValue)
    for (const item of list) {
      const bidPrice = await item.bidPrice
      if (bidPrice && bidPrice.parentId === null && bidPrice.type === enumData.DataType.Number.code) {
        if (dlc > 0) {
          item.score = scoreDLC - (item.score - minValue) / dlc
          scorePrice += item.score
        } else {
          item.score = scoreDLC
          scorePrice += item.score
        }
      }
    }

    if (isNaN(scorePrice)) {
      return { scorePrice: 0, list }
    } else if (!isFinite(scorePrice)) {
      return { scorePrice: 0, list }
    } else return { scorePrice, list }
  }

  /** Hàm tính độ lệch chuẩn */
  public calDLC(lstValue: number[]) {
    const n = lstValue.length
    if (n < 2) return 0
    let sum = 0
    for (const i of lstValue) {
      sum += i
    }
    const avg = sum / n
    let sum2 = 0
    for (const i of lstValue) {
      sum2 += Math.pow(i - avg, 2)
    }
    const variance = sum2 / (n - 1)
    const std = Math.sqrt(variance)
    return std
  }

  /** Tính điểm đánh giá giá */
  async calScorePriceItem(percent: number, value: string) {
    let score = 0
    if (value && value.trim() != '') score = (+value * percent) / 100

    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  //#endregion

  async checkFomular(fomular: string, lstField: any[]) {
    let flag = true
    const lstCol = lstField.filter((c) => c.type === enumData.DataType.Number.code)
    let tempFomular = fomular
    for (const col of lstCol) {
      tempFomular = tempFomular.replace(`[${col.code}]`, '1.01')
    }
    // [qty] là trường "Số lượng" tĩnh của hạng mục
    tempFomular = tempFomular.replace(`[qty]`, '1.01')

    try {
      const ex = math.evaluate(tempFomular)
      if (typeof ex !== 'number') flag = false
    } catch {
      flag = false
      return flag
    } finally {
      return flag
    }
  }

  async calFomular(fomular: string, lstField: any[], item: any) {
    let value = null
    const lstCol = lstField.filter((c) => c.type === enumData.DataType.Number.code)
    let tempFomular = fomular
    for (const col of lstCol) {
      tempFomular = tempFomular.replace(`[${col.code}]`, item[col.id])
    }
    // [qty] là trường "Số lượng" tĩnh của hạng mục
    tempFomular = tempFomular.replace(`[qty]`, item.number)

    try {
      value = math.evaluate(tempFomular)
      if (typeof value !== 'number') value = null
    } catch {
      value = null
      return value
    } finally {
      return value
    }
  }

  convertObjToArray(obj: any) {
    const arr = []
    for (const key in obj) arr.push({ ...obj[key] })
    return arr
  }

  public convertObjToArrayLanguage(obj: any) {
    const arr = []
    // tslint:disable-next-line:forin
    for (const key in obj) {
      const value = obj[key]
      arr.push(value)
    }
    return arr
  }

  // Chuyển number thành định dạng tiền tệ
  formatMoney(n: number) {
    return n.toFixed(0).replace(/./g, function (c, i, a) {
      return i > 0 && c !== '.' && (a.length - i) % 3 === 0 ? ',' + c : c
    })
  }

  rankABCD(score: number) {
    if (score > 90) return 'A'
    if (score > 70) return 'B'
    if (score > 50) return 'C'
    return 'D'
  }

  /** Gen mã mặc định */
  codeDefaultItem() {
    const nanoid = customAlphabet('1234567890QWERTYUIOPASDFGHJKLZXCVBNM', 6)
    let sortString = nanoid()
    const code = moment(new Date()).format('YYYYMMDD')
    return code + sortString
  }

  /** Hàm tìm key trùng value */
  findDuplicates(arr: any[], key: string): string[] {
    const seen: { [key: string]: boolean } = {}
    const duplicates: string[] = []
    var array = []
    array = arr
    for (const prop of array) {
      if (seen[prop[key]]) {
        if (!duplicates.includes(prop[key])) {
          duplicates.push(prop[key])
        }
      } else {
        seen[prop[key]] = true
      }
    }
    return duplicates
  }

  /** Lấy distinct theo 1 trường */
  selectDistinct(arr: any[], field: string): string[] {
    return [...new Set(arr.map((c) => c[field]))]
  }
  getParameterCount<T>(item: T): number {
    return Object.keys(item).length
  }

  splitArrayByParameters<T>(array: T[], maxParameters: number): T[][] {
    const chunks: T[][] = []
    let currentChunk: T[] = []
    let currentParameters = 0

    for (const item of array) {
      const itemParameterCount = this.getParameterCount(item)

      if (currentParameters + itemParameterCount > maxParameters) {
        if (currentChunk.length > 0) {
          chunks.push(currentChunk)
        }
        currentChunk = []
        currentParameters = 0
      }

      currentChunk.push(item) // Thêm item vào chunk (chunk mới hoặc chunk cũ)
      currentParameters += itemParameterCount
    }

    if (currentChunk.length > 0) {
      chunks.push(currentChunk)
    }

    return chunks
  }

  /** Lấy ngày cuối cùng của tháng */
  getLastDayOfMonth(date: Date) {
    date = new Date(date)
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const lastDay = new Date(year, month, 0, 23, 59, 59, 99)
    return lastDay
  }

  /** Lấy ngày đầu của tháng */
  getFirstDayOfMonth(date: Date) {
    date = new Date(date)
    const firstDate = new Date(moment(date).format('YYYY-MM-01 00:00:00'))
    return firstDate
  }

  calFomularTemplate(fomular: string, lstField: any[], item: any) {
    let value = null

    // Lọc các cột kiểu Number
    const lstCol = lstField.filter((c) => c.type === enumData.DataType.Number.code)

    // Tạo bản sao công thức để xử lý thay thế
    let tempFomular = fomular

    for (const col of lstCol) {
      const valueToReplace = item[col.dataMapping] || item[col.code]
      if (typeof valueToReplace === 'number') {
        // Nếu giá trị là số, thay thế trực tiếp
        tempFomular = tempFomular.replace(`[${col.code}]`, valueToReplace.toString())
      } else {
        // Nếu giá trị không phải số, thay thế bằng "0" hoặc một giá trị mặc định
        tempFomular = tempFomular.replace(`[${col.code}]`, '0')
      }
    }
    // Tính toán công thức

    try {
      // Đảm bảo rằng công thức là số trước khi tính toán
      value = math.evaluate(tempFomular) // Dùng math.js để tính toán
      if (typeof value !== 'number') value = null // Kiểm tra lại nếu kết quả không phải là số
    } catch {
      value = null
    } finally {
      return value
    }
  }

  async checkENValue(obj: any[], entityName: string) {
    let result: any[] = []
    for (let item of obj) {
      for (let key of Object.keys(item)) {
        if (key.includes(enumLanguage.LanguageType.EN.code)) {
          const newTrans = new TranslationEntity()
          newTrans.idMap = item.id.toUpperCase()
          newTrans.keyCol = key.replace(enumLanguage.LanguageType.EN.code, '')
          newTrans.keyTable = entityName
          newTrans.language = enumLanguage.LanguageType.EN.code
          newTrans.value = item[key]
          if (typeof newTrans.value !== 'undefined') {
            result.push(newTrans)
          }
        }
      }
    }
    return result
  }

  calFomularTemplatePricePO(fomular: string, lstField: any[], item: any) {
    let value = null

    // Lọc các cột kiểu Number
    const lstCol = lstField

    // Tạo bản sao công thức để xử lý thay thế
    let tempFomular = fomular

    for (const col of lstCol) {
      const valueToReplace = item[col.amountCode] || item[col.conditionCode] || item[col.per] || item[col.conditionUnit]
      if (typeof valueToReplace === 'number') {
        // Nếu giá trị là số, thay thế trực tiếp
        tempFomular = tempFomular.replace(`[${col.code}]`, valueToReplace.toString())
      } else {
        // Nếu giá trị không phải số, thay thế bằng "0" hoặc một giá trị mặc định
        tempFomular = tempFomular.replace(`[${col.code}]`, '0')
      }
    }
    // Tính toán công thức

    try {
      // Đảm bảo rằng công thức là số trước khi tính toán
      value = math.evaluate(tempFomular) // Dùng math.js để tính toán
      if (typeof value !== 'number') value = null // Kiểm tra lại nếu kết quả không phải là số
    } catch {
      value = null
    } finally {
      return value
    }
  }

  getFilterBetweenDateArrange(whereCon: any, field: string, dates?: Date[]) {
    if (dates && dates.length > 0) {
      if (!whereCon) whereCon = {}
      const ds = new Date(new Date(dates[0]).setHours(0, 0, 0, 0))
      const dsStr = moment(ds).format('YYYY-MM-DD HH:mm:ss')
      const de = new Date(new Date(dates[1]).setHours(23, 59, 59, 99))
      const deStr = moment(de).format('YYYY-MM-DD HH:mm:ss')
      whereCon[field] = Raw((alias) => `${alias} BETWEEN '${dsStr}'  AND '${deStr}'`)
    }
  }

  convertDateNumber(rawDate: any) {
    const year = Math.floor(rawDate / 10000)
    const month = Math.floor((rawDate % 10000) / 100) - 1 // Tháng trong JS tính từ 0
    const day = rawDate % 100

    const jsDate = new Date(year, month, day)

    // Kiểm tra
    return jsDate.toISOString()
  }

  /**Kiểm tra tính hợp lệ của công thức*/
  async checkFomularHeaderTemplate(fomular: string, lstField: any[]) {
    let flag = true
    let tempFomular = fomular
    for (const col of lstField) {
      tempFomular = tempFomular.replace(`[${col.code}]`, '1.01')
    }
    // [qty] là trường "Số lượng" tĩnh của hạng mục
    tempFomular = tempFomular.replace(`[qty]`, '1.01')

    try {
      const ex = math.evaluate(tempFomular)
      if (typeof ex !== 'number') flag = false
    } catch {
      flag = false
      return flag
    } finally {
      return flag
    }
  }

  /** Hàm tính toán số ngày dự trên công thức nhập ở trang admin */
  async calFomularHeaderTemplate(fomular: string, lstField: any[], currentItem: any): Promise<number | null> {
    let tempFomular = fomular

    for (const field of lstField) {
      // Đang chỉnh sửa thì dùng giá trị hiện tại
      let value: number = 0

      if (field.id === currentItem.id) {
        value = currentItem.numberOfDay ?? 0
      } else {
        value = field.numberOfDay ?? 0
      }

      // thay cột `[code]` bằng số ngày tương ứng với cột đó
      tempFomular = tempFomular.replace(`[${field.code}]`, value.toString())
    }

    // Thay thế [qty] nếu có
    tempFomular = tempFomular.replace(`[qty]`, currentItem.numberOfDay?.toString() ?? '0')

    try {
      const result = math.evaluate(tempFomular)
      return typeof result === 'number' ? result : null
    } catch {
      return null
    }
  }

  async calFomularHeaderTemplateSupplier(fomular: string, lstField: any[], currentItem: any): Promise<number | null> {
    let tempFomular = fomular

    for (const field of lstField) {
      // Đang chỉnh sửa thì dùng giá trị hiện tại
      let value: number = 0

      if (field.id === currentItem.id) {
        value = currentItem.numberOfDaySupplier ?? 0
      } else {
        value = field.numberOfDaySupplier ?? 0
      }

      // thay cột `[code]` bằng số ngày tương ứng với cột đó
      tempFomular = tempFomular.replace(`[${field.code}]`, value.toString())
    }

    // Thay thế [qty] nếu có
    tempFomular = tempFomular.replace(`[qty]`, currentItem.numberOfDaySupplier?.toString() ?? '0')

    try {
      const result = math.evaluate(tempFomular)
      return typeof result === 'number' ? result : null
    } catch {
      return null
    }
  }
  arraysEqual(a: string[], b: string[]): boolean {
    if (a.length !== b.length) return false
    const setA = new Set(a)
    const setB = new Set(b)
    if (setA.size !== setB.size) return false
    for (const val of setA) {
      if (!setB.has(val)) return false
    }
    return true
  }
}

export const coreHelper = new CoreHelper()
