import { ApiProperty, PartialType } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString, IsUrl, IsUUID } from 'class-validator'
import { GuideCreateDto } from './guideCreate.dto'

export class GuideFindByTypeDto {
  @ApiProperty({ description: 'Loại HDSD' })
  @IsNotEmpty()
  @IsString()
  guideType: string
}

export class GuideUpdateDto extends PartialType(GuideCreateDto) {
  @ApiProperty({ description: 'ID Ngân hàng' })
  @IsNotEmpty()
  @IsUUID()
  id: string
}
