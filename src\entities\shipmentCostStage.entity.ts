import { ShipmentCostEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { ShipmentCostStageCostEntity } from './shipmentCostStageCost.entity'

/** Thông tin shipment cost stage*/
@Entity('shipment_cost_stage')
export class ShipmentCostStageEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  jsonState: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentCostId: string
  @ManyToOne(() => ShipmentCostEntity, (p) => p.shipmentCostStages)
  @JoinColumn({ name: 'shipmentCostId', referencedColumnName: 'id' })
  shipmentCost: Promise<ShipmentCostEntity>

  /**Nguồn tham chiếu */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  referenceSource: string

  @OneToMany(() => ShipmentCostStageCostEntity, (p) => p.shipmentCostStage)
  shipmentCostStageCosts: Promise<ShipmentCostStageCostEntity[]>

  // /**Sử dụng giá từ nguồn tham chiếu lấy từ đấu giá shipment */
  // @Column({
  //   nullable: false,
  //   default: false,
  // })
  // isUsePrice: boolean
}
