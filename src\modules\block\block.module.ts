import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BlockRepository, DepartmentRepository } from '../../repositories'
import { BlockController } from './block.controller'
import { BlockService } from './block.service'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BlockRepository, DepartmentRepository]), OrganizationalPositionModule],
  controllers: [BlockController],
  providers: [BlockService],
})
export class BlockModule {}
