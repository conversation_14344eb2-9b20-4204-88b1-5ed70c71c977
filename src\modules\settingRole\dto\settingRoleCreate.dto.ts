import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON>Optional, IsString } from 'class-validator'

export class SettingRoleCreateDto {
  @IsNotEmpty()
  @IsString()
  code: string

  @IsNotEmpty()
  @IsString()
  orgCompanyId: string

  @IsOptional()
  orgBlockId: string

  @IsOptional()
  orgDepartmentId: string

  @IsOptional()
  orgPartId: string

  @IsOptional()
  orgPositionId: string

  employeeId: string

  id?: string

  jsonSetting: string

  @IsOptional()
  orgGroupCompanyId: string
}
