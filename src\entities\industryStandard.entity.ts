import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierNumberRequestApproveEntity } from '.'

/** Industry standard
 */
@Entity('industry_standard')
export class IndustryStandardEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.industryStandard)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>
}
