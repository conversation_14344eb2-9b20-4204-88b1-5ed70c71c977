import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { ServiceCapacityEntity } from './serviceCapacity.entity'

@Entity('service_capacity_list_detail')
export class ServiceCapacityListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  code: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  serviceCapacityId: string
  @ManyToOne(() => ServiceCapacityEntity, (p) => p.serviceCapacityListDetails)
  @JoinColumn({ name: 'serviceCapacityId', referencedColumnName: 'id' })
  serviceCapacity: Promise<ServiceCapacityEntity>
}
