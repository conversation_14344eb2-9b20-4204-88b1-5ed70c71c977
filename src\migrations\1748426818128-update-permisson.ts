import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdatePermisson1748426818128 implements MigrationInterface {
    name = 'UpdatePermisson1748426818128'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "permission_view" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_728f4098144f4d65a1586767a88" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b38850d711ba36d72032e05154b" DEFAULT 0, "companyId" varchar(255), "name" varchar(250), "code" varchar(50) NOT NULL, "path" varchar(50) NOT NULL, "view" bit NOT NULL CONSTRAINT "DF_36d4593e839b97f3126baf29cb3" DEFAULT 0, "delete" bit NOT NULL CONSTRAINT "DF_7c3a5e37b06e1bb9e42a025699a" DEFAULT 0, "edit" bit NOT NULL CONSTRAINT "DF_029108140a528722d3f3cff2c14" DEFAULT 0, "add" bit NOT NULL CONSTRAINT "DF_47ef6ef853a572f8a668c1fa667" DEFAULT 0, "watchAnother" bit NOT NULL CONSTRAINT "DF_a9990dc3783c4d5ae2d29e6e548" DEFAULT 0, "editAnother" bit NOT NULL CONSTRAINT "DF_68cadd4e64af3c08356642e152f" DEFAULT 0, CONSTRAINT "PK_728f4098144f4d65a1586767a88" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "permission_to_group" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f70ae1ea7974daf5d6546dc7c31" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3167d3a4d1b1ddbabd91d486e68" DEFAULT 0, "companyId" varchar(255), "permissionViewId" uniqueidentifier, "groupPermissionId" uniqueidentifier, CONSTRAINT "PK_f70ae1ea7974daf5d6546dc7c31" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "permission_to_group" ADD CONSTRAINT "FK_d339d5987d20a7ab389a377cffa" FOREIGN KEY ("permissionViewId") REFERENCES "permission_view"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "permission_to_group" ADD CONSTRAINT "FK_01e85204922b2099be2b6b85048" FOREIGN KEY ("groupPermissionId") REFERENCES "group_permission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "permission_to_group" DROP CONSTRAINT "FK_01e85204922b2099be2b6b85048"`);
        await queryRunner.query(`ALTER TABLE "permission_to_group" DROP CONSTRAINT "FK_d339d5987d20a7ab389a377cffa"`);
        await queryRunner.query(`DROP TABLE "permission_to_group"`);
        await queryRunner.query(`DROP TABLE "permission_view"`);
    }

}
