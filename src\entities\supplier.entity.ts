import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BidAuctionSupplierEntity } from './bidAuctionSupplier.entity'
import { BidDealSupplierEntity } from './bidDealSupplier.entity'
import { BidSupplierEntity } from './bidSupplier.entity'
import { BusinessPartnerGroupEntity } from './businessPartnerGroup.entity'
import { ContractEntity } from './contract.entity'
import { CountryEntity } from './country.entity'
import { EvaluationHistoryPurchaseEntity } from './evaluationHistoryPurchase.entity'
import { FactorySupplierEntity } from './factorySupplier.entity'
import { OfferSupplierEntity } from './offerSupplier.entity'
import { POEntity } from './po.entity'
import { POHistoryEntity } from './poHistory.entity'
import { RequestUpdateSupplierEntity } from './requestUpdateSupplier.entity'
import { SiteAssessmentEntity } from './siteAssessment.entity'
import { SupplierBankEntity } from './supplierBank.entity'
import { SupplierCapacityEntity } from './supplierCapacity.entity'
import { SupplierExpertiseEntity } from './supplierExpertise.entity'
import { SupplierHistoryEntity } from './supplierHistory.entity'
import { SupplierNotifyEntity } from './supplierNotify.entity'
import { SupplierNumberEntity } from './supplierNumber.entity'
import { SupplierServiceEntity } from './supplierService.entity'
import { UserEntity } from './user.entity'
import { RegionEntity } from './region.entity'
import { ContractInspectionEntity } from './contractInspection.entity'
import { BillEntity } from './bill.entity'
import { OfferDealSupplierEntity } from './offerDealSupplier.entity'
import { ComplaintEntity } from './complaint.entity'
import { ComplaintChatEntity } from './complaintChat.entity'
import { PaymentEntity } from './payment.entity'
import { SupplierSchemaEntity } from './supplierSchema.entity'
import { PoHistoryStatusExecutionEntity } from './poHistoryStatusExecution.entity'
import { BidEmployeeRateEntity } from './bidEmployeeRate.entity'
import { ComplaintNotifyEntity } from './complaintNotify.entity'
import { ContractDocumentHandoverEntity } from './contractDocumentHandover.entity'
import { SupplierListPricePOEntity } from './supplierListPricePo.entity'
import { SupplierPlantEntity } from './supplierPlant.entity'
import { BusinessTypeEntity } from './businessType.entity'
import { Transform } from 'class-transformer'
import { SupplierPotentialUpgradeEntity } from './supplierPotentialUpgrade.entity'
import { RecommendedPurchaseShipmentCostPriceEntity } from './recommendedPurchaseShipmentCostPrice.entity'
import { RecommendedPurchaseShipmenStageEntity } from './recommendedPurchaseShipmentStage.entity'
import { ShipmentCostDetailEntity } from './shipmentCostDetail.entity'
import { RfqEntity } from './rfq.entity'
import { CompanyEntity } from './company.entity'
import { SupplierRevenueEntity } from './supplierRevenue.entity'
import { SupplierCompanyEntity } from './supplierCompany.entity'
import { RequestQuoteSupplierEntity } from './requestQuoteSupplier.entity'

/** Thông tin NCC */
@Entity('supplier')
export class SupplierEntity extends BaseEntity {
  /**Trạng thái NCC */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /**Loại nhà cung cấp (Chính thức/tiềm năng) */
  @Column({
    type: 'varchar',
    length: 30,
    nullable: true,
  })
  type: string

  /**Sơ lược về quá trình hình thành & phát triển của Nhà Cung Cấp */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /** Mã số doanh nghiệp */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Mã code SAP doanh nghiệp */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  sapCode: string

  /** Mã filter code SAP doanh nghiệp */
  @Column({
    type: 'int',
    nullable: true,
  })
  filterSapCode: number

  /**  Tên doanh nghiệp */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: false,
  })
  name: string

  /**  Tên giao dịch */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  dealName: string

  /** Tên viết tắt */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  abbreviation: string

  /** Năm thành lập công ty */
  @Column({ type: 'datetime', nullable: true })
  @Transform(({ value }) => (value ? new Date(value) : null))
  createYear: Date

  /** doanh nghiệp Việt Nam */
  @Column({
    nullable: true,
    default: true,
  })
  isVietNam: boolean

  /** Địa chỉ trụ sở */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: false,
  })
  address: string

  /** Địa chỉ giao dịch 1 */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  dealAddress: string

  /** Địa chỉ giao dịch 2 */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  dealAddress2: string

  /** Địa chỉ giao dịch 3 */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  dealAddress3: string

  /** Địa chỉ giao dịch 4 */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  dealAddress4: string

  /** Địa chỉ giao dịch 5 */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  dealAddress5: string

  // Người đại diện pháp luật
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  represen: string

  /**Chức vụ người đại diện pháp luật */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  represenPosition: string

  /**Số điện thoại người đại diện pháp luật */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  represenPhone: string

  /**Số Fax người đại diện pháp luật  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  represenFax: string

  /** Email người đại diện pháp luật  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  represenEmail: string

  /** Lưu ý về người đại diện pháp luật */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  represenNote: string

  // Người quyết định
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  decider: string

  /**Chức vụ Người quyết định */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  deciderPosition: string

  /**Số điện thoại Người quyết định */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  deciderPhone: string

  /**Số Fax Người quyết định  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  deciderFax: string

  /** Email Người quyết định  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  deciderEmail: string

  /** Lưu ý về Người quyết định */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  deciderNote: string

  // Người giao dịch
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  trader: string

  /**Chức vụ Người giao dịch */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  traderPosition: string

  /**Số điện thoại Người giao dịch */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  traderPhone: string

  /**Số Fax Người giao dịch  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  traderFax: string

  /** Email Người giao dịch  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  traderEmail: string

  /** Lưu ý về Người giao dịch */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  traderNote: string

  // Tên giám đốc
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  chief: string

  // Người liên hệ
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  contactName: string

  // Số điện thoại nhận thông báo hệ thống
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  phone: string

  // Email
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  email: string

  /**Số Fax  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  fax: string

  /** Website */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  website: string

  // Vốn điều lệ (tỷ đồng)
  @Column({
    type: 'bigint',
    nullable: true,
    default: 0,
  })
  capital: number

  // Tài sản cố định (tỷ đồng)
  @Column({
    type: 'bigint',
    nullable: true,
    default: 0,
  })
  assets: number

  /** Ngày bắt đầu giao dịch với KimTin */
  @Column({ type: 'datetime', nullable: true })
  @Transform(({ value }) => (value ? new Date(value) : null))
  dateStart: Date

  // File đính kèm hóa đơn mẫu/phiếu thu/biên lai --> URL
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  fileBill: string

  // File đính kèm thông tin phát hành hóa đơn --> URL
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  fileInfoBill: string

  // Giấy phép đăng ký kinh doanh/Mã số thuế --> URL
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  fileMST: string

  /** Giấy phép kinh doanh có điều kiện */
  @Column({
    type: 'nvarchar',
    length: 4000,
    nullable: true,
  })
  conditionalBusinessLicense: string

  // Tên chi nhánh ngân hàng
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  bankBrand: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  userId: string
  @OneToOne(() => UserEntity, (p) => p.supplier)
  @JoinColumn({ name: 'userId', referencedColumnName: 'id' })
  user: Promise<UserEntity>

  /** employeeId của người giới thiệu (không relation) */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  introducerId: string
  //
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPartnerGroupId: string
  @OneToOne(() => BusinessPartnerGroupEntity, (p) => p.supplier)
  @JoinColumn({ name: 'businessPartnerGroupId', referencedColumnName: 'id' })
  businessPartnerGroup: Promise<BusinessPartnerGroupEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  businessPartnerGroupCode: string

  /** Loại hình doanh nghiệp*/
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  businessPartnerGroupType: string

  /** Quốc gia (Trụ sở chính) */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  countryId: string
  @ManyToOne(() => CountryEntity, (p) => p.suppliers)
  @JoinColumn({ name: 'countryId', referencedColumnName: 'id' })
  country: Promise<CountryEntity>

  /** Tỉnh Thành (Trụ sở chính) */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  regionId: string
  @ManyToOne(() => RegionEntity, (p) => p.suppliers)
  @JoinColumn({ name: 'regionId', referencedColumnName: 'id' })
  region: Promise<RegionEntity>

  /** Loại NCC(enumData: supplierType)*/
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  supplierType: string

  /** Type để phân biệt các loại NCC */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  approveType: string

  /** Trạng thái Pháp lý */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  legalStatus: string

  /** Tên đường (STREET) */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  street: string

  // Mã bưu chính (POSTAL_CODE)
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  postalCode: string

  // Mã tiền tệ -> Lấy theo quốc gia
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  currency: string

  /**Tập quán mua bán */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  purchasingHabit: string

  /** Đã Gởi duyệt hoạt động/ngưng hoạt động nhà cung cấp chưa? */
  @Column({
    nullable: true,
    default: false,
  })
  isApproveActive: boolean

  /** Trạng thái duyệt chỉnh sửa NCC enum RequestUpdateStatusSupplier */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  requestUpdateStatus: string

  /** Các thông tin năng lực Item của NCC */
  @OneToMany(() => SupplierCapacityEntity, (p) => p.supplier)
  supplierCapacities: Promise<SupplierCapacityEntity[]>

  /** Chào giá nhanh */
  @OneToMany(() => OfferSupplierEntity, (p) => p.supplier)
  offerSupplier: Promise<OfferSupplierEntity[]>

  /** Các Item NCC */
  @OneToMany(() => SupplierServiceEntity, (p) => p.supplier)
  supplierServices: Promise<SupplierServiceEntity[]>

  /** Các lần thẩm định */
  @OneToMany(() => SupplierExpertiseEntity, (p) => p.supplier)
  supplierExpertise: Promise<SupplierExpertiseEntity[]>

  @OneToMany(() => SupplierNotifyEntity, (p) => p.supplier)
  supplierNotifys: Promise<SupplierNotifyEntity[]>

  /** Danh sách nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierEntity, (p) => p.supplier)
  bidSupplier: Promise<BidSupplierEntity[]>

  /** Danh sách nhà cung cấp đấu thầu */
  @OneToMany(() => BidEmployeeRateEntity, (p) => p.supplier)
  bidEmployeeRate: Promise<BidEmployeeRateEntity[]>

  /** Các lần tham gia đàm phán */
  @OneToMany(() => BidDealSupplierEntity, (p) => p.supplier)
  bidDealSupplier: Promise<BidDealSupplierEntity[]>

  /** Các lần tham gia đàm phán */
  @OneToMany(() => OfferDealSupplierEntity, (p) => p.supplier)
  offerDealSupplier: Promise<OfferDealSupplierEntity[]>

  /** Các lần tham gia đấu giá */
  @OneToMany(() => BidAuctionSupplierEntity, (p) => p.supplier)
  bidAuctionSupplier: Promise<BidAuctionSupplierEntity[]>

  /** Các HĐ */
  @OneToMany(() => ContractEntity, (p) => p.supplier)
  contracts: Promise<ContractEntity[]>

  /** Các PO */
  @OneToMany(() => POEntity, (p) => p.supplier)
  pos: Promise<POEntity[]>

  /** Lịch sử PO */
  @OneToMany(() => POHistoryEntity, (p) => p.supplier)
  poHistorys: Promise<POHistoryEntity[]>

  /** Lịch sử NCC */
  @OneToMany(() => SupplierHistoryEntity, (p) => p.supplier)
  supplierHistorys: Promise<SupplierHistoryEntity[]>

  /** Danh sách tài khoản ngân hàng */
  @OneToMany(() => SupplierBankEntity, (p) => p.supplier)
  banks: Promise<SupplierBankEntity[]>

  /**danh sách nhà máy */
  @OneToMany(() => FactorySupplierEntity, (p) => p.supplier)
  factorySuppliers: Promise<FactorySupplierEntity[]>

  /** Loại URL: KES, KTG*/
  @Column({
    type: 'varchar',
    length: 300,
    nullable: true,
  })
  urlType: string

  /** Mối liên hệ với NCC */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierNumberId: string

  @OneToOne(() => SupplierNumberEntity, (p) => p.supplier)
  @JoinColumn({ name: 'supplierNumberId', referencedColumnName: 'id' })
  supplierNumber: Promise<SupplierNumberEntity>

  /**yêu cầu chỉnh sửa nhà cung cấp */
  @OneToMany(() => RequestUpdateSupplierEntity, (p) => p.supplier)
  requestUpdateSuppliers: Promise<RequestUpdateSupplierEntity[]>

  /**DS phiếu đánh giá hiện trường */
  @OneToMany(() => SiteAssessmentEntity, (p) => p.supplier)
  siteAssessments: Promise<SiteAssessmentEntity[]>
  /** Đánh giá lịch sử mua hàng */
  @OneToMany(() => EvaluationHistoryPurchaseEntity, (p) => p.supplier)
  evaluationHistoryPurchases: Promise<EvaluationHistoryPurchaseEntity[]>

  /** Nghiệm thu hợp đồng */
  @OneToMany(() => ContractInspectionEntity, (p) => p.supplier)
  contractInspections: Promise<ContractInspectionEntity[]>

  /** Hóa đơn */
  @OneToMany(() => BillEntity, (p) => p.supplier)
  bills: Promise<BillEntity[]>

  /** ds Khiếu nại */
  @OneToMany(() => ComplaintEntity, (p) => p.supplier)
  complaints: Promise<ComplaintEntity[]>

  /** ds chat */
  @OneToMany(() => ComplaintChatEntity, (p) => p.receiver)
  complaintChatReceivers: Promise<ComplaintChatEntity[]>

  /** Hồ sơ thanh toán */
  @OneToMany(() => PaymentEntity, (p) => p.supplier)
  payments: Promise<PaymentEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierSchemaId: string

  @ManyToOne(() => SupplierSchemaEntity, (p) => p.suppliers)
  @JoinColumn({ name: 'supplierSchemaId', referencedColumnName: 'id' })
  supplierSchema: Promise<SupplierSchemaEntity>

  /** Lịch sử trạng thái thực hiện PO */
  @OneToMany(() => PoHistoryStatusExecutionEntity, (p) => p.supplier)
  poHistoryStatusExecutions: Promise<PoHistoryStatusExecutionEntity[]>

  /** Thông báo khiếu nại */
  @OneToMany(() => ComplaintNotifyEntity, (p) => p.supplier)
  complaintNotifys: Promise<ComplaintNotifyEntity[]>

  /** chứng từ hợp đồng */
  @OneToMany(() => ContractDocumentHandoverEntity, (p) => p.supplier)
  contracyDocumentHandovers: Promise<ContractDocumentHandoverEntity[]>

  @OneToMany(() => SupplierListPricePOEntity, (p) => p.supplier)
  listPricePos: Promise<SupplierListPricePOEntity[]>

  @OneToMany(() => SupplierPlantEntity, (p) => p.supplier)
  supplierPlants: Promise<SupplierPlantEntity[]>

  /** Loại hình doanh nghiệp */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessTypeId: string
  @ManyToOne(() => BusinessTypeEntity, (p) => p.suppliers)
  @JoinColumn({ name: 'businessTypeId', referencedColumnName: 'id' })
  businessType: Promise<BusinessTypeEntity>

  @OneToMany(() => SupplierPotentialUpgradeEntity, (p) => p.supplier)
  supplierPotentialUpgrades: Promise<SupplierPotentialUpgradeEntity[]>

  @OneToMany(() => RecommendedPurchaseShipmentCostPriceEntity, (p) => p.supplier)
  recommendedPurchaseShipmentCostPrices: Promise<RecommendedPurchaseShipmentCostPriceEntity[]>

  @OneToMany(() => RecommendedPurchaseShipmenStageEntity, (p) => p.supplier)
  recommendedPurchaseShipmentStage: Promise<RecommendedPurchaseShipmenStageEntity[]>

  @OneToMany(() => ShipmentCostDetailEntity, (p) => p.supplier)
  shipmentCostDetails: Promise<ShipmentCostDetailEntity[]>

  @OneToMany(() => RfqEntity, (p) => p.supplier)
  rfqs: Promise<RfqEntity[]>

  /**Nguồn tạo nhà cung cấp enum: supplierSource */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  supplierSource: string

  /** Có được nâng cấp thẳng lên chính thức sau khi duyệt pháp, lý năng lực không */
  @Column({
    nullable: false,
    default: false,
  })
  isOfficial: boolean

  /**Lý do từ chối pháp lý */
  @Column({
    type: 'nvarchar',
    length: 400,
    nullable: true,
  })
  reasonRejectLaw: string

  /** Nhà cung cấp nội bộ? */
  @Column({
    nullable: true,
    default: false,
  })
  isInternal: boolean

  /** Loại hình sản xuất/kinh doanh */
  @ManyToOne(() => CompanyEntity, (p) => p.suppliers)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: true,
  })
  bizType: string

  /**Tầm nhìn, Sứ mệnh của Nhà cung cấp*/
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  visionAndMission: string

  /**Mục tiêu trung và dài hạn*/
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  midLongTermGoals: string

  /**Lĩnh vực hoạt động chính */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  mainBusinessArea: string

  /**File Sơ đồ tổ chức */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fileOrganization: string

  // Số lượng nhân viên trực tiếp
  @Column({
    nullable: true,
    default: 0,
  })
  directEmpCount: number

  // Số lượng nhân viên gián tiếp
  @Column({
    nullable: true,
    default: 0,
  })
  indirectEmpCount: number

  // Số lượng nhân viên khác
  @Column({
    nullable: true,
    default: 0,
  })
  otherEmpCount: number

  // Số lượng nhân viên khác
  @Column({
    nullable: true,
    default: 0,
  })
  totalEmpCount: number

  /**Giám đốc */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  director: string

  /**TP Kinh doanh/Marketing */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  marketingManager: string

  /**TP Cung ứng/Mua */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  procurementManager: string

  /**Quản Đốc */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  foreman: string

  // Điểm đạt được qua phiếu đánh giá
  @Column({
    nullable: true,
    default: 0,
  })
  score: number

  // Số lượng nhân viên Phòng R&D
  @Column({
    nullable: true,
    default: 0,
  })
  rndDepartmentCount: number

  // Số lượng nhân viên Phòng Mua hàng
  @Column({
    nullable: true,
    default: 0,
  })
  purchasingDepartmentCount: number

  // Số lượng nhân viên phòng sản xuất
  @Column({
    nullable: true,
    default: 0,
  })
  productionDepartmentCount: number

  // Số lượng nhân viên phòng chất lượng
  @Column({
    nullable: true,
    default: 0,
  })
  qualityDepartmentCount: number

  // Số lượng nhân viên phòng bảo hành, bảo trì
  @Column({
    nullable: true,
    default: 0,
  })
  warrantyMaintenanceDepartmentCount: number

  // Số lượng nhân viên khác P. Kiểm tra và thử
  @Column({
    nullable: true,
    default: 0,
  })
  testingDepartmentCount: number

  // Số lượng nhân viên phòng P. Marketing và Sale
  @Column({
    nullable: true,
    default: 0,
  })
  marketingSalesDepartmentCount: number

  // Số lượng nhân viên phòng kĩ thuật
  @Column({
    nullable: true,
    default: 0,
  })
  technicalDepartmentCount: number

  // Số giờ làm việc Trung bình tuần
  @Column({
    nullable: true,
    default: 0,
  })
  avgWeeklyWorkingHours: number

  // Năm tài chính từ
  @Column({
    nullable: true,
    default: 0,
  })
  fiscalYearRangeFrom: number

  // Năm tài chính đến
  @Column({
    nullable: true,
    default: 0,
  })
  fiscalYearRangeTo: number

  /**Đơn vị tiền tệ cho vốn điều lệ */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  capitalCurrencyCode: string

  /**Đơn vị tiền tệ cho tài sản cố định */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  assetsCurrencyCode: string

  /**Báo cáo tài chính năm gần nhất*/
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  financialReportFile: string

  /** Nhà cung cấp có bộ phận chuyên trách về nghiên cứu sản phẩm mới và phát triển sản phẩm hiện có không? */
  @Column({
    nullable: true,
    default: false,
  })
  hasResearchDept: boolean

  // Số thành viên của bộ phận nghiên cứu
  @Column({
    nullable: true,
    default: 0,
  })
  researchDeptMemberCount: number

  /**nêu một vài thành tựu về Nghiên cứu và phát triển của nhà cung cấp hoặc các giải thưởng, chứng nhận đạt được*/
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  researchAchievements: string

  /**Danh sách doanh thu những năm trở lại đây */
  @OneToMany(() => SupplierRevenueEntity, (p) => p.supplier)
  supplierRevenues: Promise<SupplierRevenueEntity[]>

  @OneToMany(() => SupplierCompanyEntity, (p) => p.supplier)
  supplierCompanies: Promise<SupplierCompanyEntity[]>

  /** Gửi cho HO duyệt? */
  @Column({
    nullable: true,
    default: false,
  })
  isHO: boolean

  /** Ncc Vãng lai */
  @Column({
    nullable: true,
    default: false,
  })
  isTransient: boolean

  /**Lý do yêu cầu kiểm tra lại */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  reasonRequestReCheck: string

  /**Cột file bất kì */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fileOther: string

  @OneToMany(() => RequestQuoteSupplierEntity, (p) => p.supplier)
  requestQuoteSuppliers: Promise<RequestQuoteSupplierEntity[]>
}
