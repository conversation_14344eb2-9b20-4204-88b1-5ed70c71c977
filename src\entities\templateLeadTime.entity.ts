import { <PERSON>ti<PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn } from 'typeorm'
import { BaseEntity } from './base.entity'
import { HeaderLeadTimeEntity } from './headerLeadTime.entity'

@Entity('template_lead_time')
export class TemplateLeadTimeEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  code: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  headerLeadTimeId: string
  @ManyToOne(() => HeaderLeadTimeEntity, (p) => p.templateLeadTimes)
  @JoinColumn({ name: 'headerLeadTimeId', referencedColumnName: 'id' })
  headerLeadTime: Promise<HeaderLeadTimeEntity>

  @Column({
    nullable: true,
  })
  numberOfDay: number

  /** Công thức */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fomular: string

  @Column({
    nullable: true,
    default: false,
  })
  isSum: boolean

  @Column({
    nullable: true,
    default: 0,
  })
  sort: number
}
