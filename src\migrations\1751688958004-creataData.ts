import { MigrationInterface, QueryRunner } from "typeorm";

export class CreataData1751688958004 implements MigrationInterface {
    name = 'CreataData1751688958004'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_group" ADD "paymentTermId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "material_group" ADD "incotermId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "material_group" ADD "lstPaymentTermId" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "material_group" ADD "lstIncotermId" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "material_group" ADD CONSTRAINT "FK_69be4008ef0aac9bc7fbf8f92c0" FOREIGN KEY ("paymentTermId") REFERENCES "payment_term"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_group" ADD CONSTRAINT "FK_6834c347240b4e61f4e25603914" FOREIGN KEY ("incotermId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_group" DROP CONSTRAINT "FK_6834c347240b4e61f4e25603914"`);
        await queryRunner.query(`ALTER TABLE "material_group" DROP CONSTRAINT "FK_69be4008ef0aac9bc7fbf8f92c0"`);
        await queryRunner.query(`ALTER TABLE "material_group" DROP COLUMN "lstIncotermId"`);
        await queryRunner.query(`ALTER TABLE "material_group" DROP COLUMN "lstPaymentTermId"`);
        await queryRunner.query(`ALTER TABLE "material_group" DROP COLUMN "incotermId"`);
        await queryRunner.query(`ALTER TABLE "material_group" DROP COLUMN "paymentTermId"`);
    }

}
