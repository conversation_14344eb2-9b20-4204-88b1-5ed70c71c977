import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { BidEntity } from './bid.entity'
import { BidEmployeeRateEntity } from './bidEmployeeRate.entity'

@Entity('bid_employee_access')
export class BidEmployeeAccessEntity extends BaseEntity {
  /** Loại quyền nhân viên được cấp */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.bidAccess)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.employeeAccess)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    nullable: false,
    default: false,
  })
  isScore: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isMember: boolean

  /** Điểm HĐXT Kỹ thuật */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  scoreManualTech: number

  /** Điểm HĐXT giá */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  scoreManualPrice: number

  /** Điểm HĐXT DKTM */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  scoreManualTrade: number

  @OneToMany(() => BidEmployeeRateEntity, (p) => p.employeeAccess)
  bidEmployeeRate: Promise<BidEmployeeRateEntity[]>
}
