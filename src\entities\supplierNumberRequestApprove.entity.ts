import { BaseEntity } from './base.entity'
import { Entity, Column, Join<PERSON>olumn, ManyToOne, OneToOne, OneToMany } from 'typeorm'
import {
  BusinessPartnerGroupEntity,
  CompanyEntity,
  CurrencyEntity,
  IncotermEntity,
  PaymentMethodEntity,
  PaymentTermEntity,
  PlanningGroupEntity,
  PurchasingGroupEntity,
  PurchasingOrgEntity,
  RoleFiSupplierEntity,
  RoleSupplierEntity,
  SupplierNumberEntity,
} from '.'
import { TitleEntity } from './title.entity'
import { StakeholderCategoryEntity } from './stakeholderCategory.entity'
import { IndustryStandardEntity } from './industryStandard.entity'
import { GLAccountEntity } from './glAccount.entity'

/** Thông tin yêu cầu duyệt đến hệ thống SAP */
@Entity('supplier_number_request_approve')
export class SupplierNumberRequestApproveEntity extends BaseEntity {
  /** Tên viết tắt */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  shortName: string

  /** TÊn viết tắt dùng để search */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  searchTerm: string

  /** Mối liên hệ với tổ chức/ cá nhân */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierNumberId: string

  @OneToOne(() => SupplierNumberEntity, (p) => p.supplierNumberRequestApprove)
  @JoinColumn({ name: 'supplierNumberId', referencedColumnName: 'id' })
  supplierNumber: Promise<SupplierNumberEntity>

  /** Mối liên hệ với tổ chức/ cá nhân */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  titleId: string

  @ManyToOne(() => TitleEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'titleId', referencedColumnName: 'id' })
  title: Promise<TitleEntity>

  /** Mối liên hệ với tổ chức/ cá nhân */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  stakeholderCategoryId: string

  @ManyToOne(() => StakeholderCategoryEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'stakeholderCategoryId', referencedColumnName: 'id' })
  stakeholderCategory: Promise<StakeholderCategoryEntity>

  /** industry standard */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  industryStandardId: string

  @ManyToOne(() => IndustryStandardEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'industryStandardId', referencedColumnName: 'id' })
  industryStandard: Promise<IndustryStandardEntity>

  /** Mối liên hệ với đối tác kinh doanh */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPartnerGroupId: string

  @ManyToOne(() => BusinessPartnerGroupEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'businessPartnerGroupId', referencedColumnName: 'id' })
  businessPartnerGroup: Promise<BusinessPartnerGroupEntity>

  /** Công ty */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string

  @ManyToOne(() => CompanyEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  /** Mối liên hệ với tổ chức mua hàng*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string

  @ManyToOne(() => PurchasingOrgEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  /** Mối liên hệ với tổ chức mua hàng*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string

  @ManyToOne(() => PurchasingGroupEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'purchasingGroupId', referencedColumnName: 'id' })
  purchasingGroup: Promise<PurchasingGroupEntity>

  /** Mối liên hệ với tổ chức mua hàng*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currencyId: string

  @ManyToOne(() => CurrencyEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  /** Mối liên hệ với ĐK thanh toán quốc tế */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermId: string

  @ManyToOne(() => IncotermEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'incotermId', referencedColumnName: 'id' })
  incoterm: Promise<IncotermEntity>

  /** Mối liên hệ với thời hạn thanh toán  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentTermId: string
  @ManyToOne(() => PaymentTermEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'paymentTermId', referencedColumnName: 'id' })
  paymentTerm: Promise<PaymentTermEntity>

  /** Mối liên hệ với PTTT  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentMethodId: string
  @ManyToOne(() => PaymentMethodEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'paymentMethodId', referencedColumnName: 'id' })
  paymentMethod: Promise<PaymentMethodEntity>

  /** Mối liên hệ với phân nhóm dòng tiền  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  planningGroupId: string
  @ManyToOne(() => PlanningGroupEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'planningGroupId', referencedColumnName: 'id' })
  planningGroup: Promise<PlanningGroupEntity>

  /** Mối liên hệ với  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  glAccountId: string
  @ManyToOne(() => GLAccountEntity, (p) => p.supplierNumberRequestApproves)
  @JoinColumn({ name: 'glAccountId', referencedColumnName: 'id' })
  glAccount: Promise<GLAccountEntity>

  @OneToMany(() => RoleSupplierEntity, (p) => p.supplierNumberRequestApprove)
  roleSuppliers: Promise<RoleSupplierEntity[]>

  @OneToMany(() => RoleFiSupplierEntity, (p) => p.supplierNumberRequestApprove)
  roleFiSuppliers: Promise<RoleFiSupplierEntity[]>

  /**Đã khóa vùng nhập liệu business partner  */
  @Column({
    nullable: true,
    default: false,
  })
  isLockBusinessPartner: boolean

  /**Đã khóa vùng nhập liệu fi supplier  */
  @Column({
    nullable: true,
    default: false,
  })
  isLockFISupplier: boolean

  /**Đã khóa vùng nhập liệu supplier  */
  @Column({
    nullable: true,
    default: false,
  })
  isLockSupplier: boolean
}
