import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { CompanyEntity } from './company.entity'
import { SupplierServiceEntity } from './supplierService.entity'

@Entity('supplier_company')
export class SupplierCompanyEntity extends BaseEntity {
  // Điểm đạt được qua phiếu đánh giá
  @Column({
    nullable: true,
    default: 0,
  })
  score: number

  /**Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Nhà cung cấp  */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.supplierCompanies)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @ManyToOne(() => CompanyEntity, (e) => e.supplierCompanies)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  /**Nguồn tạo nhà cung cấp enum: supplierSource */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  supplierSource: string

  /** Đã Gởi duyệt hoạt động/ngưng hoạt động nhà cung cấp chưa? */
  @Column({
    nullable: true,
    default: false,
  })
  isApproveActive: boolean

  /** Trạng thái duyệt chỉnh sửa NCC enum RequestUpdateStatusSupplier */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  requestUpdateStatus: string

  /** Gửi cho HO duyệt? */
  @Column({
    nullable: true,
    default: false,
  })
  isHO: boolean

  /**Mới tạo/Chính thức/tiềm năng) đổi với 1 không ty */
  @Column({
    type: 'nvarchar',
    length: 30,
    nullable: true,
  })
  type: string

  @OneToMany(() => SupplierServiceEntity, (p) => p.supplierCompany)
  supplierServices: Promise<SupplierServiceEntity[]>
}
