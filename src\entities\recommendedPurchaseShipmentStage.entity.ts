import { BidEntity, OfferEntity, RecommendedPurchaseEntity, SupplierEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'

/** Thông tin shipment cost stage*/
@Entity('recommended_purchase_shipment_stage')
export class RecommendedPurchaseShipmenStageEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseId: string
  @ManyToOne(() => RecommendedPurchaseEntity, (p) => p.recommendedPurchaseShipmentStages)
  @JoinColumn({ name: 'recommendedPurchaseId', referencedColumnName: 'id' })
  recommendedPurchase: Promise<RecommendedPurchaseEntity>

  /**Nguồn tham chiếu */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  referenceSource: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.recommendedPurchaseShipmentStage)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string
  @ManyToOne(() => OfferEntity, (p) => p.recommendedPurchaseShipmentStage)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentCostId: string

  /** Điểm bắt đầu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  startPoint: string

  /** Điểm đến */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  destination: string

  /** Mã FwdAgent */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fwdAgent: string

  /** Tên FwdAgent */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fwdAgentName: string

  /** Khoảng cách */
  @Column({
    nullable: true,
    type: 'bigint',
  })
  distance: number

  /** Shipping type */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  shippingType: string

  /** Shipping type desc */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  shippingTypeDesc: string

  /** TC gói thầu */
  @Column({
    nullable: true,
    default: false,
  })
  tcBid: boolean

  /** TC báo giá */
  @Column({
    nullable: true,
    default: false,
  })
  tcOffer: boolean

  /** Mã nguồn tham chiếu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  referenceCode: string

  /** Tổng chi phí dự kiến  */
  @Column({
    nullable: true,
    type: 'bigint',
  })
  estimatedCost: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.recommendedPurchaseShipmentStage)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>
}
