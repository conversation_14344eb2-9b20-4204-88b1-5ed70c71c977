import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnContract1752130012318 implements MigrationInterface {
  name = 'AddColumnContract1752130012318'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "itemNo" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "lotNumber" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "acccate" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "category" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "materialGroupId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "assetCode" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "orderCode" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "plantId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "storageLocation" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "ounCode" varchar(10)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "ounId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "quantityAlternative" bigint`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "totalPriceVND" bigint`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "taxCode" nvarchar(100)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "taxRate" int CONSTRAINT "DF_fc50a2c05f9b397a2a96857eb1b" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "totalPriceAfterTax" bigint`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "origin" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "manufacturer" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "deliveryDate" datetime`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "underDeliveryTolerance" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "overDeliveryTolerance" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "stockType" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "valuationType" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "foreignContractCode" nvarchar(150)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "nameEN" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractType" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "faxBuyer" nvarchar(50)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "telSeller" nvarchar(50)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractAmountBeforeTax" bigint CONSTRAINT "DF_02c0c8206d9394d427078f4e2f2" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractAmountBeforeTaxText" nvarchar(400)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractAmountAfterTax" bigint CONSTRAINT "DF_98f3974fd55029e29719e687824" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractAmountAfterTaxText" nvarchar(400)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "exchangeRate" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractAmountBeforeTaxInVND" bigint CONSTRAINT "DF_d5fa3315444e5b64afecb3e3dbf" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "contractAmountAfterTaxInVND" bigint CONSTRAINT "DF_52d0b64f1307dd7fd0c32bc4c5f" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "paymentTermId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "incotermVersion" nvarchar(100)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "incotermLocation" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "companyId"`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "companyId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "purchasingOrgId"`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "purchasingOrgId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "purchasingGroupId"`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "purchasingGroupId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "contract_item" ADD CONSTRAINT "FK_a23f2f34c5e56aec59ae6c2cd1b" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_item" ADD CONSTRAINT "FK_a2f918319e13a14ceb928196a32" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_item" ADD CONSTRAINT "FK_d6a0b19213afb354227c3d5ce29" FOREIGN KEY ("ounId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_cb7b6e7be4a366a8997865f19cf" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_613254f1691ded7ca54cdf4136f" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_ba4311c222bf9285d9d06f3b477" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_3a509d64777f85466ea0d4191b0" FOREIGN KEY ("paymentTermId") REFERENCES "payment_term"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_3a509d64777f85466ea0d4191b0"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_ba4311c222bf9285d9d06f3b477"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_613254f1691ded7ca54cdf4136f"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_cb7b6e7be4a366a8997865f19cf"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP CONSTRAINT "FK_d6a0b19213afb354227c3d5ce29"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP CONSTRAINT "FK_a2f918319e13a14ceb928196a32"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP CONSTRAINT "FK_a23f2f34c5e56aec59ae6c2cd1b"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "purchasingGroupId"`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "purchasingGroupId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "purchasingOrgId"`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "purchasingOrgId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "companyId"`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "companyId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "incotermLocation"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "incotermVersion"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "paymentTermId"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_52d0b64f1307dd7fd0c32bc4c5f"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractAmountAfterTaxInVND"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_d5fa3315444e5b64afecb3e3dbf"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractAmountBeforeTaxInVND"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "exchangeRate"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractAmountAfterTaxText"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_98f3974fd55029e29719e687824"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractAmountAfterTax"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractAmountBeforeTaxText"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_02c0c8206d9394d427078f4e2f2"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractAmountBeforeTax"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "telSeller"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "faxBuyer"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractType"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "nameEN"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "foreignContractCode"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "valuationType"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "stockType"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "overDeliveryTolerance"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "underDeliveryTolerance"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "deliveryDate"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "manufacturer"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "origin"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "totalPriceAfterTax"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP CONSTRAINT "DF_fc50a2c05f9b397a2a96857eb1b"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "taxRate"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "taxCode"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "totalPriceVND"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "quantityAlternative"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "ounId"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "ounCode"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "storageLocation"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "plantId"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "orderCode"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "assetCode"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "materialGroupId"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "category"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "acccate"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "lotNumber"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "itemNo"`)
  }
}
