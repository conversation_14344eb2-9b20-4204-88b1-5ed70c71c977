import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON>olumn } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
@Entity('employee_warning')
export class EmployeeWarningEntity extends BaseEntity {
  /** Tiêu đề */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  message: string

  /** Nội dung */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  messageFull: string

  /** <PERSON>à thông báo mới */
  @Column({
    nullable: false,
    default: true,
  })
  isNew: boolean

  /** Loại cảnh báo WarningType */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  warningType: string

  /** Loại dữ liệu cần cảnh báo DataWarningType */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  dataType: string

  /** ID dữ liệu cần cảnh báo */
  @Column({
    type: 'varchar',

    nullable: true,
  })
  dataId: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.employeeWarning)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>
}
