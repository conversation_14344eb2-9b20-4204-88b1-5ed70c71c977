import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { PurchasingGroupEntity } from './purchasingGroup.entity'
@Entity('employee_purchasing_group')
export class EmployeePurchasingGroupEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.employeePurchasingGroup)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  /** <PERSON><PERSON><PERSON> liên hệ với tổ chức mua hàng*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string
  @ManyToOne(() => PurchasingGroupEntity, (p) => p.employeePurchasingGroup)
  @JoinColumn({ name: 'purchasingGroupId', referencedColumnName: 'id' })
  purchasingGroup: Promise<PurchasingGroupEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string
}
