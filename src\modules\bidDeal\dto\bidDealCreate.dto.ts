import { ApiProperty } from '@nestjs/swagger'
import { <PERSON>String, IsNumber, IsBoolean, IsArray, IsNotEmpty } from 'class-validator'

export class BidDealCreateDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  bidId: string
  @ApiProperty()
  @IsNotEmpty()
  endDate: Date

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  isSendDealPrice: boolean

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  isRequireFilePriceDetail: boolean

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  isRequireFileTechDetail: boolean

  @ApiProperty()
  @IsArray()
  lstSupplierChoose: any[]

  @ApiProperty()
  @IsArray()
  lstPrice: BidDealPriceCreateDto[]
}

class BidDealPriceCreateDto {
  @ApiProperty()
  @IsString()
  bidPriceId: string

  @ApiProperty()
  @IsNumber()
  bestPrice: number

  @ApiProperty()
  @IsNumber()
  suggestPrice: number

  @ApiProperty()
  @IsNumber()
  maxPrice: number

  @ApiProperty()
  @IsNumber()
  sort: number

  @ApiProperty()
  @IsNumber()
  number: number
}
