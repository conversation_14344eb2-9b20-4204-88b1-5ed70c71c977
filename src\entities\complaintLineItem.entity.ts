import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ComplaintEntity } from './complaint.entity'
import { POProductEntity } from './poProduct.entity'
import { ContractItemEntity } from './contractItem.entity'

/** Khiếu nại - line item */
@Entity('complaint_line_item')
export class ComplaintLineItemEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  poItemId: string
  @ManyToOne(() => POProductEntity, (p) => p.complaintLineItems)
  @JoinColumn({ name: 'poItemId', referencedColumnName: 'id' })
  poItem: Promise<POProductEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractItemId: string
  @ManyToOne(() => ContractItemEntity, (p) => p.complaintLineItems)
  @JoinColumn({ name: 'contractItemId', referencedColumnName: 'id' })
  contractItem: Promise<ContractItemEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  complaintId: string
  @ManyToOne(() => ComplaintEntity, (p) => p.complaintLineItems)
  @JoinColumn({ name: 'complaintId', referencedColumnName: 'id' })
  complaint: Promise<ComplaintEntity>
}
