import { MigrationInterface, QueryRunner } from 'typeorm'

export class TablePaymentCon1752827729593 implements MigrationInterface {
  name = 'TablePaymentCon1752827729593'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "payment_term_conversion" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ad8145b6100a48a62c0c9ea8c3a" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6387f378066dda1457e7c19d5a4" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "paymentTermId" uniqueidentifier, "code" varchar(50), "percentBank" float, "dayOfRecommendedPurchase" float, "percentOfRecommendedPurchase" float, "recommendedPurchaseCodeLeadTime" varchar(50), "dayOfContract" float, "percentOfContract" float, "contractCodeLeadTime" varchar(50), "dayOfSupplierPrepare" float, "percentSupplierPrepare" float, "supplierPrepareCodeLeadTime" varchar(50), "dayOfSupplierProduction" float, "percentSupplierProduction" float, "supplierProductionCodeLeadTime" varchar(50), "dayOfSupplierProductionToPort" float, "percentSupplierProductionToPort" float, "supplierProductionToPortCodeLeadTime" varchar(50), "dayOfTransportSupplierToVietNam" float, "percentTransportSupplierToVietNam" float, "transportSupplierToVietNamCodeLeadTime" varchar(50), "dayOfTransportVietNamToWarehouse" float, "percentTransportVietNamToWarehouse" float, "percentTransportVietNamToWarehouseCodeLeadTime" varchar(50), "dayOfQualityCheckAndReceiving" float, "percentQualityCheckAndReceiving" float, "qualityCheckAndReceivingCodeLeadTime" varchar(50), "dayOfLeadtimePurchase" float, "percentLeadtimePurchase" float, "percentLeadtimePurchaseCodeLeadTime" varchar(50), "dayOfLeadtimeDelivery" float, "percentLeadtimeDelivery" float, "percentLeadtimeDeliveryCodeLeadTime" varchar(50), CONSTRAINT "PK_ad8145b6100a48a62c0c9ea8c3a" PRIMARY KEY ("id"))`,
    )

    await queryRunner.query(
      `ALTER TABLE "payment_term_conversion" ADD CONSTRAINT "FK_2a92b908344510047784579ac58" FOREIGN KEY ("paymentTermId") REFERENCES "payment_term"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "payment_term_conversion" DROP CONSTRAINT "FK_2a92b908344510047784579ac58"`)
    await queryRunner.query(`DROP TABLE "payment_term_conversion"`)
  }
}
