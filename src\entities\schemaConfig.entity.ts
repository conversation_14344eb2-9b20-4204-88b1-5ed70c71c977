import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { PurchasingOrgSchemaEntity } from './purchasingOrgSchema.entity'
import { SupplierSchemaEntity } from './supplierSchema.entity'
import { ProcedureEntity } from './procedure.entity'

@Entity('schemaConfig')
export class SchemaConfigEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgSchemaId: string
  @ManyToOne(() => PurchasingOrgSchemaEntity, (p) => p.schemaConfig)
  @JoinColumn({ name: 'purchasingOrgSchemaId', referencedColumnName: 'id' })
  purchasingOrgSchema: Promise<PurchasingOrgSchemaEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierSchemaId: string

  @ManyToOne(() => SupplierSchemaEntity, (p) => p.schemaConfig)
  @JoinColumn({ name: 'supplierSchemaId', referencedColumnName: 'id' })
  supplierSchema: Promise<SupplierSchemaEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  procedureId: string

  @ManyToOne(() => ProcedureEntity, (p) => p.schemaConfig)
  @JoinColumn({ name: 'procedureId', referencedColumnName: 'id' })
  procedure: Promise<ProcedureEntity>

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string
}
