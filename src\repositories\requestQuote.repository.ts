import { RequestQuoteEntity, RequestQuoteSupplierEntity } from '../entities'
import { CustomRepository } from '../typeorm'
import { BaseRepository } from './base.repository'

@CustomRepository(RequestQuoteEntity)
export class RequestQuoteRepository extends BaseRepository<RequestQuoteEntity> {}

@CustomRepository(RequestQuoteSupplierEntity)
export class RequestQuoteSupplierRepository extends BaseRepository<RequestQuoteSupplierEntity> {}
