import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { BidEvaluationService } from './bidEvaluation.service'
import { JwtAuthGuard } from '../../common/guards'
import { UserDto } from '../../../dto'
import { CurrentUser } from '../../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

/** Chọn NCC thắng thầu */
@ApiBearerAuth()
@ApiTags('Bid')
@UseGuards(JwtAuthGuard)
@Controller('bid_evaluation')
export class BidEvaluationController {
  constructor(private readonly service: BidEvaluationService) { }

  @ApiOperation({ summary: 'Tự động chọn NCC thắng thầu và kết thúc thầu' })
  @Post('auto_bid')
  public async autoBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.autoBid(user, data)
  }

  @ApiOperation({ summary: 'Load ds Doanh nghiệp để chọn trúng thầu' })
  @Post('load_supplier_data')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: { bidId: string, isMobile?: boolean }) {
    return await this.service.loadSupplierData(user, data)
  }

  @ApiOperation({ summary: 'Chọn Doanh nghiệp trúng thầu, trượt thầu theo từng Item' })
  @Post('evaluation_bid_supplier')
  public async evaluationBidSupplier(@CurrentUser() user: UserDto, @Body() data: { bidId: string; listItem: any[]; comment: string }) {
    return await this.service.evaluationBidSupplier(user, data)
  }

  @ApiOperation({ summary: 'Phê duyệt Doanh nghiệp thắng thầu' })
  @Post('approve_supplier_win_bid')
  public async approveSupplierWinBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string; comment: string }) {
    return await this.service.approveSupplierWinBid(user, data)
  }

  @ApiOperation({ summary: 'Yêu cầu đánh giá và chọn lại Doanh nghiệp thắng thầu' })
  @Post('reject_supplier_win_bid')
  public async rejectSupplierWinBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string; comment: string; recheck: any[] }) {
    return await this.service.rejectSupplierWinBid(user, data)
  }
}
