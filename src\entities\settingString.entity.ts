import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { BidEntity } from './bid.entity'
import { RecommendedPurchaseSettingValueEntity } from './recommendedPurchaseSettingValue.entity'
import { BusinessPlanSettingValueEntity } from './businessPlanSettingValue.entity'

@Entity('setting_string')
export class SettingStringEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @OneToMany(() => BidEntity, (p) => p.bidType)
  masterBidGuarantee: Promise<BidEntity[]>

  /** giá trị cấu hình  động */
  @Column({
    type: 'float',
    nullable: true,
  })
  value: number

  /** Giá trị chữ */
  @Column({
    type: 'nvarchar',
    length: 400,
    nullable: true,
  })
  valueString: string

  @OneToMany(() => RecommendedPurchaseSettingValueEntity, (p) => p.settingString)
  recommendedPurchaseSettingValue: Promise<RecommendedPurchaseSettingValueEntity[]>

  @OneToMany(() => BusinessPlanSettingValueEntity, (p) => p.settingString)
  businessPlanSettingValue: Promise<BusinessPlanSettingValueEntity[]>
}
