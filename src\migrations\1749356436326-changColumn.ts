import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangColumn1749356436326 implements MigrationInterface {
  name = 'ChangColumn1749356436326'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "comment" DROP CONSTRAINT "DF_aaa8cc745b2dd540f94fa2ca933"`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "comment" ADD CONSTRAINT "DF_aaa8cc745b2dd540f94fa2ca933" DEFAULT 0 FOR "comment"`)
  }
}
