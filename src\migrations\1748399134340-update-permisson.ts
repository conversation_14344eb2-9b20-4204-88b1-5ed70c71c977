import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdatePermisson1748399134340 implements MigrationInterface {
    name = 'UpdatePermisson1748399134340'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "group_permission" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_12f86c54cc64469ecdb10edc29d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b2ee38ef7f842db2f1a36a3333b" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "purchasingOrgId" varchar(255), "purchasingGrId" varchar(255), "companyOrgId" varchar(255), "blockOrgId" varchar(255), "departmentOrgId" varchar(255), "partOrgId" varchar(255), "positionOrgId" varchar(255), "employeeOrgId" varchar(255), "employeeId" varchar(255), CONSTRAINT "PK_12f86c54cc64469ecdb10edc29d" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "group_permission"`);
    }

}
