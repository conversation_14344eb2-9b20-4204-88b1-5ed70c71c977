import { Module } from '@nestjs/common'
import { TransportationPlanController } from './transportationPlan.controller'
import { TransportationPlanService } from './transportationPlan.service'
import { TypeOrmExModule } from '../../typeorm'
import { ShipmentRepository, TransportationPlanRepository } from '../../repositories'
import { TransportationPlanDetailRepository } from '../../repositories/transportationPlan.repository'
import { ShipmentConditionTypeTemplateRepository, ShipmentConditionTypeTemplateValueRepository } from '../../repositories/shipmentTemplate.repository'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      TransportationPlanRepository,
      TransportationPlanDetailRepository,
      ShipmentRepository,
      ShipmentConditionTypeTemplateRepository,
      ShipmentConditionTypeTemplateValueRepository,
    ]),
  ],
  controllers: [TransportationPlanController],
  providers: [TransportationPlanService],
})
export class TransportationPlanModule {}
