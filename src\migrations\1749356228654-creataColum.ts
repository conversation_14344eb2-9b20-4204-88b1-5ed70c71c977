import { MigrationInterface, QueryRunner } from "typeorm";

export class CreataColum1749356228654 implements MigrationInterface {
    name = 'CreataColum1749356228654'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation" ADD "lstReservationNo" nvarchar(max)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "lstReservationNo"`);
    }

}
