import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateUser1749179856270 implements MigrationInterface {
  name = 'UpdateUser1749179856270'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "currentCompanyId" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "currentCompanyId"`)
  }
}
