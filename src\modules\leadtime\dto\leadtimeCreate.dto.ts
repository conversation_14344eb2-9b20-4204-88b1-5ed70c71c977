import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

/** Interface tạo mới một. */
export class LeadtimeCreateDto {
  @ApiProperty({ description: 'Mô tả về materialGroupId.' })
  @IsOptional()
  @IsString()
  materialGroupId?: string

  year: Date

  lstDetail: LeadtimeDetailCreateDto[]

  plantId: string

  description: string
}

export class LeadtimeDetailCreateDto {
  @ApiProperty({ description: 'Mô tả về materialGroupId.' })
  @IsOptional()
  @IsString()
  materialGroupId?: string

  @ApiProperty({ description: 'Mô tả về materialGroupId.' })
  @IsOptional()
  @IsString()
  materialId?: string

  expireDate: Date

  startDate: Date

  dayOfRecommendedPurchase: number

  dayOfContract: number

  dayOfSupplierPrepare: number

  dayOfSupplierProduction: number

  dayOfSupplierProductionToPort: number

  dayOfTransportSupplierToVietNam: number

  dayOfTransportVietNamToWarehouse: number

  dayOfQualityCheckAndReceiving: number

  dayOfLeadtimePurchase: number

  dayOfLeadtimeDelivery: number

  quarter: string

  rouding: number

  moqToPull: number

  moqBuy: number

  packagingSpecifications: number

  materialCode?: string
}
