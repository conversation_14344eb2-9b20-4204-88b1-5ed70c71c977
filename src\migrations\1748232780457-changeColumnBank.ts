import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeColumnBank1748232780457 implements MigrationInterface {
    name = 'ChangeColumnBank1748232780457'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "bank_branch" ALTER COLUMN "name" nvarchar(500)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "bank_branch" ALTER COLUMN "name" nvarchar(500) NOT NULL`);
    }

}
