import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeEntityPo1753010988109 implements MigrationInterface {
    name = 'ChangeEntityPo1753010988109'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po_contract" DROP CONSTRAINT "FK_66e273ae29d05ea3faca09ec8d2"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_bd5a0aadb506f49b0245881fd95"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_8ab4e7565a0e9566ef537713579"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_4a1964e21f4038584bec464969b"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_f76c836c62573749e880c2833cb"`);
        await queryRunner.query(`EXEC sp_rename "ktg-dev.dbo.po_pr.prIds", "prId"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "contractIds"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "posId"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "poContractId"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP COLUMN "posId"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP COLUMN "poPrId"`);
        await queryRunner.query(`ALTER TABLE "po" ADD "quantityPo" varchar(250)`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "contractId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "usedBudget" varchar(250)`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "contractId"`);
        await queryRunner.query(`ALTER TABLE "po" ADD "contractId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "poId"`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "poId" nvarchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "prId"`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD "prId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_717ae013a85f97e8e5685225037" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD CONSTRAINT "FK_6a4db1ae62b6adab3addeecb9c9" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD CONSTRAINT "FK_667faf8aa18019a0be5969f00a3" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po_pr" DROP CONSTRAINT "FK_667faf8aa18019a0be5969f00a3"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP CONSTRAINT "FK_6a4db1ae62b6adab3addeecb9c9"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_717ae013a85f97e8e5685225037"`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "prId"`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD "prId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "poId"`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "poId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "contractId"`);
        await queryRunner.query(`ALTER TABLE "po" ADD "contractId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "usedBudget"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "contractId"`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "quantityPo"`);
        await queryRunner.query(`ALTER TABLE "pr" ADD "poPrId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "pr" ADD "posId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "poContractId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "posId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "contractIds" nvarchar(255)`);
        await queryRunner.query(`EXEC sp_rename "ktg-dev.dbo.po_pr.prId", "prIds"`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_f76c836c62573749e880c2833cb" FOREIGN KEY ("poPrId") REFERENCES "po_pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_4a1964e21f4038584bec464969b" FOREIGN KEY ("posId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_8ab4e7565a0e9566ef537713579" FOREIGN KEY ("poContractId") REFERENCES "po_contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_bd5a0aadb506f49b0245881fd95" FOREIGN KEY ("posId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD CONSTRAINT "FK_66e273ae29d05ea3faca09ec8d2" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
