import { BaseEntity } from './base.entity'
import { Entity, Column, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { InboundEntity, MaterialEntity, POProductEntity } from '.'

/** DS Items */
@Entity('inbound_item')
export class InboundItemEntity extends BaseEntity {
  /** Inbound */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  inboundId: string
  @ManyToOne(() => InboundEntity, (p) => p.inboundItems)
  @JoinColumn({ name: 'inboundId', referencedColumnName: 'id' })
  inbound: Promise<InboundEntity>

  /** Material */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.inboundItems)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** PO Product Detail */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  poProductId: string
  @ManyToOne(() => POProductEntity, (p) => p.inboundItems)
  @JoinColumn({ name: 'poProductId', referencedColumnName: 'id' })
  poProduct: Promise<POProductEntity>

  /** Số lượng gói (kiện hàng) */
  @Column({
    nullable: true,
    type: 'int',
  })
  packageQuantity: number

  /** Số lô hàng */
  @Column({
    nullable: true,
    type: 'int',
  })
  batchNumber: number

  /** Số lượng giao dự kiến */
  @Column({
    nullable: true,
    type: 'int',
  })
  quantityDelivery: number
}
