import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinC<PERSON>umn, OneToMany } from 'typeorm'
import { BidPriceColValueEntity } from './bidPriceColValue.entity'
import { BidSupplierPriceColValueEntity } from './bidSupplierPriceColValue.entity'
import { BidEntity } from './bid.entity'
import { BidPrItemEntity } from './bidPrItem.entity'
import { BidExMatGroupEntity } from './bidExgroup.entity'

@Entity('bid_price_col')
export class BidPriceColEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fomular: string

  /** Có bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    length: 50,
    nullable: false,
  })
  colType: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidId: string

  @ManyToOne(() => BidEntity, (p) => p.bidPriceCols)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @OneToMany(() => BidPriceColValueEntity, (p) => p.bidPriceCol)
  bidPriceColValue: Promise<BidPriceColValueEntity[]>

  @OneToMany(() => BidSupplierPriceColValueEntity, (p) => p.bidPriceCol)
  bidSupplierPriceColValue: Promise<BidSupplierPriceColValueEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidItemId: string
  @ManyToOne(() => BidPrItemEntity, (p) => p.trades)
  @JoinColumn({ name: 'bidItemId', referencedColumnName: 'id' })
  bidItem: Promise<BidPrItemEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidExgroupId: string
  /** 1 công thức chỉ có 1 dịch vụ cha */
  @ManyToOne(() => BidExMatGroupEntity, (p) => p.bidPriceCol)
  @JoinColumn({ name: 'bidExgroupId', referencedColumnName: 'id' })
  bidExgroup: Promise<BidExMatGroupEntity>
}
