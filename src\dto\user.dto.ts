import { IsUUID, IsString, <PERSON><PERSON><PERSON>y, IsOptional } from 'class-validator'

export class UserDto {
  @IsUUID()
  id: string

  @IsString()
  username?: string

  @IsString()
  type?: string

  @IsOptional()
  @IsString()
  supplierId?: string | null

  @IsOptional()
  @IsString()
  employeeId: string | null

  @IsOptional()
  @IsString()
  companyId?: string | null

  @IsOptional()
  @IsString()
  departmentId?: string | null

  @IsOptional()
  @IsArray()
  roles?: any[]

  @IsOptional()
  @IsArray()
  employeePosition: string[]

  @IsOptional()
  @IsArray()
  employeeOrgPosition: string

  /** Nếu On-Premise và là Doanh nghiệp thì all tính năng */
  @IsOptional()
  hasAllRoles?: boolean

  @IsString()
  companyCode?: string

  orgCompanyId?: string

  purchasingOrgId?: string

  purchasingGroupId?: string

  orgDepartmentId?: string

  orgPositionId?: string

  queryParams?: string

  viewPermission?: string

  roleUpdated?: boolean

  roleViewUpdated?: boolean

  rolesAction?: any[]

  @IsString()
  supplierCode?: string
}
