import { Column, Entity, Join<PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { PrEntity } from './pr.entity'

@Entity('bid_pr')
export class BidPrEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.bidPr)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  /** Id của pr ở gói thầu tổng/gói thầu chi tiết khi tạo gói thầu từ việc chọn pr */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId?: string
  /** Pr */
  @ManyToOne(() => PrEntity, (p) => p.bidPr)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>
}
