import { MigrationInterface, QueryRunner } from 'typeorm'

export class ShippmentConfig1750154293703 implements MigrationInterface {
  name = 'ShippmentConfig1750154293703'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "shipment_fee_conditions" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_294c7ffbf2446de76aa39e8d95b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_1968a96cacc42d0c1f951be9c05" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(100) NOT NULL, "description" varchar(255), "isRelatedToSupplier" bit NOT NULL CONSTRAINT "DF_375be80acf50c77f7b1b6ec6825" DEFAULT 0, CONSTRAINT "PK_294c7ffbf2446de76aa39e8d95b" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "shipment_fee_conditions_list" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ee71519aaba28bb3cfe40b09541" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_5f5a3d8708f9ac6243f226bbe8a" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(100) NOT NULL, "description" varchar(255), "shipmentConditionTypeId" uniqueidentifier NOT NULL, CONSTRAINT "PK_ee71519aaba28bb3cfe40b09541" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "shipment_condition_type" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a121be94adf876a111fa4c2fc49" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_568bba436ae984f9e8d8735d429" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(100) NOT NULL, "description" varchar(255), "shipmentFeeConditions" varchar(max), CONSTRAINT "PK_a121be94adf876a111fa4c2fc49" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "shipment_fee_conditions_list" ADD CONSTRAINT "FK_30329fcbf4cb9c965f5b4de5d67" FOREIGN KEY ("shipmentConditionTypeId") REFERENCES "shipment_fee_conditions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_fee_conditions_list" DROP CONSTRAINT "FK_30329fcbf4cb9c965f5b4de5d67"`)
    await queryRunner.query(`DROP TABLE "shipment_condition_type"`)
    await queryRunner.query(`DROP TABLE "shipment_fee_conditions_list"`)
    await queryRunner.query(`DROP TABLE "shipment_fee_conditions"`)
  }
}
