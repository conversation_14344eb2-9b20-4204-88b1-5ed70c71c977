import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { POEntity } from './po.entity'

/** Thành viên trong PO và quyền*/
@Entity({ name: 'po_member' })
export class POMemberEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.poMembers)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.members)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  /** <PERSON>uyền */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  poRoleCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string
}
