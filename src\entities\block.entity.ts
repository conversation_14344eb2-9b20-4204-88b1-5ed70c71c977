import { <PERSON>tity, Column, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CompanyEntity } from './company.entity'
import { PlantEntity } from './plant.entity'
import { DepartmentEntity } from './department.entity'
import { EmployeeEntity } from './employee.entity'
import { TicketEvaluationKpiEmployeeEntity } from './ticketEvaluationKpiEmployee.entity'


/** Cấu hình khối */
@Entity('block')
export class BlockEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Mô tả khối */
  @Column({
    type: 'varchar',
    length: 'max',
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string

  @ManyToOne(() => CompanyEntity, (p) => p.blocks)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @ManyToOne(() => PlantEntity, (p) => p.blocks)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @OneToMany(() => DepartmentEntity, (p) => p.block)
  departments: Promise<DepartmentEntity[]>

  @OneToMany(() => EmployeeEntity, (p) => p.block)
  employee: Promise<EmployeeEntity[]>

  /** ds phiếu đánh giá KPI và NV */
  @OneToMany(() => TicketEvaluationKpiEmployeeEntity, (p) => p.block)
  ticketEvaluationKpiEmployees: Promise<TicketEvaluationKpiEmployeeEntity[]>
}
