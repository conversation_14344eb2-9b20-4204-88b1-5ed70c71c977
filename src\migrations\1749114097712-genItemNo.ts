import { MigrationInterface, QueryRunner } from "typeorm";

export class GenItemNo1749114097712 implements MigrationInterface {
    name = 'GenItemNo1749114097712'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "itemNo" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "itemNo"`);
    }

}
