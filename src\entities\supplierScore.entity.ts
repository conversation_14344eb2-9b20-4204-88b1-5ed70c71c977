import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

@Entity('supplier_score')
export class SupplierScoreEntity extends BaseEntity {
  // Điểm đạt được qua phiếu đánh giá
  @Column({
    nullable: true,
    default: 0,
  })
  score: number

  /* người đánh giá */
  @Column({ type: 'varchar', nullable: false })
  employeeId: string

  /* nhà cung cấp được đ<PERSON>h giá */
  @Column({ type: 'varchar', nullable: true })
  supplierId: string

  /* nhà cung cấp được đánh giá */
  @Column({ type: 'varchar', nullable: false })
  templateCriterialId: string
}
