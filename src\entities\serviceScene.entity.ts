import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { ServiceEntity } from './service.entity'
import { ServiceSceneListDetailEntity } from './serviceSceneListDetail.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'

/** Đ<PERSON>h giá hiện trường */
@Entity('service_scene')
export class ServiceSceneEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** C<PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** <PERSON><PERSON>ch tính điểm theo loại càng cao càng tốt */
  @Column({
    nullable: false,
    default: true,
  })
  isCalUp: boolean

  /** <PERSON><PERSON><PERSON> dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  percent: number

  /** % điều kiện đạt tỉ trọng */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentRule: number

  /** % điều kiện liệt tỉ trọng khi tính theo chiều giảm dần */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentDownRule: number

  /** Cấp độ */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** Mô tả */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => ServiceSceneEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: ServiceSceneEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => ServiceSceneEntity, (p) => p.parent)
  childs: Promise<ServiceSceneEntity[]>

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Giá trị nhỏ nhất */
  @Column({
    nullable: true,
  })
  requiredMin: number

  /** Thuộc tính của tiêu chí thể hệ Doanh nghiệp sẽ được highlight màu xanh nếu đạt giá trị X khi xếp hạng năng lực */
  @Column({
    nullable: false,
    default: false,
  })
  isHighlight: boolean

  @Column({
    nullable: true,
  })
  hightlightValue: number

  @Column({ type: 'varchar', nullable: true })
  serviceId?: string
  @ManyToOne(() => ServiceEntity, (p) => p.scenes)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service?: Promise<ServiceEntity>

  @Column({ type: 'varchar', nullable: true })
  exMatGroupId?: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.capacities)
  @JoinColumn({ name: 'exMatGroupId', referencedColumnName: 'id' })
  exMatGroup?: Promise<ExternalMaterialGroupEntity>

  /** Con - 1 công thức sẽ có thể có nhiều detail list */
  @OneToMany(() => ServiceSceneListDetailEntity, (p) => p.serviceScene)
  serviceSceneListDetails: Promise<ServiceSceneListDetailEntity[]>
}
