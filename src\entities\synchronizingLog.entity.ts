import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

@Entity('synchronizing_log')
export class SynchronizingLogEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  apiEndpoint: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  apiStatus: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  messageType: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  message: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  error: string

  /** dataJson */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  dataJson: string

  /** Id của từng object tương ứng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  datId: string

  /** Tên từng entity */
  @Column({
    length: 250,
    nullable: true,
    type: 'varchar',
  })
  entityName: string

  /** URL SYNC */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  urlSync: string

  /** URL SYNC */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  urlSend: string
}
