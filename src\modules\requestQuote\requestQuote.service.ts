import { Injectable } from '@nestjs/common'
import {
  ActionLogRepository,
  CompanyRepository,
  IncotermRepository,
  MaterialGroupRepository,
  PaymentTermRepository,
  PlantRepository,
  PrRepository,
  RequestQuoteRepository,
  RequestQuoteSupplierRepository,
} from '../../repositories'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { CREATE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { In, IsNull, Like } from 'typeorm'
import { v4 as uuidv4 } from 'uuid'
import { coreHelper } from '../../helpers'
import * as moment from 'moment'
import { customAlphabet } from 'nanoid'
import {
  ActionLogEntity,
  MediaFileEntity,
  PermissionApproveEntity,
  RequestQuoteDetailEntity,
  RequestQuoteEntity,
  RequestQuoteFeeEntity,
  RequestQuoteSupplierEntity,
} from '../../entities'
import { RequestQuoteCreateDto, RequestQuoteUpdateDto } from './dto'
import { FlowApproveService } from '../flowApprove/flowApprove.service'
import { RoleDataPermission } from '../../constants/permission'
import { EmailService } from '../email/email.service'

@Injectable()
export class RequestQuoteService {
  constructor(
    private repo: RequestQuoteRepository,
    private prRepo: PrRepository,
    private companyRepo: CompanyRepository,
    private plantRepo: PlantRepository,
    private flowService: FlowApproveService,
    private actionLogRepo: ActionLogRepository,
    private requestQuoteSupplierRepo: RequestQuoteSupplierRepository,
    private incotermRepo: IncotermRepository,
    private emailService: EmailService,
    private materialGroupRepo: MaterialGroupRepository,
    private paymentTermRepo: PaymentTermRepository,
  ) {}

  async createData(user: UserDto, data: RequestQuoteCreateDto) {
    const nanoid = customAlphabet('QWERTYUIOPASDFGHJKLZXCVBNM', 6)
    if (data.prId) {
      const checkPR = await this.prRepo.findOne({ where: { id: data.prId, isDeleted: false } })
      if (!checkPR) throw new Error(`PR không tồn tại. Vui lòng kiểm tra lại`)
    }
    if (data.companyId) {
      const checkCompany = await this.companyRepo.findOne({ where: { id: data.companyId, isDeleted: false } })
      if (!checkCompany) throw new Error(`Công ty không tồn tại. Vui lòng kiểm tra lại`)
    }
    const lstPlant = data.lstDetail.filter((x) => x.plantId).map((x) => x.plantId)
    const checkPlant = await this.plantRepo.findOne({ where: { id: In(lstPlant), isDeleted: false } })
    if (!checkPlant) throw new Error(`Nhà máy không tồn tại. Vui lòng kiểm tra lại`)

    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const repo = trans.getRepository(RequestQuoteEntity)
      const detailRepo = trans.getRepository(RequestQuoteDetailEntity)
      const requestQuoteSupplierRepo = trans.getRepository(RequestQuoteSupplierEntity)
      const mediaRepo = trans.getRepository(MediaFileEntity)
      const requestQuoteFeeRepo = trans.getRepository(RequestQuoteFeeEntity)
      const entity = new RequestQuoteEntity()
      entity.code = nanoid()
      entity.name = data.name
      entity.companyId = data.companyId || user.companyId
      entity.purchasingGroupId = data.purchasingGroupId
      entity.purchasingOrgId = data.purchasingOrgId
      entity.employeeId = data.employeeId
      entity.purpose = data.purpose
      entity.typeQuote = data.typeQuote
      entity.quotationPeriod = data.quotationPeriod
      entity.timeStartReceiving = data.timeStartReceiving
      entity.timeEndReceiving = data.timeEndReceiving
      entity.quantity = data.quantity
      entity.description = data.description
      entity.referenceQuote = data.referenceQuote
      entity.shipmentPlanId = data.shipmentPlanId
      entity.prId = data.prId
      if (data.lstIncotermId) entity.lstIncotermId = data.lstIncotermId.join()
      if (data.lstPaymentTermId) entity.lstPaymentTermId = data.lstPaymentTermId.join()
      entity.quotationForm = data.quotationForm
      entity.quotationValidity = data.quotationValidity
      entity.status = data.status
      entity.createdAt = new Date()
      entity.createdBy = user.id
      entity.id = uuidv4()
      entity.timeConfirmParticipationQuotation = data.timeConfirmParticipationQuotation
      entity.plantId = data.plantId
      entity.shipmentFeeConditionTypeCompactId = data.lstFee.map((item: any) => item.id) || []
      entity.shipmentFeeConditionTypeCompactCode = data.lstFee.map((item: any) => item.code) || []
      entity.shipmentFeeConditionTypeCompactValue = data.lstFee.map((item: any) => item.shipmentFeeConditionsListId) || []

      await repo.insert(entity)
      if (data.lstDetail && data.lstDetail.length > 0) {
        for (let item of data.lstDetail) {
          const detail = new RequestQuoteDetailEntity()
          detail.requestQuoteId = entity.id
          detail.itemNo = item.itemNo
          detail.assetCode = item.assetCode
          detail.category = item.category
          detail.materialId = item.materialId
          detail.materialGroupId = item.materialGroupId
          detail.shortText = item.shortText
          detail.deliveryDate = item.deliveryDate
          detail.orderCode = item.orderCode
          detail.plantId = item.plantId
          detail.quantity = item.quantity
          detail.assetDesc = item.assetDesc
          detail.sloc = item.sloc
          detail.prItemId = item.prItemId || item.id
          detail.orderName = item.orderName
          detail.createdAt = new Date()
          detail.createdBy = user.id
          detail.externalMaterialGroupId = item.externalMaterialGroupId
          detail.unitId = item.unitId
          await detailRepo.insert(detail)
        }
      }

      if (data.lstFileList && data.lstFileList.length > 0) {
        for (let item of data.lstFileList) {
          const newImg = new MediaFileEntity()
          newImg.requestQuoteId = entity.id
          newImg.fileUrl = item.fileUrl
          newImg.fileName = item.fileName
          newImg.createdAt = new Date()
          newImg.createdBy = user.id
          await mediaRepo.insert(newImg)
        }
      }

      if (data.lstFee && data.lstFee.length > 0) {
        for (let item of data.lstFee) {
          const newItem = new RequestQuoteFeeEntity()
          newItem.requestQuoteId = entity.id
          newItem.shipmentFeeConditionsId = item.id
          newItem.shipmentFeeConditionsToListId = item.shipmentFeeConditionsToListId
          newItem.shipmentFeeConditionsListId = item.shipmentFeeConditionsListId
          newItem.createdAt = new Date()
          newItem.createdBy = user.id
          newItem.checked = item.checked
          await requestQuoteFeeRepo.insert(newItem)
        }
      }

      if (data.lstSupplier && data.lstSupplier.length > 0) {
        for (let item of data.lstSupplier) {
          const newItem = new RequestQuoteSupplierEntity()
          newItem.requestQuoteId = entity.id
          newItem.supplierId = item.supplierId
          newItem.createdAt = new Date()
          newItem.createdBy = user.id
          newItem.serviceId = item.serviceId
          newItem.externalMaterialGroupId = item.externalMaterialGroupId
          newItem.status = enumData.SupplierRequestStatus.ChoXacNhan.code
          await requestQuoteSupplierRepo.insert(newItem)
        }
      }

      await this.repo.saveWithLog(
        entity,
        { reload: true },
        {
          isLog: true,
          user: user,
          manager: trans,
          managerEntity: RequestQuoteEntity.name,
          isSaveJson: true,
          textLog: `Nhân viên [${user.username}] vừa [Tạo mới]  yêu cầu báo giá có mã là [${entity.code}]`,
        },
      )

      await this.flowService.setRoleRule(user, {
        targetId: entity.id,
        target: entity,
        entityName: RequestQuoteEntity.name,
        flowType: enumData.FlowCode.REQUEST_QUOTE.code,
        type: enumData.FlowAppoveType.None.code,
      })
    })
    return { message: CREATE_SUCCESS }
  }

  async updateData(user: UserDto, data: RequestQuoteUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    if (data.prId) {
      const checkPR = await this.prRepo.findOne({ where: { id: data.prId, isDeleted: false } })
      if (!checkPR) throw new Error(`PR không tồn tại. Vui lòng kiểm tra lại`)
    }
    if (data.companyId) {
      const checkCompany = await this.companyRepo.findOne({ where: { id: data.companyId, isDeleted: false } })
      if (!checkCompany) throw new Error(`Công ty không tồn tại. Vui lòng kiểm tra lại`)
    }
    const lstPlant = data.lstDetail.filter((x) => x.plantId).map((x: any) => x.plantId)
    const checkPlant = await this.plantRepo.findOne({ where: { id: In(lstPlant), isDeleted: false } })
    if (!checkPlant) throw new Error(`Nhà máy không tồn tại. Vui lòng kiểm tra lại`)

    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const repo = trans.getRepository(RequestQuoteEntity)
      const detailRepo = trans.getRepository(RequestQuoteDetailEntity)
      const requestQuoteSupplierRepo = trans.getRepository(RequestQuoteSupplierEntity)
      const mediaRepo = trans.getRepository(MediaFileEntity)
      const requestQuoteFeeRepo = trans.getRepository(RequestQuoteFeeEntity)
      const permissionApproveRepo = trans.getRepository(PermissionApproveEntity)
      entity.name = data.name
      entity.companyId = data.companyId || user.companyId
      entity.purchasingGroupId = data.purchasingGroupId
      entity.purchasingOrgId = data.purchasingOrgId
      entity.employeeId = data.employeeId
      entity.purpose = data.purpose
      entity.typeQuote = data.typeQuote
      entity.quotationPeriod = data.quotationPeriod
      entity.timeStartReceiving = data.timeStartReceiving
      entity.timeEndReceiving = data.timeEndReceiving
      entity.quantity = data.quantity
      entity.description = data.description
      entity.referenceQuote = data.referenceQuote
      entity.shipmentPlanId = data.shipmentPlanId
      entity.prId = data.prId
      if (data.lstIncotermId) entity.lstIncotermId = data.lstIncotermId.join()
      if (data.lstPaymentTermId) entity.lstPaymentTermId = data.lstPaymentTermId.join()
      entity.quotationForm = data.quotationForm
      entity.quotationValidity = data.quotationValidity
      entity.status = data.status
      entity.createdAt = new Date()
      entity.createdBy = user.id
      entity.timeConfirmParticipationQuotation = data.timeConfirmParticipationQuotation
      entity.shipmentFeeConditionTypeCompactId = data.lstFee.map((item: any) => item.id) || []
      entity.shipmentFeeConditionTypeCompactCode = data.lstFee.map((item: any) => item.code) || []
      entity.shipmentFeeConditionTypeCompactValue = data.lstFee.map((item: any) => item.shipmentFeeConditionsListId) || []
      entity.plantId = data.plantId
      await detailRepo.delete({ requestQuoteId: entity.id })
      if (data.lstDetail && data.lstDetail.length > 0) {
        for (let item of data.lstDetail) {
          const detail = new RequestQuoteDetailEntity()
          detail.requestQuoteId = entity.id
          detail.itemNo = item.itemNo
          detail.assetCode = item.assetCode
          detail.category = item.category
          detail.materialId = item.materialId
          detail.materialGroupId = item.materialGroupId
          detail.shortText = item.shortText
          detail.deliveryDate = item.deliveryDate
          detail.orderCode = item.orderCode
          detail.plantId = item.plantId
          detail.quantity = item.quantity
          detail.assetDesc = item.assetDesc
          detail.sloc = item.sloc
          detail.prItem = item.prItem
          detail.prItemId = item.prItemId
          detail.orderName = item.orderName
          detail.createdAt = new Date()
          detail.createdBy = user.id
          detail.externalMaterialGroupId = item.externalMaterialGroupId
          detail.unitId = item.unitId

          await detailRepo.insert(detail)
        }
      }
      await mediaRepo.delete({ requestQuoteId: entity.id })
      if (data.lstFileList && data.lstFileList.length > 0) {
        for (let item of data.lstFileList) {
          const newImg = new MediaFileEntity()
          newImg.requestQuoteId = entity.id
          newImg.fileUrl = item.fileUrl
          newImg.fileName = item.fileName
          newImg.createdAt = new Date()
          newImg.createdBy = user.id
          await mediaRepo.insert(newImg)
        }
      }
      await requestQuoteFeeRepo.delete({ requestQuoteId: entity.id })
      if (data.lstFee && data.lstFee.length > 0) {
        for (let item of data.lstFee) {
          const newItem = new RequestQuoteFeeEntity()
          newItem.requestQuoteId = entity.id
          newItem.shipmentFeeConditionsId = item.shipmentFeeConditionsId
          newItem.shipmentFeeConditionsToListId = item.shipmentFeeConditionsToListId
          newItem.shipmentFeeConditionsListId = item.shipmentFeeConditionsListId
          newItem.createdAt = new Date()
          newItem.createdBy = user.id
          newItem.checked = item.checked
          await requestQuoteFeeRepo.insert(newItem)
        }
      }
      await requestQuoteSupplierRepo.delete({ requestQuoteId: entity.id })
      if (data.lstSupplier && data.lstSupplier.length > 0) {
        for (let item of data.lstSupplier) {
          const newItem = new RequestQuoteSupplierEntity()
          newItem.requestQuoteId = entity.id
          newItem.supplierId = item.supplierId
          newItem.createdAt = new Date()
          newItem.createdBy = user.id
          newItem.serviceId = item.serviceId
          newItem.externalMaterialGroupId = item.externalMaterialGroupId
          newItem.status = enumData.SupplierRequestStatus.ChoXacNhan.code
          await requestQuoteSupplierRepo.insert(newItem)
        }
      }

      await repo.save(entity)

      const checkFlow = await permissionApproveRepo.findOne({ where: { targetId: entity.id, isDeleted: false } })
      if (!checkFlow) {
        await this.flowService.setRoleRule(user, {
          targetId: entity.id,
          target: entity,
          entityName: RequestQuoteEntity.name,
          flowType: enumData.FlowCode.REQUEST_QUOTE.code,
          type: enumData.FlowAppoveType.None.code,
        })
      }

      await this.repo.saveWithLog(
        entity,
        { reload: true },
        {
          isLog: true,
          user: user,
          manager: trans,
          managerEntity: RequestQuoteEntity.name,
          isSaveJson: true,
          textLog: `Nhân viên [${user.username}] vừa [Cập nhật]  yêu cầu báo giá có mã là [${entity.code}]`,
        },
      )
    })
    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.companyId) whereCon.companyId = data.where.companyId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.name) whereCon.code = Like(`%${data.where.name}%`)
    if (data.where.companyCode || data.where.companyName) {
      whereCon.company = {}
      whereCon.company.code = Like(`%${data.where.companyCode}%`)
      whereCon.company.name = Like(`%${data.where.companyName}%`)
    }

    if (data.where.employeeCode || data.where.employeeName) {
      whereCon.employee = {}
      whereCon.employee.code = Like(`%${data.where.employeeCode}%`)
      whereCon.employee.name = Like(`%${data.where.employeeName}%`)
    }

    if (data.where.purchasingGroupCode || data.where.purchasingGroupName) {
      whereCon.purchasingGroup = {}
      whereCon.purchasingGroup.code = Like(`%${data.where.purchasingGroupCode}%`)
      whereCon.purchasingGroup.name = Like(`%${data.where.purchasingGroupName}%`)
    }

    if (data.where.purchasingOrgCode || data.where.purchasingOrgName) {
      whereCon.purchasingOrg = {}
      whereCon.purchasingOrg.code = Like(`%${data.where.purchasingOrgCode}%`)
      whereCon.purchasingOrg.name = Like(`%${data.where.purchasingOrgName}%`)
    }
    if (data.where.purpose) whereCon.purpose = data.where.purpose
    if (data.where.status) whereCon.status = data.where.status
    if (data.where.quotationPeriod) whereCon.quotationPeriod = data.where.quotationPeriod
    if (data.where.typeQuote) whereCon.typeQuote = data.where.typeQuote

    if (data.where.timeStartReceiving?.length > 0) {
      coreHelper.getFilterBetweenDateArrange(whereCon, 'timeStartReceiving', data.where.timeStartReceiving)
    }

    if (data.where.timeEndReceiving?.length > 0) {
      coreHelper.getFilterBetweenDateArrange(whereCon, 'timeEndReceiving', data.where.timeEndReceiving)
    }

    if (data.where.timeConfirmParticipationQuotation?.length > 0) {
      coreHelper.getFilterBetweenDateArrange(whereCon, 'timeConfirmParticipationQuotation', data.where.timeConfirmParticipationQuotation)
    }

    const res: any[] = await this.repo.findAndCount(
      {
        where: whereCon,
        order: { createdAt: 'DESC', code: 'DESC' },
        skip: data.skip,
        take: data.take,
        relations: { company: true, purchasingOrg: true, purchasingGroup: true, employee: true },
      },
      user,
      RoleDataPermission.PriceQuoteList.code,
    )

    if (res[0].length == 0) return [[], 0]

    for (let item of res[0]) {
      // Kiểm tra quyền duyệt
      item.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
        lsType: [enumData.FlowCode.REQUEST_QUOTE.code],
      })

      item.companyName = item.__company__?.name
      item.purchasingOrgName = item.__purchasingOrg__?.name
      item.purchasingGroupName = item.__purchasingGroup__?.name

      item.companyCode = item.__company__?.code
      item.purchasingOrgCode = item.__purchasingOrg__?.code
      item.purchasingGroupCode = item.__purchasingGroup__?.code

      item.statusColor = enumData.RequestStatus[item.status]?.color
      item.statusBgColor = enumData.RequestStatus[item.status]?.bgColor
      item.statusBorderColor = enumData.RequestStatus[item.status]?.borderColor
      item.statusName = enumData.RequestStatus[item.status]?.name

      item.purposeQuoteName = enumData.PurposeQuote[item.purpose]?.name
      item.typeQuoteName = enumData.TypeQuote[item.typeQuote]?.name
      item.quotationPeriodName = enumData.QuotationPeriod[item.quotationPeriod]?.name
      item.quotationFormName = enumData.QuotationForm[item.quotationForm]?.name

      item.employeeCode = item.__employee__?.code
      item.employeeName = item.__employee__?.name
      item.permission = await this.repo.getRowRole(user, item, RoleDataPermission.PriceQuoteList.code)
      const revertOption = await this.flowService.checkCanRemoveApprove(user, {
        targetId: item.id,
        entityName: RequestQuoteEntity.name,
        type: enumData.FlowCode.REQUEST_QUOTE.code,
      })
      item.canRevert = revertOption.canRevert
      // Kiểm tra trạng thái phê duyệt
      const { canApprove, approvalProgress, level, maxLevel } = await this.flowService.getRoleApprove(user, {
        targetId: item.id,
        entityName: RequestQuoteEntity.name,
        type: enumData.FlowCode.REQUEST_QUOTE.code,
      })
      Object.assign(item, { approvalProgress, canApprove, level, maxLevel })

      item.isCanApprove = Object.values(item.objPermissionApprove).some((value) => value)
      item.isCreatedItem = item.createdBy === user.id

      item.isShowReport = moment().isAfter(moment(item.timeEndReceiving))

      delete item.__employee__
      delete item.__company__
      delete item.__purchasingGroup__
      delete item.__purchasingOrg__
    }

    return res
  }

  async loadDetail(user: UserDto, data: FilterOneDto) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: {
        mediaFiles: true,
        requestQuoteDetails: { material: true, materialGroup: true, externalMaterialGroup: true, unit: true, plant: true },
        requestQuoteFees: true,
        requestQuoteSuppliers: { supplier: true, externalMaterialGroup: true },
        company: true,
        purchasingOrg: true,
        purchasingGroup: true,
        employee: true,
        pr: true,
      },
    })
    res.prCode = res.__pr__?.code
    delete res.__pr__

    res.companyName = res.__company__?.name
    res.purchasingOrgName = res.__purchasingOrg__?.name
    res.purchasingGroupName = res.__purchasingGroup__?.name

    res.companyCode = res.__company__?.code
    res.purchasingOrgCode = res.__purchasingOrg__?.code
    res.purchasingGroupCode = res.__purchasingGroup__?.code

    res.statusColor = enumData.RequestStatus[res.status]?.color
    res.statusBgColor = enumData.RequestStatus[res.status]?.bgColor
    res.statusBorderColor = enumData.RequestStatus[res.status]?.borderColor
    res.statusName = enumData.RequestStatus[res.status]?.name

    res.purposeQuoteName = enumData.PurposeQuote[res.purpose]?.name
    res.typeQuoteName = enumData.TypeQuote[res.typeQuote]?.name
    res.quotationPeriodName = enumData.QuotationPeriod[res.quotationPeriod]?.name
    res.quotationFormName = enumData.QuotationForm[res.quotationForm]?.name

    for (let item of res.__requestQuoteDetails__) {
      item.lstMaterial = [{ id: item.materialId, name: item.__material__?.name, code: item.__material__?.code }]
      item.lstPlant = [{ id: item.plantId, name: item.__plant__?.name, code: item.__plant__?.code }]
      item.lstMatGroup = [{ id: item.materialGroupId, name: item.__materialGroup__?.name, code: item.__materialGroup__?.code }]
      item.externalMaterialGroupName = item.__externalMaterialGroup__?.name
      item.lstUnit = [{ id: item.unitId, name: item.__unit__?.name, code: item.__unit__?.code }]
      item.lstExternalMatGroup = [
        { id: item.externalMaterialGroupId, name: item.__externalMaterialGroup__?.name, code: item.__externalMaterialGroup__?.code },
      ]

      item.materialCode = item.__material__?.code
      item.shortText = item.__material__?.name
      item.unitCode = item.__unit__?.code

      delete item.__material__
      delete item.__unit__
      delete item.__plant__
      delete item.__materialGroup__
    }

    res.__mediaFiles__.map((file: any) => {
      return {
        uid: file.uid,
        name: file.name ?? file.fileName,
        fileUrl: file.fileUrl,
        fileName: file.name ?? file.fileName,
        dataType: file.type ?? file.dataType,
      }
    })

    res.lstFileList = res?.__mediaFiles__ || []
    res.lstDetail = res?.__requestQuoteDetails__ || []
    res.lstFee = res?.__requestQuoteFees__ || []

    for (let item of res.__requestQuoteSuppliers__) {
      item.supplierName = item.__supplier__?.name
      item.supplierCode = item.__supplier__?.code
      item.checked = true
      item.name = item.__externalMaterialGroup__?.name
      item.externalMaterialGroupName = item.__externalMaterialGroup__?.name
      item.externalMaterialGroupCode = item.__externalMaterialGroup__?.code
      delete item.__supplier__
      delete item.__externalMaterialGroup__
    }

    if (res.lstIncotermId) {
      res.lstIncotermId = res.lstIncotermId.split(',')
    } else {
      res.lstIncotermId = []
    }

    if (res.lstPaymentTermId) {
      res.lstPaymentTermId = res.lstPaymentTermId.split(',')
    } else {
      res.lstPaymentTermId = []
    }

    const lstHistory = await this.actionLogRepo.find({ where: { targetId: res.id, entityName: RequestQuoteEntity.name } })
    res.lstHistory = lstHistory
    res.permission = await this.repo.getRowRole(user, res, RoleDataPermission.PriceQuoteList.code)

    res.lstSupplier = res?.__requestQuoteSuppliers__ || []

    if (res.createdBy === user.id) res.isCreatedItem = true
    res.objPermissionApprove = await this.flowService.checkCanApproveByType(user, { lsType: [enumData.FlowCode.REQUEST_QUOTE.code] })
    const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
      targetId: res.id,
      entityName: RequestQuoteEntity.name,
      type: enumData.FlowCode.REQUEST_QUOTE.code,
    })
    Object.assign(res, { approvalProgress, canApprove })
    res.isCanApprove = Object.values(res.objPermissionApprove).some((value) => value)

    res.lstApprovalProgress = await this.flowService.getListApproveDetail(user, {
      targetId: res.id,
      entityName: RequestQuoteEntity.name,
      type: enumData.FlowCode.REQUEST_QUOTE.code,
    })

    if (user.supplierId) {
      const itemStatus = res.__requestQuoteSuppliers__.find((x) => x.supplierId === user.supplierId)

      if (itemStatus.status === enumData.SupplierRequestStatus.XacNhan.code) {
        res.isShowButtonConfirm = false
      } else if (moment(res.timeConfirmParticipationQuotation).isBefore(moment())) {
        res.isShowButtonConfirm = false
      } else {
        res.isShowButtonConfirm = true
      }
    }
    res.employeeCode = res.__employee__?.code
    res.employeeName = res.__employee__?.name
    res.employeePhone = res.__employee__?.phone
    res.employeeEmail = res.__employee__?.email
    delete res?.__mediaFiles__
    delete res?.__requestQuoteDetails__
    delete res?.__requestQuoteSuppliers__
    delete res?.__requestQuoteFees__

    delete res.__employee__
    delete res.__company__
    delete res.__purchasingGroup__
    delete res.__purchasingOrg__
    return res
  }

  public async updateCancel(user: UserDto, data: FilterOneDto) {
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(RequestQuoteEntity)
      const entity = await repo.findOne({ where: { id: data.id, isDeleted: false } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
      if (entity.status !== enumData.RequestStatus.N.code) {
        throw new Error(` Trạng thái yêu cầu báo giá không phù hợp để hủy. Vui lòng kiểm tra lại`)
      }

      entity.status = enumData.RequestStatus.C.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await repo.save(entity)

      await this.repo.saveWithLog(
        entity,
        { reload: true },
        {
          isLog: true,
          user: user,
          manager: transac,
          managerEntity: RequestQuoteEntity.name,
          isSaveJson: true,
          textLog: `Nhân viên [${user.username}] vừa [Cập nhật] hủy yêu cầu báo giá có mã là [${entity.code}]`,
        },
      )
    })

    return { message: UPDATE_SUCCESS }
  }

  public async updateWaitApproved(user: UserDto, data: FilterOneDto) {
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(RequestQuoteEntity)
      const entity = await repo.findOne({ where: { id: data.id, isDeleted: false } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.RequestStatus.N.code) {
        throw new Error(` Trạng thái quản lý báo giá không phù hợp để chờ duyệt. Vui lòng kiểm tra lại`)
      }

      entity.status = enumData.RequestStatus.W_A.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await repo.save(entity)

      await this.repo.saveWithLog(
        entity,
        { reload: true },
        {
          isLog: true,
          user: user,
          manager: transac,
          managerEntity: RequestQuoteEntity.name,
          isSaveJson: true,
          textLog: `Nhân viên [${user.username}] vừa [Cập nhật] chờ duyệt quản lý báo giá có mã là [${entity.code}]`,
        },
      )
    })

    return { message: UPDATE_SUCCESS }
  }

  public async updateApproved(user: UserDto, data: FilterOneDto) {
    const approveStatus = await this.flowService.approveRule(user, { targetId: data.id, entityName: RequestQuoteEntity.name })
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(RequestQuoteEntity)

      const entity: any = await repo.findOne({
        where: { id: data.id, isDeleted: false },
        relations: { requestQuoteSuppliers: true },
      })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      // Nếu chưa phải lần duyệt cuối
      if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
        let description = 'Tài Khoản [' + user.username + '] vừa cập nhật duyệt quản lý báo giá có mã [' + entity.code + '] '
        description += `<br/>  quản lý báo giá được duyệt, Vui lòng chờ cấp sau duyệt `
        await this.repo.saveWithLog(
          entity,
          { reload: true },
          {
            isLog: true,
            user: user,
            manager: transac,
            managerEntity: RequestQuoteEntity.name,
            isSaveJson: true,
            textLog: description,
          },
        )

        return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
      }
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      entity.isApprovedDone = true
      entity.status = enumData.RequestStatus.A.code

      for (let item of await entity.__requestQuoteSuppliers__) {
        await this.emailService.GuiThongBaoChaoGia(item.supplierId, item.requestQuoteId)
      }

      await this.repo.save(entity, {}, transac)

      let description = 'Tài Khoản [' + user.username + '] vừa cập nhật duyệt quản lý báo giá có mã [' + entity.code + '] '
      description += `<br/>  quản lý báo giá được duyệt, Chuyển trạng thái mới cho quản lý báo giá `
      await this.repo.saveWithLog(
        entity,
        { reload: true },
        {
          isLog: true,
          user: user,
          manager: transac,
          managerEntity: RequestQuoteEntity.name,
          isSaveJson: true,
          textLog: description,
        },
      )
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async updateRemoveRule(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const rejectStatus = await this.flowService.revertApprove(user, {
      targetId: data.id,
      entityName: RequestQuoteEntity.name,
      type: enumData.FlowCode.REQUEST_QUOTE.code,
    })
    if (rejectStatus.isFist) {
      if (entity.status !== enumData.RequestStatus.W_A.code) {
        throw new Error(` Trạng thái quản lý báo giá không phù hợp để từ chối duyệt. Vui lòng kiểm tra lại`)
      }
      entity.status = enumData.RequestStatus.N.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await this.repo.save(entity)
    }

    if (rejectStatus) {
      await this.repo.saveWithLog(
        entity,
        { reload: true },
        {
          isLog: true,
          user: user,
          managerEntity: RequestQuoteEntity.name,
          isSaveJson: true,
          textLog: `Tài Khoản ${user.username} vừa cập nhật từ chối duyệt quản lý báo giá của quản lý báo giá với mã ${entity.code}`,
        },
      )
    }
    return { message: UPDATE_SUCCESS }
  }

  public async updateRejectRule(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const rejectStatus = await this.flowService.rejectRulePR(user, { targetId: data.id, entityName: RequestQuoteEntity.name, level: data.level })
    if (rejectStatus.status === 200) {
      entity.status = enumData.RequestStatus.R.code
      entity.updatedAt = new Date()
      entity.updatedBy = user.employeeId
      await this.repo.save(entity)
      await this.repo.saveWithLog(
        entity,
        { reload: true },
        {
          isLog: true,
          user: user,
          managerEntity: RequestQuoteEntity.name,
          isSaveJson: true,
          textLog: `Tài Khoản ${user.username} vừa cập nhật từ chối duyệt quản lý báo giá của quản lý báo giá với mã ${entity.code}`,
        },
      )
    }
    return { message: UPDATE_SUCCESS }
  }

  public async updateRevertStatus(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    await this.flowService.rejectRejectRulePR(user, { targetId: data.id, entityName: RequestQuoteEntity.name, level: data.level })

    if (entity.status !== enumData.RequestStatus.R.code) {
      throw new Error('Trạng thái không phù hợp. Vui lòng kiểm tra lại')
    }
    entity.status = enumData.RequestStatus.W_A.code
    entity.updatedBy = user.id
    entity.updatedAt = new Date()
    await this.repo.save(entity)
    await this.repo.saveWithLog(
      entity,
      { reload: true },
      {
        isLog: true,
        user: user,
        managerEntity: RequestQuoteEntity.name,
        isSaveJson: true,
        textLog: `Tài Khoản ${user.username} vừa cập nhật gỡ từ chối duyệt quản lý báo giá của quản lý báo giá với mã ${entity.code}`,
      },
    )
    return { message: UPDATE_SUCCESS }
  }

  public async findListApproved(user: UserDto, data: FilterOneDto) {
    const entity: any = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    let approvalProgress: any = await this.flowService.getListApproveDetail(user, {
      targetId: entity.id,
      entityName: RequestQuoteEntity.name,
      type: enumData.FlowCode.REQUEST_QUOTE.code,
    })

    if (approvalProgress.length === 0) {
      approvalProgress = await this.flowService.getListApproveDetail(user, {
        targetId: entity.requestQuoteParentId,
        entityName: RequestQuoteEntity.name,
        type: enumData.FlowCode.REQUEST_QUOTE.code,
      })
    }

    return approvalProgress
  }

  public async paginationClient(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.companyId) whereCon.companyId = data.where.companyId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.name) whereCon.code = Like(`%${data.where.name}%`)
    if (data.where.companyCode || data.where.companyName) {
      whereCon.company = {}
      whereCon.company.code = Like(`%${data.where.companyCode}%`)
      whereCon.company.name = Like(`%${data.where.companyName}%`)
    }

    if (data.where.employeeCode || data.where.employeeName) {
      whereCon.employee = {}
      whereCon.employee.code = Like(`%${data.where.employeeCode}%`)
      whereCon.employee.name = Like(`%${data.where.employeeName}%`)
    }

    if (data.where.purchasingGroupCode || data.where.purchasingGroupName) {
      whereCon.purchasingGroup = {}
      whereCon.purchasingGroup.code = Like(`%${data.where.purchasingGroupCode}%`)
      whereCon.purchasingGroup.name = Like(`%${data.where.purchasingGroupName}%`)
    }

    if (data.where.purchasingOrgCode || data.where.purchasingOrgName) {
      whereCon.purchasingOrg = {}
      whereCon.purchasingOrg.code = Like(`%${data.where.purchasingOrgCode}%`)
      whereCon.purchasingOrg.name = Like(`%${data.where.purchasingOrgName}%`)
    }
    if (data.where.purpose) whereCon.purpose = data.where.purpose

    if (data.where.quotationPeriod) whereCon.quotationPeriod = data.where.quotationPeriod
    if (data.where.typeQuote) whereCon.typeQuote = data.where.typeQuote

    whereCon.requestQuoteSuppliers = {}
    whereCon.requestQuoteSuppliers.supplierId = user.supplierId
    whereCon.requestQuoteSuppliers.status = In([enumData.SupplierRequestStatus.ChoXacNhan.code, enumData.SupplierRequestStatus.XacNhan.code])

    whereCon.status = enumData.RequestStatus.A.code

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC', code: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: { company: true, purchasingOrg: true, purchasingGroup: true, employee: true },
    })

    if (res[0].length == 0) return [[], 0]

    const lstData = await this.requestQuoteSupplierRepo.find({
      where: { requestQuoteId: In(coreHelper.selectDistinct(res[0], 'id')), isDeleted: false, supplierId: user.supplierId },
      select: { id: true, status: true, requestQuoteId: true },
    })
    const dictStatus = Object.fromEntries(lstData.map((item) => [item.requestQuoteId, item]))

    for (let item of res[0]) {
      item.companyName = item.__company__?.name
      item.purchasingOrgName = item.__purchasingOrg__?.name
      item.purchasingGroupName = item.__purchasingGroup__?.name

      item.companyCode = item.__company__?.code
      item.purchasingOrgCode = item.__purchasingOrg__?.code
      item.purchasingGroupCode = item.__purchasingGroup__?.code

      item.statusColor = enumData.RequestStatus[item.status]?.color
      item.statusBgColor = enumData.RequestStatus[item.status]?.bgColor
      item.statusBorderColor = enumData.RequestStatus[item.status]?.borderColor
      item.statusName = enumData.RequestStatus[item.status]?.name

      item.purposeQuoteName = enumData.PurposeQuote[item.purpose]?.name
      item.typeQuoteName = enumData.TypeQuote[item.typeQuote]?.name
      item.quotationPeriodName = enumData.QuotationPeriod[item.quotationPeriod]?.name
      item.quotationFormName = enumData.QuotationForm[item.quotationForm]?.name

      item.employeeCode = item.__employee__?.code
      item.employeeName = item.__employee__?.name

      if (dictStatus[item.id]?.status === enumData.SupplierRequestStatus.XacNhan.code) {
        item.isButtonCreateRFQ = true
      }

      delete item.__employee__
      delete item.__company__
      delete item.__purchasingGroup__
      delete item.__purchasingOrg__
    }

    return res
  }

  public async updateSuplierApproved(user: UserDto, data: FilterOneDto) {
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(RequestQuoteEntity)
      const requestQuoteSupplierRepo = transac.getRepository(RequestQuoteSupplierEntity)
      const entity = await repo.findOne({ where: { id: data.id, isDeleted: false } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.RequestStatus.A.code) {
        throw new Error(` Trạng thái quản lý báo giá không phù hợp để xác nhận. Vui lòng kiểm tra lại`)
      }

      if (moment(entity.timeConfirmParticipationQuotation).isBefore(moment())) {
        throw new Error(` Đã quá thời gian xác nhận báo giá. Vui lòng kiểm tra lại`)
      }

      const item = await requestQuoteSupplierRepo.findOne({ where: { requestQuoteId: entity.id, supplierId: user.supplierId } })
      if (item) {
        item.status = enumData.SupplierRequestStatus.XacNhan.code
        item.timeConfirmParticipationQuotation = new Date()
        item.updatedAt = new Date()
        item.updatedBy = user.id
        await requestQuoteSupplierRepo.save(item)
      }

      await this.repo.saveWithLog(
        entity,
        { reload: true },
        {
          isLog: true,
          user: user,
          manager: transac,
          managerEntity: RequestQuoteEntity.name,
          isSaveJson: true,
          textLog: `Nhà cung cấp [${user.supplierCode}] vừa [Cập nhật] tham gia quản lý báo giá có mã là [${entity.code}]`,
        },
      )
    })

    return { message: UPDATE_SUCCESS }
  }

  public async updateSuplierReject(user: UserDto, data: FilterOneDto) {
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(RequestQuoteEntity)
      const requestQuoteSupplierRepo = transac.getRepository(RequestQuoteSupplierEntity)
      const entity = await repo.findOne({ where: { id: data.id, isDeleted: false } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.RequestStatus.A.code) {
        throw new Error(` Trạng thái quản lý báo giá không phù hợp để xác nhận. Vui lòng kiểm tra lại`)
      }

      if (moment(entity.timeConfirmParticipationQuotation).isBefore(moment())) {
        throw new Error(` Đã quá thời gian xác nhận báo giá. Vui lòng kiểm tra lại`)
      }

      const item = await requestQuoteSupplierRepo.findOne({ where: { requestQuoteId: entity.id, supplierId: user.supplierId } })
      if (item) {
        item.status = enumData.SupplierRequestStatus.TuChoi.code
        item.timeRejectParticipationQuotation = new Date()
        item.updatedAt = new Date()
        item.updatedBy = user.id
        await requestQuoteSupplierRepo.save(item)
      }

      await this.repo.saveWithLog(
        entity,
        { reload: true },
        {
          isLog: true,
          user: user,
          manager: transac,
          managerEntity: RequestQuoteEntity.name,
          isSaveJson: true,
          textLog: `Nhà cung cấp [${user.supplierCode}] vừa [Cập nhật] từ chối tham gia báo giá có mã là [${entity.code}]`,
        },
      )
    })

    return { message: UPDATE_SUCCESS }
  }

  async loadDetailBySupplier(user: UserDto, data: FilterOneDto) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: {
        mediaFiles: true,
        requestQuoteDetails: { material: true, materialGroup: true, externalMaterialGroup: true, unit: true, plant: true },
        requestQuoteFees: true,
        requestQuoteSuppliers: { supplier: true, externalMaterialGroup: true },
        company: true,
        purchasingOrg: true,
        purchasingGroup: true,
        employee: true,
        pr: true,
      },
    })
    res.prCode = res.__pr__?.code
    delete res.__pr__

    if (user.supplierId) {
      const itemStatus = res.__requestQuoteSuppliers__.find((x) => x.supplierId === user.supplierId)

      if (itemStatus.status !== enumData.SupplierRequestStatus.XacNhan.code) {
        throw new Error(`Vui lòng xác nhận tham gia báo giá`)
      }
    }

    res.companyName = res.__company__?.name
    res.purchasingOrgName = res.__purchasingOrg__?.name
    res.purchasingGroupName = res.__purchasingGroup__?.name

    res.companyCode = res.__company__?.code
    res.purchasingOrgCode = res.__purchasingOrg__?.code
    res.purchasingGroupCode = res.__purchasingGroup__?.code

    res.purposeQuoteName = enumData.PurposeQuote[res.purpose]?.name

    const lstIncoterm = await this.incotermRepo.find({ where: { id: In(res.lstIncotermId.split(',')), isDeleted: false } })
    res.lstIncoterm = lstIncoterm

    for (let item of res.__requestQuoteDetails__) {
      item.lstMaterial = [{ id: item.materialId, name: item.__material__?.name, code: item.__material__?.code }]
      item.lstPlant = [{ id: item.plantId, name: item.__plant__?.name, code: item.__plant__?.code }]
      item.lstMatGroup = [{ id: item.materialGroupId, name: item.__materialGroup__?.name, code: item.__materialGroup__?.code }]
      item.externalMaterialGroupName = item.__externalMaterialGroup__?.name
      item.lstUnit = [{ id: item.unitId, name: item.__unit__?.name, code: item.__unit__?.code }]
      item.lstExternalMatGroup = [
        { id: item.externalMaterialGroupId, name: item.__externalMaterialGroup__?.name, code: item.__externalMaterialGroup__?.code },
      ]

      item.materialCode = item.__material__?.code
      item.shortText = item.__material__?.name
      item.unitCode = item.__unit__?.code

      delete item.__material__
      delete item.__unit__
      delete item.__plant__
      delete item.__materialGroup__
      delete item.__externalMaterialGroup__
      item.lstIncoterm = lstIncoterm
    }

    res.lstDetail = res?.__requestQuoteDetails__ || []
    res.lstFee = res?.__requestQuoteFees__ || []

    if (res.lstIncotermId) {
      res.lstIncotermId = res.lstIncotermId.split(',')
    } else {
      res.lstIncotermId = []
    }

    res.lstSupplier = res?.__requestQuoteSuppliers__ || []

    if (user.supplierId) {
      const itemStatus = res.__requestQuoteSuppliers__.find((x) => x.supplierId === user.supplierId)

      if (itemStatus.status === enumData.SupplierRequestStatus.XacNhan.code) {
        res.isShowButtonConfirm = false
      } else if (moment(res.timeConfirmParticipationQuotation).isBefore(moment())) {
        res.isShowButtonConfirm = false
      } else {
        res.isShowButtonConfirm = true
      }
    }
    res.employeeCode = res.__employee__?.code
    res.employeeName = res.__employee__?.name
    res.employeePhone = res.__employee__?.phone
    res.employeeEmail = res.__employee__?.email
    delete res?.__mediaFiles__
    delete res?.__requestQuoteDetails__
    delete res?.__requestQuoteSuppliers__
    delete res?.__requestQuoteFees__

    delete res.__employee__
    delete res.__company__
    delete res.__purchasingGroup__
    delete res.__purchasingOrg__
    return res
  }

  async autoCreateData() {
    const lstRequestQuote: any = await this.repo.find({
      where: { status: enumData.RequestStatus.A.code, requestQuoteParentId: IsNull(), typeQuote: enumData.TypeQuote.PERIODIC.code },
      relations: { requestQuoteDetails: true, requestQuoteFees: true, requestQuoteSuppliers: true, mediaFiles: true },
    })
    if (lstRequestQuote.length === 0) return
    const nanoid = customAlphabet('QWERTYUIOPASDFGHJKLZXCVBNM', 6)
    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const repo = trans.getRepository(RequestQuoteEntity)
      const detailRepo = trans.getRepository(RequestQuoteDetailEntity)
      const requestQuoteSupplierRepo = trans.getRepository(RequestQuoteSupplierEntity)
      const mediaRepo = trans.getRepository(MediaFileEntity)
      const requestQuoteFeeRepo = trans.getRepository(RequestQuoteFeeEntity)
      const actionLogRepo = trans.getRepository(ActionLogEntity)
      for (let data of lstRequestQuote) {
        const code = nanoid()
        const entity = new RequestQuoteEntity()
        entity.code = code
        entity.name = data.name
        entity.companyId = data.companyId
        entity.purchasingGroupId = data.purchasingGroupId
        entity.purchasingOrgId = data.purchasingOrgId
        entity.employeeId = data.employeeId
        entity.purpose = data.purpose
        entity.typeQuote = data.typeQuote
        entity.quotationPeriod = data.quotationPeriod

        const now = new Date()
        switch (data.quotationPeriod) {
          case 'DAY': {
            const next = new Date(now)
            next.setDate(next.getDate() + 1)
            next.setHours(8, 0, 0, 0) // 8:00 hôm nay
            data.timeStartReceiving = next

            const end = new Date(now)
            end.setDate(end.getDate() + 1)
            end.setHours(12, 0, 0, 0) // 12h00 hôm nay
            data.timeEndReceiving = end
            break
          }
          case 'WEEK': {
            const next = new Date(now)
            const dayDiff = (1 + 7 - next.getDay()) % 7 // đến thứ 2
            next.setDate(next.getDate() + dayDiff)
            next.setHours(8, 0, 0, 0)
            data.timeStartReceiving = next

            const end = new Date(now)
            const dayDiff1 = (1 + 7 - end.getDay()) % 7 // Thứ 2
            end.setDate(end.getDate() + dayDiff1)
            end.setHours(17, 0, 0, 0) // 17h00
            data.timeEndReceiving = end
            break
          }
          case 'MONTH': {
            const next = new Date(now.getFullYear(), now.getMonth(), 1, 8, 0, 0)
            if (now.getDate() > 1) next.setMonth(now.getMonth() + 1) // sang tháng sau nếu đã qua mùng 1
            data.timeStartReceiving = next

            const end = new Date(now.getFullYear(), now.getMonth(), 1, 17, 0, 0)
            if (now.getDate() > 1) end.setMonth(now.getMonth() + 1) // Sang tháng sau nếu đã qua mùng 1
            data.timeEndReceiving = end
            break
          }
          case 'YEAR': {
            const next = new Date(now.getFullYear(), 0, 1, 8, 0, 0)
            if (now > next) next.setFullYear(now.getFullYear() + 1) // sang năm sau nếu đã qua 01/01
            data.timeStartReceiving = next

            const end = new Date(now.getFullYear(), 0, 1, 17, 0, 0)
            if (now > end) end.setFullYear(now.getFullYear() + 1)
            data.timeEndReceiving = end
            break
          }
          default:
            break // 'DotXuat' thì giữ nguyên, người dùng tự chọn
        }

        if (!this.shouldGenerateToday(data.quotationPeriod, now)) continue

        entity.timeStartReceiving = data.timeStartReceiving
        entity.timeEndReceiving = data.timeEndReceiving
        entity.quantity = data.quantity
        entity.description = data.description
        entity.referenceQuote = data.referenceQuote
        entity.shipmentPlanId = data.shipmentPlanId
        entity.prId = data.prId
        if (data.lstIncotermId) entity.lstIncotermId = data.lstIncotermId
        if (data.lstPaymentTermId) entity.lstPaymentTermId = data.lstPaymentTermId
        entity.quotationForm = data.quotationForm
        entity.quotationValidity = data.quotationValidity
        entity.status = enumData.RequestStatus.A.code
        entity.createdAt = new Date()
        entity.createdBy = data.createdBy
        entity.id = uuidv4()
        entity.timeConfirmParticipationQuotation = data.timeConfirmParticipationQuotation
        entity.plantId = data.plantId
        entity.shipmentFeeConditionTypeCompactId = data.shipmentFeeConditionTypeCompactId || []
        entity.shipmentFeeConditionTypeCompactCode = data.shipmentFeeConditionTypeCompactCode || []
        entity.shipmentFeeConditionTypeCompactValue = data.shipmentFeeConditionTypeCompactValue || []
        entity.requestQuoteParentId = data.id

        await repo.insert(entity)

        // // Details

        if (data.__requestQuoteDetails__?.length > 0) {
          for (const item of data.__requestQuoteDetails__) {
            const detail = new RequestQuoteDetailEntity()
            detail.requestQuoteId = entity.id
            detail.itemNo = item.itemNo
            detail.assetCode = item.assetCode
            detail.category = item.category
            detail.materialId = item.materialId
            detail.materialGroupId = item.materialGroupId
            detail.shortText = item.shortText
            detail.deliveryDate = item.deliveryDate
            detail.orderCode = item.orderCode
            detail.plantId = item.plantId
            detail.quantity = item.quantity
            detail.assetDesc = item.assetDesc
            detail.sloc = item.sloc
            detail.prItemId = item.prItemId
            detail.orderName = item.orderName
            detail.createdAt = new Date()
            detail.createdBy = item.createdBy
            detail.externalMaterialGroupId = item.externalMaterialGroupId
            detail.unitId = item.unitId
            await detailRepo.insert(detail)
          }
        }

        // // Files
        if (data.__mediaFiles__?.length > 0) {
          for (const item of data.__mediaFiles__) {
            const newImg = new MediaFileEntity()
            newImg.requestQuoteId = entity.id
            newImg.fileUrl = item.fileUrl
            newImg.fileName = item.fileName
            newImg.createdAt = new Date()
            newImg.createdBy = item.createdBy
            await mediaRepo.insert(newImg)
          }
        }

        // // Fees
        if (data.__requestQuoteFees__?.length > 0) {
          for (const item of data.__requestQuoteFees__) {
            const newItem = new RequestQuoteFeeEntity()
            newItem.requestQuoteId = entity.id
            newItem.shipmentFeeConditionsId = item.shipmentFeeConditionsId
            newItem.shipmentFeeConditionsToListId = item.shipmentFeeConditionsToListId
            newItem.shipmentFeeConditionsListId = item.shipmentFeeConditionsListId
            newItem.createdAt = new Date()
            newItem.createdBy = item.createdBy
            newItem.checked = item.checked
            await requestQuoteFeeRepo.insert(newItem)
          }
        }

        // // Suppliers
        if (data.__requestQuoteSuppliers__?.length > 0) {
          for (const item of data.__requestQuoteSuppliers__) {
            const newItem = new RequestQuoteSupplierEntity()
            newItem.requestQuoteId = entity.id
            newItem.supplierId = item.supplierId
            newItem.createdAt = new Date()
            newItem.createdBy = data.createdBy
            newItem.serviceId = item.serviceId
            newItem.externalMaterialGroupId = item.externalMaterialGroupId
            newItem.status = enumData.SupplierRequestStatus.ChoXacNhan.code
            await requestQuoteSupplierRepo.insert(newItem)
          }
        }

        // gửi mail cho ncc
        // for (let item of data.__requestQuoteSuppliers__) {
        //   await this.emailService.GuiThongBaoChaoGia(item.supplierId, entity.id)
        // }

        const actionLog = new ActionLogEntity()
        actionLog.targetId = entity.id
        actionLog.createdByName = 'System'
        actionLog.description = `Hệ thống tự sinh ra yêu cầu báo giá yêu cầu báo giá có mã là [${entity.code}]`
        actionLog.entityName = RequestQuoteEntity.name
        actionLog.jsonNew = JSON.stringify(entity)
        actionLog.createdAt = new Date()
        actionLog.createdBy = 'Admin'
        await actionLogRepo.insert(actionLog)
      }
    })
  }

  shouldGenerateToday(period: string, now: Date): boolean {
    switch (period) {
      case 'DAY':
        return true // ngày nào cũng chạy
      case 'WEEK':
        return now.getDay() === 1 // thứ 2
      case 'MONTH':
        return now.getDate() === 1 // mùng 1
      case 'YEAR':
        return now.getDate() === 1 && now.getMonth() === 0 // 01/01
      default:
        return true // 'DotXuat' thì sinh ngay
    }
  }

  public async findListIncoterm(data: FilterOneDto) {
    const requestQuote = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    const lstIncoterm = await this.incotermRepo.find({
      where: { id: In(requestQuote.lstIncotermId.split(',')), isDeleted: false },
      order: { sort: 'DESC' },
    })

    return lstIncoterm
  }

  public async findListPaymentTerm(data: FilterOneDto) {
    const requestQuote: any = await this.repo.findOne({ where: { id: data.id, isDeleted: false }, relations: { requestQuoteDetails: true } })
    if (!requestQuote) throw new Error(ERROR_NOT_FOUND_DATA)
    const lstMaterialGroupId = requestQuote?.__requestQuoteDetails__?.map((x) => x.materialGroupId)
    if (lstMaterialGroupId.length === 0) return []
    const lstMaterialGroup = await this.materialGroupRepo.find({ where: { id: In(lstMaterialGroupId), isDeleted: false } })
    const lstPayment = lstMaterialGroup.filter((x) => x.paymentTermId).map((x) => x.paymentTermId)
    if (lstPayment.length === 0) return []
    return this.paymentTermRepo.find({ where: { id: In(lstPayment), isDeleted: false } })
  }
}
