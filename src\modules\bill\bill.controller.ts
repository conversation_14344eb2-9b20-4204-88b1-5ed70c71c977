import { Controller, UseGuards, Post, Body, Get, Request } from '@nestjs/common'
import { CurrentUser } from '../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { JwtAuthGuard } from '../common/guards'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { BillService } from './bill.service'
import { BillCreateDto, BillUpdateDto } from './dto'
import { Request as IRequest } from 'express'
import { ACCEPT_LANGUAGE } from '../../constants'

@ApiBearerAuth()
@ApiTags('BILL')
@UseGuards(JwtAuthGuard)
@Controller('bill')
export class BillController {
  constructor(private readonly service: BillService) {}

  @ApiOperation({ summary: 'Lấy danh sách hóa đơn' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Danh sách hóa đơn phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto, @Request() req: IRequest) {
    return await this.service.pagination(user, data, req.headers[ACCEPT_LANGUAGE])
  }

  @ApiOperation({ summary: 'Tạo hóa đơn' })
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BillCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hóa đơn' })
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BillUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động hóa đơn' })
  @Post('update_active')
  public async updateActive(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    const warehouse = await this.service.updateIsDelete(data, user)
    return warehouse
  }

  @ApiOperation({ summary: 'Chi tiết hóa đơn' })
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: FilterOneDto, @Request() req: IRequest) {
    return await this.service.findDetail(user, data, req.headers[ACCEPT_LANGUAGE])
  }

  @ApiOperation({ summary: 'API Cập nhật hủy Hóa đơn' })
  @Post('update_cancel')
  public async updateCancel(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateCancel(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật gửi  Hóa đơn' })
  @Post('update_send')
  public async updateSend(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateSend(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật duyệt Hóa đơn' })
  @Post('update_confirmed')
  public async updateConfirmed(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateConfirmed(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách poId của các hóa đơn' })
  @Post('find_po')
  public async findPOId(@CurrentUser() user: UserDto, @Body() data: { listBillId: string[] }) {
    return await this.service.findPOId(user, data)
  }
  @ApiOperation({ summary: 'Lấy danh sách contractId của các hóa đơn' })
  @Post('find_contract')
  public async findContractId(@CurrentUser() user: UserDto, @Body() data: { listBillId: string[] }) {
    return await this.service.findContractId(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách hóa đơn theo trạng thái Đã xác nhận và chưa thanh toán' })
  @Post('find_bill')
  public async findBillByStatus(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.findBillByStatus(user, data)
  }

  @ApiOperation({ summary: 'Hàm lấy danh sách công ty trong hệ thống bizzi' })
  @Get('load_companies_from_bizzi')
  public async loadCompany(@CurrentUser() user: UserDto) {
    return await this.service.loadCompany(user)
  }

  @ApiOperation({ summary: 'Lấy danh sách hóa đơn theo trạng thái Đã xác nhận và chưa thanh toán client' })
  @Post('find_bill_by_supplier')
  public async findBillSupplier(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.findBillSupplier(user, data)
  }
}
