import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ProcedureEntity } from './procedure.entity'
import { POProductEntity } from './poProduct.entity'
import { PoPriceListEntity } from './poPriceList.entity'
import { MaterialEntity } from './material.entity'
import { POEntity } from './po.entity'

/** <PERSON>ấu hình bảng giá PO */
@Entity('po_product_price_list')
export class PoProductPriceListEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.poProductPriceList)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  procedureId: string

  @ManyToOne(() => ProcedureEntity, (p) => p.poProductPriceList)
  @JoinColumn({ name: 'procedureId', referencedColumnName: 'id' })
  procedure: Promise<ProcedureEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  poProductId: string

  @ManyToOne(() => POProductEntity, (p) => p.poProductPriceList)
  @JoinColumn({ name: 'poProductId', referencedColumnName: 'id' })
  poProduct: Promise<POProductEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  poPriceListId: string

  @ManyToOne(() => PoPriceListEntity, (p) => p.poProductPriceList)
  @JoinColumn({ name: 'poPriceListId', referencedColumnName: 'id' })
  poPriceList: Promise<PoPriceListEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.poProductPriceList)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** Quantity */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  quantity: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  conditionTypeCode: string

  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  conditionTypeName: string

  /** Loại cấu hình cho Amount */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  amountType: string

  /** Công thức */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  amountFormula: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  amountCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  valueAmountFormula: string

  /** Loại cấu hình cho Amount */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  conditionValueType: string

  /** Công thức */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  conditionValueFormula: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  conditionValueCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  valueConditionValueFormula: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  per: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  valuePer: string

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  percent: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  conditionUnit: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  valueConditionUnit: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  qtyCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  valueQty: string
}
