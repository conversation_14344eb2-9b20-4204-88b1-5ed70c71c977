import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangePermissionApprove1748512865548 implements MigrationInterface {
    name = 'ChangePermissionApprove1748512865548'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "permission_approve" ADD "isEmployee" bit CONSTRAINT "DF_6bfb9e4855ce89b38a8f9ea4a69" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "permission_approve" DROP CONSTRAINT "DF_6bfb9e4855ce89b38a8f9ea4a69"`);
        await queryRunner.query(`ALTER TABLE "permission_approve" DROP COLUMN "isEmployee"`);
    }

}
