import { ApiProperty, PartialType } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'
import { ShipmentFeeConditionsEntity } from '../../../entities/shipmentFeeConditions.entity'
import { ShipmentFeeConditionsListEntity } from '../../../entities/shipmentFeeConditionsList.entity'

export class ShipmentFeeConditionExcelDto extends PartialType(ShipmentFeeConditionsEntity) {
  @ApiProperty({ description: 'Mã' })
  @IsString()
  code: string

  @ApiProperty({ description: 'Tên ' })
  name: string
}

export class ShipmentFeeConditionListExcelDto extends PartialType(ShipmentFeeConditionsListEntity) {
  @ApiProperty({ description: 'Mã' })
  @IsString()
  code: string

  @ApiProperty({ description: 'Tên ' })
  name: string

  @ApiProperty({ description: 'Id ' })
  id: string
}
