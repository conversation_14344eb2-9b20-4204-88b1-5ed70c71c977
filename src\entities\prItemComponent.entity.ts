import { Entity, Column, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PrEntity } from './pr.entity'
import { PrItemEntity } from './prItem.entity'
import { MaterialEntity } from './material.entity'
import { UomEntity } from './uom.entity'

@Entity('pr_item_compoment')
export class PrItemCompomentEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.prItemCompoments)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemId: string
  @ManyToOne(() => PrItemEntity, (p) => p.prItemCompoments)
  @JoinColumn({ name: 'prItemId', referencedColumnName: 'id' })
  prItem: Promise<PrItemEntity>

  /** số lượng cần đặt hàng */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  /** tên hàng */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  shortText: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.prItemCompoments)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** tên hàng */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  batch: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.prItemCompoments)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>
}
