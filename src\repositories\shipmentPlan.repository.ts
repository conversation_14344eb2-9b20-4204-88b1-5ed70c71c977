import { ShipmentPlanEntity } from '../entities/shipmentPlan.entity'
import { ShipmentPlanNumberEntity } from '../entities/shipmentPlanNumber.entity'
import { ShipmentPlanNumberDetailEntity } from '../entities/shipmentPlanNumberDetail.entity'
import { CustomRepository } from '../typeorm'
import { BaseRepository } from './base.repository'

@CustomRepository(ShipmentPlanEntity)
export class ShipmentPlanRepository extends BaseRepository<ShipmentPlanEntity> {}

@CustomRepository(ShipmentPlanNumberEntity)
export class ShipmentPlanNumberRepository extends BaseRepository<ShipmentPlanNumberEntity> {}

@CustomRepository(ShipmentPlanNumberDetailEntity)
export class ShipmentPlanNumberDetailRepository extends BaseRepository<ShipmentPlanNumberDetailEntity> {}
