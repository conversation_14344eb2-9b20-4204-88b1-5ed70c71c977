import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateMigration1748319758510 implements MigrationInterface {
    name = 'UpdateMigration1748319758510'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" ADD "plantId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" ADD "purGrId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" ADD "purOrgId" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" DROP COLUMN "purOrgId"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" DROP COLUMN "purGrId"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" DROP COLUMN "plantId"`);
    }

}
