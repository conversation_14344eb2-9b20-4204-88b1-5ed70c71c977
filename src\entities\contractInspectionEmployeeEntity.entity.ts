import { BaseEntity } from './base.entity'
import { <PERSON>ti<PERSON>, Column, JoinColumn, ManyToOne } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { ContractInspectionEntity } from './contractInspection.entity'

@Entity('contract_inspection_employee')
export class ContractInspectionEmployeeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.contractInspectionEmps)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  contractInspectionId: string
  @ManyToOne(() => ContractInspectionEntity, (p) => p.contractInspectionEmps)
  @JoinColumn({ name: 'contractInspectionId', referencedColumnName: 'id' })
  contractInspection: Promise<ContractInspectionEntity>
}
