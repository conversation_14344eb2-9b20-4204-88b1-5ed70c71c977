import { MigrationInterface, QueryRunner } from 'typeorm'

export class RemoveAndAddColumIncoterm1752638772773 implements MigrationInterface {
  name = 'RemoveAndAddColumIncoterm1752638772773'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "lstShipmentFeeConditionTypeId"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "lstShipmentFeeConditionTypeCode"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "lstShipmentConditionTypeId" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "lstShipmentFeeConditionCode" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "lstSettingCostId"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "lstSettingCostId" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "lstSettingCostCode"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "lstSettingCostCode" varchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "lstSettingCostCode"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "lstSettingCostCode" nvarchar(MAX)`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "lstSettingCostId"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "lstSettingCostId" nvarchar(MAX)`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "lstShipmentFeeConditionCode"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "lstShipmentConditionTypeId"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "lstShipmentFeeConditionTypeCode" nvarchar(MAX)`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "lstShipmentFeeConditionTypeId" nvarchar(MAX)`)
  }
}
