import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeType1750067336755 implements MigrationInterface {
    name = 'ChangeType1750067336755'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "norm"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "norm" decimal(20,2) CONSTRAINT "DF_677ec8ea328882889a7cbc31906" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP CONSTRAINT "DF_677ec8ea328882889a7cbc31906"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "norm"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "norm" int`);
    }

}
