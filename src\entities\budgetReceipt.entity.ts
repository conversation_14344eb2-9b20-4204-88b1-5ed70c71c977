import { <PERSON><PERSON><PERSON>, Column, Jo<PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CompanyEntity } from './company.entity'
import { PrEntity } from './pr.entity'
import { EmployeeEntity } from './employee.entity'
import { CurrencyEntity } from './currency.entity'
import { BudgetReceiptItemEntity } from './budgetReceiptItem.entity'
import { POEntity } from './po.entity'
import { BudgetReceiptHistoryEntity } from './budgetReceiptHistory.entity'
import { RoundUpContEntity } from './roundUpCont.entity'

/** Phiếu đề xuất điều chỉnh ngân sách */
@Entity('budget_receipt')
export class BudgetReceiptEntity extends BaseEntity {
  /** PR */
  @Column({ type: 'varchar', nullable: true })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.budgetReceipts)
  @JoinC<PERSON>umn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  /** PO */
  @Column({ type: 'varchar', nullable: true })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.budgetReceipts)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  /** Mã */
  @Column({ type: 'varchar', length: 250, nullable: false })
  code: string

  /** Ngày phiếu */
  @Column({ nullable: true, type: 'datetime' })
  date: Date

  /** Kỳ ngân sách */
  @Column({ nullable: true, type: 'datetime' })
  dateBudget: Date

  /** Công ty */
  @Column({ type: 'varchar', nullable: true })
  companyId: string
  @ManyToOne(() => CompanyEntity, (p) => p.id)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  /** Ngân sách liên công ty */
  @Column({ nullable: false, default: true })
  isMultiCompany: boolean

  /** list id công ty (Nếu check isMultiCompany) */
  @Column({ type: 'varchar', length: 500, nullable: true })
  lstCompanyId: string

  /** Loại đề xuất (enum ProposeBudgetType) */
  @Column({ type: 'varchar', length: 50, nullable: true })
  proposeType: string

  /** Loại chứng từ (enum ReceiptBudgetType) */
  @Column({ type: 'varchar', length: 50, nullable: true })
  receiptType: string

  /** Người đề xuất */
  @Column({ type: 'varchar', nullable: true })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.id)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  /** Id Phòng QLNS */
  @Column({ type: 'varchar', length: 50, nullable: true })
  departmentQLNSId: string

  /** Phòng QLNS */
  @Column({ type: 'varchar', length: 250, nullable: true })
  departmentQLNS: string

  /** Id Đơn vị tiền tệ */
  @Column({ type: 'varchar', nullable: true })
  currencyId: string
  @ManyToOne(() => CurrencyEntity, (p) => p.id)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currencyValue: Promise<CurrencyEntity>

  /** Đơn vị tiền tệ */
  @Column({ type: 'varchar', length: 250, nullable: true })
  currency: string

  /** Diễn giải */
  @Column({ type: 'varchar', length: 500, nullable: true })
  interpretation: string

  /** Ghi chú */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  description: string

  /** Các ghi chú */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  descriptionOverview: string

  /** Ghi chú duyệt */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  descriptionApproved: string

  /** Ghi chú khác */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  descriptionOther: string

  /** Url tệp đính kèm */
  @Column({ type: 'varchar', length: 500, nullable: true })
  fileAttachmentUrl: string

  /** Số tiền khả dụng */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  totalMoneyAvailable: number

  /** Số tiền đề xuất */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  totalMoneyPropose: number

  /** Số tiền duyệt */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  totalMoneyApproved: number

  /** Số tiền còn lại */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  totalMoneyRemaining: number

  /** Trạng thái enum BudgetStatus */
  @Column({ type: 'varchar', length: 50, nullable: true })
  status: string

  /** Yêu cầu từ */
  @Column({ type: 'varchar', length: 250, nullable: true })
  requestFrom: string

  /** Số chứng từ */
  @Column({ type: 'varchar', length: 250, nullable: true })
  podNumber: string

  /** Người duyệt trước */
  @Column({ type: 'varchar', nullable: true })
  employeeApprovedId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.id)
  @JoinColumn({ name: 'employeeApprovedId', referencedColumnName: 'id' })
  employeeApproved: Promise<EmployeeEntity>

  /** Ngày duyệt */
  @Column({ nullable: true, type: 'datetime' })
  dateApproved: Date

  /** Lịch sử duyệt */
  @Column({ type: 'varchar', length: 'max', nullable: true })
  lstHistoryApproved: string

  /** Ds người đã duyệt */
  @Column({ type: 'varchar', length: 250, nullable: true })
  lstEmployeeApprovedId: string

  /** Ds ngày đã duyệt */
  @Column({ type: 'varchar', length: 250, nullable: true })
  lstDateApproved: string

  /** Danh sách Item trong phiếu điều chỉnh ngân sách */
  @OneToMany(() => BudgetReceiptItemEntity, (p) => p.budgetReceipt)
  items: Promise<BudgetReceiptItemEntity[]>

  @OneToMany(() => BudgetReceiptHistoryEntity, (p) => p.budgetReceipt)
  histories: Promise<BudgetReceiptHistoryEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  roundUpContId: string
  @ManyToOne(() => RoundUpContEntity, (p) => p.budgetReceipt)
  @JoinColumn({ name: 'roundUpContId', referencedColumnName: 'id' })
  roundUpCont: Promise<RoundUpContEntity>

  /**Chứng từ bên SAP */
  @Column({ type: 'varchar', length: 50, nullable: true })
  fmDoc: string
}
