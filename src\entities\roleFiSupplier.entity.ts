import { Entity, Column, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierPlantEntity } from './supplierPlant.entity'
import { GLAccountEntity } from './glAccount.entity'
import { PaymentTermEntity } from './paymentTerm.entity'
import { PaymentMethodEntity } from './paymentMethod.entity'
import { PlanningGroupEntity } from './planningGroup.entity'
import { SupplierNumberRequestApproveEntity } from './supplierNumberRequestApprove.entity'

/**vai trò role fi supplier : nhập thông tin phân bổ plant */
@Entity('role_fi_supplier')
export class RoleFiSupplierEntity extends BaseEntity {
  /**Tài khoản công nợ hoạch toán tự động */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  glAccountId: string
  @ManyToOne(() => GLAccountEntity, (p) => p.roleFiSuppliers)
  @JoinColumn({ name: 'glAccountId', referencedColumnName: 'id' })
  glAccount: Promise<GLAccountEntity>

  /** Thời hạn thanh toán  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentTermId: string
  @ManyToOne(() => PaymentTermEntity, (p) => p.roleFiSuppliers)
  @JoinColumn({ name: 'paymentTermId', referencedColumnName: 'id' })
  paymentTerm: Promise<PaymentTermEntity>

  /** Mối liên hệ với phương thức thanh toán  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentMethodId: string
  @ManyToOne(() => PaymentMethodEntity, (p) => p.roleFiSuppliers)
  @JoinColumn({ name: 'paymentMethodId', referencedColumnName: 'id' })
  paymentMethod: Promise<PaymentMethodEntity>

  /** Phân nhóm dòng tiền  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  planningGroupId: string
  @ManyToOne(() => PlanningGroupEntity, (p) => p.roleFiSuppliers)
  @JoinColumn({ name: 'planningGroupId', referencedColumnName: 'id' })
  planningGroup: Promise<PlanningGroupEntity>

  /** Supplier plant*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierPlantId: string

  @ManyToOne(() => SupplierPlantEntity, (p) => p.roleFiSuppliers)
  @JoinColumn({ name: 'supplierPlantId', referencedColumnName: 'id' })
  supplierPlant: Promise<SupplierPlantEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierNumberRequestApproveId: string

  @ManyToOne(() => SupplierNumberRequestApproveEntity, (p) => p.roleFiSuppliers)
  @JoinColumn({ name: 'supplierNumberRequestApproveId', referencedColumnName: 'id' })
  supplierNumberRequestApprove: Promise<SupplierNumberRequestApproveEntity>
}
