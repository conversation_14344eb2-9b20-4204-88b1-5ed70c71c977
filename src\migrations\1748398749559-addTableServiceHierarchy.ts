import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTableServiceHierarchy1748398749559 implements MigrationInterface {
    name = 'AddTableServiceHierarchy1748398749559'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "service_hierarchy" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0e47e976a01e3b4c4975e05fa62" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_5a12a633e866cfb102e9cb5ce16" DEFAULT 0, "companyId" varchar(255), "name" varchar(250), "code" varchar(250) NOT NULL, "level" int NOT NULL, "isLast" bit NOT NULL CONSTRAINT "DF_e63239002e9066e2edc8357c2e4" DEFAULT 0, "parentId" uniqueidentifier, CONSTRAINT "PK_0e47e976a01e3b4c4975e05fa62" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "service_hierarchy" ADD CONSTRAINT "FK_9ea4ea287e064b5159ab22194e0" FOREIGN KEY ("parentId") REFERENCES "service_hierarchy"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service_hierarchy" DROP CONSTRAINT "FK_9ea4ea287e064b5159ab22194e0"`);
        await queryRunner.query(`DROP TABLE "service_hierarchy"`);
    }

}
