import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { ShipmentConfigTemplateEntity } from './shipmentConfigTemplate.entity'
import { BaseEntity } from './base.entity'
import { ShipmentConditionTypeEntity } from './shipmentCondictionType.entity'

@Entity('shipment_config_template_detail')
export class ShipmentConfigTemplateDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentConfigTemplateId: string
  @ManyToOne(() => ShipmentConfigTemplateEntity, (p) => p.shipmentConfigTemplateDetails)
  @JoinColumn({ name: 'shipmentConfigTemplateId', referencedColumnName: 'id' })
  shipmentConfigTemplate: Promise<ShipmentConfigTemplateEntity>

  /* Mã mẫu cấu hình */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentConditionTypeId: string

  @ManyToOne(() => ShipmentConditionTypeEntity, (p) => p.shipmentConfigTemplateDetails)
  @JoinColumn({ name: 'shipmentConditionTypeId', referencedColumnName: 'id' })
  shipmentConditionType: Promise<ShipmentConditionTypeEntity>
}
