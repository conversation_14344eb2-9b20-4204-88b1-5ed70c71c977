import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateColum1751977307502 implements MigrationInterface {
  name = 'CreateColum1751977307502'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "request_quote_fee" ADD "checked" bit CONSTRAINT "DF_160bf556376d13ebd195fc18a29" DEFAULT 1`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "DF_160bf556376d13ebd195fc18a29"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP COLUMN "checked"`)
  }
}
