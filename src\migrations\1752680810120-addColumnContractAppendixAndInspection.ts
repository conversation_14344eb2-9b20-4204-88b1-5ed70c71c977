import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnContractAppendixAndInspection1752680810120 implements MigrationInterface {
  name = 'AddColumnContractAppendixAndInspection1752680810120'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_inspection" ADD "fileUrl" nvarchar(255)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "fileUrl" nvarchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "fileUrl"`)
    await queryRunner.query(`ALTER TABLE "contract_inspection" DROP COLUMN "fileUrl"`)
  }
}
