import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { TemplateSiteAssessmentEntity } from './templateSiteAssessment.entity'
import { TemplateSiteAssessmentItemChildEntity } from './templateSiteAssessmentItemChild.entity'

// nội dung đánh giá template hiện trường
@Entity('template_site_assessment_item')
export class TemplateSiteAssessmentItemEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: false,
  })
  content: string

  @Column({
    type: 'float',
    default: 0,
  })
  weight: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  templateSiteAssessmentId: string
  @ManyToOne(() => TemplateSiteAssessmentEntity, (p) => p.templateSiteAssessments)
  @JoinColumn({ name: 'templateSiteAssessmentId', referencedColumnName: 'id' })
  templateSiteAssessment: Promise<TemplateSiteAssessmentEntity>

  // các câu hỏi
  @OneToMany(() => TemplateSiteAssessmentItemChildEntity, (p) => p.templateSiteAssessmentItem)
  templateSiteAssessmentItemChilds: Promise<TemplateSiteAssessmentItemChildEntity[]>
}
