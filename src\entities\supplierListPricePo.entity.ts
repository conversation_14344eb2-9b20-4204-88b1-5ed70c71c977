import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
import { SupplierSchemaEntity } from './supplierSchema.entity'
import { CurrencyEntity } from './currency.entity'

/**Thông tin bảng giá PO: tác vụ phần nhà cung cấp */
@Entity('supplier_list_price_po')
export class SupplierListPricePOEntity extends BaseEntity {

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierSchemaId: string
  @ManyToOne(() => SupplierSchemaEntity, (p) => p.listPricePos)
  @JoinColumn({ name: 'supplierSchemaId', referencedColumnName: 'id' })
  supplierSchema: Promise<SupplierSchemaEntity>


  @Column({
    type: 'varchar',
    nullable: false,
  })
  currencyId: string
  @ManyToOne(() => CurrencyEntity, (p) => p.listPricePos)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.listPricePos)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>


}
