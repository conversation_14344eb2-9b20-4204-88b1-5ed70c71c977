import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnCont1750768130975 implements MigrationInterface {
  name = 'AddColumnCont1750768130975'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "colConfigData" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "colConfigData"`)
  }
}
