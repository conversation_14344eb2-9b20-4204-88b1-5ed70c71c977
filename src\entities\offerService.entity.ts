import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { OfferEntity } from './offer.entity'
import { ServiceEntity } from './service.entity'
import { OfferPriceEntity } from './offerPrice.entity'
import { MaterialEntity } from './material.entity'
import { PlantEntity } from './plant.entity'
import { UomEntity } from './uom.entity'
import { MaterialGroupEntity } from './materialGroup.entity'
import { OfferSupplierServiceEntity } from './offerSupplierService.entity'
import { OfferTechEntity } from './offerTech.entity'
import { OfferTradeEntity } from './offerTrade.entity'
import { OfferPriceColEntity } from './offerPriceCol.entity'
import { OfferCustomPriceEntity } from './offerCustomPrice.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { PrItemEntity } from './prItem.entity'

/** Bảng chào giá nhanh */
@Entity('offer_service')
export class OfferServiceEntity extends BaseEntity {
  /** Item Line */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  itemNo: number

  /** Nhóm vật tư */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  materialGroupName: string

  /** Category */
  @Column({
    type: 'varchar',
    nullable: true,
    length: 50,
  })
  categoryName: string

  /** Category */
  @Column({
    type: 'varchar',
    nullable: true,
    length: 1,
  })
  category: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.offerService)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })

  /**external matGroup */
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (emg) => emg.offerService)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  exMatGroup: Promise<ExternalMaterialGroupEntity>

  /** tên hàng */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  shortText: string

  /** số lượng cần đặt hàng */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Công ty */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.offerService)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  unitCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.offerService)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  ounId: string
  @ManyToOne(() => UomEntity, (p) => p.offerServices)
  @JoinColumn({ name: 'ounId', referencedColumnName: 'id' })
  oun: Promise<UomEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string

  @ManyToOne(() => MaterialGroupEntity, (p) => p.offerService)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>

  /** offer */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string
  @ManyToOne(() => OfferEntity, (p) => p.offerService)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemId?: string
  /** Pr */
  @ManyToOne(() => PrItemEntity, (p) => p.offerService)
  @JoinColumn({ name: 'prItemId', referencedColumnName: 'id' })
  prItem: Promise<PrItemEntity>

  /** service  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.offerService)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  /** Tỉ trọng % kỹ thuật */
  @Column({
    nullable: true,
    default: 0,
  })
  percentTech: number

  /** Tỉ trọng % DKTM */
  @Column({
    nullable: true,
    default: 0,
  })
  percentTrade: number

  /** Tỉ trọng % giá */
  @Column({
    nullable: true,
    default: 0,
  })
  percentPrice: number

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Tên */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  serviceName: string

  /** Công thức tính cột đơn giá */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fomular: string

  /** Thuộc về đấu giá ? */
  @Column({
    nullable: false,
    default: false,
  })
  isExGr: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPriceId?: string

  /* này cho shipment */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  conditionType: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @Column({
    nullable: true,
  })
  amount: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  crcy: string

  @Column({
    nullable: true,
  })
  per: number

  @Column({
    nullable: true,
  })
  conditionValue: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  curr: string

  @Column({
    nullable: true,
  })
  cConDe: number

  @Column({
    nullable: true,
  })
  numCCo: number

  /** Tên */
  @Column({
    type: 'int',
    nullable: true,
  })
  value: string

  @OneToMany(() => OfferSupplierServiceEntity, (p) => p.offerService)
  offerSupplierService: Promise<OfferSupplierServiceEntity[]>

  /** Ngày cần giao */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  /** 1 dịch vụ sẽ có nhiều cấu hình kỹ thuật */
  @OneToMany(() => OfferTechEntity, (p) => p.offerService)
  techs: Promise<OfferTechEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình thương mại */
  @OneToMany(() => OfferTradeEntity, (p) => p.offerService)
  trades: Promise<OfferTradeEntity[]>

  @OneToMany(() => OfferPriceEntity, (p) => p.offerItem)
  basePrices: Promise<OfferPriceEntity[]>

  @OneToMany(() => OfferPriceEntity, (p) => p.offerService)
  prices: Promise<OfferPriceEntity[]>

  @OneToMany(() => OfferPriceColEntity, (p) => p.offerService)
  offerPriceCol: Promise<OfferPriceColEntity[]>

  @OneToMany(() => OfferCustomPriceEntity, (p) => p.offerService)
  customPrices: Promise<OfferCustomPriceEntity[]>
}
