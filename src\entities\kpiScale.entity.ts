import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { KpiEntity } from './kpi.entity'

/**  kpi mua hàng vị trí*/
@Entity('kpi_scale')
export class KpiScaleEntity extends BaseEntity {
  /** id kpi mua hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  kpiId: string
  @ManyToOne(() => KpiEntity, (p) => p.kpiScales)
  @JoinColumn({ name: 'kpiId', referencedColumnName: 'id' })
  kpi: Promise<KpiEntity>

  /**Chặn dưới */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  blockBelow: number

  /**Chặn trên */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  blockOn: number

  /** Điểm */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  point: string
}
