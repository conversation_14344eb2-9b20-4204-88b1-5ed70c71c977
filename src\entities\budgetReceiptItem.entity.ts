import { Entity, Column, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BudgetReceiptEntity } from './budgetReceipt.entity'
import { PrItemEntity } from './prItem.entity'

/** Item trong phiếu đề xuất điều chỉnh ngân sách */
@Entity('budget_receipt_item')
export class BudgetReceiptItemEntity extends BaseEntity {
  /** Phiếu điều chỉnh ngân sách */
  @Column({ type: 'varchar', nullable: true })
  budgetReceiptId: string
  @ManyToOne(() => BudgetReceiptEntity, (p) => p.items)
  @JoinColumn({ name: 'budgetReceiptId', referencedColumnName: 'id' })
  budgetReceipt: Promise<BudgetReceiptEntity>

  /** Là điều chỉnh tăng */
  @Column({ nullable: false, default: true })
  isIncrease: boolean

  /** funCenter */
  @Column({ type: 'varchar', length: 250, nullable: true })
  funCenter: string

  /** Mã CI */
  @Column({ type: 'varchar', length: 250, nullable: true })
  CICode: string

  /** Tên CI */
  @Column({ type: 'varchar', length: 250, nullable: true })
  CIName: string

  /** Số tiền khả dụng */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  moneyAvailable: number

  /** Số tiền đề xuất */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  moneyPropose: number

  /** Số tiền duyệt */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  moneyApproved: number

  /** Cơ sở điều chỉnh / Cơ cấu điều chỉnh (enum BaseAdjust) */
  @Column({ type: 'varchar', length: 250, nullable: true })
  baseAdjust: string

  /** Lý do điều chỉnh */
  @Column({ type: 'varchar', length: 500, nullable: true })
  reasonAdjust: string

  /** NS TNTC Tháng */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  tntcMonth: number

  /** NS TNTC Năm */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  tntcYear: number

  /** NS Sử dụng lũy kế */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  useAccumulate: number

  /** Tổng GT DX tháng */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  proposeMonth: number

  /** Tổng GT DX năm */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  proposeYear: number

  /** Chênh lệch */
  @Column({ nullable: true, type: 'bigint', default: 0 })
  difference: number

  @Column({
    length: 'max',
    nullable: true,
  })
  fp: string

  @Column({
    length: 'max',
    nullable: true,
  })
  fc: string

  @Column({
    length: 'max',
    nullable: true,
  })
  ci: string

  @Column({
    length: 'max',
    nullable: true,
  })
  budgetPeriod: string

  @Column({
    length: 'max',
    nullable: true,
  })
  fund: string

  /* Bid Pr item */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemId: string
  @ManyToOne(() => PrItemEntity, (p) => p.budgetReceiptItems)
  @JoinColumn({ name: 'prItemId', referencedColumnName: 'id' })
  prItem: Promise<PrItemEntity>
}
