import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { UserEntity } from './user.entity'

@Entity({ name: 'user_confirm_code' })
export class UserConfirmCodeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  userId: string
  @ManyToOne(() => UserEntity, (user) => user.userConfirm)
  @JoinColumn({ name: 'userId', referencedColumnName: 'id' })
  user: Promise<UserEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  code: string

  @Column({
    nullable: false,
    type: 'datetime',
  })
  exDate: Date
}
