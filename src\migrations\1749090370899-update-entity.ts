import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateEntity1749090370899 implements MigrationInterface {
    name = 'UpdateEntity1749090370899'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "auction_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_capacity_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_capacity_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_year_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_year_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "item_tech_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "item_tech_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "item_tech" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "item_tech" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_member" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_member" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_purchasing_group" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "title" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "title" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "stakeholder_category" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "stakeholder_category" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "industry_standard" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "industry_standard" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "master_condition_type" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "master_condition_type" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "procedure" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "procedure" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_price_list" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_price_list" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill_lookup" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill_lookup" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_bill" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_bill" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_po" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_po" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_contract" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_contract" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "currency_exchange" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "currency_exchange" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "currency" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "currency" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_schema" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_schema" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_org_schema" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_org_schema" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_org" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_org" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_tech_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_tech_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_tech" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_tech" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_trade_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_trade_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_trade" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_trade" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_custom_price_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_custom_price_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "cost" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "cost" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_group" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_group" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_scene_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_scene_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_scene" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_scene" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_col" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_col" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_trade_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_trade_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_trade" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_trade" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_custom_price_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_custom_price_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_tech_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_tech_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_tech" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_tech" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template_col" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template_col" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_col" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_col" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_template" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_template" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rfq_details" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rfq_details" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_string" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_string" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template_col" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template_col" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_type" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_type" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user_confirm_code" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user_confirm_code" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user_external_material_group" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user_external_material_group" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "external_material_group" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "external_material_group" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_uom" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_uom" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "uom" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "uom" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_method" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_method" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "incoterm" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "incoterm" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_term" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_term" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "planning_group" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "planning_group" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "gl_account" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "gl_account" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_group" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_department" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_department" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_handling_plan" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_handling_plan" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_product" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_product" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_document_handover" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_document_handover" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_container" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_container" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_po" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_po" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_lead_time" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_lead_time" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "header_lead_time" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "header_lead_time" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_progress" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_progress" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_pr" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_pr" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "media_file" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "media_file" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_company" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_company" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "block" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "block" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "part" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "part" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_scale" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_scale" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_position" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_position" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "position" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "position" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plant" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plant" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "company" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve_base" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve_base" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "department" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "department" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_access" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_access" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_member" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_member" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_notify" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_notify" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_warning" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_warning" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_role" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_role" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_approve" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_approve" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_acceptance" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_acceptance" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_individual" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_individual" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_criterial_child" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_criterial_child" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_criterial" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_criterial" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_evaluation_potential" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_evaluation_potential" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_law_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_law_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_product_service" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_product_service" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service_factory" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service_factory" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_product_line" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_product_line" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_certificate" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_certificate" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_year_value" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_year_value" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_capacity" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_capacity" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_tech_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_tech_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_tech" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_tech" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_trade_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_trade_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_trade" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_trade" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_custom_price" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_custom_price" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_type" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_type" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_partner_group" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_partner_group" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "region" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "region" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bank" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bank" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "country" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "country" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "factory_supplier" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "factory_supplier" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plan_site_assessment" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plan_site_assessment" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "site_assessment" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "site_assessment" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_notify" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_notify" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_number" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_number" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_type" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_type" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_potential_upgrade" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_potential_upgrade" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_revenue" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_revenue" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "banner_client" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "banner_client" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "faq_category" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "faq_category" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "faq" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "faq" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "link_client" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "link_client" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "email_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "email_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "email_template" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "email_template" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_string_client" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_string_client" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_role" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_role" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_type" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_type" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_route" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_route" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_type" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_type" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_container" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_container" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "data_history" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "data_history" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "language" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "language" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "language_key" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "language_key" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "condition_type" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "condition_type" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_area" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_area" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_employee" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_employee" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "organizational_tree" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "organizational_tree" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "action_log" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "action_log" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "organizational_position" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "organizational_position" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rate_data" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rate_data" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rating_configuration" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rating_configuration" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_item" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_item" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "product_hierarchy" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "product_hierarchy" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_view" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_view" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_to_group" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_to_group" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "warehouse" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "warehouse" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" DROP CONSTRAINT "FK_deab8f46837df555b923140cd3e"`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" ALTER COLUMN "purchasingOrgId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "auction" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "auction" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "auction" ADD "purchasingGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "purchasingOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" ADD CONSTRAINT "FK_deab8f46837df555b923140cd3e" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" DROP CONSTRAINT "FK_deab8f46837df555b923140cd3e"`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "purchasingOrgId" varchar(MAX)`);
        await queryRunner.query(`ALTER TABLE "auction" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "auction" ADD "purchasingGroupId" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "auction" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "auction" ADD "purchasingOrgId" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" ALTER COLUMN "purchasingOrgId" uniqueidentifier NOT NULL`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" ADD CONSTRAINT "FK_deab8f46837df555b923140cd3e" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "warehouse" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "warehouse" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "permission_to_group" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "permission_to_group" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "permission_view" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "permission_view" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "product_hierarchy" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "product_hierarchy" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "rating_configuration" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "rating_configuration" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "rate_data" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "rate_data" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "organizational_position" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "organizational_position" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "action_log" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "action_log" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "organizational_tree" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "organizational_tree" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "permission_employee" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "permission_employee" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "purchasing_area" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "purchasing_area" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "condition_type" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "condition_type" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "language_key" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "language_key" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "language" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "language" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "data_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "data_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "inbound_container" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "inbound_container" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "inbound_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "inbound_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_type" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_type" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_route" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_route" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_type" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_type" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "setting_role" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "setting_role" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "setting_string_client" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "setting_string_client" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "email_template" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "email_template" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "link_client" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "link_client" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "faq" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "faq" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "faq_category" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "faq_category" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "banner_client" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "banner_client" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_revenue" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_revenue" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_potential_upgrade" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_potential_upgrade" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "business_type" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "business_type" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_number" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_number" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_notify" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_notify" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "site_assessment" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "site_assessment" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "plan_site_assessment" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "plan_site_assessment" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "factory_supplier" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "factory_supplier" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bank_branch" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bank_branch" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bank" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bank" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "region" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "region" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "business_partner_group" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "business_partner_group" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_auction" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_auction" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_type" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_type" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_custom_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_custom_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_trade" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_trade" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_trade_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_trade_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_tech" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_tech" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_tech_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_tech_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_capacity" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_capacity" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_year_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_year_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_certificate" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_certificate" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_product_line" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_product_line" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_service_factory" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_service_factory" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_product_service" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_product_service" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_service_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_service_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_law_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_law_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "template_evaluation_potential" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "template_evaluation_potential" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "template_criterial" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "template_criterial" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "template_criterial_child" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "template_criterial_child" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "permission_individual" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "permission_individual" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "po_acceptance" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "po_acceptance" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "permission_approve" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "permission_approve" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "employee_role" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "employee_role" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "employee_warning" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "employee_warning" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "employee_notify" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "employee_notify" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract_member" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract_member" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_access" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_access" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "department" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "department" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "flow_approve" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "flow_approve" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "plant" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "plant" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "position" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "position" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "kpi_position" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "kpi_position" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "kpi" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "kpi" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "kpi_scale" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "kpi_scale" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "part" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "part" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "block" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "block" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "kpi_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "kpi_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "kpi_company" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "kpi_company" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "media_file" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "media_file" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_pr" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_pr" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "payment_progress" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "payment_progress" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "header_lead_time" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "header_lead_time" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "template_lead_time" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "template_lead_time" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_po" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_po" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_container" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_container" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "inbound_document_handover" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "inbound_document_handover" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "po_product" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "po_product" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "complaint_handling_plan" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "complaint_handling_plan" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "complaint_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "complaint_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "complaint_department" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "complaint_department" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "purchasing_group" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "gl_account" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "gl_account" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "planning_group" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "planning_group" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "payment_term" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "payment_term" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "incoterm" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "incoterm" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "payment_method" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "payment_method" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "uom" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "uom" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "material_uom" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "material_uom" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "external_material_group" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "external_material_group" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "user_external_material_group" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "user_external_material_group" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "user_confirm_code" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "user_confirm_code" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "material_type" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "material_type" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template_col" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template_col" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "setting_string" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "setting_string" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_col" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_col" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template_col" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template_col" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_tech" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_tech" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_tech_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_tech_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_custom_price_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_custom_price_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_trade" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_trade" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_trade_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_trade_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_price_col" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_price_col" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_price_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_price_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_deal" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_deal" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_price_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_price_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_scene" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_scene" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_scene_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_scene_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "material_group" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "material_group" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "cost" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "cost" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_deal" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_deal" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_price_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_price_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_custom_price_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_custom_price_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_trade" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_trade" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_trade_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_trade_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_tech" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_tech" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_tech_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_tech_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "purchasing_org" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "purchasing_org" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "purchasing_org_schema" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "purchasing_org_schema" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_schema" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_schema" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "currency" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "currency" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "currency_exchange" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "currency_exchange" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "material_price" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "material_price" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "payment" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "payment" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "payment_contract" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "payment_contract" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "payment_po" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "payment_po" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "payment_bill" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "payment_bill" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bill" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bill" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bill_lookup" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bill_lookup" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "bill_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "bill_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "po_price_list" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "po_price_list" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "procedure" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "procedure" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "master_condition_type" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "master_condition_type" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "industry_standard" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "industry_standard" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "stakeholder_category" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "stakeholder_category" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "title" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "title" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "employee_purchasing_group" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "po_member" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "po_member" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "po_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "po_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "pr_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "pr_history" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "item_tech" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "item_tech" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "item_tech_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "item_tech_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_year_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_year_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "service_capacity_list_detail" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "service_capacity_list_detail" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "auction_history" DROP COLUMN "purchasingGroupId"`);
        await queryRunner.query(`ALTER TABLE "auction_history" DROP COLUMN "purchasingOrgId"`);
    }

}
