import { MigrationInterface, QueryRunner } from 'typeorm'

export class Material1749462808872 implements MigrationInterface {
  name = 'Material1749462808872'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "material" ADD "lastChange" datetime`)
    await queryRunner.query(`ALTER TABLE "material" ADD "materialPriceId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "material" ADD "lotSize" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "mrpProfile" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "mrpController" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "minimumLotSize" decimal(20,3) CONSTRAINT "DF_bc48bd82bb8b5920d04b80bb44c" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "material" ADD "mrpGroup" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "planningStrategyGroup" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "originGroup" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "roundingValue" decimal(20,3) CONSTRAINT "DF_0e5f9261a6752beff0e6257ea16" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "material" ADD "maximumLotSize" decimal(20,3) CONSTRAINT "DF_f5f0034cbd4b069fec536aa8314" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "material" ADD "safetyStock" decimal(20,3) CONSTRAINT "DF_3e3aed95cdbdeb91c00401949a6" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "material" ADD "deliveryTime" float`)
    await queryRunner.query(`ALTER TABLE "material" ADD "maximumStockLevel" decimal(20,3) CONSTRAINT "DF_ca6bd4b5370a07ef15dc4457f49" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "material" ADD "plantCode" varchar(250)`)
    await queryRunner.query(
      `ALTER TABLE "material" ADD CONSTRAINT "FK_13b64d9a10631da673245cb8254" FOREIGN KEY ("materialPriceId") REFERENCES "material_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_13b64d9a10631da673245cb8254"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "plantCode"`)
    await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_ca6bd4b5370a07ef15dc4457f49"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "maximumStockLevel"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "deliveryTime"`)
    await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_3e3aed95cdbdeb91c00401949a6"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "safetyStock"`)
    await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_f5f0034cbd4b069fec536aa8314"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "maximumLotSize"`)
    await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_0e5f9261a6752beff0e6257ea16"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "roundingValue"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "originGroup"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "planningStrategyGroup"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "mrpGroup"`)
    await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_bc48bd82bb8b5920d04b80bb44c"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "minimumLotSize"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "mrpController"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "mrpProfile"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "lotSize"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "materialPriceId"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "lastChange"`)
  }
}
