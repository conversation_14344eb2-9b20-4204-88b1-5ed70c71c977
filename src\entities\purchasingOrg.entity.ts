import { <PERSON><PERSON><PERSON>, Column, OneToMany, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierNumberRequestApproveEntity } from './supplierNumberRequestApprove.entity'
import { PrEntity } from './pr.entity'
import { ComplaintEntity } from './complaint.entity'
import { EmployeeEntity } from './employee.entity'
import { PurchasingGroupEntity } from './purchasingGroup.entity'
import { PlantPurchaseOrgEntity } from './plantPurchasingOrg.entity'
import { PurchasingOrgSchemaEntity } from './purchasingOrgSchema.entity'
import { CompanyEntity } from './company.entity'
import { MaterialEntity } from './material.entity'
import { RoleSupplierEntity } from './roleSupplier.entity'
import { PrItemEntity } from './prItem.entity'
import { RequestQuoteEntity } from './requestQuote.entity'
import { ContractEntity } from './contract.entity'
import { RfqEntity } from './rfq.entity'

/** Tổ chức mua hàng
 */
@Entity('purchasing_org')
export class PurchasingOrgEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.purchasingOrg)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>

  @OneToMany(() => PrEntity, (p) => p.purchasingOrg)
  prs: Promise<PrEntity[]>

  /** ds Khiếu nại */
  @OneToMany(() => ComplaintEntity, (p) => p.purchasingOrg)
  complaints: Promise<ComplaintEntity[]>

  @OneToMany(() => EmployeeEntity, (p) => p.purchasingOrg)
  employee: Promise<EmployeeEntity[]>

  @OneToMany(() => PurchasingGroupEntity, (p) => p.purchasingOrg)
  purchasingGroups: Promise<PurchasingGroupEntity[]>

  @OneToMany(() => PlantPurchaseOrgEntity, (p) => p.purchasingOrg)
  plantPurchaseOrgs: Promise<PlantPurchaseOrgEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgSchemaId: string

  @ManyToOne(() => PurchasingOrgSchemaEntity, (p) => p.purchasingOrgs)
  @JoinColumn({ name: 'purchasingOrgSchemaId', referencedColumnName: 'id' })
  purchasingOrgSchema: Promise<PurchasingOrgSchemaEntity>

  @OneToMany(() => CompanyEntity, (p) => p.purchasingOrg)
  company: Promise<CompanyEntity[]>

  @OneToMany(() => MaterialEntity, (p) => p.purchasingOrg)
  materials: Promise<MaterialEntity[]>

  @OneToMany(() => RoleSupplierEntity, (p) => p.purchasingOrg)
  roleSuppliers: Promise<RoleSupplierEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.purchasingGroup)
  prItems: Promise<PrItemEntity[]>

  @OneToMany(() => RequestQuoteEntity, (p) => p.purchasingOrg)
  requestQuotes: Promise<RequestQuoteEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.purchasingOrg)
  contracts: Promise<ContractEntity[]>

  @OneToMany(() => RfqEntity, (p) => p.purchasingOrg)
  rfqs: Promise<RfqEntity[]>
}
