import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BidController } from './bid.controller'
import { BidService } from './bid.service'
import { EmailModule } from '../email/email.module'
import {
  BidRepository,
  ServiceRepository,
  EmployeeRepository,
  BidTypeRepository,
  SettingStringRepository,
  BidEmployeeAccessRepository,
  ServiceTechRepository,
  ServiceTradeRepository,
  ServicePriceRepository,
  SupplierServiceRepository,
  ServiceCustomPriceRepository,
  BidCustomPriceRepository,
  BidTechRepository,
  BidTradeRepository,
  BidPriceRepository,
  BidPriceColRepository,
  BidSupplierRepository,
  SupplierRepository,
  BidPrItemRepository,
  BidTechListDetailRepository,
  BidTradeListDetailRepository,
  BidShipmentPriceRepository,
  BidSupplierShipmentValueRepository,
  BidDealRepository,
  BidAuctionRepository,
  BidExMatGroupRepository,
  OrganizationalTreeRepository,
  CompanyRepository,
  PlantRepository,
  PurchasingGroupRepository,
  MaterialRepository,
} from '../../repositories'
import { FlowApproveModule } from '../flowApprove/flowApprove.module'
import { BidTechTradeService } from './bidTechTrade.service'
import { BidPriceSupService } from './bidPriceSup.service'
import { BidClientService } from './bidClient.service'
import { BidCreateService } from './bidCreate.service'
import { BidRoleService } from './bidRole.service'
import { SupplierModule } from '../supplier/supplier.module'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'
import { BidRateModule } from './bidRate/bidRate.module'
import { TranslationRepository } from '../../repositories/translation.repository'
import { RfqDetailsRepository, RfqRepository } from '../../repositories/rfq.repository'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BidEmployeeAccessRepository,
      BidRepository,
      ServiceRepository,
      EmployeeRepository,
      BidTypeRepository,
      SettingStringRepository,
      ServiceTechRepository,
      BidTechRepository,
      BidTechListDetailRepository,
      TranslationRepository,
      ServiceTradeRepository,
      ServiceRepository,
      BidPrItemRepository,
      BidTradeRepository,
      BidTradeListDetailRepository,
      CompanyRepository,
      PlantRepository,
      SupplierRepository,
      PurchasingGroupRepository,
      RfqDetailsRepository,
      MaterialRepository,
      ServicePriceRepository,
      ServiceCustomPriceRepository,
      BidPriceRepository,
      BidCustomPriceRepository,
      OrganizationalTreeRepository,
      BidPriceColRepository,
      RfqRepository,
      BidShipmentPriceRepository,
      SupplierServiceRepository,
      BidSupplierRepository,
      BidDealRepository,
      BidAuctionRepository,
      SupplierRepository,
      BidSupplierShipmentValueRepository,
      BidExMatGroupRepository,
    ]),
    EmailModule,
    FlowApproveModule,
    BidRateModule,
    OrganizationalPositionModule,
  ],
  controllers: [BidController],
  providers: [BidService, BidTechTradeService, BidPriceSupService, BidClientService, BidCreateService, BidRoleService],
  exports: [BidService, BidTechTradeService, BidPriceSupService, BidClientService, BidCreateService, BidRoleService],
})
export class BidModule {}
