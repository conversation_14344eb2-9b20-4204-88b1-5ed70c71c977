import { ShipmentConfigTemplateService } from './shipmentConfigTemplate.service'
import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { FlowApproveModule } from '../flowApprove/flowApprove.module'
import { ShipmentConfigTemplateDetailRepository, ShipmentConfigTemplateRepository } from '../../repositories/shipmentConfigTemplate.repository'
import { EmployeeRepository } from '../../repositories'
import { ShipmentConfigTemplateController } from './shipmentConfigTemplate.controller'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([ShipmentConfigTemplateRepository, ShipmentConfigTemplateDetailRepository, EmployeeRepository]),
    FlowApproveModule,
  ],
  controllers: [ShipmentConfigTemplateController],
  providers: [ShipmentConfigTemplateService],
  exports: [ShipmentConfigTemplateService],
})
export class ShipmentConfigTemplateModule {}
