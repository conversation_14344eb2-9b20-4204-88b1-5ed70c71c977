import { MigrationInterface, QueryRunner } from "typeorm";

export class GenMigra1750326384170 implements MigrationInterface {
    name = 'GenMigra1750326384170'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "transportation_plan_detail" DROP CONSTRAINT "FK_bac2363017c8bddfc73468a9d22"`);
        await queryRunner.query(`ALTER TABLE "transportation_plan_detail" DROP COLUMN "TransportationPlanId"`);
        await queryRunner.query(`ALTER TABLE "transportation_plan_detail" DROP COLUMN "transportationPlanId"`);
        await queryRunner.query(`ALTER TABLE "transportation_plan_detail" ADD "transportationPlanId" uniqueidentifier NOT NULL`);
        await queryRunner.query(`ALTER TABLE "transportation_plan_detail" ADD CONSTRAINT "FK_c543bf69c13ac303747b1c700cf" FOREIGN KEY ("transportationPlanId") REFERENCES "transportation_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "transportation_plan_detail" DROP CONSTRAINT "FK_c543bf69c13ac303747b1c700cf"`);
        await queryRunner.query(`ALTER TABLE "transportation_plan_detail" DROP COLUMN "transportationPlanId"`);
        await queryRunner.query(`ALTER TABLE "transportation_plan_detail" ADD "transportationPlanId" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "transportation_plan_detail" ADD "TransportationPlanId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "transportation_plan_detail" ADD CONSTRAINT "FK_bac2363017c8bddfc73468a9d22" FOREIGN KEY ("TransportationPlanId") REFERENCES "transportation_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
