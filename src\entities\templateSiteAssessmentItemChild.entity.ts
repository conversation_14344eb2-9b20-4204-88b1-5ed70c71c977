import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { TemplateSiteAssessmentItemEntity } from './templateSiteAssessmentItem.entity'
import { TemplateSiteAssessmentListDetailEntity } from './templateSiteAssessmentListDetail.entity'

// nội dung đánh giá template hiện trường
@Entity('template_site_assessment_item_child')
export class TemplateSiteAssessmentItemChildEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  name: string

  @Column({
    nullable: true,
    default: false,
  })
  isToStore: boolean

  @Column({
    nullable: true,
    default: 0,
  })
  maxScore: number

  @Column({
    nullable: true,
    default: false,
  })
  isSenSupplier: boolean

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  dataType: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  document: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  templateSiteAssessmentItemId: string
  @ManyToOne(() => TemplateSiteAssessmentItemEntity, (p) => p.templateSiteAssessmentItemChilds)
  @JoinColumn({ name: 'templateSiteAssessmentItemId', referencedColumnName: 'id' })
  templateSiteAssessmentItem: Promise<TemplateSiteAssessmentItemEntity>

  // các list detail của các câu hỏi con
  @OneToMany(() => TemplateSiteAssessmentListDetailEntity, (p) => p.templateSiteAssessmentItemChild)
  templateSiteAssessmentListDetails: Promise<TemplateSiteAssessmentListDetailEntity[]>
}
