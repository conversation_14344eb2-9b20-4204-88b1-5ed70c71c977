import { BaseEntity } from './base.entity'
import { Entity, Column, OneToOne, JoinColumn, OneToMany, ManyToOne } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
import { MediaFileEntity, SupplierUpgradeDetailEntity } from '.'

/** <PERSON><PERSON><PERSON> nâng cấp nhà cung cấp */
@Entity('supplier_upgrade')
export class SupplierUpgradeEntity extends BaseEntity {
  /** Ngày duyệt*/
  @Column({
    type: 'datetime',
    nullable: true,
  })
  dateApprove: Date

  /**Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** <PERSON><PERSON>i liên hệ với NCC */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string

  @ManyToOne(() => SupplierEntity, (p) => p.supplierNumber)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Phiếu đánh giá LSMH tham chiếu */
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  fileUrl: string

  /** Tên file */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  fileName: string

  /** Ghi chú */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** DS chi tiết  */
  @OneToMany(() => SupplierUpgradeDetailEntity, (p) => p.supplierUpgrade)
  supplierUpgradeDetails: Promise<SupplierUpgradeDetailEntity[]>

  @OneToMany(() => MediaFileEntity, (p) => p.supplierUpgrade)
  mediaFiles: Promise<MediaFileEntity[]>
}
