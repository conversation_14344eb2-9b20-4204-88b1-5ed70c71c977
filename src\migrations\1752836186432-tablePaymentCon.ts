import { MigrationInterface, QueryRunner } from 'typeorm'

export class TablePaymentCon1752836186432 implements MigrationInterface {
  name = 'TablePaymentCon1752836186432'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "payment_term_conversion" DROP COLUMN "percentTransportVietNamToWarehouseCodeLeadTime"`)
    await queryRunner.query(`ALTER TABLE "payment_term_conversion" DROP COLUMN "percentLeadtimePurchaseCodeLeadTime"`)
    await queryRunner.query(`ALTER TABLE "payment_term_conversion" DROP COLUMN "percentLeadtimeDeliveryCodeLeadTime"`)

    await queryRunner.query(`ALTER TABLE "payment_term_conversion" ADD "transportVietNamToWarehouseCodeLeadTime" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "payment_term_conversion" ADD "leadtimePurchaseCodeLeadTime" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "payment_term_conversion" ADD "leadtimeDeliveryCodeLeadTime" varchar(50)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "payment_term_conversion" DROP COLUMN "leadtimeDeliveryCodeLeadTime"`)
    await queryRunner.query(`ALTER TABLE "payment_term_conversion" DROP COLUMN "leadtimePurchaseCodeLeadTime"`)
    await queryRunner.query(`ALTER TABLE "payment_term_conversion" DROP COLUMN "transportVietNamToWarehouseCodeLeadTime"`)

    await queryRunner.query(`ALTER TABLE "payment_term_conversion" ADD "percentLeadtimeDeliveryCodeLeadTime" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "payment_term_conversion" ADD "percentLeadtimePurchaseCodeLeadTime" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "payment_term_conversion" ADD "percentTransportVietNamToWarehouseCodeLeadTime" varchar(50)`)
  }
}
