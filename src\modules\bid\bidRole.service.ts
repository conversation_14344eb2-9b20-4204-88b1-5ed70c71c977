import { Injectable, BadRequestException, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'

import { ERROR_YOU_DO_NOT_HAVE_PERMISSION, ERROR_NOT_FOUND_DATA } from '../../constants'
import { EmailService } from '../email/email.service'
import { BidRepository, BidEmployeeAccessRepository } from '../../repositories'

import { enumData } from '../../constants/enumData'
import { UserDto } from '../../dto'
import { IsNull } from 'typeorm'
import { BidUpdateSettingDto } from './dto'
import { BidEntity, BidHistoryEntity } from '../../entities'
import { FlowApproveService } from '../flowApprove/flowApprove.service'

@Injectable()
export class BidRoleService {
  constructor(
    private readonly repo: BidRepository,
    private readonly emailService: EmailService,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,
    private readonly flowService: FlowApproveService,
  ) {}

  /** Kiểm tra quyền Gửi MPOLeader phê duyệt gói thầu tạm */
  async checkPermissionSendMpoleaderCheckBid(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatus = [enumData.BidStatus.GoiThauTam.code]
    const bid = await this.repo.findOne({
      where: [{ id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
    })
    if (bid) {
      if (lstStatus.includes(bid.status)) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền gửi MPOLeader phê duyệt gói thầu tạm.'
        }
      } else {
        result = false
        message = 'Gói thầu đã thay đổi trạng thái, vui lòng kiểm tra lại.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Gửi MPOLeader phê duyệt gói thầu tạm */
  async sendMpoleaderCheckBid(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionSendMpoleaderCheckBid(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    // let flowType: string
    // flowType = enumData.FlowCode.BID.code
    // await this.flowService.setRoleRule(user, {
    //   targetId: bidId,
    //   entityName: BidEntity.name,
    //   flowType: flowType,
    //   companyId: user.orgCompanyId,
    // })

    await this.repo.update(bidId, { status: enumData.BidStatus.ChoDuyetGoiThauTam.code, updatedBy: user.id })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.YeuCauDuyetGoiThauTam.code
    bidHistory.save()

    //send mail MPOLeader
    this.emailService.GuiMPOLeadDuyetGoiThauTam(bidId)

    // tạo quyền duyệt gói thầu tạm

    return { message: 'Đã gửi yêu cầu phê duyệt gói thầu tạm.' }
  }

  /** Kiểm tra quyền phê duyệt gói thầu tạm */
  async checkPermissionAcceptBid(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const lstStatus = [enumData.BidStatus.ChoDuyetGoiThauTam.code]
    const bid = await this.repo.findOne({ where: { id: bidId, parentId: IsNull(), companyId: user.companyId } })
    if (bid) {
      if (lstStatus.includes(bid.status)) {
        result = true
        if (!result) {
          message = 'Bạn không có quyền phê duyệt gói thầu tạm.'
        }
      } else {
        result = false
        message = 'Gói thầu đã thay đổi trạng thái, vui lòng kiểm tra lại.'
      }
    }

    return { hasPermission: result, message }
  }

  mpoleaderAcceptBidAdapter(user: UserDto, data: { bidId: string; comment: string }) {
    return this.mpoleaderAcceptBid(user, data.bidId, false, data.comment)
  }

  /** MPOLeader phê duyệt gói thầu tạm */
  async mpoleaderAcceptBid(user: UserDto, bidId: string, isMobile?: boolean, comment?: string) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: bidId,
      entityName: BidEntity.name,
      type: enumData.FlowCode.BID.code,
      comment: comment,
    })
    if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
      return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    } else {
      // if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
      // const objPermission = await this.checkPermissionAcceptBid(user, bidId)
      // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
      await this.repo.update(bidId, { status: enumData.BidStatus.DangCauHinhGoiThau.code, updatedBy: user.id })
      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidId
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.DuyetGoiThauTam.code
      bidHistory.save()

      //send mail nội bộ
      this.emailService.ThongBaoDaTaoGoiThau(bidId)
    }
    return { message: 'Đã phê duyệt gói thầu tạm thành công.' }
  }

  /** MPOLeader duyệt thầu nhanh */
  async mpoleaderAcceptBidQuick(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionAcceptBid(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      status: enumData.BidStatus.DangCauHinhGoiThau.code,
      statusTech: enumData.BidTechStatus.DaDuyet.code,
      statusTrade: enumData.BidTradeStatus.DaDuyet.code,
      statusPrice: enumData.BidPriceStatus.DangTao.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DuyetThauNhanh.code
    bidHistory.save()

    //send mail nội bộ
    this.emailService.ThongBaoDaTaoGoiThau(bidId)

    return { message: 'Đã duyệt thầu nhanh thành công.' }
  }

  mpoleaderRejectBidAdapter(user: UserDto, data: { bidId: string; comment: string }) {
    return this.mpoleaderRejectBid(user, data.bidId, false, data.comment)
  }

  /** MPOLeader từ chối gói thầu tạm */
  async mpoleaderRejectBid(user: UserDto, bidId: string, isMobile?: boolean, comment?: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionAcceptBid(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      status: enumData.BidStatus.GoiThauTam.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TuChoiGoiThauTam.code
    bidHistory.save()

    //send mail MPO
    this.emailService.GuiMPOKiemTraLaiGoiThauTam(bidId)

    return { message: 'Đã gửi yêu cầu kiểm tra lại gói thầu tạm thành công.' }
  }

  /** Chỉnh sửa thông tin gói thầu */
  async updateSettingRate(user: UserDto, data: BidUpdateSettingDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bid = await this.repo.findOne({ where: { id: data.id, companyId: user.companyId, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    let isSendEmailInviteBidOld = bid.isSendEmailInviteBid
    let isSendEmailInviteBidNew = data.isSendEmailInviteBid

    const objPermission = await this.checkPermissionMpoEdit(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    data.startBidDate = new Date(data.submitEndDate)
    data.startBidDate.setDate(data.startBidDate.getDate() + 1)
    data.startBidDate.setHours(8, 0, 0, 0)
    if (data.startBidDate >= data.timeCheckTechDate) {
      throw new BadRequestException('Thời điểm mở thầu phải sớm hơn thời hạn đánh giá yêu cầu kỹ thuật, năng lực.')
    }

    bid.acceptEndDate = data.acceptEndDate
    bid.submitEndDate = data.submitEndDate
    bid.startBidDate = data.startBidDate
    bid.timeCheckTechDate = data.timeCheckTechDate
    bid.timeCheckPriceDate = data.timeCheckPriceDate
    bid.percentTech = data.percentTech
    bid.percentTrade = data.percentTrade
    bid.percentPrice = data.percentPrice
    bid.isAutoBid = data.isAutoBid
    if (!bid.hasSendEmailInviteBid) {
      bid.isSendEmailInviteBid = data.isSendEmailInviteBid
    }
    bid.updatedBy = user.id
    await bid.save()

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bid.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.SuaTaoGoiThauSauDuyet.code
    bidHistory.save()

    // nếu tích "Gửi thông báo mời thầu Doanh nghiệp qua email" khi chưa gửi mail mời thầu và gói thầu trang thái Đang mời thầu => gửi mail lại
    if (!isSendEmailInviteBidOld && isSendEmailInviteBidNew && !bid.hasSendEmailInviteBid && bid.status == enumData.BidStatus.DangNhanBaoGia.code) {
      this.emailService.GuiNccThongBaoMoiThau(user, data.id)
    }

    return { message: 'Chỉnh sửa thông tin chung của gói thầu thành công.' }
  }

  /** Kiểm tra quyền sửa thông tin gói thầu */
  async checkPermissionMpoEdit(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const lstStatusCanEdit = [
      enumData.BidStatus.GoiThauTam.code,
      enumData.BidStatus.ChoDuyetGoiThauTam.code,
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({ where: { id: bidId, parentId: IsNull(), companyId: user.companyId, isDeleted: false } })
    if (bid) {
      if (lstStatusCanEdit.includes(bid.status)) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền chỉnh sửa thông tin gói thầu.'
        }
      } else {
        result = false
        message = 'Chỉ được phép chỉnh sửa thông tin gói thầu khi chưa mở thầu.'
      }
    }

    return { hasPermission: result, message }
  }
}
