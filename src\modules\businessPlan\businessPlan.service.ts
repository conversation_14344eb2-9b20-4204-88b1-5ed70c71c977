import { Injectable } from '@nestjs/common'
import {
  BusinessPlanHistoryRepository,
  BusinessPlanRepository,
  BusinessPlanTemplateColRepository,
  CompanyRepository,
  ContractRepository,
  CurrencyRepository,
  EmployeeRepository,
  ExternalMaterialGroupRepository,
  IncotermRepository,
  PermissionApproveRepository,
  PlantRepository,
  PrItemRepository,
  PrRepository,
  SettingStringRepository,
} from '../../repositories'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { CREATE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { Between, Equal, FindOptionsWhere, In, Like, Not, Raw } from 'typeorm'
import { v4 as uuidv4 } from 'uuid'
import { coreHelper } from '../../helpers'
import * as moment from 'moment'
import { BussinessPlanCreateDto, BussinessPlanUpdateDto } from './dto'
import {
  BusinessPlanColValueEntity,
  BusinessPlanEntity,
  BusinessPlanHistoryEntity,
  BusinessPlanTemplateEntity,
  PermissionApproveEntity,
  MediaFileEntity,
  BusinessPlanSettingValueEntity,
} from '../../entities'
import { FlowApproveService } from '../flowApprove/flowApprove.service'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'

@Injectable()
export class BussinessPlanService {
  constructor(
    private repo: BusinessPlanRepository,
    private companyRepo: CompanyRepository,
    private plantRepo: PlantRepository,
    private prRepo: PrRepository,
    private permissionApproveRepo: PermissionApproveRepository,
    private externalMaterialGroupRepo: ExternalMaterialGroupRepository,
    private employeeRepo: EmployeeRepository,
    private businessPlanTemplateColRepo: BusinessPlanTemplateColRepository,
    private businessPlanHistoryRepo: BusinessPlanHistoryRepository,
    private currencyRepo: CurrencyRepository,
    private contractRepo: ContractRepository,
    private incotermRepo: IncotermRepository,
    private settingStringRepo: SettingStringRepository,
    private readonly flowService: FlowApproveService,
    private organizationalPositionService: OrganizationalPositionService,
    private prItemRepo: PrItemRepository,
  ) { }

  async createData(user: UserDto, data: BussinessPlanCreateDto) {
    const checkCompany = await this.companyRepo.exists({ where: { id: data.companyId, isDeleted: false } })
    if (!checkCompany) throw new Error(`Công ty không tồn tại. Vui lòng kiểm tra lại`)
    if (data.plantId) {
      const checkPlant = await this.plantRepo.exists({ where: { id: data.plantId, isDeleted: false } })
      if (!checkPlant) throw new Error(`Plant không tồn tại. Vui lòng kiểm tra lại`)
    }

    if (data.currencyFromId) {
      const checkCurrencyFrom = await this.currencyRepo.exists({ where: { id: data.currencyFromId, isDeleted: false } })
      if (!checkCurrencyFrom) throw new Error(`Currency From không tồn tại. Vui lòng kiểm tra lại`)
    }

    if (data.currencyToId) {
      const checkCurrencyTo = await this.currencyRepo.exists({ where: { id: data.currencyToId, isDeleted: false } })
      if (!checkCurrencyTo) throw new Error(`Currency To không tồn tại. Vui lòng kiểm tra lại`)
    }

    if (data.contractId) {
      const checkContract = await this.contractRepo.exists({ where: { id: data.contractId, isDeleted: false } })
      if (!checkContract) throw new Error(`Hợp đồng không tồn tại. Vui lòng kiểm tra lại`)
    }

    const checkExternalMatGroup = await this.externalMaterialGroupRepo.exists({ where: { id: data.externalMaterialGroupId, isDeleted: false } })
    if (!checkExternalMatGroup) throw new Error(`External Mat Group không tồn tại. Vui lòng kiểm tra lại`)

    if (data.incotermId) {
      const checkIncoterm = await this.incotermRepo.exists({ where: { id: data.incotermId, isDeleted: false } })
      if (!checkIncoterm) throw new Error(`Điều kiện thương mại không tồn tại. Vui lòng kiểm tra lại`)
    }

    const checkPr = await this.prRepo.exists({ where: { id: In(data.lstPr), isDeleted: false } })
    if (!checkPr) throw new Error(`Pr không tồn tại. Vui lòng kiểm tra lại`)

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BusinessPlanEntity)
      const historyRepo = trans.getRepository(BusinessPlanHistoryEntity)
      const businessPlanColValueRepo = trans.getRepository(BusinessPlanColValueEntity)
      const businessPlanTemplateRepo = trans.getRepository(BusinessPlanTemplateEntity)
      const mediaRepo = trans.getRepository(MediaFileEntity)
      const businessPlanSettingValueRepo = trans.getRepository(BusinessPlanSettingValueEntity)

      let checkTemplate = await businessPlanTemplateRepo.findOne({
        where: { id: data.businessPlanTemplateId, status: Not(Equal(enumData.BusinessPlanTemplateStatus.UnActive.code)) },
      })
      if (!checkTemplate) throw new Error(`Template không tồn tại. Vui lòng kiểm tra lại`)

      let res = await this.genCode()
      const checkCode = await repo.findOne({ where: { code: res.code } })
      if (checkCode) throw new Error(`Mã đã tồn tại. Vui lòng kiểm tra lại`)
      const newBusinessPlanEntity = new BusinessPlanEntity()
      newBusinessPlanEntity.id = uuidv4()
      newBusinessPlanEntity.code = res.code
      newBusinessPlanEntity.name = data.name
      newBusinessPlanEntity.companyId = data.companyId
      newBusinessPlanEntity.description = data.description
      newBusinessPlanEntity.status = data.status
      newBusinessPlanEntity.businessPlanTemplateId = data.businessPlanTemplateId
      newBusinessPlanEntity.plantId = data.plantId
      newBusinessPlanEntity.createdAt = new Date()
      newBusinessPlanEntity.createdBy = user.id
      newBusinessPlanEntity.currencyFromId = data.currencyFromId
      newBusinessPlanEntity.currencyToId = data.currencyToId
      newBusinessPlanEntity.exchangeRate = data.exchangeRate
      newBusinessPlanEntity.paymentMethodId = data.paymentMethodId
      newBusinessPlanEntity.incotermId = data.incotermId
      newBusinessPlanEntity.incotermDescription = data.incotermDescription
      newBusinessPlanEntity.deliveryAddress = data.deliveryAddress
      newBusinessPlanEntity.estimatedTime = data.estimatedTime
      newBusinessPlanEntity.purchasingSource = data.purchasingSource
      newBusinessPlanEntity.sellingSource = data.sellingSource
      newBusinessPlanEntity.intermediarySupplier = data.intermediarySupplier
      newBusinessPlanEntity.contractId = data.contractId
      newBusinessPlanEntity.plantId = data.plantId
      newBusinessPlanEntity.sloc = data.sloc
      newBusinessPlanEntity.employeeId = user.employeeId
      newBusinessPlanEntity.externalMaterialGroupId = data.externalMaterialGroupId
      await repo.insert(newBusinessPlanEntity)

      if (data.fileList && data.fileList.length > 0) {
        for (let item of data.fileList) {
          const newItem = new MediaFileEntity()
          newItem.fileUrl = item.fileUrl
          newItem.fileName = item.fileName
          newItem.businessPlanId = newBusinessPlanEntity.id
          newItem.createdAt = new Date()
          newItem.createdBy = user.id
          await mediaRepo.insert(newItem)
        }
      }

      if (data.lstColValue && data.lstColValue.length > 0) {
        for (let x of data.lstCol) {
          for (let item of data.lstColValue) {
            const valueToReplace = item[x.dataMapping] || item[x.code]
            const newItem = new BusinessPlanColValueEntity()
            newItem.prId = item.prId
            newItem.businessPlanId = newBusinessPlanEntity.id
            newItem.businessPlanTemplateId = x.businessPlanTemplateId
            newItem.materialId = item.materialId
            newItem.businessPlanTemplateColId = x.id
            newItem.createdAt = new Date()
            newItem.createdBy = user.id
            newItem.value = valueToReplace
            newItem.quantity = item.quantity
            await businessPlanColValueRepo.insert(newItem)
          }
        }
      }
      if (data.status === enumData.BusinessPlanStatus.New.code) {
        await businessPlanTemplateRepo.update({ id: data.businessPlanTemplateId }, { status: enumData.RoundUpContStatus.Complete.code })
      }

      if (data.lstBusinessPlanTemplateSettingCol && data.lstBusinessPlanTemplateSettingCol.length > 0) {
        for (let item of data.lstBusinessPlanTemplateSettingCol) {
          const newItem = new BusinessPlanSettingValueEntity()
          newItem.settingStringId = item.settingStringId
          newItem.businessPlanId = newBusinessPlanEntity.id
          newItem.createdAt = new Date()
          newItem.createdBy = user.id
          newItem.businessPlanTemplateColId = item.businessPlanTemplateColId
          newItem.value = item.value
          await businessPlanSettingValueRepo.insert(newItem)
        }
      }

      const hisTem = new BusinessPlanHistoryEntity()
      hisTem.businessPlanTemplateId = data.businessPlanTemplateId
      hisTem.createdByName = user.username
      hisTem.createdAt = new Date()
      hisTem.createdBy = user.id
      hisTem.description =
        'Tài Khoản [' + user.username + '] vừa cập nhật trạng thái sử dụng cho template phương án kinh doanh mã [' + checkTemplate.code + '] '
      await historyRepo.insert(hisTem)

      const his = new BusinessPlanHistoryEntity()
      his.businessPlanId = newBusinessPlanEntity.id
      his.createdByName = user.username
      his.createdAt = new Date()
      his.createdBy = user.id
      his.description = 'Tài Khoản [' + user.username + '] vừa tạo mới phương án kinh doanh với mã [' + newBusinessPlanEntity.code + '] '
      await historyRepo.insert(his)
    })
    return { message: CREATE_SUCCESS }
  }

  async updateData(user: UserDto, data: BussinessPlanUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(`Phương án kinh doanh không tồn tại. Vui lòng kiểm tra lại`)

    const checkCompany = await this.companyRepo.exists({ where: { id: data.companyId, isDeleted: false } })
    if (!checkCompany) throw new Error(`Công ty không tồn tại. Vui lòng kiểm tra lại`)

    const checkPlant = await this.plantRepo.exists({ where: { id: data.plantId, isDeleted: false } })
    if (!checkPlant) throw new Error(`Plant không tồn tại. Vui lòng kiểm tra lại`)

    const checkExternalMatGroup = await this.externalMaterialGroupRepo.exists({ where: { id: data.externalMaterialGroupId, isDeleted: false } })
    if (!checkExternalMatGroup) throw new Error(`External Mat Group không tồn tại. Vui lòng kiểm tra lại`)

    const checkPr = await this.prRepo.exists({ where: { id: In(data.lstPr), isDeleted: false } })
    if (!checkPr) throw new Error(`Pr không tồn tại. Vui lòng kiểm tra lại`)

    if (data.currencyFromId) {
      const checkCurrencyFrom = await this.currencyRepo.exists({ where: { id: data.currencyFromId, isDeleted: false } })
      if (!checkCurrencyFrom) throw new Error(`Currency From không tồn tại. Vui lòng kiểm tra lại`)
    }

    if (data.currencyToId) {
      const checkCurrencyTo = await this.currencyRepo.exists({ where: { id: data.currencyToId, isDeleted: false } })
      if (!checkCurrencyTo) throw new Error(`Currency To không tồn tại. Vui lòng kiểm tra lại`)
    }

    if (data.contractId) {
      const checkContract = await this.contractRepo.exists({ where: { id: data.contractId, isDeleted: false } })
      if (!checkContract) throw new Error(`Hợp đồng không tồn tại. Vui lòng kiểm tra lại`)
    }

    if (data.incotermId) {
      const checkIncoterm = await this.incotermRepo.exists({ where: { id: data.incotermId, isDeleted: false } })
      if (!checkIncoterm) throw new Error(`Điều kiện thương mại không tồn tại. Vui lòng kiểm tra lại`)
    }

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BusinessPlanEntity)
      const historyRepo = trans.getRepository(BusinessPlanHistoryEntity)

      const businessPlanColValueRepo = trans.getRepository(BusinessPlanColValueEntity)
      const businessPlanTemplateRepo = trans.getRepository(BusinessPlanTemplateEntity)
      const businessPlanSettingValueRepo = trans.getRepository(BusinessPlanSettingValueEntity)
      const mediaRepo = trans.getRepository(MediaFileEntity)

      let checkTemplate = await businessPlanTemplateRepo.findOne({
        where: { id: data.businessPlanTemplateId, status: Not(Equal(enumData.RoundUpContTemplateStatus.UnActive.code)) },
      })
      if (!checkTemplate) throw new Error(`Template không tồn tại. Vui lòng kiểm tra lại`)
      entity.name = data.name
      entity.companyId = data.companyId
      entity.description = data.description

      if (entity.status === enumData.BusinessPlanStatus.CheckAgain.code || entity.status === enumData.BusinessPlanStatus.New.code) {
        await businessPlanTemplateRepo.update({ id: data.businessPlanTemplateId }, { status: enumData.BusinessPlanTemplateStatus.Active.code })
        entity.status = entity.status
      } else {
        entity.status = data.status
      }

      entity.businessPlanTemplateId = data.businessPlanTemplateId
      entity.plantId = data.plantId
      entity.updatedAt = new Date()
      entity.updatedBy = user.id
      entity.currencyFromId = data.currencyFromId
      entity.currencyToId = data.currencyToId
      entity.exchangeRate = data.exchangeRate
      entity.paymentMethodId = data.paymentMethodId
      entity.incotermId = data.incotermId
      entity.incotermDescription = data.incotermDescription
      entity.deliveryAddress = data.deliveryAddress
      entity.estimatedTime = data.estimatedTime
      entity.purchasingSource = data.purchasingSource
      entity.sellingSource = data.sellingSource
      entity.intermediarySupplier = data.intermediarySupplier
      entity.contractId = data.contractId
      entity.plantId = data.plantId
      entity.sloc = data.sloc
      entity.employeeId = user.employeeId
      entity.externalMaterialGroupId = data.externalMaterialGroupId
      await repo.save(entity)

      await mediaRepo.delete({ businessPlanId: entity.id })
      if (data.fileList && data.fileList.length > 0) {
        for (let item of data.fileList) {
          const newItem = new MediaFileEntity()
          newItem.fileUrl = item.fileUrl
          newItem.fileName = item.fileName
          newItem.businessPlanId = entity.id
          newItem.createdAt = new Date()
          newItem.createdBy = user.id
          await mediaRepo.insert(newItem)
        }
      }

      await businessPlanColValueRepo.delete({ businessPlanId: entity.id })

      if (data.lstColValue && data.lstColValue.length > 0) {
        for (let x of data.lstCol) {
          for (let item of data.lstColValue) {
            let valueToReplace: any
            if (x.isNew) {
              valueToReplace = item[x.dataMapping] || item[x.code]
            } else {
              valueToReplace = item[x.id]
            }

            const newItem = new BusinessPlanColValueEntity()
            newItem.prId = item.prId
            newItem.businessPlanId = entity.id
            newItem.businessPlanTemplateId = x.businessPlanTemplateId
            newItem.materialId = item.materialId
            newItem.businessPlanTemplateColId = x.id
            newItem.createdAt = new Date()
            newItem.createdBy = user.id
            newItem.value = valueToReplace
            newItem.quantity = item.quantity
            await businessPlanColValueRepo.insert(newItem)
          }
        }
      }

      await businessPlanSettingValueRepo.delete({ businessPlanId: entity.id })
      if (data.lstBusinessPlanTemplateSettingCol && data.lstBusinessPlanTemplateSettingCol.length > 0) {
        for (let item of data.lstBusinessPlanTemplateSettingCol) {
          const newItem = new BusinessPlanSettingValueEntity()
          newItem.settingStringId = item.settingStringId
          newItem.businessPlanId = entity.id
          newItem.createdAt = new Date()
          newItem.createdBy = user.id
          if (item.businessPlanTemplateColId) {
            newItem.businessPlanTemplateColId = item.businessPlanTemplateColId
          } else {
            newItem.businessPlanTemplateColId = item.id
          }

          newItem.value = item.value
          await businessPlanSettingValueRepo.insert(newItem)
        }
      }

      const his = new BusinessPlanHistoryEntity()
      his.businessPlanId = entity.id
      his.createdByName = user.username
      his.createdAt = new Date()
      his.createdBy = user.id
      his.description = 'Tài Khoản [' + user.username + '] vừa cập nhật phương án kinh doanh với mã [' + entity.code + '] '
      await historyRepo.insert(his)
    })
    return { message: UPDATE_SUCCESS }
  }

  async genCode() {
    const curDate = new Date()
    const fd = coreHelper.getFirstDayOfMonth(curDate)
    const ld = coreHelper.getLastDayOfMonth(curDate)

    let count = await this.repo.count({
      where: { createdAt: Between(fd, ld) },
    })

    const month = moment(curDate).format('MM')
    const year = moment(curDate).format('YY')

    let code: string
    let isDuplicate: boolean

    do {
      let countText = `000${count + 1}`
      countText = countText.slice(countText.length - 4, countText.length)
      code = `PAKD${month}${year}${countText}`

      // Kiểm tra mã đã tồn tại
      isDuplicate = await this.repo.exists({
        where: { code },
      })

      if (isDuplicate) {
        count++
      }
    } while (isDuplicate)

    return { code }
  }

  public async pagination(data: PaginationDto, user: UserDto) {
    const whereCon: FindOptionsWhere<BusinessPlanEntity> = {}
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.BusinessPlan.code)
    if (dataRs.type === enumData.RoleData.All.code) {
      /* do nothing */
    }
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.businessPlanTemplateId) whereCon.businessPlanTemplateId = data.where.businessPlanTemplateId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.paymentMethodId) whereCon.paymentMethodId = data.where.paymentMethodId
    if (data.where.companyId) whereCon.companyId = data.where.companyId
    if (data.where.plantId) whereCon.plantId = data.where.plantId
    if (data.where.currencyFromId) whereCon.currencyFromId = data.where.currencyFromId
    if (data.where.currencyToId) whereCon.currencyToId = data.where.currencyToId
    if (data.where.contractId) whereCon.contractId = data.where.contractId
    if (data.where.externalMaterialGroupId) whereCon.externalMaterialGroupId = data.where.externalMaterialGroupId

    if (data.where.materialId) {
      whereCon.businessPlanColValues = {}
      if (typeof data.where.materialId == 'string') {
        whereCon.businessPlanColValues.materialId = data.where.materialId
      } else {
        whereCon.businessPlanColValues.materialId = In(data.where.materialId)
      }
    }

    if (data.where.estimatedTime) {
      whereCon.estimatedTime = Raw((alias) => `DATE(${alias}) = DATE("${moment(data.where.estimatedTime).format('YYYY-MM-DD')}")`)
    }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC', code: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: { businessPlanTemplate: true, company: true, paymentMethod: true, businessPlanColValues: { material: true } },
    })

    if (res[0].length == 0) return [[], 0]

    const dictUser: any = {}
    {
      const lstId = coreHelper.selectDistinct(res[0], 'createdBy')
      const lstUser: any = await this.employeeRepo.find({
        where: { userId: In(lstId), isDeleted: false },
        select: { id: true, name: true, userId: true },
      })
      lstUser.forEach((c) => (dictUser[c.userId] = c))
    }

    const dictUserUpdate: any = {}
    {
      const lstId = coreHelper.selectDistinct(res[0], 'updatedBy')
      const lstUser: any = await this.employeeRepo.find({
        where: { userId: In(lstId), isDeleted: false },
        select: { id: true, name: true, userId: true },
      })
      lstUser.forEach((c) => (dictUserUpdate[c.userId] = c))
    }
    for (const item of res[0]) {
      if (item.updatedBy) {
        item.employeeName = dictUserUpdate[item.updatedBy]?.name
      } else {
        item.employeeName = dictUser[item.createdBy]?.name
      }
      item.businessPlanTemplateName = item?.__businessPlanTemplate__?.name
      item.companyName = item?.__company__?.name
      item.paymentMethodName = item?.__paymentMethod__?.name
      item.statusName = enumData.BusinessPlanStatus[item.status].name
      item.statusColor = enumData.BusinessPlanStatus[item.status].color
      item.statusBgColor = enumData.BusinessPlanStatus[item.status].backgroundColor
      item.statusBorderColor = enumData.BusinessPlanStatus[item.status].statusBorderColor

      item.lstMaterialName = Array.from(new Set(item?.__businessPlanColValues__?.map((x: any) => x?.__material__?.name)))

      let canApprove = false
      const lstRole = await this.flowService.getLstRole(user, {
        targetId: item.id,
        entityName: BusinessPlanEntity.name,
        approvedType: user.employeePosition,
      })
      item.createdByName = dictUser[item.createdBy]?.name
      item.createdByCode = dictUser[item.createdBy]?.code
      item.level = lstRole.level
      for (const role of lstRole.lstApprove) {
        if (user.employeePosition.includes(role)) {
          canApprove = true
          break
        }
      }
      item.canApprove = canApprove

      if (item.createdBy === user.id) {
        item.isCreate = true
      } else {
        item.isCreate = false
      }

      delete item.__businessPlanColValues__
      delete item.__businessPlanTemplate__
      delete item.__company__
      delete item.__paymentMethod__
    }
    return res
  }

  /** Cập nhật trạng thái kích hoạt của. */
  async updateActive(user: UserDto, id: string) {
    const entity = await this.repo.findOne({
      where: { id: id },
      select: { id: true, isDeleted: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const newIsDeleted = !entity.isDeleted
    await this.repo.update(id, {
      isDeleted: newIsDeleted,
      updatedBy: user.id,
      updatedAt: new Date(),
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async findDetail(data: FilterOneDto) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: {
        businessPlanTemplate: true,
        histories: true,
        company: true,
        plant: true,
        businessPlanColValues: { material: true, businessPlanTemplateCol: true },
        currencyFrom: true,
        currencyTo: true,
        paymentMethod: true,
        incoterm: true,
        mediaFiles: true,
        contract: true,
        businessPlanSettingValue: { settingString: true, businessPlanTemplateCol: true },
        externalMaterialGroup: true,
      },
      order: { histories: { createdAt: 'DESC' } },
      select: {
        company: { id: true, name: true, code: true },
        plant: { id: true, name: true, code: true },
        businessPlanTemplate: { id: true, name: true, code: true, type: true, isDeleted: true, status: true },
        contract: { id: true, code: true },
        currencyFrom: { code: true, name: true },
        currencyTo: { code: true, name: true },
        paymentMethod: { code: true, name: true },
        incoterm: { code: true, name: true },
        externalMaterialGroup: { code: true, name: true, id: true },
      },
    })
    res.businessPlanTemplateName = res?.__businessPlanTemplate__?.name
    res.businessPlanTemplateCode = res?.__businessPlanTemplate__?.code
    res.businessPlanTemplateLabel = res.businessPlanTemplateCode + ' - ' + res.businessPlanTemplateName

    res.externalMaterialGroupName = res?.__externalMaterialGroup__?.name
    res.externalMaterialGroupCode = res?.__externalMaterialGroup__?.code
    res.externalMaterialGroupLabel = res.externalMaterialGroupCode + ' - ' + res.externalMaterialGroupName

    res.companyName = res?.__company__?.name
    res.companyCode = res?.__company__?.code
    res.companylabel = res?.__company__?.code + ' - ' + res?.__company__?.name

    res.plantName = res?.__plant__?.name
    res.plantCode = res?.__plant__?.code
    res.plantLabel = res?.__plant__?.code + ' - ' + res?.__plant__?.name
    res.currencyFromLabel = res?.__currencyFrom__?.code + ' - ' + res?.__currencyFrom__?.name
    res.currencyToLabel = res?.__currencyTo__?.code + ' - ' + res?.__currencyTo__?.name
    if (res.paymentMethodId) res.paymentMethodLabel = res?.__paymentMethod__?.code + ' - ' + res?.__paymentMethod__?.name
    if (res.incotermId) res.incotermLabel = res.incotermId ? res?.__incoterm__?.code + ' - ' + res?.__incoterm__?.name : ''

    res.lstHistory = res.__histories__
    const lstId = res?.__businessPlanColValues__?.map((x: any) => x.businessPlanTemplateColId) || []
    const lstColum: any = await this.businessPlanTemplateColRepo.find({
      where: { businessPlanTemplateId: res.businessPlanTemplateId, id: In(lstId) },
      order: { sort: 'ASC' },
    })

    const lstItem = []
    let lstMaterial = this.sumQuantityAndRemoveDuplicates(lstItem)
    for (let mat of lstMaterial) {
      for (let item of res.__businessPlanColValues__) {
        item.businessPlanTemplateColCode = item.__businessPlanTemplateCol__?.code
        item.businessPlanTemplateColName = item.__businessPlanTemplateCol__?.name
        mat[item.businessPlanTemplateColId] = item.value
      }
    }

    for (let item of lstColum) {
      item.total = 0
      for (let mat of lstMaterial) {
        item.isEdit = true
        if (item.type === enumData.DataType.Number.code && item.colType === enumData.DataMapping.Database.code) {
          // Nếu cột có kiểu number
          const columnValue = mat[item.id]
          item.total += +columnValue
        } else if (item.type === enumData.DataType.Number.code && item.colType === enumData.DataMapping.DynamicSetup.code) {
          const columnValue = mat[item.id]
          item.total = columnValue
        } else {
          const columnValue = mat[item.id]
          item.total += +columnValue
        }
      }
    }

    for (let mat of lstMaterial) {
      mat.materialCode = mat.__material__?.code
      mat.materialName = mat.__material__?.name
      mat.unitCode = mat.__material__?.unitCode
      mat.lngth = mat.__material__?.lngth
      mat.width = mat.__material__?.width
      mat.height = mat.__material__?.height
      for (let x of mat.lstGroup) {
        delete x.__material__
      }
      delete mat.__businessPlanTemplateCol__
    }

    res.lstPr = res?.__businessPlanPrs__?.map((x: any) => x.prId) || []
    res.lstPrCode = res?.__businessPlanPrs__?.map((x: any) => x.__pr__.code) || []
    res.listMaterial = { lstMaterial, lstColum }

    res.statusName = enumData.BusinessPlanStatus[res.status].name
    res.statusColor = enumData.BusinessPlanStatus[res.status].color
    res.statusBgColor = enumData.BusinessPlanStatus[res.status].backgroundColor
    res.statusBorderColor = enumData.BusinessPlanStatus[res.status].statusBorderColor
    res.fileList = res.__mediaFiles__
    if (res.contractId) res.contractCode = res?.__contract__?.code

    for (let item of res?.__businessPlanSettingValue__) {
      item.code = item?.__settingString__?.code
      item.name = item?.__settingString__?.name
      item.isEdited = item?.__businessPlanTemplateCol__?.isEdited
      delete item.__settingString__
      delete item.__businessPlanTemplateCol__
    }

    res.lstBusinessPlanTemplateSettingCol = res?.__businessPlanSettingValue__
    delete res.__externalMaterialGroup__
    delete res.__businessPlanSettingValue__
    delete res.__mediaFiles__
    delete res.__businessPlanTemplate__
    delete res.__businessPlanPrs__
    delete res.__company__
    delete res.__histories__
    delete res.__plant__
    delete res.__roundUpContColValue__
    delete res?.__businessPlanExternalMaterialGroup__
    delete res.__currencyFrom__
    delete res?.__currencyTo__
    delete res.__paymentMethod__
    delete res?.__incoterm__
    delete res?.__businessPlanColValues__
    delete res?.__businessPlanPrItems__
    return res
  }

  sumQuantityAndRemoveDuplicates(array: any) {
    const materialMap = new Map()

    // Nhóm các phần tử theo materialId và tính tổng quantity
    array.forEach((item: any) => {
      const key = item.materialId

      if (!materialMap.has(key)) {
        // Nếu chưa tồn tại, tạo mới
        materialMap.set(key, { ...item, lstGroup: [item] })
      } else {
        // Nếu đã tồn tại, cập nhật quantity và thêm vào groupedItems
        const existing = materialMap.get(key)
        existing.quantity += item.quantity
        existing.lstGroup.push(item)
      }
    })

    // Chuyển Map thành mảng
    return Array.from(materialMap.values())
  }

  async calculator(data: { lstColum: any; lstMaterial: any; lstBusinessPlanTemplateSettingCol: any }) {
    const dictCol: any = {}
    {
      let lstCol = coreHelper.convertObjToArray(enumData.BusinessPlanCol)
      for (const item of lstCol) dictCol[item.code] = item
    }

    const lstColEnum = coreHelper
      .convertObjToArray(enumData.SettingString)
      .filter((x) => x.type === enumData.SettingStringTypeDynamic.DynamicConfiguration.code)
    const dictColDymamic: any = {}
    {
      let lstCol = await this.settingStringRepo.find({ where: { type: enumData.SettingStringTypeDynamic.DynamicConfiguration.code } })
      for (const item of lstCol) dictColDymamic[item.code] = item
    }

    for (let item of data.lstColum) {
      item.total = 0
      item.isNew = true
      for (let mat of data.lstMaterial) {
        if (item.colType === enumData.DataMapping.Database.code) {
          mat.roundUpContTemplateColId = item.id
          mat.value = dictCol[item.dataMapping]?.value
          if (mat.value <= 15) {
            if (mat.value <= 5) {
              mat[dictCol[item.dataMapping]?.code] = 1500
            } else if (mat.value > 5 && mat.value <= 10) {
              mat[dictCol[item.dataMapping]?.code] = 1700
            } else {
              mat[dictCol[item.dataMapping]?.code] = 500
            }
          } else if (mat.value > 15 && mat.value <= 19) {
            mat[dictCol[item.dataMapping]?.code] = 2000
          }
        } else if (item.colType === enumData.DataMapping.DynamicSetup.code) {
          if (dictColDymamic[item.dataMapping] && item.isEdited === false) {
            mat[dictColDymamic[item.dataMapping]?.code] = dictColDymamic[item.dataMapping]?.value
          } else if (dictColDymamic[item.dataMapping] && item.isEdited === true) {
            const itemSetting = data.lstBusinessPlanTemplateSettingCol.find((x) => x.dataMapping === item.dataMapping || x.code === item.dataMapping)
            mat[dictColDymamic[item.dataMapping]?.code] = itemSetting?.value
          } else {
            const itemSetting = lstColEnum.find((x) => x.code === item.dataMapping)
            mat[dictColDymamic[item.dataMapping]?.code] = itemSetting?.value
          }
        }
        if (item.fomular && item.fomular.length > 0) {
          let value = await coreHelper.calFomularTemplate(item.fomular, data.lstColum, mat)
          mat[item.code] = value
        }

        if (item.type === enumData.DataType.Number.code && item.colType === enumData.DataMapping.Database.code) {
          // Nếu cột có kiểu number
          const columnValue = mat[item.dataMapping] || mat[item.code]
          if (typeof columnValue === 'number') {
            item.total += columnValue // Cộng dồn vào tổng
          }
        } else if (item.type === enumData.DataType.Number.code && item.colType === enumData.DataMapping.DynamicSetup.code) {
          const columnValue = mat[item.dataMapping] || mat[item.code]
          if (typeof columnValue === 'number') {
            item.total = columnValue // Cộng dồn vào tổng
          }
        } else {
          const columnValue = mat[item.dataMapping] || mat[item.code]
          if (typeof columnValue === 'number') {
            item.total += columnValue // Cộng dồn vào tổng
          }
        }
      }
    }

    return data
  }

  /* Hàm load item bằng buisiness Plan */
  public async loadItemByBusinessPlan(user: UserDto, data: { id: string }) {
    // const res: any = await this.businessPlanPrItemRepo.find({
    //   where: { businessPlanId: data.id },
    //   relations: {
    //     prItem: {
    //       material: true,
    //       purchasingGroup: true,
    //       materialGroup: true,
    //       glAccount: true,
    //       plant: true,
    //       unit: true,
    //       mediaFiles: true,
    //     },
    //   },
    // })

    // let index = 0
    // for (let item of res) {
    //   const category = lstCategory.find((x: any) => x.code === item?.__prItem__?.category)
    //   item.category = item?.__prItem__?.category
    //   item.categoryName = category?.name
    //   item.materialCode = item?.__prItem__?.__material__?.code
    //   item.shortText = item?.__prItem__?.shortText
    //   item.itemNo = item?.__prItem__?.itemNo
    //   item.purchasingGroupCode = item?.__prItem__?.__purchasingGroup__?.code
    //   item.purchasingGroupName = item?.__prItem__?.__purchasingGroup__?.name
    //   item.materialGroupCode = item?.__prItem__?.__materialGroup__?.code
    //   item.materialGroupName = item?.__prItem__?.__materialGroup__?.name
    //   item.glAccountName = item?.__prItem__?.__glAccount__?.name
    //   item.plantName = item?.__prItem__?.__plant__?.name
    //   item.isChangePrItem = item.isDeleted
    //   item.lstItemCompoment = item?.__prItem__?.__prItemCompoments__ || []
    //   item.unitName = item?.__prItem__?.__unit__?.name
    //   item.fileList = item?.__prItem__?.__mediaFiles__ || []
    //   item.index = index
    //   index++
    //   delete item.__material__
    //   delete item.__purchasingGroup__
    //   delete item.__materialGroup__
    //   delete item.__order__
    //   delete item.__glAccount__
    //   delete item.__plant__
    //   delete item.__prItemCompoments__
    //   delete item.__unit__
    //   delete item.__mediaFiles__
    // }

    return []
  }

  async updateUnActive(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({
      where: { id: data.id },
      select: { id: true, isDeleted: true, code: true, status: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, {
      status: enumData.BusinessPlanStatus.Cancel.code,
      updatedBy: user.id,
      updatedAt: new Date(),
    })

    const his = new BusinessPlanHistoryEntity()
    his.businessPlanId = entity.id
    his.createdByName = user.username
    his.createdAt = new Date()
    his.createdBy = user.id
    his.description = 'Tài Khoản [' + user.username + '] vừa hủy phương án kinh doanh với mã [' + entity.code + '] '
    await this.businessPlanHistoryRepo.insert(his)

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async updateWait(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({
      where: { id: data.id },
      select: { id: true, isDeleted: true, code: true, status: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    if (entity.status !== enumData.BusinessPlanStatus.New.code && entity.status !== enumData.BusinessPlanStatus.CheckAgain.code) {
      throw new Error(`Trạng thái không hợp lệ. Vui lòng kiểm tra lại`)
    }

    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(BusinessPlanEntity)
      const businessPlanHistoryRepo = transac.getRepository(BusinessPlanHistoryEntity)
      const permissionApproveRepo = transac.getRepository(PermissionApproveEntity)
      await repo.update(data.id, {
        status: enumData.BusinessPlanStatus.Wait.code,
        updatedBy: user.id,
        updatedAt: new Date(),
      })
      await this.permissionApproveRepo.delete({ entityName: BusinessPlanEntity.name, targetId: entity.id }, transac)
      await this.flowService.setRoleRule(user, {
        targetId: entity.id,
        target: entity,
        entityName: BusinessPlanEntity.name,
        flowType: enumData.FlowCode.BUSINESSPLAN.code,
        companyId: user.orgCompanyId,
      })
      const his = new BusinessPlanHistoryEntity()
      his.businessPlanId = entity.id
      his.createdByName = user.username
      his.createdAt = new Date()
      his.createdBy = user.id
      his.description = 'Tài Khoản [' + user.username + '] vừa gửi duyệt phương án kinh doanh với mã [' + entity.code + '] '
      await businessPlanHistoryRepo.insert(his)
    })

    return { message: 'Đã gửi yêu cầu phê duyệt PAKD' }
  }

  async updateCheckAgain(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({
      where: { id: data.id },
      select: { id: true, isDeleted: true, code: true, status: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    if (entity.status !== enumData.BusinessPlanStatus.Wait.code) {
      throw new Error(`Trạng thái không hợp lệ. Vui lòng kiểm tra lại`)
    }

    await this.repo.update(data.id, {
      status: enumData.BusinessPlanStatus.CheckAgain.code,
      updatedBy: user.id,
      updatedAt: new Date(),
    })

    const his = new BusinessPlanHistoryEntity()
    his.businessPlanId = entity.id
    his.createdByName = user.username
    his.createdAt = new Date()
    his.createdBy = user.id
    his.description = 'Tài Khoản [' + user.username + '] vừa yêu cầu kiểm tra lại phương án kinh doanh với mã [' + entity.code + '] '
    await this.businessPlanHistoryRepo.insert(his)

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async updateApproved(user: UserDto, data: FilterOneDto) {
    const approveStatus = await this.flowService.approveRule(user, { targetId: data.id, entityName: BusinessPlanEntity.name })
    const entity = await this.repo.findOne({
      where: { id: data.id },
      select: { id: true, isDeleted: true, code: true, status: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status !== enumData.BusinessPlanStatus.Wait.code) {
      throw new Error(`Trạng thái không hợp lệ. Vui lòng kiểm tra lại`)
    }
    await this.repo.manager.transaction(async (transac) => {
      const repo = transac.getRepository(BusinessPlanEntity)
      const businessPlanHistoryRepo = transac.getRepository(BusinessPlanHistoryEntity)
      if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
        const his = new BusinessPlanHistoryEntity()
        his.businessPlanId = entity.id
        his.createdByName = user.username
        his.createdAt = new Date()
        his.createdBy = user.id
        his.description = 'Tài Khoản [' + user.username + '] vừa cập nhật duyệt phương án kinh doanh có mã [' + entity.code + '] '
        his.description += `<br/>  Phương án kinh doanh được duyệt, Vui lòng chờ cấp sau duyệt `
        await businessPlanHistoryRepo.insert(his)
        return { message: ` Đã duyệt PAKD thành công, Vui lòng chờ cấp sau duyệt` }
      }
      await repo.update(data.id, {
        status: enumData.BusinessPlanStatus.Approved.code,
        updatedBy: user.id,
        updatedAt: new Date(),
      })
      const his = new BusinessPlanHistoryEntity()
      his.businessPlanId = entity.id
      his.createdByName = user.username
      his.createdAt = new Date()
      his.createdBy = user.id
      his.description = 'Tài Khoản [' + user.username + '] vừa duyệt phương án kinh doanh với mã [' + entity.code + '] '
      await businessPlanHistoryRepo.insert(his)
    })
    return { message: 'Đã duyệt PAKD thành công' }
  }

  public async find(data: FilterOneDto) {
    const whereCon: FindOptionsWhere<BusinessPlanEntity> = {}
    if (data.code) whereCon.code = data.code
    if (data.status) {
      if (typeof data.status == 'string') {
        whereCon.status = data.status
      } else {
        whereCon.status = In(data.status)
      }
    }
    let res: any = await this.repo.find({ where: whereCon, order: { createdAt: 'DESC' } })
    if (res.length === 0) return []
    return res
  }

  public async findListPr(data: FilterOneDto) {
    const res: any = await this.repo.findOne({
      where: { id: data.id, isDeleted: false },
      relations: {
        businessPlanColValues: { material: true },
      },
    })
    if (!res) throw new Error(`Phương án kinh doanh không tồn tại. Vui lòng kiểm tra lại`)
    res.lstPrCode = Array.from(new Set(res?.__businessPlanPrs__?.map((x: any) => x?.__pr__?.code)))
    res.lstPrId = Array.from(new Set(res?.__businessPlanPrs__?.map((x: any) => x?.__pr__?.id)))
    for (let item of res?.__businessPlanColValues__) {
      item.materialName = item.__material__?.name
      item.materialCode = item.__material__?.code
      item.unitCode = item.__material__?.unitCode
      delete item.__material__
    }

    res.lstExternalMaterialGroupCode = Array.from(
      new Set(res?.__businessPlanExternalMaterialGroup__?.map((x: any) => x?.__externalMaterialGroup__?.code)),
    )
    res.lstExternalMaterialGroupId = Array.from(
      new Set(res?.__businessPlanExternalMaterialGroup__?.map((x: any) => x?.__externalMaterialGroup__?.id)),
    )

    for (let item of res?.__businessPlanPrItems__) {
      item.materialName = item.__material__?.name
      item.materialCode = item.__material__?.code
      item.unitCode = item.__material__?.unitCode
      delete item.__material__
    }

    res.lstMaterial = this.sumQuantityAndRemoveDuplicates(res?.__businessPlanPrItems__)
    delete res.__businessPlanColValues__
    delete res.__businessPlanPrs__
    delete res.__businessPlanExternalMaterialGroup__
    delete res.__businessPlanPrItems__
    return res
  }

  async findColSetting(data: FilterOneDto) {
    const res: any = await this.businessPlanTemplateColRepo.find({
      where: { businessPlanTemplateId: data.businessPlanTemplateId, isDeleted: false, colType: enumData.DataMapping.DynamicSetup.code },
    })
    if (res.length === 0) return []
    const lstCodeMapping = res.map((x) => x.dataMapping)
    if (lstCodeMapping.length === 0) return []
    const lstColData: any = await this.settingStringRepo.find({ where: { code: In(lstCodeMapping) } })
    if (lstColData.length === 0) return []
    const mapping: any = new Map(res.map((x) => [x.dataMapping, x]))

    for (let item of lstColData) {
      const mappedItem = mapping.get(item.code)
      if (mappedItem) {
        mappedItem.value = item.value
        mappedItem.settingStringId = item.id
        mappedItem.settingStringCode = item.code
        mappedItem.businessPlanTemplateColId = mapping.get(item.code).id
      }
    }
    return res
  }

  public async findListMaterialItem(data: FilterOneDto) {
    const res: any = await this.repo.findOne({
      where: { id: data.id, isDeleted: false },
      relations: {},
    })
    if (!res) throw new Error(`Phương án kinh doanh không tồn tại. Vui lòng kiểm tra lại`)

    let lstPrItemId = res?.__businessPlanPrItems__?.map((x: any) => x.prItemId)

    let dict: any = {}
    {
      let lstItem = await this.prItemRepo.find({
        where: { id: In(lstPrItemId), isDeleted: false },
        relations: {
          material: true,
          materialGroup: true,
          purchasingGroup: true,
          glAccount: true,
          prItemCompoments: true,
          plant: true,
          unit: true,
          mediaFiles: true,
          oun: true,
        },
      })
      lstItem.forEach((c) => (dict[c.id] = c))
    }
    let index = 1

    for (let item of res?.__businessPlanPrItems__) {
      item.materialName = item.__material__?.name
      item.unitCode = item.__material__?.unitCode
    }

    res.lstMaterial = this.sumQuantityAndRemoveDuplicates(res?.__businessPlanPrItems__)
    for (let item of res.lstMaterial) {
      item.shortText = item.materialName
      item.materialId = dict[item.prItemId]?.__material__?.id
      item.materialCode = dict[item.prItemId]?.__material__?.code



      item.fundProgram = dict[item.prItemId]?.__materialGroup__?.fundProgram
      item.fundCenter = dict[item.prItemId]?.__materialGroup__?.fundCenter

      item.purchasingGroupCode = dict[item.prItemId]?.__purchasingGroup__?.code
      item.purchasingGroupName = dict[item.prItemId]?.__purchasingGroup__?.name
      item.purchasingGroupId = dict[item.prItemId]?.__purchasingGroup__?.id

      item.materialGroupCode = dict[item.prItemId]?.__materialGroup__?.code
      item.materialGroupName = dict[item.prItemId]?.__materialGroup__?.name
      item.materialGroupId = dict[item.prItemId]?.__materialGroup__?.id
      item.isChangePrItem = false

      item.glAccountName = dict[item.prItemId]?.__glAccount__?.name
      item.glAccountId = dict[item.prItemId]?.__glAccount__?.id
      item.glAccountCode = dict[item.prItemId]?.__glAccount__?.code

      item.ounName = dict[item.prItemId]?.__oun__?.name
      item.ounId = dict[item.prItemId]?.__oun__?.id

      item.plantName = dict[item.prItemId]?.__plant__?.name
      item.plantCode = dict[item.prItemId]?.__plant__?.code
      item.plantId = dict[item.prItemId]?.__plant__?.id

      item.isChangePrItem = dict[item.prItemId].isDeleted

      item.unitName = dict[item.prItemId]?.__unit__?.name
      item.unitId = dict[item.prItemId]?.__unit__?.id
      item.unitCode = dict[item.prItemId]?.__unit__?.code

      item.fileList = dict[item.prItemId]?.__mediaFiles__ || []
      item.index = index
      item.deliveryDate = dict[item.prItemId]?.deliveryDate
      item.valuationPrice = dict[item.prItemId]?.valuationPrice
      item.batch = dict[item.prItemId]?.batch
      item.deliveryText = dict[item.prItemId]?.deliveryText

      item.itemText = dict[item.prItemId]?.itemText
      item.trackingNumber = dict[item.prItemId]?.trackingNumber
      item.subNoAset = dict[item.prItemId]?.subNoAset
      item.valuationType = dict[item.prItemId]?.valuationType
      item.batch = dict[item.prItemId]?.batch

      item.isNew = true
      item.itemNo = index * 10
      item.budget = 0

      item.isBussinessPlan = true
      index++
      delete item.__material__
      delete item.__purchasingGroup__
      delete item.__materialGroup__
      delete item.__asset__
      delete item.__order__
      delete item.__glAccount__
      delete item.__costCenter__
      delete item.__plant__
      delete item.__prItemCompoments__
      delete item.__unit__
      delete item.__mediaFiles__
      delete item.__oun__
      delete item.__material__
      delete item.lstGroup
    }
    delete res.__businessPlanPrItems__
    return res
  }
}
