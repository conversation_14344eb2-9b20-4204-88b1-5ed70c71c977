import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { SiteAssessmentEntity } from './siteAssessment.entity'
import { PlanSiteAssessmentEmployeeEntity } from './planSiteAssessmentEmployee.entity'

/** Thông tin kế hoạch đánh giá hiện trường*/
@Entity('plan_site_assessment')
export class PlanSiteAssessmentEntity extends BaseEntity {
  /**Trạng thái  */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Mã kế hoạch */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  code: string

  /** Tên kế hoạch */
  @Column({
    type: 'nvarchar',
    length: 150,
    nullable: true,
  })
  name: string

  /** Tên kế hoạch */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /** Lí do */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  reason: string

  @OneToMany(() => SiteAssessmentEntity, (p) => p.plantSiteAssessment)
  siteAssessments: Promise<SiteAssessmentEntity[]>

  @OneToMany(() => PlanSiteAssessmentEmployeeEntity, (p) => p.planSiteAssessment)
  employees: Promise<PlanSiteAssessmentEmployeeEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgCompanyId: string
}
