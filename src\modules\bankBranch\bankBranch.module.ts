import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BankBranchRepository, BankRepository, CountryRepository, RegionRepository } from '../../repositories'
import { BankBranchService } from './bankBranch.service'
import { BankBranchController } from './bankBranch.controller'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([BankBranchRepository, BankRepository, CountryRepository, RegionRepository]),
    OrganizationalPositionModule,
  ],
  controllers: [BankBranchController],
  providers: [BankBranchService],
})
export class BankBranchModule {}
