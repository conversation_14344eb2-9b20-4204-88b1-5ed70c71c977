import { Body, Controller, Post, Res, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { PaginationDto, UserDto } from '../../dto'
import { JwtAuthGuard } from '../common/guards'
import { CurrentUser } from '../common/decorators'
import { TemplateConfigService } from './templateConfig.service'
import { TemplateConfigDto, UpdateTemplateConfigDto } from './dto/templateConfigDetail.dto'

@ApiBearerAuth()
@ApiTags('TemplateConfig')
@UseGuards(JwtAuthGuard)
@Controller('templateConfig')
export class TemplateConfigController {
  constructor(private service: TemplateConfigService) {}
  @ApiOperation({ summary: 'Tạo template' })
  @Post('templates/create')
  public async create(@CurrentUser() user: UserDto, @Body() data: TemplateConfigDto) {
    return await this.service.create(data)
  }

  @ApiOperation({ summary: 'Lấy ds template' })
  @Post('templates/get-list')
  public async getList(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.getList(data)
  }

  @ApiOperation({ summary: 'Lấy chi tiết template theo code' })
  @Post('templates/detail')
  public async detailTemplate(@CurrentUser() user: UserDto, @Body() data: { code: string }) {
    return await this.service.detailTemplate(data)
  }

  @ApiOperation({ summary: 'Lấy ds nhóm template' })
  @Post('templates/list-group')
  public async getGroupTemplate(@CurrentUser() user: UserDto) {
    return await this.service.getGroupTemplate()
  }

  @ApiOperation({ summary: 'Cập nhật template' })
  @Post('templates/update')
  public async update(@CurrentUser() user: UserDto, @Body() data: UpdateTemplateConfigDto) {
    return await this.service.update(data)
  }
}
