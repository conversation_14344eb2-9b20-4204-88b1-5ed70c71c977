import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateCriterial1749112410383 implements MigrationInterface {
    name = 'UpdateCriterial1749112410383'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "supplier_score" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_edb4d76d1c2ede4cf0590313203" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_5cb52fac0c7105501a56171ee90" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "score" int CONSTRAINT "DF_02e2303db60a3ed085077fb08e9" DEFAULT 0, "employeeId" varchar(255) NOT NULL, "supplierId" varchar(255) NOT NULL, "templateCriterialId" varchar(255) NOT NULL, CONSTRAINT "PK_edb4d76d1c2ede4cf0590313203" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "score" int CONSTRAINT "DF_18a711918835a718bca44596641" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_18a711918835a718bca44596641"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "score"`);
        await queryRunner.query(`DROP TABLE "supplier_score"`);
    }

}
