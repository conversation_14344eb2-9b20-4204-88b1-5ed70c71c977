import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PrItemEntity, ReservationEntity, SupplierNumberRequestApproveEntity } from '.'
import { RoleFiSupplierEntity } from './roleFiSupplier.entity'

/** DANH MỤC TÀI KHOẢN CÔNG NỢ HẠCH TOÁN TỰ ĐỘNG
 */
@Entity('gl_account')
export class GLAccountEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.glAccount)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.glAccount)
  prItems: Promise<PrItemEntity[]>

  @OneToMany(() => RoleFiSupplierEntity, (p) => p.glAccount)
  roleFiSuppliers: Promise<RoleFiSupplierEntity[]>

  @OneToMany(() => ReservationEntity, (p) => p.glAccount)
  reservations: Promise<ReservationEntity[]>
}
