import { MigrationInterface, QueryRunner } from 'typeorm'

export class BusinessTemplatePlan1752717437753 implements MigrationInterface {
  name = 'BusinessTemplatePlan1752717437753'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "shipmentPlanPrice" decimal(10,2)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "shipmentPlanPrice"`)
  }
}
