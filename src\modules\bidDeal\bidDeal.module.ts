import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import {
  BidDealRepository,
  BidDealSupplierPriceValueRepository,
  BidDealSupplierRepository,
  BidExMatGroupRepository,
  BidPriceRepository,
  BidPrItemRepository,
  BidRepository,
  BidSupplierPriceRepository,
  BidSupplierRepository,
  ServiceRepository,
} from '../../repositories'
import { BidDealController } from './bidDeal.controller'
import { BidDealService } from './bidDeal.service'
import { EmailModule } from '../email/email.module'
import { ExternalMaterialGroupRepository } from '../../repositories/externalMaterialGroup.repository'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BidDealRepository,
      BidExMatGroupRepository,
      BidDealSupplierPriceValueRepository,
      BidPriceRepository,
      BidDealSupplierRepository,
      BidRepository,
      BidSupplierRepository,
      ExternalMaterialGroupRepository,
      ServiceRepository,
      BidSupplierPriceRepository,
      BidPrItemRepository,
    ]),
    EmailModule,
  ],
  controllers: [BidDealController],
  providers: [BidDealService],
  exports: [BidDealService],
})
export class BidDealModule {}
