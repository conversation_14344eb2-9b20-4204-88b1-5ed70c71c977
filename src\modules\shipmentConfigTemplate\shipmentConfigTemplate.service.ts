import { Injectable } from '@nestjs/common'
import { ShipmentConfigTemplateDetailRepository, ShipmentConfigTemplateRepository } from '../../repositories/shipmentConfigTemplate.repository'
import { PaginationDto, UserDto } from '../../dto'
import { Between, Like } from 'typeorm'
import { ShipmentConfigTemplateCreateDto, ShipmentConfigTemplateUpdateDto } from './dto/shipmentConfigTemplateCreate.dto'
import { EmployeeRepository } from '../../repositories'
import { ShipmentConfigTemplateEntity } from '../../entities/shipmentConfigTemplate.entity'
import { ShipmentConfigTemplateDetailEntity } from '../../entities/shipmentConfigTemplateDetail.entity'
import { enumData } from '../../constants'

@Injectable()
export class ShipmentConfigTemplateService {
  constructor(
    private readonly repo: ShipmentConfigTemplateRepository,
    private readonly shipmentConfigTemplateDetailRepo: ShipmentConfigTemplateDetailRepository,
    private readonly employeeRepo: EmployeeRepository,
  ) {}

  async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { isDeleted: false, status: 'Active' }
    if (data.where.name) whereCon.createdBy = Like(`%${data.where.name}%`)
    if (data.where.createdAt?.length === 2) {
      whereCon.createdAt = Between(new Date(data.where.createdAt[0]), new Date(data.where.createdAt[1]))
    }
    if (data.where.status) whereCon.status = Like(`%${data.where.status}%`)

    let res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'ASC' },
    })
    const dicEmployee = await this.employeeRepo.find({
      where: {
        isDeleted: false,
      },
    })
    for (const item of res[0]) {
      const employee = dicEmployee.find((emp) => emp.id === item.createdBy)
      // item.createdByEmployee = employee?.name
      item.createdByEmployeeName = employee.name
      item.createdByEmployee = employee.id
      item.employeeId = employee.id
      item.statusName = enumData.ShipmentConfigTemplate[item.status]?.name
      item.statusColor = enumData.ShipmentConfigTemplate[item.status]?.color
      item.statusBgColor = enumData.ShipmentConfigTemplate[item.status]?.backgroundColor
      item.statusBorderColor = enumData.ShipmentConfigTemplate[item.status]?.statusBorderColor
    }

    return res
  }

  async create(user: UserDto, data: ShipmentConfigTemplateCreateDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(ShipmentConfigTemplateEntity)
      const repoDetail = trans.getRepository(ShipmentConfigTemplateDetailEntity)
      const entity = repo.create(data)
      entity.createdBy = data.employeeId
      entity.companyId = user.companyId
      const template = await repo.save(entity)

      const lstDetailId = data.lstShipmentConfigTemplateDetail.map((item) => item.id)
      for (const id of lstDetailId) {
        const detail = {
          shipmentConfigTemplateId: template.id,
          shipmentConditionTypeId: id,
        }
        const detailEntity = repoDetail.create(detail)
        await repoDetail.save(detailEntity)
      }
    })
    return { message: 'IMPORT_SUCCESS' }
  }

  async update(user: UserDto, data: ShipmentConfigTemplateUpdateDto) {
    await this.repo.update(data.id, {
      updatedBy: user.id,
      createdBy: data.employeeId,
      description: data.description,
      updatedAt: new Date(),
    })

    // danh sách condition type gốc
    const dicConditionType = await this.shipmentConfigTemplateDetailRepo.find({
      where: {
        shipmentConfigTemplateId: data.id,
        isDeleted: false,
      },
    })
    // danh sách condition type update
    const lstConditionTypeUpdate = data.lstShipmentConfigTemplateDetail.map((item) => item.id)
    // Xóa các conditionType bị xóa khi update
    for (const item of dicConditionType) {
      const conditionTypeUpdate = lstConditionTypeUpdate.find((emp) => emp === item.shipmentConditionTypeId)
      if (!conditionTypeUpdate) {
        await this.shipmentConfigTemplateDetailRepo.delete({
          shipmentConfigTemplateId: data.id,
          shipmentConditionTypeId: item.shipmentConditionTypeId,
        })
      }
    }

    for (const item of data.lstShipmentConfigTemplateDetail) {
      const existing = await this.shipmentConfigTemplateDetailRepo.findOne({
        where: {
          shipmentConfigTemplateId: data.id,
          shipmentConditionTypeId: item.id,
        },
      })
      /**Kiểm tra xem ds detail có condition type này hay chưa */
      if (existing) {
        await this.shipmentConfigTemplateDetailRepo.update({ shipmentConfigTemplateId: data.id }, { shipmentConditionTypeId: item.id })
      } else {
        const detail = {
          shipmentConfigTemplateId: data.id,
          shipmentConditionTypeId: item.id,
        }
        const detailEntity = this.shipmentConfigTemplateDetailRepo.create(detail)
        await this.shipmentConfigTemplateDetailRepo.save(detailEntity)
      }
    }
    return { message: 'UPDATE_SUCCESS' }
  }
  async detailTemplate(user: UserDto, data: any) {
    const template: any = await this.repo.findOne({
      where: {
        id: data.id,
        isDeleted: false,
      },
      relations: {
        shipmentConfigTemplateDetails: {
          shipmentConditionType: true,
        },
      },
    })
    const dicEmployee = await this.employeeRepo.find({
      where: {
        isDeleted: false,
      },
    })
    const employee = dicEmployee.find((emp) => emp.id === template.createdBy)
    template.createdByEmployeeName = employee.name
    template.createdByEmployee = employee.id
    template.employeeId = employee.id
    const lstDetail = template?.__shipmentConfigTemplateDetails__ || []
    const lstConditionType = lstDetail.map((item) => item.__shipmentConditionType__).filter((x) => !!x)
    template.lstConditionType = lstConditionType
    delete template.__shipmentConfigTemplateDetails__
    return template
  }

  async deleteTempDetail(user: UserDto, data: any) {
    return await this.shipmentConfigTemplateDetailRepo.delete({ shipmentConfigTemplateId: data.id, shipmentConditionTypeId: data.idType })
  }

  async deleteTempConfig(user: UserDto, data: any) {
    return await this.repo.update({ id: data.id }, { isDeleted: true })
  }
  async changStatus(user: UserDto, data: any) {
    return this.repo.update({ id: data.id }, { status: data.status })
  }
}
