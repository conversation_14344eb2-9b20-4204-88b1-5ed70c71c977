import { BaseEntity } from './base.entity'
import { Entity, Column } from 'typeorm'

@Entity('setting_string_client')
export class SettingStringClientEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  domain: string
}
