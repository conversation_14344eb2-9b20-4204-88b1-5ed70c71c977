import { TransportationPlanEntity } from '../entities/transportationPlan.entity'
import { TransportationPlanDetailEntity } from '../entities/transportationPlanDetail.entity'
import { CustomRepository } from '../typeorm'
import { BaseRepository } from './base.repository'

@CustomRepository(TransportationPlanEntity)
export class TransportationPlanRepository extends BaseRepository<TransportationPlanEntity> {}

@CustomRepository(TransportationPlanDetailEntity)
export class TransportationPlanDetailRepository extends BaseRepository<TransportationPlanDetailEntity> {}
