import { Entity, Column, Jo<PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { MaterialEntity } from './material.entity'
import { PrEntity } from './pr.entity'
import { PrItemEntity } from './prItem.entity'

@Entity('pr_item_child')
export class PrItemChildEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.prItemChilds)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemId: string
  @ManyToOne(() => PrItemEntity, (p) => p.prItemChilds)
  @JoinColumn({ name: 'prItemId', referencedColumnName: 'id' })
  prItem: Promise<PrItemEntity>

  /** số lượng cần đặt hàng */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  /** tên hàng */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  shortText: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.prItemChilds)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** asset */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetCode: string

  /** asset Desc */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetDesc: string

  /** io */
  @Column({
    length: 'max',
    nullable: true,
  })
  io: string

  /** io Name */
  @Column({
    length: 'max',
    nullable: true,
  })
  ioName: string

  /** iotype */
  @Column({
    length: 'max',
    nullable: true,
  })
  iotype: string

  /** io Name */
  @Column({
    length: 'max',
    nullable: true,
  })
  relstatus: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemNewId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemOldId: string
}
