/** <PERSON><PERSON> quyền hệ thống Role group cho website*/
//  View: { name: '', code: '', value: false, path: '' },
export const RolePermission = {
  // 1. Báo cáo
  Report: {
    id: 1,
    name: '<PERSON><PERSON><PERSON> cáo',
    code: 'Report',
    children: [
      {
        code: 'ReportSupplier',
        name: 'BC NCC',
        path: '/bid/report-supplier',
        View: { name: 'Xem', code: 'View', value: false, path: '/bid/report-supplier' },
      },
      {
        code: 'ReportExpertise',
        name: 'BC thẩm định',
        path: '/bid/report-expertise',
        View: { name: 'Xem', code: 'View', value: false, path: '/bid/report-expertise' },
      },
      {
        code: 'ReportBid',
        name: '<PERSON> gói thầu',
        path: '/bid/report-bid',
        View: { name: 'Xem', code: 'View', value: false, path: '/bid/report-bid' },
      },
    ],
  },

  // 2. <PERSON>h<PERSON><PERSON> b<PERSON><PERSON> duy<PERSON>t
  NotifyApprove: {
    id: 2,
    name: 'Thông b<PERSON><PERSON> duy<PERSON>t',
    code: 'NotifyApprove',
  },

  // 3.<PERSON><PERSON><PERSON> c<PERSON>u mua hàng
  PurchaseRequest: {
    id: 3,
    name: '<PERSON><PERSON><PERSON> c<PERSON>u mua hàng',
    code: 'PurchaseRequest',
    children: [
      {
        code: 'PurchaseRequestList',
        name: 'DS yêu cầu mua hàng',
        path: '/pr/pr',
        View: { name: 'Xem', code: 'View', value: false, path: '/pr/pr' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/pr/pr' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/pr/pr' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/pr/pr' },
      },

      {
        name: 'Tổng hợp yêu cầu mua hàng',
        code: 'PurchaseRequestPR',
        children: [
          {
            code: 'PurchaseRequestPRList',
            name: 'Danh sách PR tổng hợp',
            path: '/pr/pr-total',
            View: { name: 'Xem', code: 'View', value: false, path: '/pr/pr-total' },
          },
          {
            code: 'PurchaseRequestPRAll',
            name: 'Tổng hợp PR',
            path: '/pr/pr-total-create',
            View: { name: 'Xem', code: 'View', value: false, path: '/pr/pr-total-create' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/pr/pr-total-create' },
          },
          {
            code: 'PurchaseRequestPRLocation',
            name: 'Phân bổ PR',
            path: '/pr/pr-allocation',
            View: { name: 'Xem', code: 'View', value: false, path: '/pr/pr-allocation' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/pr/pr-allocation' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/pr/pr-allocation' },
          },
        ],
      },

      {
        code: 'Reservation',
        name: 'Nhu cầu sử dụng',
        path: '/reservation/reservation',
        View: { name: 'Xem', code: 'View', value: false, path: '/reservation/reservation' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/reservation/reservation' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/reservation/reservation' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/reservation/reservation' },
      },

      {
        code: 'ReservationNorm',
        name: 'Định mức nhu cầu sử dụng',
        path: '/reservation/reservation-norm',
        View: { name: 'Xem', code: 'View', value: false, path: '/reservation/reservation-norm' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/reservation/reservation-norm' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/reservation/reservation-norm' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/reservation/reservation-norm' },
      },

      {
        name: 'Tổng hợp nhu cầu sử dụng',
        code: 'PurchaseRequestReservation',
        children: [
          {
            code: 'PurchaseRequestReservationList',
            name: 'Danh sách NCSD tổng hợp',
            path: '/reservation/reservation-total',
            View: { name: 'Xem', code: 'View', value: false, path: '/reservation/reservation-total' },
          },
          {
            code: 'PurchaseRequestReservationAll',
            name: 'Tổng hợp NCSD',
            path: 'reservation/reservation-total-create',
            View: { name: 'Xem', code: 'View', value: false, path: '/reservation/reservation-total-create' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/reservation/reservation-total-create' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/reservation/reservation-total-create' },
          },
        ],
      },
    ],
  },

  // 4.Nhà cung cấp
  Supplier: {
    id: 4,
    name: 'Nhà cung cấp',
    code: 'Supplier',
    children: [
      {
        code: 'ManagementSupSupplier',
        name: 'Nhà cung cấp',
        path: '/bid/supplier-manage',
        View: { name: 'Xem', code: 'View', value: false, path: '/bid/supplier-manage' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/supplier-manage' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/bid/supplier-manage' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/supplier-manage' },
        Import: { name: 'Import excel', code: 'Import', value: false, path: '/bid/supplier-manage' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/supplier-manage' },
      },
      {
        name: 'Pháp lý/Năng Lực',
        path: '/bid/supplier-capacity',
        View: { name: 'Xem', code: 'View', value: false, path: '/bid/supplier-capacity' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/supplier-capacity' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/bid/supplier-capacity' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/supplier-capacity' },
        Import: { name: 'Import excel', code: 'Import', value: false, path: '/bid/supplier-capacity' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/supplier-manage' },
      },
      {
        name: 'Tạo mã SAP',
        children: [
          {
            name: 'Phân quyền nhập liệu',
            path: '/bid/supplier-number-setting-role',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/supplier-number-setting-role' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/supplier-number-setting-role' },
          },
          {
            name: 'Tạo mã SAP',
            path: '/bid/supplier-create-code',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/supplier-create-code' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/supplier-create-code' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/bid/supplier-create-code' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/supplier-create-code' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/bid/supplier-create-code' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/supplier-create-code' },
          },
        ],
      },
      {
        name: 'Điều chỉnh nhà cung cấp',
        children: [
          {
            name: 'Pháp lý',
            path: '/bid/approve-request-update-law-supplier',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/approve-request-update-law-supplier' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/approve-request-update-law-supplier' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/approve-request-update-law-supplier' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/approve-request-update-law-supplier' },
          },
          {
            name: 'Năng lực',
            path: '/bid/approve-request-update-capacity-supplier',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/approve-request-update-capacity-supplier' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/approve-request-update-capacity-supplier' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/approve-request-update-capacity-supplier' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/approve-request-update-capacity-supplier' },
          },
          {
            name: 'Mở/khóa LVKD',
            path: '/bid/approve-request-active-supplier-service',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/approve-request-active-supplier-service' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/approve-request-active-supplier-service' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/approve-request-active-supplier-service' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/approve-request-active-supplier-service' },
          },
          {
            name: 'Mở/khóa NCC',
            path: '/bid/approve-request-active-supplier',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/approve-request-active-supplier' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/approve-request-active-supplier' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/approve-request-active-supplier' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/approve-request-active-supplier' },
          },
          {
            name: 'Nâng cấp lên NCC tiềm năng',
            path: '/bid/supplier-potential-upgrade',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/supplier-potential-upgrade' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/bid/supplier-potential-upgrade' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/supplier-potential-upgrade' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/supplier-potential-upgrade' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/supplier-potential-upgrade' },
          },

          {
            name: 'Nâng cấp lên NCC chính thức',
            path: '/bid/supplier-official-upgrade',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/supplier-official-upgrade' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/bid/supplier-official-upgrade' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/supplier-official-upgrade' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/supplier-official-upgrade' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/supplier-official-upgrade' },
          },
        ],
      },
      {
        name: 'Đánh giá nhà cung cấp',
        children: [
          {
            name: 'Hiện trường',
            path: '/bid/site-assessment',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/site-assessment' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/bid/site-assessment' },

            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/site-assessment' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/site-assessment' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/site-assessment' },
          },
          {
            name: 'Lịch sử mua hàng',
            path: '/bid/evaluation-history-purchase',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/evaluation-history-purchase' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/bid/evaluation-history-purchase' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/evaluation-history-purchase' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/evaluation-history-purchase' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/evaluation-history-purchase' },
          },
        ],
      },
    ],
  },

  // 3. Quản trị mua hàng
  PurchaseManagement: {
    id: 5,
    name: 'Quản trị mua hàng',
    code: 'PurchaseManagement',
    children: [
      {
        name: 'Quản lý đấu thầu',
        children: [
          {
            name: 'Tạo gói thầu',
            path: '/bid/bid-new',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/bid-new' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/bid/bid-new' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/bid-new' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/bid-new' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/bid/bid-new' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/bid-new' },
          },
          {
            name: 'Đánh giá gói thầu',
            path: '/bid/bid-rate',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/bid-rate' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/bid-rate' },
            // Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/bid-rate' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/bid-rate' },
          },
          {
            name: 'Truy vấn gói thầu',
            path: '/bid/bid-info',
            View: { name: 'Xem', code: 'View', value: false, path: '/bid/bid-info' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/bid-info' },
            // Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/bid-info' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/bid-info' },
          },
        ],
      },
      {
        name: 'Gói thầu khảo sát',
        path: '/bid/bid-new-survey',
        View: { name: 'Xem', code: 'View', value: false, path: '/bid/bid-new-survey' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/bid/bid-new-survey' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/bid-new-survey' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/bid-new-survey' },
        Import: { name: 'Import excel', code: 'Import', value: false, path: '/bid/bid-new-survey' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/bid-new-survey' },
      },

      {
        name: 'Yêu cầu báo giá',
        path: '/price-quote/list',
        View: { name: 'Xem', code: 'View', value: false, path: '/price-quote/list' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/price-quote/list' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/price-quote/list' },
      },
      {
        name: 'Đấu giá',
        path: '/bid/auction',
        View: { name: 'Xem', code: 'View', value: false, path: '/bid/auction' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bid/auction' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/bid/auction' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bid/auction' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bid/auction' },
      },
      {
        name: 'Phương án kinh doanh',
        path: '/business-plan',
        View: { name: 'Xem', code: 'View', value: false, path: '/business-plan' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/business-plan' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/business-plan' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/business-plan' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/business-plan' },
      },
      {
        name: 'Đề nghị mua hàng',
        path: '/recommended-purchase',
        View: { name: 'Xem', code: 'View', value: false, path: '/recommended-purchase' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/recommended-purchase' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/recommended-purchase' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/recommended-purchase' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/recommended-purchase' },
      },
      {
        name: 'Làm tròn cont',
        path: '/round-up-cont',
        View: { name: 'Xem', code: 'View', value: false, path: '/round-up-cont' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/round-up-cont' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/round-up-cont' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/round-up-cont' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/round-up-cont' },
      },
      {
        name: 'Hợp đồng',
        path: '/contract/contract',
        View: { name: 'Xem', code: 'View', value: false, path: '/contract/contract' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/contract/contract' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/contract/contract' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/contract/contract' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/contract/contract' },
      },
      {
        name: 'PO',
        path: '/po/po',
        View: { name: 'Xem', code: 'View', value: false, path: '/po/po' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/po/po' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/po/po' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/po/po' },
        Import: { name: 'Import excel', code: 'Import', value: false, path: '/po/po' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/po/po' },
      },

      {
        name: 'Quản Lý Inbound',
        children: [
          {
            name: 'Danh sách Inbound',
            path: '/inbound/inbound',
            View: { name: 'Xem', code: 'View', value: false, path: '/inbound/inbound' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/inbound/inbound' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/inbound/inbound' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/inbound/inbound' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/inbound/inbound' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/inbound/inbound' },
          },
          {
            name: 'Lịch giao hàng',
            path: '/inbound/shipment-schedule',
            View: { name: 'Xem', code: 'View', value: false, path: '/inbound/shipment-schedule' },
          },
        ],
      },

      {
        name: 'Hóa đơn',
        path: '/bill',
        View: { name: 'Xem', code: 'View', value: false, path: '/bill' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/bill' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/bill' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/bill' },
        Import: { name: 'Import excel', code: 'Import', value: false, path: '/bill' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/bill' },
      },

      {
        name: 'Thanh toán',
        path: '/payment',
        View: { name: 'Xem', code: 'View', value: false, path: '/payment' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/payment' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/payment' },
        // Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/payment' },
        // Import: { name: 'Import excel', code: 'Import', value: false, path: '/payment' },
        // Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/payment' },
      },

      {
        name: 'Danh sách khiếu nại',
        path: '/complaint/complaint',
        View: { name: 'Xem', code: 'View', value: false, path: '/complaint/complaint' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/complaint/complaint' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/complaint/complaint' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/complaint/complaint' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/complaint/complaint' },
      },

      {
        name: 'Quản Lý shipment',
        children: [
          {
            name: 'Shipment',
            path: '/shipment/shipment',
            View: { name: 'Xem', code: 'View', value: false, path: '/shipment/shipment' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/shipment/shipment' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/shipment/shipment' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/shipment/shipment' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/shipment/shipment' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/shipment/shipment' },
          },
          {
            name: 'Đồng bộ shipment sap',
            path: '/shipment/shipment/shipment-sap',
            View: { name: 'Xem', code: 'View', value: false, path: '/shipment/shipment/shipment-sap' },
          },
        ],
      },

      // {
      //   name: 'Shipment',
      //   path: '/shipment/shipment',
      //   View: { name: 'Xem', code: 'View', value: false, path: '/shipment/shipment' },
      //   Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/shipment/shipment' },
      //   Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/shipment/shipment' },
      //   Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/shipment/shipment' },
      //   Import: { name: 'Import excel', code: 'Import', value: false, path: '/shipment/shipment' },
      //   Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/shipment/shipment' },
      // },

      {
        name: 'Shipment cost',
        path: '/shipment/shipment-cost',
        View: { name: 'Xem', code: 'View', value: false, path: '/shipment/shipment-cost' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/shipment/shipment-cost' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/shipment/shipment-cost' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/shipment/shipment-cost' },
        Import: { name: 'Import excel', code: 'Import', value: false, path: '/shipment/shipment-cost' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/shipment/shipment-cost' },
      },

      {
        name: 'Đề xuất điều chỉnh ngân sách',
        path: '/budget',
        View: { name: 'Xem', code: 'View', value: false, path: '/budget' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/budget' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/budget' },
        Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/budget' },
        // Import: { name: 'Import excel', code: 'Import', value: false, path: '/budget' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/budget' },
      },

      {
        name: 'Phiếu đánh giá KPI mua hàng',
        path: '/purchase-kpi/ticket-evaluation-kpi',
        View: { name: 'Xem', code: 'View', value: false, path: '/purchase-kpi/ticket-evaluation-kpi' },
        Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/purchase-kpi/ticket-evaluation-kpi' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/purchase-kpi/ticket-evaluation-kpi' },
        // Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/purchase-kpi/ticket-evaluation-kpi' },
        // Import: { name: 'Import excel', code: 'Import', value: false, path: '/purchase-kpi/ticket-evaluation-kpi' },
        Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/purchase-kpi/ticket-evaluation-kpi' },
      },

      // TODO ....
    ],
  },

  // 4. Thiết lập
  Setting: {
    id: 6,
    name: 'Thiết lập',
    code: 'Setting',
    children: [
      {
        name: 'Hành chính',
        children: [
          {
            name: 'Quốc gia',
            path: '/setting/country',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/country' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/country' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/country' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/country' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/country' },
          },
          {
            name: 'Tỉnh thành',
            path: '/setting/region',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/region' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/region' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/region' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/region' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/region' },
          },
          // {
          //   name: 'Vùng thu mua',
          //   path: '/setting/purchasing-area',
          //   View: { name: 'Xem', code: 'View', value: false, path: '/setting/purchasing-area' },
          //   Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/purchasing-area' },
          //   Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/purchasing-area' },
          // },
        ],
      },
      {
        name: 'Cấu hình template',
        children: [
          {
            name: 'Template mua hàng',
            path: '/setting/service-template',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/service-template' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/service-template' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/service-template' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/service-template' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/service-template' },
          },
          {
            name: 'Template đánh giá NCC',
            path: '/setting/supplier-template',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/supplier-template' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/supplier-template' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/supplier-template' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/supplier-template' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/supplier-template' },
          },
          {
            name: 'Template Làm Tròn Cont',
            path: '/setting/round-up-cont-template',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/round-up-cont-template' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/round-up-cont-template' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/round-up-cont-template' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/round-up-cont-template' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/round-up-cont-template' },
          },
          {
            name: 'Template PAKD',
            path: '/setting/business-plan-template',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/business-plan-template' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/business-plan-template' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/business-plan-template' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/business-plan-template' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/business-plan-template' },
          },
          {
            name: 'Template ĐNMH',
            path: '/setting/recommended-purchase-template',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/recommended-purchase-template' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/recommended-purchase-template' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/recommended-purchase-template' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/recommended-purchase-template' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/recommended-purchase-template' },
          },
          {
            name: 'Thiết lập bảng giá PO',
            path: '/setting/price-list',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/price-list' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/price-list' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/price-list' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/price-list' },
          },
          {
            name: 'Template KPI mua hàng',
            path: '/purchase-kpi/purchase-kpi',
            View: { name: 'Xem', code: 'View', value: false, path: '/purchase-kpi/purchase-kpi' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/purchase-kpi/purchase-kpi' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/purchase-kpi/purchase-kpi' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/purchase-kpi/purchase-kpi' },
          },
        ],
      },
      {
        name: 'Cấu hình PO',
        children: [
          {
            name: 'Procedure',
            path: '/setting/procedure',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/procedure' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/procedure' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/procedure' },
          },
          {
            name: 'Condition type',
            path: '/setting/condition-type-master',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/condition-type-master' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/condition-type-master' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/condition-type-master' },
          },
          {
            name: 'Supplier Schema',
            path: '/setting/supplier-schema',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/supplier-schema' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/supplier-schema' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/supplier-schema' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/supplier-schema' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/supplier-schema' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/supplier-schema' },
          },
          {
            name: 'Purchasing Org Schema',
            path: '/setting/purchasing-org-schema',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/purchasing-org-schema' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/purchasing-org-schema' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/purchasing-org-schema' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/purchasing-org-schema' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/purchasing-org-schema' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/purchasing-org-schema' },
          },
          {
            name: 'Bảng Schema PO ',
            path: '/setting/schema',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/schema' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/schema' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/schema' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/schema' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/schema' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/schema' },
          },
        ],
      },
      {
        name: 'Công ty',
        children: [
          {
            name: 'Danh sách công ty',
            path: '/setting/company',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/company' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/company' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/company' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/company' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/company' },
          },
          {
            name: 'Danh sách nhà máy',
            path: '/setting/plant',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/plant' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/plant' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/plant' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/plant' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/plant' },
          },
          {
            name: 'Danh sách khối',
            path: '/setting/block',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/block' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/block' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/block' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/block' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/block' },
          },
          {
            name: 'Danh sách phòng ban',
            path: '/setting/department',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/department' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/department' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/department' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/department' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/department' },
          },
          {
            name: 'Danh sách bộ phận',
            path: '/setting/part',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/part' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/part' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/part' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/part' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/part' },
          },
          {
            name: 'Danh sách vị trí',
            path: '/setting/position',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/position' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/position' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/position' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/position' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/position' },
          },

          {
            name: 'Sơ đồ tổ chức',
            path: '/setting/organizational',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/organizational' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/organizational' },
          },
          {
            name: 'Tổ chức mua hàng',
            path: '/setting/purchasing-org',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/purchasing-org' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/purchasing-org' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/purchasing-org' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/purchasing-org' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/purchasing-org' },
          },
          {
            name: 'Nhóm mua hàng',
            path: '/setting/purchasing-group',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/purchasing-group' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/purchasing-group' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/purchasing-group' },
          },
          {
            name: 'Tiền tệ',
            path: '/setting/currency',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/currency' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/currency' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/currency' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/currency' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/currency' },
          },
          {
            name: 'Điều kiện thương mại',
            path: '/setting/incoterm',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/incoterm' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/incoterm' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/incoterm' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/incoterm' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/incoterm' },
          },
          {
            name: 'Thời hạn thanh toán',
            path: '/setting/payment-term',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/payment-term' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/payment-term' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/payment-term' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/payment-term' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/payment-term' },
          },

          {
            name: 'Phương thức thanh toán',
            path: '/setting/payment-method',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/payment-method' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/payment-method' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/payment-method' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/payment-method' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/payment-method' },
          },
          {
            name: 'Dòng tiền',
            path: '/setting/planning-group',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/planning-group' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/planning-group' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/planning-group' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/planning-group' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/planning-group' },
          },
          {
            name: 'Danh mục cổ đông',
            path: '/setting/shareholder-category',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/shareholder-category' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/shareholder-category' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/shareholder-category' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/shareholder-category' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/shareholder-category' },
          },
          {
            name: 'Phòng ban - bộ phận',
            path: '/setting/cost-center',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/cost-center' },
          },
          {
            name: 'Nhóm đối tác kinh doanh',
            path: '/setting/business-partner-group',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/business-partner-group' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/business-partner-group' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/business-partner-group' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/business-partner-group' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/business-partner-group' },
          },

          {
            name: 'Loại hình doanh nghiệp',
            path: '/setting/business-type',
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/business-type' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/business-type' },
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/business-type' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/business-type' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/business-type' },
          },

          {
            name: 'Tra cứu hóa đơn',
            path: '/setting/bill-lookup',
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/bill-lookup' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/bill-lookup' },
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/bill-lookup' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/bill-lookup' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/bill-lookup' },
          },
        ],
      },

      {
        name: 'Nhân viên',
        children: [
          {
            name: 'Nhân viên',
            path: '/setting/employee',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/employee' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/employee' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/employee' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/employee' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/employee' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/employee' },
          },
          {
            name: 'Phân quyền bổ sung',
            path: '/setting/permission-additional',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/permission-additional' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/permission-additional' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/permission-additional' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/permission-additional' },
          },

          {
            name: 'Chiến lược duyệt',
            path: '/setting/flow-approve',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/flow-approve' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/flow-approve' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/flow-approve' },
          },
          {
            name: 'Phân quyền mặc định',
            path: '/setting/organizational-permission',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/organizational-permission' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/organizational-permission' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/organizational-permission' },
          },
        ],
      },

      {
        name: 'Thiết lập Material',
        children: [
          {
            name: 'UOM',
            path: '/setting/uom',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/uom' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/uom' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/uom' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/uom' },
          },
          {
            name: 'Material tyep',
            path: '/setting/material-type',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/material-type' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/material-type' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/material-type' },
          },
          {
            name: 'External Material Group',
            path: '/setting/external-material-group',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/external-material-group' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/external-material-group' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/external-material-group' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/external-material-group' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/external-material-group' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/external-material-group' },
          },
          {
            name: 'Material Group',
            path: '/setting/mat-group',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/mat-group' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/mat-group' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/mat-group' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/mat-group' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/mat-group' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/mat-group' },
          },
          {
            name: 'Material',
            path: '/pr/material',
            View: { name: 'Xem', code: 'View', value: false, path: '/pr/material' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/pr/materialgroup' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/pr/material' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/pr/material' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/pr/material' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/pr/material' },
          },
          {
            name: 'Lĩnh vực mua hàng',
            path: '/setting/service',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/service' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/service' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/service' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/service' },
          },
        ],
      },
      {
        name: 'Shipment/Shipment Cost',
        children: [
          {
            name: 'Shipment route',
            path: '/shipment/shipment-route',
            View: { name: 'Xem', code: 'View', value: false, path: '/shipment/shipment-route' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/shipment/shipment-route' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/shipment/shipment-route' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/shipment/shipment-route' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/shipment/shipment-route' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/shipment/shipment-route' },
          },
          {
            name: 'Shipment cost type',
            path: '/shipment/shipment-cost-type',
            View: { name: 'Xem', code: 'View', value: false, path: '/shipment/shipment-cost-type' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/shipment/shipment-cost-type' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/shipment/shipment-cost-type' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/shipment/shipment-cost-type' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/shipment/shipment-cost-type' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/shipment/shipment-cost-type' },
          },
        ],
      },
      {
        name: 'Thiết lập thanh toán',
        children: [
          {
            name: 'Ngân hàng',
            path: '/setting/bank',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/bank' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/bank' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/bank' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/bank' },
          },
          {
            name: 'Chi nhánh ngân hàng',
            path: '/setting/bank-branch',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/bank-branch' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/bank-branch' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/bank-branch' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/bank-branch' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/bank-branch' },
          },
          {
            name: 'GL account',
            path: '/setting/gl-account',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/gl-account' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/gl-account' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/gl-account' },
            Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/gl-account' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/gl-account' },
          },
          {
            name: 'Tỉ giá',
            path: '/setting/currency-exchange',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/currency-exchange' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/currency-exchange' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/currency-exchange' },
            Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/currency-exchange' },
          },
        ],
      },

      {
        name: 'Thiết lập đầu thầu',
        children: [
          {
            name: 'Hình thức đấu thầu',
            path: '/setting/master-bid-form',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/master-bid-form' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/master-bid-form' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/master-bid-form' },
          },
          {
            name: 'Bảo lãnh dự thầu',
            path: '/setting/master-bid-guarantee-form',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/master-bid-guarantee-form' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/master-bid-guarantee-form' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/master-bid-guarantee-form' },
          },
          {
            name: 'Trang tạo HS thầu',
            path: '/setting/client',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/client' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/client' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/client' },
          },
          {
            name: 'Template email',
            path: '/setting/email-template',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/email-template' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/email-template' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/email-template' },
          },
        ],
      },
      {
        name: 'Thiết lập khác',
        children: [
          {
            name: 'Danh mục FAQ',
            path: '/setting/faq-category',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/faq-category' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/faq-category' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/faq-category' },
          },
          {
            name: 'FAQ',
            path: '/setting/faq',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/faq' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/faq' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/faq' },
          },
          {
            name: 'Thiết lập ngôn ngữ',
            path: '/setting/language-key',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/language-key' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/language-key' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/language-key' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/language-key' },
          },
          {
            name: 'Công ty mời thầu',
            path: '/setting/company-invite',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/company-invite' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/company-invite' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/company-invite' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/company-invite' },
          },
          {
            name: 'Địa chỉ',
            path: '/setting/company-address',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/company-address' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/company-address' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/company-address' },
            Delete: { name: 'Xóa', code: 'Delete', value: false, path: '/setting/company-address' },
          },
          {
            name: 'Thiết lập động',
            path: '/setting/setting-string',
            View: { name: 'Xem', code: 'View', value: false, path: '/setting/setting-string' },
            Create: { name: 'Thêm mới', code: 'Create', value: false, path: '/setting/setting-string' },
            Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/setting/setting-string' },
          },
        ],
      },
    ],
  },
}
