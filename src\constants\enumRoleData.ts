/** <PERSON><PERSON> quyề<PERSON> hệ thống Role group cho website*/
//  View: { name: '', code: '', value: false, path: '' },
export const RoleDataGroup = {
  // 1. Báo cáo
  Report: {
    id: 1,
    name: '<PERSON><PERSON><PERSON> cáo',
    code: 'Report',
    children: [
      {
        code: 'ReportSupplier',
        name: 'BC NCC',
        path: '/bid/report-supplier',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Th<PERSON>y được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },
      {
        code: 'ReportExpertise',
        name: '<PERSON> thẩm định',
        path: '/bid/report-expertise',
        Child: { name: '<PERSON><PERSON> liệu của các cấp tr<PERSON><PERSON> thuộ<PERSON>', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },
      {
        code: 'ReportBid',
        name: 'BC gói thầu',
        path: '/bid/report-bid',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },
    ],
  },

  // 2. Thông báo duyệt
  NotifyApprove: {
    id: 2,
    name: 'Thông báo duyệt',
    code: 'NotifyApprove',
    Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
    AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
    All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
  },

  // 3.Yêu cầu mua hàng
  PurchaseRequest: {
    id: 3,
    name: 'Yêu cầu mua hàng',
    code: 'PurchaseRequest',
    children: [
      {
        code: 'PurchaseRequestList',
        name: 'DS yêu cầu mua hàng',
        path: '/pr/pr',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },

      {
        name: 'Tổng hợp yêu cầu mua hàng',
        code: 'PurchaseRequestPR',
        children: [
          {
            code: 'PurchaseRequestPRList',
            name: 'Danh sách PR tổng hợp',
            path: '/pr/pr-total',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            code: 'PurchaseRequestPRAll',
            name: 'Tổng hợp PR',
            path: '/pr/pr-total-create',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            code: 'PurchaseRequestPRLocation',
            name: 'Phân bổ PR',
            path: '/pr/pr-allocation',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },

      {
        code: 'Reservation',
        name: 'Nhu cầu sử dụng',
        path: '/reservation/reservation',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },

      {
        name: 'Tổng hợp nhu cầu sử dụng',
        code: 'PurchaseRequestReservation',
        children: [
          {
            code: 'PurchaseRequestReservationList',
            name: 'Danh sách NCSD tổng hợp',
            path: '/reservation/reservation-total',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            code: 'PurchaseRequestReservationAll',
            name: 'Tổng hợp NCSD',
            path: 'reservation/reservation-total-create',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },
    ],
  },

  // 4.Nhà cung cấp
  Supplier: {
    id: 4,
    name: 'Nhà cung cấp',
    code: 'Supplier',
    children: [
      {
        code: 'ManagementSupSupplier',
        name: 'Nhà cung cấp',
        path: '/bid/supplier-manage',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },
      {
        name: 'Pháp lý/Năng Lực',
        code: 'SupplierCapacity',
        path: '/bid/supplier-capacity',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },
      {
        name: 'Tạo mã SAP',
        children: [
          {
            name: 'Phân quyền nhập liệu',
            code: 'SupplierNumberSettingRole',
            path: '/bid/supplier-number-setting-role',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Tạo mã SAP',
            path: '/bid/supplier-create-code',
            code: 'SupplierCreateCode',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },
      {
        name: 'Điều chỉnh nhà cung cấp',
        children: [
          {
            name: 'Pháp lý',
            path: '/bid/approve-request-update-law-supplier',
            code: 'ApproveRequestUpdateLawSupplier',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Năng lực',
            path: '/bid/approve-request-update-capacity-supplier',
            code: 'ApproveRequestUpdateCapacitySupplier',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Mở/khóa LVKD',
            path: '/bid/approve-request-active-supplier-service',
            code: 'ApproveRequestActiveSupplierService',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Mở/khóa NCC',
            path: '/bid/approve-request-active-supplier',
            code: 'ApproveRequestActiveSupplier',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Nâng cấp nhà cung cấp',
            path: '/bid/supplier-upgrade',
            code: 'SupplierUpgrade',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },
      {
        name: 'Đánh giá nhà cung cấp',
        children: [
          {
            name: 'Hiện trường',
            path: '/bid/site-assessment',
            code: 'SiteAssessment',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Lịch sử mua hàng',
            path: '/bid/evaluation-history-purchase',
            code: 'EvaluationHistoryPurchase',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },
    ],
  },

  // 3. Quản trị mua hàng
  PurchaseManagement: {
    id: 5,
    name: 'Quản trị mua hàng',
    code: 'PurchaseManagement',
    children: [
      {
        name: 'Quản lý đấu thầu',
        children: [
          {
            name: 'Tạo gói thầu',
            path: '/bid/bid-new',
            code: 'BidNew',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Đánh giá gói thầu',
            path: '/bid/bid-rate',
            code: 'BidRate',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Truy vấn gói thầu',
            path: '/bid/bid-info',
            code: 'BidInfo',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },
      {
        name: 'Gói thầu khảo sát',
        path: '/bid/bid-new-survey',
        code: 'BidNewSurvey',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },

      {
        name: 'Yêu cầu báo giá',
        path: '/price-quote/list',
        code: 'PriceQuoteList',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },
      {
        name: 'Đấu giá',
        path: '/bid/auction',
        code: 'Auction',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },
      {
        name: 'Phương án kinh doanh',
        path: '/business-plan',
        code: 'BusinessPlan',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },
      {
        name: 'Đề nghị mua hàng',
        path: '/recommended-purchase',
        code: 'RecommendedPurchase',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },
      {
        name: 'Làm tròn cont',
        path: '/round-up-cont',
        code: 'RoundUpCont',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },
      {
        name: 'Hợp đồng',
        path: '/contract/contract',
        code: 'Contract',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },
      {
        name: 'PO',
        path: '/po/po',
        code: 'PO',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },

      {
        name: 'Quản Lý Inbound',
        children: [
          {
            name: 'Danh sách Inbound',
            path: '/inbound/inbound',
            code: 'Inbound',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Lịch giao hàng',
            path: '/inbound/shipment-schedule',
            code: 'ShipmentSchedule',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },

      {
        name: 'Hóa đơn',
        path: '/bill',
        code: 'Bill',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },

      {
        name: 'Thanh toán',
        path: '/payment',
        code: 'Payment',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },

      {
        name: 'Danh sách khiếu nại',
        path: '/complaint/complaint',
        code: 'Complaint',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },

      {
        name: 'Shipment',
        path: '/shipment/shipment',
        code: 'Shipment',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },

      {
        name: 'Shipment cost',
        path: '/shipment/shipment-cost',
        code: 'ShipmentCost',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },

      {
        name: 'Đề xuất điều chỉnh ngân sách',
        path: '/budget',
        code: 'Budget',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },

      {
        name: 'Phiếu đánh giá KPI mua hàng',
        path: '/purchase-kpi/ticket-evaluation-kpi',
        code: 'TicketEvaluationKpi',
        Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
        AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
        All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
      },

      // TODO ....
    ],
  },

  // 4. Thiết lập
  Setting: {
    id: 6,
    name: 'Thiết lập',
    code: 'Setting',
    children: [
      {
        name: 'Hành chính',
        children: [
          {
            name: 'Quốc gia',
            path: '/setting/country',
            code: 'Country',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Tỉnh thành',
            path: '/setting/region',
            code: 'Region',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          // {
          //   name: 'Vùng thu mua',
          //   path: '/setting/purchasing-area',
          //   View: { name: 'Xem', code: 'View', value: false, path: '/setting/purchasing-area' },
          //   Export: { name: 'Xuất excel', code: 'Export', value: false, path: '/setting/purchasing-area' },
          //   Import: { name: 'Import excel', code: 'Import', value: false, path: '/setting/purchasing-area' },
          // },
        ],
      },
      {
        name: 'Cấu hình template',
        children: [
          {
            name: 'Template mua hàng',
            path: '/setting/service-template',
            code: 'ServiceTemplate',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Template đánh giá NCC',
            path: '/setting/supplier-template',
            code: 'SupplierTemplate',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Template Làm Tròn Cont',
            path: '/setting/round-up-cont-template',
            code: 'RoundUpContTemplate',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Template PAKD',
            path: '/setting/business-plan-template',
            code: 'BusinessPlanTemplate',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Template ĐNMH',
            path: '/setting/recommended-purchase-template',
            code: 'RecommendedPurchaseTemplate',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Thiết lập bảng giá PO',
            path: '/setting/price-list',
            code: 'PriceList',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Template KPI mua hàng',
            path: '/purchase-kpi/purchase-kpi',
            code: 'PurchaseKpi',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },
      {
        name: 'Cấu hình PO',
        children: [
          {
            name: 'Procedure',
            path: '/setting/procedure',
            code: 'Procedure',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Condition type',
            path: '/setting/condition-type-master',
            code: 'ConditionTypeMaster',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Supplier Schema',
            path: '/setting/supplier-schema',
            code: 'SupplierSchema',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Purchasing Org Schema',
            path: '/setting/purchasing-org-schema',
            code: 'PurchasingOrgSchema',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Bảng Schema PO ',
            path: '/setting/schema',
            code: 'Schema',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },
      {
        name: 'Công ty',
        children: [
          {
            name: 'Danh sách công ty',
            path: '/setting/company',
            code: 'Company',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Danh sách nhà máy',
            path: '/setting/plant',
            code: 'Plant',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Danh sách khối',
            path: '/setting/block',
            code: 'Block',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Danh sách phòng ban',
            path: '/setting/department',
            code: 'Department',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Danh sách bộ phận',
            path: '/setting/part',
            code: 'Part',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Danh sách vị trí',
            path: '/setting/position',
            code: 'Position',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },

          {
            name: 'Sơ đồ tổ chức',
            path: '/setting/organizational',
            code: 'Organizational',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Tổ chức mua hàng',
            path: '/setting/purchasing-org',
            code: 'PurchasingOrg',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Nhóm mua hàng',
            path: '/setting/purchasing-group',
            code: 'PurchasingGroup',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Tiền tệ',
            path: '/setting/currency',
            code: 'Currency',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Điều kiện thương mại',
            path: '/setting/incoterm',
            code: 'Incoterm',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Thời hạn thanh toán',
            path: '/setting/payment-term',
            code: 'PaymentTerm',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },

          {
            name: 'Phương thức thanh toán',
            path: '/setting/payment-method',
            code: 'PaymentMethod',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Dòng tiền',
            path: '/setting/planning-group',
            code: 'PlanningGroup',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Danh mục cổ đông',
            path: '/setting/shareholder-category',
            code: 'ShareholderCategory',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Phòng ban - bộ phận',
            path: '/setting/cost-center',
            code: 'CostCenter',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Nhóm đối tác kinh doánh',
            path: '/setting/business-partner-group',
            code: 'BusinessPartnerGroupr',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },

      {
        name: 'Nhân viên',
        children: [
          {
            name: 'Nhân viên',
            path: '/setting/employee',
            code: 'Employee',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Phân quyền bổ sung',
            path: '/setting/permission-additional',
            code: 'PermissionAdditional',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },

          {
            name: 'Chiến lược duyệt',
            path: '/setting/flow-approve',
            code: 'FlowApprove',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Phân quyền mặc định',
            path: '/setting/organizational-permission',
            code: 'OrganizationalPermission',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },

      {
        name: 'Thiết lập Material',
        children: [
          {
            name: 'UOM',
            path: '/setting/uom',
            code: 'Uom',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Material type',
            path: '/setting/material-type',
            code: 'MaterialType',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'External Material Group',
            path: '/setting/external-material-group',
            code: 'ExternalMaterialGroup',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Material Group',
            path: '/setting/mat-group',
            code: 'MatGroup',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Material',
            path: '/pr/material',
            code: 'Material',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Lĩnh vực mua hàng',
            path: '/setting/service',
            code: 'Service',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Tỉ giá',
            path: '/setting/currency-exchange',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },
      {
        name: 'Shipment/Shipment Cost',
        children: [
          {
            name: 'Shipment route',
            path: '/shipment/shipment-route',
            code: 'ShipmentRoute',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Shipment cost type',
            path: '/shipment/shipment-cost-type',
            code: 'ShipmentCostType',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },
      {
        name: 'Thiết lập thanh toán',
        children: [
          {
            name: 'Ngân hàng',
            path: '/setting/bank',
            code: 'Bank',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Chi nhánh ngân hàng',
            path: '/setting/bank-branch',
            code: 'BankBranch',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'GL account',
            path: '/setting/gl-account',
            code: 'GlAccount',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },

      {
        name: 'Thiết lập đầu thầu',
        children: [
          {
            name: 'Hình thức đấu thầu',
            path: '/setting/master-bid-form',
            code: 'MasterBidForm',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Bảo lãnh dự thầu',
            path: '/setting/master-bid-guarantee-form',
            code: 'MasterBidGuaranteeForm',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Trang tạo HS thầu',
            path: '/setting/client',
            code: 'Client',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Template email',
            path: '/setting/email-template',
            code: 'EmailTemplate',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },
      {
        name: 'Thiết lập khác',
        children: [
          {
            name: 'Danh mục FAQ',
            path: '/setting/faq-category',
            code: 'FaqCategory',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'FAQ',
            path: '/setting/faq',
            code: 'Faq',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Thiết lập ngôn ngữ',
            path: '/setting/language-key',
            code: 'LanguageKey',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Địa chỉ',
            path: '/setting/company-address',
            code: 'CompanyAddress',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },

          {
            name: 'Hình thức đấu thầu',
            path: '/setting/bid-type',
            code: 'BidType',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
          {
            name: 'Thiết lập động',
            path: '/setting/setting-string',
            code: 'SettingString',
            Child: { name: 'Dữ liệu của các cấp trực thuộc', code: 'Child', value: false },
            AllCompany: { name: 'Tất cả dữ liệu của công ty', code: 'AllCompany', value: false },
            All: { name: 'Thấy được tất cả dữ liệu của tất cả công ty', code: 'All', value: false },
          },
        ],
      },
    ],
  },
}
