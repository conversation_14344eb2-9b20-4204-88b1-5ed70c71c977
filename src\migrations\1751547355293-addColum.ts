import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColum1751547355293 implements MigrationInterface {
    name = 'AddColum1751547355293'

    public async up(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE "leadtime_detail" ADD "rouding" float`);
        await queryRunner.query(`ALTER TABLE "leadtime_detail" ADD "moqToPull" float`);
        await queryRunner.query(`ALTER TABLE "leadtime_detail" ADD "moqBuy" float`);
        await queryRunner.query(`ALTER TABLE "leadtime_detail" ADD "packagingSpecifications" float`);
        await queryRunner.query(`ALTER TABLE "leadtime" ADD "plantId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "leadtime" ADD "description" nvarchar(max)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "leadtime" DROP COLUMN "description"`);
        await queryRunner.query(`ALTER TABLE "leadtime" DROP COLUMN "plantId"`);
        await queryRunner.query(`ALTER TABLE "leadtime_detail" DROP COLUMN "packagingSpecifications"`);
        await queryRunner.query(`ALTER TABLE "leadtime_detail" DROP COLUMN "moqBuy"`);
        await queryRunner.query(`ALTER TABLE "leadtime_detail" DROP COLUMN "moqToPull"`);
        await queryRunner.query(`ALTER TABLE "leadtime_detail" DROP COLUMN "rouding"`);

    }

}
