import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { BidEntity } from './bid.entity'

@Entity('bid_type')
export class BidTypeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @OneToMany(() => BidEntity, (p) => p.bidType)
  bids: Promise<BidEntity[]>
}
