import { ShipmentEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, Index } from 'typeorm'

/** Thông tin shipment type*/
@Entity('shipment_route')
export class ShipmentRouteEntity extends BaseEntity {

  /**Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Số shipment */
  @Index({ unique: true })
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** name */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @OneToMany(() => ShipmentEntity, (p) => p.shipmentRoute)
  shipments: Promise<ShipmentEntity[]>

}
