import { ApiProperty } from '@nestjs/swagger'

export class RequestQuoteCreateDto {
  @ApiProperty()
  code: string

  @ApiProperty({ required: false })
  name?: string

  @ApiProperty({ required: false })
  companyId?: string

  @ApiProperty({ required: false })
  purchasingOrgId?: string

  @ApiProperty({ required: false })
  purchasingGroupId?: string

  @ApiProperty({ required: false })
  employeeId?: string

  @ApiProperty({ required: false })
  purpose?: string

  @ApiProperty({ required: false })
  typeQuote?: string

  @ApiProperty({ required: false })
  quotationPeriod?: string

  @ApiProperty({ required: false, type: String, format: 'date-time' })
  timeStartReceiving?: Date

  @ApiProperty({ required: false, type: String, format: 'date-time' })
  timeEndReceiving?: Date

  @ApiProperty({ required: false, default: 0 })
  quantity?: number

  @ApiProperty({ required: false, type: String, format: 'date-time' })
  timeConfirmParticipationQuotation?: Date

  @ApiProperty({ required: false })
  description?: string

  @ApiProperty({ required: false })
  referenceQuote?: string

  @ApiProperty({ required: false })
  prId?: string

  @ApiProperty({ required: false })
  shipmentPlanId?: string

  @ApiProperty({ required: false })
  lstIncotermId: any

  @ApiProperty({ required: false })
  lstPaymentTermId?: any

  @ApiProperty({ required: false })
  quotationForm?: string

  @ApiProperty({ required: false })
  status?: string

  @ApiProperty({ required: false })
  quotationValidity?: number

  lstFileList: any[]

  lstDetail: any[]

  lstFee: any[]

  lstSupplier: any[]

  plantId: string
}
