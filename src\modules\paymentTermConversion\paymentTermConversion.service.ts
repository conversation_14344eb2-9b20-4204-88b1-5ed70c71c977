import { Injectable } from '@nestjs/common'
import { In, Like } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS } from '../../constants'
import { PaymentTermConversionEntity } from '../../entities'
import { customAlphabet } from 'nanoid'
import { PaymentTermConversionRepository, PaymentTermRepository } from '../../repositories'
import { PaymentTermConversionCreateDto } from './dto'
import { inspect } from 'node:util'

@Injectable()
export class PaymentTermConversionService {
  constructor(private repo: PaymentTermConversionRepository, private paymentTermRepo: PaymentTermRepository) {}

  /** Lấy ds incoterm */
  public async find() {
    return await this.repo.find({ where: { isDeleted: false } })
  }

  /** <PERSON><PERSON>y ds incoterm có phân trang */
  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}

    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)

    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })

    return res
  }

  /** Tạo mới một incoterm với dữ liệu được cung cấp. */
  async createData(user: UserDto, data: PaymentTermConversionCreateDto[]) {
    if (data.length === 0) throw new Error(` Vui lòng thêm ít nhất một Item. Vui lòng kiểm tra lại`)

    const dictPayment: any = {}
    {
      const lstIncotem: any = await this.paymentTermRepo.find({ where: { isDeleted: false }, select: { id: true, code: true } })
      lstIncotem.forEach((c) => (dictPayment[c.id] = c))
    }

    for (let item of data) {
      if (item.paymentTermId) {
        if (!dictPayment[item.paymentTermId]) throw new Error(` Payment Term không tồn tại. Vui lòng kiểm tra lại`)
      }
    }

    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const repo = trans.getRepository(PaymentTermConversionEntity)

      for (let item of data) {
        const findEntity = await repo.findOne({ where: { paymentTermId: item.paymentTermId } })
        if (findEntity) {
          findEntity.updatedAt = new Date()
          findEntity.updatedBy = user.id
          findEntity.percentBank = item.percentBank || 0

          // 1
          findEntity.paymentTermId = item.paymentTermId
          findEntity.dayOfRecommendedPurchase = item.finalDto.dayOfRecommendedPurchase || 0
          findEntity.percentOfRecommendedPurchase = item.finalDto.percentOfRecommendedPurchase || 0
          findEntity.recommendedPurchaseCodeLeadTime = item.finalDto.recommendedPurchaseCodeLeadTime

          // 2
          findEntity.dayOfContract = item.finalDto.dayOfContract || 0
          findEntity.percentOfContract = item.finalDto.percentOfContract || 0
          findEntity.contractCodeLeadTime = item.finalDto.contractCodeLeadTime

          // 3
          findEntity.dayOfSupplierPrepare = item.finalDto.dayOfSupplierPrepare || 0
          findEntity.percentSupplierPrepare = item.finalDto.percentSupplierPrepare || 0
          findEntity.supplierPrepareCodeLeadTime = item.finalDto.supplierPrepareCodeLeadTime

          // 4
          findEntity.dayOfSupplierProduction = item.finalDto.dayOfSupplierProduction || 0
          findEntity.percentSupplierProduction = item.finalDto.percentSupplierProduction || 0
          findEntity.supplierProductionCodeLeadTime = item.finalDto.supplierProductionCodeLeadTime

          // 5
          findEntity.dayOfSupplierProductionToPort = item.finalDto.dayOfSupplierProductionToPort || 0
          findEntity.percentSupplierProductionToPort = item.finalDto.percentSupplierProductionToPort || 0
          findEntity.supplierProductionToPortCodeLeadTime = item.finalDto.supplierProductionToPortCodeLeadTime

          //6
          findEntity.dayOfTransportSupplierToVietNam = item.finalDto.dayOfTransportSupplierToVietNam || 0
          findEntity.percentTransportSupplierToVietNam = item.finalDto.percentTransportSupplierToVietNam || 0
          findEntity.transportSupplierToVietNamCodeLeadTime = item.finalDto.transportSupplierToVietNamCodeLeadTime

          //7
          findEntity.dayOfTransportVietNamToWarehouse = item.finalDto.dayOfTransportVietNamToWarehouse || 0
          findEntity.percentTransportVietNamToWarehouse = item.finalDto.percentTransportVietNamToWarehouse || 0
          findEntity.transportVietNamToWarehouseCodeLeadTime = item.finalDto.transportVietNamToWarehouseCodeLeadTime

          //8
          findEntity.dayOfQualityCheckAndReceiving = item.finalDto.dayOfQualityCheckAndReceiving || 0
          findEntity.percentQualityCheckAndReceiving = item.finalDto.percentQualityCheckAndReceiving || 0
          findEntity.qualityCheckAndReceivingCodeLeadTime = item.finalDto.qualityCheckAndReceivingCodeLeadTime

          //9
          findEntity.dayOfLeadtimePurchase = item.finalDto.dayOfLeadtimePurchase || 0
          findEntity.percentLeadtimePurchase = item.finalDto.percentLeadtimePurchase || 0
          findEntity.leadtimeDeliveryCodeLeadTime = item.finalDto.leadtimeDeliveryCodeLeadTime

          //10
          findEntity.dayOfLeadtimeDelivery = item.finalDto.dayOfLeadtimeDelivery || 0
          findEntity.percentLeadtimeDelivery = item.finalDto.percentLeadtimeDelivery || 0
          findEntity.leadtimePurchaseCodeLeadTime = item.finalDto.leadtimePurchaseCodeLeadTime
          await repo.save(findEntity)
        } else {
          const nanoid = customAlphabet('QWERTYUIOPASDFGHJKLZXCVBNM', 10)
          const entity = new PaymentTermConversionEntity()
          entity.code = nanoid()
          entity.createdAt = new Date()
          entity.createdBy = user.id
          entity.percentBank = item.percentBank || 0
          entity.paymentTermId = item.paymentTermId
          entity.dayOfRecommendedPurchase = item.finalDto.dayOfRecommendedPurchase || 0
          entity.percentOfRecommendedPurchase = item.finalDto.percentOfRecommendedPurchase || 0
          entity.recommendedPurchaseCodeLeadTime = item.finalDto.recommendedPurchaseCodeLeadTime
          entity.dayOfContract = item.finalDto.dayOfContract || 0
          entity.percentOfContract = item.finalDto.percentOfContract || 0
          entity.contractCodeLeadTime = item.finalDto.contractCodeLeadTime
          entity.dayOfSupplierPrepare = item.finalDto.dayOfSupplierPrepare || 0
          entity.percentSupplierPrepare = item.finalDto.percentSupplierPrepare || 0
          entity.supplierPrepareCodeLeadTime = item.finalDto.supplierPrepareCodeLeadTime
          entity.dayOfSupplierProduction = item.finalDto.dayOfSupplierProduction || 0
          entity.percentSupplierProduction = item.finalDto.percentSupplierProduction || 0
          entity.supplierProductionCodeLeadTime = item.finalDto.supplierProductionCodeLeadTime
          entity.dayOfSupplierProductionToPort = item.finalDto.dayOfSupplierProductionToPort || 0
          entity.percentSupplierProductionToPort = item.finalDto.percentSupplierProductionToPort || 0
          entity.supplierProductionToPortCodeLeadTime = item.finalDto.supplierProductionToPortCodeLeadTime
          entity.dayOfTransportSupplierToVietNam = item.finalDto.dayOfTransportSupplierToVietNam || 0
          entity.percentTransportSupplierToVietNam = item.finalDto.percentTransportSupplierToVietNam || 0
          entity.transportSupplierToVietNamCodeLeadTime = item.finalDto.transportSupplierToVietNamCodeLeadTime
          entity.dayOfTransportVietNamToWarehouse = item.finalDto.dayOfTransportVietNamToWarehouse || 0
          entity.percentTransportVietNamToWarehouse = item.finalDto.percentTransportVietNamToWarehouse || 0
          entity.transportVietNamToWarehouseCodeLeadTime = item.finalDto.transportVietNamToWarehouseCodeLeadTime
          entity.dayOfQualityCheckAndReceiving = item.finalDto.dayOfQualityCheckAndReceiving || 0
          entity.percentQualityCheckAndReceiving = item.finalDto.percentQualityCheckAndReceiving || 0
          entity.qualityCheckAndReceivingCodeLeadTime = item.finalDto.qualityCheckAndReceivingCodeLeadTime
          entity.dayOfLeadtimePurchase = item.finalDto.dayOfLeadtimePurchase || 0
          entity.percentLeadtimePurchase = item.finalDto.percentLeadtimePurchase || 0
          entity.leadtimePurchaseCodeLeadTime = item.finalDto.leadtimePurchaseCodeLeadTime
          entity.dayOfLeadtimeDelivery = item.finalDto.dayOfLeadtimeDelivery || 0
          entity.percentLeadtimeDelivery = item.finalDto.percentLeadtimeDelivery || 0
          entity.leadtimeDeliveryCodeLeadTime = item.finalDto.leadtimeDeliveryCodeLeadTime
          await repo.insert(entity)
        }
      }
    })

    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật trạng thái kích hoạt của incoterm. */
  async updateActive(user: UserDto, id: string) {
    const entity = await this.repo.findOne({
      where: { id: id },
      select: { id: true, isDeleted: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const newIsDeleted = !entity.isDeleted
    await this.repo.update(id, {
      isDeleted: newIsDeleted,
      updatedBy: user.id,
      updatedAt: new Date(),
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async loadDataSelect(user: UserDto) {
    return await this.repo.find({
      where: { isDeleted: false },
      select: { id: true, code: true },
    })
  }
}
