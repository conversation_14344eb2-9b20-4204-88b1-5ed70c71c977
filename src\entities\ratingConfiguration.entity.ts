import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

@Entity('rating_configuration')
export class RatingConfigurationEntity extends BaseEntity {
  /** <PERSON><PERSON><PERSON> độ */
  @Column({
    nullable: false,
    default: 0,
  })
  level: number

  /** Loại teamplate: (enumData: TemplateSupplierType) */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  /** Xếp loại: (enumData: RatingType) */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  ratingType: string
}
