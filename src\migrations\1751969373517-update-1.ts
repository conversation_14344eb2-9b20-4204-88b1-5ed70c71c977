import { MigrationInterface, QueryRunner } from 'typeorm'

export class Update11751969373517 implements MigrationInterface {
  name = 'Update11751969373517'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_fee_conditions" DROP COLUMN "description"`)
    await queryRunner.query(`ALTER TABLE "shipment_fee_conditions" ADD "description" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_fee_conditions" DROP COLUMN "description"`)
    await queryRunner.query(`ALTER TABLE "shipment_fee_conditions" ADD "description" varchar(255)`)
  }
}
