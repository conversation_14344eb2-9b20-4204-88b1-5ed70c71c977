import { Injectable } from '@nestjs/common'
import { In, Like } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { CREATE_SUCCESS, enumData, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { CostSettingEntity, CountryEntity } from '../../entities'
import { coreHelper } from '../../helpers'
import { CostSettingRepository } from '../../repositories'
import { CostSettingCreateDto, CostSettingUpdateDto } from './dto'
import { CountryCreateByExcelDto } from '../country/dto'

@Injectable()
export class CostSettingService {
  constructor(private repo: CostSettingRepository) {}

  /** Lấy ds quốc gia */
  public async find() {
    return await this.repo.find({ where: { isDeleted: false }, order: { name: 'ASC' } })
  }

  /** <PERSON><PERSON>y ds quốc gia có phân trang */
  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}

    if (data.where.name) {
      whereCon.name = Like(`%${data.where.name}%`)
    }
    if (data.where.code) {
      whereCon.code = Like(`%${data.where.code}%`)
    }

    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res = await this.repo.findAndCount({
      where: whereCon,
      order: { code: 'ASC' },
      skip: data.skip,
      take: data.take,
    })

    return res
  }

  /** Tạo mới một quốc gia với dữ liệu được cung cấp. */
  async createData(user: UserDto, data: CostSettingCreateDto) {
    const isCodeExist = await this.repo.findOne({ where: { code: data.code }, select: { id: true } })
    if (isCodeExist) throw new Error(ERROR_CODE_TAKEN)

    const countryNew = new CountryEntity()
    countryNew.code = data.code
    countryNew.name = data.name
    countryNew.description = data.description

    countryNew.createdAt = new Date()
    countryNew.createdBy = user.id
    await this.repo.insert(countryNew)

    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật thông tin của quốc gia với dữ liệu được cung cấp. */
  async updateData(user: UserDto, data: CostSettingUpdateDto) {
    const country = await this.repo.findOne({ where: { id: data.id } })
    if (!country) throw new Error(ERROR_NOT_FOUND_DATA)

    country.code = data.code
    country.name = data.name
    country.description = data.description

    country.updatedBy = user.id
    country.updatedAt = new Date()

    await this.repo.update(country.id, country)

    return { message: UPDATE_SUCCESS }
  }

  /** Cập nhật trạng thái kích hoạt của quốc gia. */
  async updateActive(user: UserDto, id: string) {
    const entity = await this.repo.findOne({
      where: { id: id },
      select: { id: true, isDeleted: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const newIsDeleted = !entity.isDeleted
    await this.repo.update(id, {
      isDeleted: newIsDeleted,
      updatedBy: user.id,
      updatedAt: new Date(),
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /**Hàm import excel */
  public async importData(user: UserDto, data: CountryCreateByExcelDto[]) {
    //check xem data có trùng nhau hoặc không có không
    if (data.length < 1) throw new Error('Vui lòng điền ít nhất 1 dòng dữ liệu!')
    {
      const dublicateArayCode = coreHelper.findDuplicates(data, 'code')
      if (dublicateArayCode.length > 0) throw new Error(`Danh sách mã chi phí trùng nhau ${dublicateArayCode.toString()}`)
    }

    //lọc lấy danh sách code của quốc gia
    const codes = data.map(function (obj) {
      return obj.code
    })

    // tìm ra những mã bị trùng dưới data base
    const dupCount = await this.repo.find({ where: { code: In(codes) } })
    if (dupCount.length > 0) {
      const dupCode = data.map(function (obj) {
        return obj.code
      })
      throw new Error(`Danh sách mã quốc gia trùng nhau [${dupCode.toString()}]`)
    }
    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const lstTask = []
      const dictCountry: any = {}
      const set = new Set()
      for (const item of data) {
        item.code = item.code.toString()
        if (dictCountry[item.code]) continue
        if (set.has(item.code)) continue
        set.add(item.code)

        const country = new CostSettingEntity()
        country.createdBy = user.id
        country.createdAt = new Date()
        country.code = item.code
        country.name = item.name
        country.description = item.description
        lstTask.push(country)
      }
      const lstChild = coreHelper.splitArrayByParameters(lstTask, 2000)
      for (const chunk of lstChild) {
        await trans.insert(CostSettingEntity, chunk)
      }
    })
    return { message: CREATE_SUCCESS }
  }

  async loadDataSelect(user: UserDto) {
    return await this.repo.find({
      where: { isDeleted: false },
      select: { id: true, code: true, name: true },
    })
  }
}
