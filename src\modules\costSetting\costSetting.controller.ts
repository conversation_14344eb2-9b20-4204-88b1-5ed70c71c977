import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { CountryCreateByExcelDto } from '../country/dto'
import { CostSettingCreateDto, CostSettingUpdateDto } from './dto'
import { CostSettingService } from './costSetting.service'

@ApiBearerAuth()
@ApiTags('CostSetting')
@Controller('cost_setting')
export class CostSettingController {
  constructor(private readonly service: CostSettingService) {}

  @ApiOperation({ summary: 'Lấy danh sách quốc gia' })
  @UseGuards(JwtAuthGuard)
  @Post('find')
  public async find() {
    return await this.service.find()
  }

  @ApiOperation({ summary: 'Danh sách quốc gia phân trang' })
  @UseGuards(JwtAuthGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới một quốc gia với dữ liệu được cung cấp.' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: CostSettingCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin của quốc gia với dữ liệu được cung cấp.' })
  @UseGuards(JwtAuthGuard)
  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: CostSettingUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái kích hoạt của quốc gia.' })
  @UseGuards(JwtAuthGuard)
  @Post('update_active')
  async updateActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Tạo mới một quốc gia với danh sách dữ liệu danh được cung cấp.' })
  @UseGuards(JwtAuthGuard)
  @Post('import_data')
  async importData(@CurrentUser() user: UserDto, @Body() data: CountryCreateByExcelDto[]) {
    return await this.service.importData(user, data)
  }

  @ApiOperation({ summary: 'Load data select' })
  @Post('load_data_select')
  async loadDataSelect(@CurrentUser() user: UserDto) {
    return await this.service.loadDataSelect(user)
  }
}
