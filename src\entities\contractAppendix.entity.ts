import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ContractEntity } from './contract.entity'
import { ContractAppendixItemEntity } from './contractAppendixItem.entity'
import { ContractAppendixPaymentProgressEntity } from './contractAppendixPayment.entity'
import { PaymentTermEntity } from './paymentTerm.entity'
import { IncotermEntity } from './incoterm.entity'

/** <PERSON><PERSON> lục hợp đồng */
@Entity({ name: 'contract_appendix' })
export class ContractAppendixEntity extends BaseEntity {
  /**Số phụ lục hợp đồng */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** S<PERSON> phụ lục hợp đồng ngoại thương */
  @Column({
    type: 'nvarchar',
    length: 150,
    nullable: true,
  })
  foreignCode: string
  /**Tên phụ lụ<PERSON> hợp đồng */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  name: string

  /**Tên phụ lục hợp đồng tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  nameEN: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  title: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.appendixs)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileAttach: string

  /** Ngày ký phụ lục hợp đồng */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  contractAppendixDate: Date

  /** Ngày có hiệu lực */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  effectiveDate: Date

  /** Ngày hết hiệu lực*/
  @Column({
    nullable: true,
    type: 'datetime',
  })
  expiredDate: Date

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  createdBy: string

  /** Ngày hiệu lực cũ */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  effectiveAppendixDateOld: Date

  /** Ngày hiệu lực mới */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  effectiveAppendixDateNew: Date

  /** Ngày hết hạn cũ */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  expiredAppendixDateOld: Date

  /** Ngày hết hạn mới */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  expiredAppendixDateNew: Date

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileAttachChange: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  descriptionChange: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /**Hiệu lực hợp đồng*/
  @Column({
    nullable: true,
    default: false,
  })
  isEffContract: boolean

  /**Tiến độ thanh toán*/
  @Column({
    nullable: true,
    default: false,
  })
  isPaymentProgress: boolean

  /**Item */
  @OneToMany(() => ContractAppendixItemEntity, (p) => p.contractAppendix)
  contractAppendixItems: Promise<ContractAppendixItemEntity[]>

  /** Tiến độ thanh toán */

  @OneToMany(() => ContractAppendixPaymentProgressEntity, (p) => p.contractAppendix)
  contractAppendixPaymentProgressItems: Promise<ContractAppendixPaymentProgressEntity[]>

  //#region Thông tin hợp đồng được chỉnh sửa
  /** Trị giá hợp đồng trước thuế thay đổi */
  @Column({
    type: 'float',
    default: 0,
    nullable: true,
  })
  contractAmountBeforeTax: number

  /** Trị giá hợp đồng trước thuế bằng chữ thay đổi*/
  @Column({
    type: 'nvarchar',
    length: 400,
    nullable: true,
  })
  contractAmountBeforeTaxText: string

  /** Trị giá hợp đồng sau thuế thay đổi*/
  @Column({
    type: 'float',
    default: 0,
    nullable: true,
  })
  contractValueAfterTax: number

  /**Trị giá hợp đồng sau thuế bằng chữ thay đổi*/
  @Column({
    type: 'nvarchar',
    length: 400,
    nullable: true,
  })
  contractAmountAfterTaxText: string

  /** Giá trị hợp đồng quy đổi sang VNĐ thay đổi*/
  @Column({
    type: 'float',
    default: 0,
    nullable: true,
  })
  contractValueBeforeTaxInVND: number

  /** Giá trị hợp đồng sau thuế quy đổi sang VNĐ  thay đổi*/
  @Column({
    type: 'float',
    default: 0,
    nullable: true,
  })
  contractValueAfterTaxInVND: number

  // Thời hạn thanh toán
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentTermId: string
  @ManyToOne(() => PaymentTermEntity, (p) => p.appendixs)
  @JoinColumn({ name: 'paymentTermId', referencedColumnName: 'id' })
  paymentTerm: Promise<PaymentTermEntity>

  /**Sổ tài khoản */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  bankNumber: string

  /**Chủ tài khoản */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  bankUsername: string

  /**Ngân hàng */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  bankName: string

  /**Chi nhánh */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  bankBranchName: string

  /**Swift Code */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  swiftCode: string

  /**IBAN */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  iban: string

  /**Điều kiện thương mại */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermId: string
  @ManyToOne(() => IncotermEntity, (incoterm) => incoterm.appendixs)
  @JoinColumn({ name: 'incotermId', referencedColumnName: 'id' })
  incoterm: Promise<IncotermEntity>

  /** Phiên bản Incoterm */
  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: true,
  })
  incotermVersion: string

  /** Địa điểm áp dụng Incoterm */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  incotermLocation: string

  @Column({ type: 'nvarchar', nullable: true })
  fileUrl: string

  /**Lý do yêu cầu kiểm tra lại */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  reason: string
  //#region
}
