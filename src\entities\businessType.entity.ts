import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, Index } from 'typeorm'
import { SupplierEntity } from './supplier.entity'

/** Thông tin loại hình doanh nghiệp*/
@Entity('business_type')
export class BusinessTypeEntity extends BaseEntity {
  /**Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Mã */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: false,
  })
  code: string

  /** name */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierEntity, (p) => p.businessType)
  suppliers: Promise<SupplierEntity[]>
}
