import { MigrationInterface, QueryRunner } from 'typeorm'

export class GenColum1750737795697 implements MigrationInterface {
  name = 'GenColum1750737795697'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pr_item" ADD "quantityBefore" int`)
    await queryRunner.query(`ALTER TABLE "pr_item" ADD "budgetBefore" bigint`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "budgetBefore"`)
    await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "quantityBefore"`)
  }
}
