import { ApiProperty } from '@nestjs/swagger'
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class GuideCreateDto {
  @ApiProperty({ description: '<PERSON><PERSON><PERSON>ớ<PERSON> dẫn sử dụng' })
  @IsNotEmpty()
  @IsString()
  guideName: string

  @ApiProperty({ description: 'Loại HDSD' })
  @IsNotEmpty()
  @IsString()
  guideType: string

  @ApiProperty({ description: 'Đường link đọc Hướng dẫn sử dụng' })
  @IsNotEmpty()
  @IsString()
  guideLink: string
}
