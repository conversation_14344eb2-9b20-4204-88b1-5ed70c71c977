import { BaseEntity } from './base.entity'
import { Entity, Column, ManyTo<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { RecommendedPurchaseTemplateEntity } from './recommendedPurchaseTemplate.entity'
import { RecommendedPurchaseColValueEntity } from './recommendedPurchaseColValue.entity'
import { RecommendedPurchaseSettingValueEntity } from './recommendedPurchaseSettingValue.entity'

@Entity('recommended_purchase_template_col')
export class RecommendedPurchaseTemplateColEntity extends BaseEntity {
  /** Mã */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  /** Công thức */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fomular: string

  /** C<PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: true,
  })
  isRequired: boolean

  /** <PERSON><PERSON><PERSON><PERSON> chỉnh sửa */
  @Column({
    nullable: false,
    default: true,
  })
  isEdited: boolean

  @Column({
    nullable: true,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** Kiểu dữ liệu */
  @Column({
    length: 50,
    nullable: true,
  })
  colType: string

  /** Kiểu dữ liệu */
  @Column({
    length: 50,
    nullable: true,
  })
  type: string

  /** Lấy dữ liệu từ hệ thống */
  @Column({
    nullable: true,
    default: false,
  })
  isSystemData: boolean

  /** Data Mapping */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  dataMapping: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseTemplateId: string
  @ManyToOne(() => RecommendedPurchaseTemplateEntity, (p) => p.recommendedPurchaseTemplateCols)
  @JoinColumn({ name: 'recommendedPurchaseTemplateId', referencedColumnName: 'id' })
  recommendedPurchaseTemplate: Promise<RecommendedPurchaseTemplateEntity>

  /** recommendedPurchase Template Code */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  recommendedPurchaseTemplateCode: string

  @OneToMany(() => RecommendedPurchaseColValueEntity, (p) => p.recommendedPurchaseTemplate)
  recommendedPurchaseColValues: Promise<RecommendedPurchaseColValueEntity[]>

  @OneToMany(() => RecommendedPurchaseSettingValueEntity, (p) => p.recommendedPurchaseTemplateCol)
  recommendedPurchaseSettingValue: Promise<RecommendedPurchaseSettingValueEntity[]>
}
