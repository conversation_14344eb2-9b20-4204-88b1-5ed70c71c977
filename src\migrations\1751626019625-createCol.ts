import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateCol1751626019625 implements MigrationInterface {
    name = 'CreateCol1751626019625'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation" ADD "glAccountId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD "order" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD "movementType" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD "baseDate" datetime`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD CONSTRAINT "FK_b68abab40d167da5659d457c279" FOREIGN KEY ("glAccountId") REFERENCES "gl_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation" DROP CONSTRAINT "FK_b68abab40d167da5659d457c279"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "baseDate"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "movementType"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "order"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "glAccountId"`);
    }

}
