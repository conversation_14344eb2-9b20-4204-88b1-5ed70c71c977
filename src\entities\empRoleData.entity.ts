import { BaseEntity } from './base.entity'
import { En<PERSON><PERSON>, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { EmployeeEntity } from './employee.entity'

@Entity('employee_role')
export class EmployeeRoleEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  roleCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  prType: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  poType: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.employeeRole)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>
}
