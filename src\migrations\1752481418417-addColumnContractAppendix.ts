import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnContractAppendix1752481418417 implements MigrationInterface {
  name = 'AddColumnContractAppendix1752481418417'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_inspection" ADD "nameEN" nvarchar(300)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "materialId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "shortText" nvarchar(350)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "unitId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "unitName" nvarchar(255)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "itemNo" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "lotNumber" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "acccate" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "category" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "materialGroupId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "assetCode" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "orderCode" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "plantId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "storageLocation" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "ounCode" varchar(10)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "ounId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "quantityAlternative" bigint`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "totalPriceVND" bigint`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "taxCode" nvarchar(100)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "taxRate" int CONSTRAINT "DF_a8185b44f36ac8d3541c2c0c23e" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "totalPriceAfterTax" bigint`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "origin" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "manufacturer" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "deliveryDate" datetime`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "underDeliveryTolerance" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "overDeliveryTolerance" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "stockType" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "valuationType" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "technicalSpec" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "latestDeliveryDate" datetime`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "toleranceRange" nvarchar(100)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "materialStorageLocationId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" ADD "name" varchar(250)`)
    await queryRunner.query(
      `ALTER TABLE "contract_appendix_payment_progress" ADD "percent" int NOT NULL CONSTRAINT "DF_ddd3809b5ef762667c1a08fb3b8" DEFAULT 0`,
    )
    await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" ADD "paymentMethodId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" ADD "description" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "foreignCode" nvarchar(150)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "nameEN" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "contractAppendixDate" datetime`)
    await queryRunner.query(
      `ALTER TABLE "contract_appendix" ADD "contractAmountBeforeTax" bigint CONSTRAINT "DF_6ac98f4816e080139bf62485578" DEFAULT 0`,
    )
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "contractAmountBeforeTaxText" nvarchar(400)`)
    await queryRunner.query(
      `ALTER TABLE "contract_appendix" ADD "contractValueAfterTax" bigint CONSTRAINT "DF_f463ba6bb67cc057ef421ead61e" DEFAULT 0`,
    )
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "contractAmountAfterTaxText" nvarchar(400)`)
    await queryRunner.query(
      `ALTER TABLE "contract_appendix" ADD "contractValueBeforeTaxInVND" bigint CONSTRAINT "DF_f9b5ecaed2c2380f766ed260762" DEFAULT 0`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_appendix" ADD "contractValueAfterTaxInVND" bigint CONSTRAINT "DF_b202bf15b8b01321f52395f16f3" DEFAULT 0`,
    )
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "paymentTermId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "bankNumber" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "bankUsername" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "bankName" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "bankBranchName" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "swiftCode" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "iban" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "incotermId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "incotermVersion" nvarchar(100)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "incotermLocation" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "name"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "name" nvarchar(250) NOT NULL`)
    await queryRunner.query(
      `ALTER TABLE "contract_appendix_item" ADD CONSTRAINT "FK_b645a80bcc622c30887865c83a0" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_appendix_item" ADD CONSTRAINT "FK_14acd974ddb66637ac894eaf24a" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_appendix_item" ADD CONSTRAINT "FK_a281fb3222d70c920f5c034d250" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_appendix_item" ADD CONSTRAINT "FK_af5e8484e74ef451daa0a60248e" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_appendix_item" ADD CONSTRAINT "FK_a51bfcc87ebaa647b99a44af1d0" FOREIGN KEY ("ounId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_appendix_item" ADD CONSTRAINT "FK_f5a30f94173ecbdd3030daff7a5" FOREIGN KEY ("materialStorageLocationId") REFERENCES "material_storage_location"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_appendix_payment_progress" ADD CONSTRAINT "FK_0df112f71bf565f4860b44a4194" FOREIGN KEY ("paymentMethodId") REFERENCES "payment_method"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_appendix" ADD CONSTRAINT "FK_1f5ada535e33b06c9603c6f6886" FOREIGN KEY ("paymentTermId") REFERENCES "payment_term"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_appendix" ADD CONSTRAINT "FK_91ab9505449372f6faa87937d0d" FOREIGN KEY ("incotermId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "FK_91ab9505449372f6faa87937d0d"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "FK_1f5ada535e33b06c9603c6f6886"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP CONSTRAINT "FK_0df112f71bf565f4860b44a4194"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP CONSTRAINT "FK_f5a30f94173ecbdd3030daff7a5"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP CONSTRAINT "FK_a51bfcc87ebaa647b99a44af1d0"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP CONSTRAINT "FK_af5e8484e74ef451daa0a60248e"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP CONSTRAINT "FK_a281fb3222d70c920f5c034d250"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP CONSTRAINT "FK_14acd974ddb66637ac894eaf24a"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP CONSTRAINT "FK_b645a80bcc622c30887865c83a0"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "name"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "name" varchar(250) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "incotermLocation"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "incotermVersion"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "incotermId"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "iban"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "swiftCode"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "bankBranchName"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "bankName"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "bankUsername"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "bankNumber"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "paymentTermId"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_b202bf15b8b01321f52395f16f3"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractValueAfterTaxInVND"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_f9b5ecaed2c2380f766ed260762"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractValueBeforeTaxInVND"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractAmountAfterTaxText"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_f463ba6bb67cc057ef421ead61e"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractValueAfterTax"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractAmountBeforeTaxText"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_6ac98f4816e080139bf62485578"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractAmountBeforeTax"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractAppendixDate"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "nameEN"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "foreignCode"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP COLUMN "description"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP COLUMN "paymentMethodId"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP CONSTRAINT "DF_ddd3809b5ef762667c1a08fb3b8"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP COLUMN "percent"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP COLUMN "name"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "materialStorageLocationId"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "toleranceRange"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "latestDeliveryDate"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "technicalSpec"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "valuationType"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "stockType"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "overDeliveryTolerance"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "underDeliveryTolerance"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "deliveryDate"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "manufacturer"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "origin"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "totalPriceAfterTax"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP CONSTRAINT "DF_a8185b44f36ac8d3541c2c0c23e"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "taxRate"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "taxCode"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "totalPriceVND"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "quantityAlternative"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "ounId"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "ounCode"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "storageLocation"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "plantId"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "orderCode"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "assetCode"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "materialGroupId"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "category"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "acccate"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "lotNumber"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "itemNo"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "unitName"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "unitId"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "shortText"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "materialId"`)
    await queryRunner.query(`ALTER TABLE "contract_inspection" DROP COLUMN "nameEN"`)
  }
}
