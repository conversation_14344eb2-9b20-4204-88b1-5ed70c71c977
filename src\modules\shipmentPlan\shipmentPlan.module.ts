import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { ShipmentPlanController } from './shipmentPlan.controller'
import { ShipmentPlanNumberDetailRepository, ShipmentPlanNumberRepository, ShipmentPlanRepository } from '../../repositories/shipmentPlan.repository'
import { ShipmentPlanService } from './shipmentPlan.service'
import { ShipmentConfigTemplateDetailRepository, ShipmentConfigTemplateRepository } from '../../repositories/shipmentConfigTemplate.repository'
import { ShipmentConditionTypeTemplateRepository, ShipmentFeeConditionsRepository } from '../../repositories/shipmentTemplate.repository'
import { FlowApproveModule } from '../flowApprove/flowApprove.module'
import { SupplierRepository } from '../../repositories'

@Module({
  controllers: [ShipmentPlanController],
  imports: [
    TypeOrmExModule.forCustomRepository([
      ShipmentPlanRepository,
      ShipmentConditionTypeTemplateRepository,
      ShipmentConfigTemplateRepository,
      ShipmentPlanNumberDetailRepository,
      ShipmentPlanNumberRepository,
      ShipmentFeeConditionsRepository,
      SupplierRepository,
    ]),
    FlowApproveModule,
  ],
  providers: [ShipmentPlanService],
})
export class ShipmentPlanModule {}
