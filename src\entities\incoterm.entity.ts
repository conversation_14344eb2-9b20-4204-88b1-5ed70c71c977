import {
  BusinessPlanEntity,
  ContractAppendixEntity,
  ContractEntity,
  MaterialGroupEntity,
  POEntity,
  RfqEntity,
  SupplierNumberRequestApproveEntity,
} from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { RoleSupplierEntity } from './roleSupplier.entity'
import { ShipmentPlanNumberEntity } from './shipmentPlanNumber.entity'
import { BusinessTemplatePlanEntity } from './businessTemplatePlan.entity'
import { IncotermConversionEntity } from './incotermConversion.entity'

@Entity('incoterm')
export class IncotermEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'int',
    nullable: true,
  })
  sort: number

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.incoterm)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>

  @OneToMany(() => ShipmentPlanNumberEntity, (p) => p.incoterm)
  shipmentPlanNumbers: Promise<ShipmentPlanNumberEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.incoterm)
  contracts: Promise<ContractEntity[]>

  @OneToMany(() => POEntity, (p) => p.incoterm)
  pos: Promise<POEntity[]>

  @OneToMany(() => BusinessPlanEntity, (p) => p.incoterm)
  businessPlan: Promise<BusinessPlanEntity[]>

  @OneToMany(() => BusinessTemplatePlanEntity, (p) => p.incoterm)
  businessTemplatePlans: Promise<BusinessTemplatePlanEntity[]>

  @OneToMany(() => RecommendedPurchaseEntity, (p) => p.incoterm)
  recommendedPurchase: Promise<RecommendedPurchaseEntity[]>

  @OneToMany(() => RoleSupplierEntity, (p) => p.incoterm)
  roleSuppliers: Promise<RoleSupplierEntity[]>

  @OneToMany(() => MaterialGroupEntity, (p) => p.incoterm)
  materialGroups: Promise<MaterialGroupEntity[]>

  @OneToMany(() => RfqEntity, (p) => p.incoterm)
  rfqs: Promise<RfqEntity[]>

  @OneToMany(() => ContractAppendixEntity, (p) => p.incoterm)
  appendixs: Promise<ContractAppendixEntity[]>

  @OneToMany(() => IncotermConversionEntity, (p) => p.incotermTo)
  incotermToConversions: Promise<IncotermConversionEntity[]>

  @OneToMany(() => IncotermConversionEntity, (p) => p.incotermForm)
  incotermFromConversions: Promise<IncotermConversionEntity[]>
}
