import { AfterLoad, BeforeInsert, Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PurchasingOrgEntity } from './purchasingOrg.entity'
import { PermissionToGroupEntity } from './permissionToGroup.entity'

@Entity('group_permission')
export class GroupPermissionEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
  })
  purchasingOrgId: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
  })
  purchasingOrgIdTemp: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, tr<PERSON> về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  poTypeCode: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  prTypeCode: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  fundProgram: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  fundCenter: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  purchasingGrId: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
  })
  companyId: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
  })
  companyIdTemp: string
  /* truớc khi load thì gắn companyId =  companyIdTemp */
  @AfterLoad()
  changeData() {
    if (this.companyIdTemp) {
      this.companyId = this.companyIdTemp
    }
    if (this.purchasingOrgIdTemp) {
      this.purchasingOrgId = this.purchasingOrgIdTemp
    }
    if (typeof this.purchasingOrgId === 'string' && this.purchasingOrgId) {
      const dataConvert: any = this.purchasingOrgId
        .split(',')
        .map((item) => item.trim())
        .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
      this.purchasingOrgId = dataConvert
    }
  }

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  companyOrgId: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  blockOrgId: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  departmentOrgId: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  partOrgId: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  positionOrgId: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  employeeOrgId: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  employeeId: string

  @OneToMany(() => PermissionToGroupEntity, (p) => p.groupPermission)
  permissionToGroups: Promise<PermissionToGroupEntity[]>
}
