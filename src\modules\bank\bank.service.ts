import { Injectable } from '@nestjs/common'
import { FindOptionsWhere, In, Like, Not } from 'typeorm'
import { coreHelper } from '../../helpers'
import {
  CREATE_SUCCESS,
  enumData,
  ERROR_CODE_TAKEN,
  ERROR_NOT_FOUND_DATA,
  IMPORT_SUCCESS,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../constants'
import { PaginationDto, UserDto } from '../../dto'
import { BankEntity } from '../../entities'
import { BankRepository } from '../../repositories'
import { BankCreateDto, BankCreateExcelDto, BankUpdateDto } from './dto'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'

@Injectable()
export class BankService {
  constructor(private readonly repo: BankRepository, private organizationalPositionService: OrganizationalPositionService) {}

  /** Lấy ds */
  async find(user: UserDto) {
    return await this.repo.find({ where: { isDeleted: false }, order: { createdAt: 'ASC' } })
  }

  /** Lấy ds có phân trang */
  async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: FindOptionsWhere<BankEntity> = {}
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.Bank.code)
    if (dataRs.type === enumData.RoleData.All.code) {
      /* do nothing */
    }
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where.name) {
      whereCon.name = Like(`%${data.where.name}%`)
    }
    if (data.where.code) {
      whereCon.code = Like(`%${data.where.code}%`)
    }
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res = await this.repo.findAndCount({
      where: whereCon,
      order: { code: 'DESC', createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })

    return res
  }

  /** Tạo mới một với dữ liệu được cung cấp. */
  async createData(user: UserDto, data: BankCreateDto) {
    const isCodeExist = await this.repo.exists({ where: { code: data.code } })
    if (isCodeExist) throw new Error(ERROR_CODE_TAKEN)

    const entity = new BankEntity()
    entity.code = data.code
    entity.name = data.name
    entity.description = data.description

    entity.createdBy = user.id
    await this.repo.insert(entity)

    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật thông tin của với dữ liệu được cung cấp. */
  async updateData(user: UserDto, data: BankUpdateDto) {
    const existEntity = await this.repo.findOne({ where: { id: data.id } })
    if (!existEntity) throw new Error(ERROR_NOT_FOUND_DATA)

    const isCodeExist = await this.repo.exists({ where: { code: data.code, id: Not(data.id) } })
    if (isCodeExist) throw new Error(ERROR_CODE_TAKEN)

    existEntity.code = data.code
    existEntity.name = data.name
    existEntity.description = data.description
    existEntity.updatedBy = user.id

    await this.repo.update(existEntity.id, existEntity)

    return { message: UPDATE_SUCCESS }
  }

  /** Cập nhật trạng thái kích hoạt của. */
  async updateActive(user: UserDto, id: string) {
    const entity = await this.repo.findOne({
      where: { id: id },
      select: { id: true, isDeleted: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const newIsDeleted = !entity.isDeleted
    await this.repo.update(id, {
      isDeleted: newIsDeleted,
      updatedBy: user.id,
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /** Hàm import excel */
  public async importData(user: UserDto, data: BankCreateExcelDto[]) {
    if (data.length < 1) throw new Error('Vui lòng điền ít nhất 1 dòng dữ liệu!')
    const dictBank: any = {}
    {
      const lstCode = coreHelper.selectDistinct(data, 'code')
      const lstBank: any = await this.repo.find({ where: { code: In(lstCode), isDeleted: false }, select: { id: true, code: true } })
      lstBank.forEach((c) => (dictBank[c.code] = c))
    }

    for (let item of data) {
      if (dictBank[item.code]) throw new Error(`Ngân hàng [${item.code}] đã tồn tại . Vui lòng kiểm tra lại`)
    }

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BankEntity)
      const lstEntity = data.map((item) => {
        return repo.create({
          code: item.code.trim().toUpperCase(),
          name: item.name,
          createdBy: user.id,
        })
      })
      await repo.insert(lstEntity)
    })
    return { message: IMPORT_SUCCESS }
  }

  /** Hàm loadDataSelect  */
  async loadDataSelect(user: UserDto) {
    return await this.repo.find({
      where: { isDeleted: false },
      select: { id: true, code: true, name: true },
    })
  }
}
