import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateFlowApprove1748315450536 implements MigrationInterface {
  name = 'UpdateFlowApprove1748315450536'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "flow_approve_base" ADD "applyPlant" bit CONSTRAINT "DF_2b2f22ccfa0e1897ce568dcf6ec" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "flow_approve_base" ADD "applyPurOrg" bit CONSTRAINT "DF_44e7c86c257015b7e8fb30835f6" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "flow_approve_base" ADD "applyPurGr" bit CONSTRAINT "DF_a1924c240a8e470b8faf41be494" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "flow_approve_base" ADD "plantId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "flow_approve_base" ADD "purGrId" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP COLUMN "purGrId"`)
    await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP COLUMN "plantId"`)
    await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP CONSTRAINT "DF_a1924c240a8e470b8faf41be494"`)
    await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP COLUMN "applyPurGr"`)
    await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP CONSTRAINT "DF_44e7c86c257015b7e8fb30835f6"`)
    await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP COLUMN "applyPurOrg"`)
    await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP CONSTRAINT "DF_2b2f22ccfa0e1897ce568dcf6ec"`)
    await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP COLUMN "applyPlant"`)
  }
}
