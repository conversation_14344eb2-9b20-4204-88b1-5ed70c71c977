import { BaseEntity } from './base.entity'
import { Entity, Column } from 'typeorm'

@Entity('link_client')
export class LinkClientEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  url: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  domain: string
}
