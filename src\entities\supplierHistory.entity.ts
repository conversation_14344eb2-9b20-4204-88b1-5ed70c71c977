import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'

/** <PERSON><PERSON><PERSON> lại lịch sử NCC */
@Entity('supplier_history')
export class SupplierHistoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.supplierHistorys)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** <PERSON>ô tả chi tiết */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string
}
