import { MigrationInterface, QueryRunner } from 'typeorm'

export class GenColumNorm1749722977806 implements MigrationInterface {
  name = 'GenColumNorm1749722977806'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Xóa default constraint trước khi xóa cột totalEmployee (nếu có)
    await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_6358e9bf8aea81f257020141be6"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "totalEmployee"`)

    // Thêm cột mới storageLocation
    await queryRunner.query(`ALTER TABLE "material" ADD "storageLocation" varchar(250)`)

    // Thêm các cột mới vào reservation_norm
    await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "uomAlternativeId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "normAlternative" float CONSTRAINT "DF_7b82877095547e218d38397e4a3" DEFAULT 0`)

    // Thêm foreign key
    await queryRunner.query(
      `ALTER TABLE "reservation_norm" ADD CONSTRAINT "FK_6691c9664e1adc1b068c9fe02f2" FOREIGN KEY ("uomAlternativeId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa foreign key
    await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "FK_6691c9664e1adc1b068c9fe02f2"`)

    // Xóa các cột mới thêm vào reservation_norm
    await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "DF_7b82877095547e218d38397e4a3"`)
    await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "normAlternative"`)
    await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "uomAlternativeId"`)

    // Xóa storageLocation và khôi phục lại totalEmployee
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "storageLocation"`)
    await queryRunner.query(`ALTER TABLE "material" ADD "totalEmployee" int`)
    await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "DF_6358e9bf8aea81f257020141be6" DEFAULT 0 FOR "totalEmployee"`)
  }
}
