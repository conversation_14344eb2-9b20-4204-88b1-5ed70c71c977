import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnSupplier1750147655807 implements MigrationInterface {
    name = 'AddColumnSupplier1750147655807'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier" ADD "fileOther" nvarchar(max)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "fileOther"`);
    }

}
