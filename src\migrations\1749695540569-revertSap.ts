import { MigrationInterface, QueryRunner } from 'typeorm'

export class RevertSap1749695540569 implements MigrationInterface {
  name = 'RevertSap1749695540569'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier_number" ADD "businessPartnerGroupId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "supplier_number" ADD "isRequestApprove" bit CONSTRAINT "DF_c6ffe5c637fb132b9e38076f5c5" DEFAULT 0`)
    await queryRunner.query(
      `ALTER TABLE "supplier_number" ADD CONSTRAINT "FK_6883442a1e4c4469ae07d295ec2" FOREIGN KEY ("businessPartnerGroupId") REFERENCES "business_partner_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier_number" DROP CONSTRAINT "FK_6883442a1e4c4469ae07d295ec2"`)
    await queryRunner.query(`ALTER TABLE "supplier_number" DROP CONSTRAINT "DF_c6ffe5c637fb132b9e38076f5c5"`)
    await queryRunner.query(`ALTER TABLE "supplier_number" DROP COLUMN "isRequestApprove"`)
    await queryRunner.query(`ALTER TABLE "supplier_number" DROP COLUMN "businessPartnerGroupId"`)
  }
}
