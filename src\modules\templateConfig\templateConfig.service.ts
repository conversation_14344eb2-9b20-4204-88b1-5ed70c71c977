import { Inject, Injectable, OnModuleInit } from '@nestjs/common'
import { Between, DataSource, EntityManager, ILike } from 'typeorm'
import { TemplateRepository } from '../../repositories/template.repository'
import { PaginationDto, UserDto } from '../../dto'
import { TemplateEntity } from '../../entities'
import moment from 'moment'
import { AllTemplates } from '../../constants/enumTemplate'
import { InjectEntityManager } from '@nestjs/typeorm'
import { DATA_SOURCE } from '../../constants'
import axios from 'axios'
import PizZip from 'pizzip'
import Docxtemplater from 'docxtemplater'
import { TemplateConfigRepository } from '../../repositories/templateConfig.repository'
import { TemplateConfigDto, UpdateTemplateConfigDto } from './dto/templateConfigDetail.dto'
import { apiHelper } from '../../helpers'

@Injectable()
export class TemplateConfigService {
  constructor(private readonly repo: TemplateConfigRepository) {}
  async create(data: TemplateConfigDto) {
    const dataInput: any = { ...data }
    //  const newTemplateToSave = this.repo.create(dataInput)
    /* lưu lại ở PMS */
    // const template = await this.repo.save(newTemplateToSave)
    /* call api để đồng bộ qua document */
    const resul = await apiHelper.postTemplateConfig(dataInput)
    return resul
  }
  async getList(data: any) {
    return await apiHelper.getTemplateConfig(data)
  }
  async detailTemplate(data: any) {
    const template = await apiHelper.getTemplateConfigDetail(data.code)
    return template
  }

  async getGroupTemplate() {
    return await apiHelper.getGroupTemplate()
  }

  async update(data: UpdateTemplateConfigDto) {
    const dataInput: any = { ...data }

    const res = await apiHelper.postUpdateTemplateConfig(dataInput)
    return res
  }
}
