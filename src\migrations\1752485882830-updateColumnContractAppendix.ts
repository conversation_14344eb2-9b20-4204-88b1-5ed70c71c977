import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateColumnContractAppendix1752485882830 implements MigrationInterface {
  name = 'UpdateColumnContractAppendix1752485882830'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "type"`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "type" varchar(50) NOT NULL`)
  }
}
