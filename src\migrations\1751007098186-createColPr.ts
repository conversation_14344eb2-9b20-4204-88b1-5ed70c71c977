import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateColPr1751007098186 implements MigrationInterface {
    name = 'CreateColPr1751007098186'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "pr_item" ADD "acccate" varchar(1)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "acccate"`);
    }

}
