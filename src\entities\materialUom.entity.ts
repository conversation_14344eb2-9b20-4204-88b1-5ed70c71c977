import { Entity, Column, ManyTo<PERSON>ne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base.entity'
import { MaterialEntity } from './material.entity'
import { UomEntity } from './uom.entity'

@Entity('material_uom')
export class MaterialUomEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string

  /** Đơn vị tính  */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  uomId: string

  /** Đơn vị tính mặc định */
  @Column({
    nullable: true,
    default: false,
  })
  isDefault: boolean

  /** coefficientX */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  coefficientX: number

  /** coefficientY */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  coefficientY: number

  @ManyToOne(() => MaterialEntity, (p) => p.materialUoms)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @ManyToOne(() => UomEntity, (p) => p.materialUoms)
  @JoinColumn({ name: 'uomId', referencedColumnName: 'id' })
  uom: Promise<UomEntity>

  /** Đơn vị thay thế  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  uomAlternativeId: string
  @ManyToOne(() => UomEntity, (p) => p.materialUom)
  @JoinColumn({ name: 'uomAlternativeId', referencedColumnName: 'id' })
  uomAlternative: Promise<UomEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialCode: string
}
