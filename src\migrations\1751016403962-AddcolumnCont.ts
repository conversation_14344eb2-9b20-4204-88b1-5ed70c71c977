import { MigrationInterface, QueryRunner } from "typeorm";

export class AddcolumnCont1751016403962 implements MigrationInterface {
    name = 'AddcolumnCont1751016403962'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "excelId" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "excelId"`);
    }

}
