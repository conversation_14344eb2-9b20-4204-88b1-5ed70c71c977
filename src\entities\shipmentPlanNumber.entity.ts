import { AfterLoad, Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ShipmentPlanEntity } from './shipmentPlan.entity'
import { ShipmentPlanNumberDetailEntity } from './shipmentPlanNumberDetail.entity'
import { IncotermEntity } from './incoterm.entity'
import { CurrencyEntity } from './currency.entity'

@Entity('shipment_plan_number')
export class ShipmentPlanNumberEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  title: string

  /** Shipment */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPlanId: string
  @ManyToOne(() => ShipmentPlanEntity, (p) => p.shipmentPlanNumbers)
  @JoinColumn({ name: 'shipmentPlanId', referencedColumnName: 'id' })
  shipmentPlan: Promise<ShipmentPlanEntity>

  /* incoterm */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermId: string
  @ManyToOne(() => IncotermEntity, (p) => p.shipmentPlanNumbers)
  @JoinColumn({ name: 'incotermId', referencedColumnName: 'id' })
  incoterm: Promise<IncotermEntity>

  /* currency */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currencyId: string
  @ManyToOne(() => CurrencyEntity, (p) => p.shipmentPlanNumbers)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  /* Id template */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentConfigTemplateId: string

  /* LOẠI NGUỒN DỮ LIỆU THAM CHIẾU */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPlanNumberType: string
  /* ngày giao hàng */
  @Column({
    type: 'date',
    nullable: true,
  })
  deliveryDate: Date
  /* shipment conditionTypeid compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactId: string[]

  /* shipment conditionType code compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactCode: string[]

  /* shipment conditionType price compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactValue: string[]

  /* json config bảng */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        return JSON.stringify(value)
      },
      from(value) {
        return value ? JSON.parse(value) : {}
      },
    },
  })
  shipmentPlanNumberConfigTable: any

  @OneToMany(() => ShipmentPlanNumberDetailEntity, (p) => p.shipmentPlanNumber)
  shipmentPlanNumberDetails: Promise<ShipmentPlanNumberDetailEntity[]>

  /** Giá đấu */

  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 10, default: 0 })
  totalValue?: number

  //Mapping data
  templateId: string
  tableConfig: any
  // afterload
  @AfterLoad()
  updateEntity() {
    if (this.shipmentConfigTemplateId) this.templateId = this.shipmentConfigTemplateId
    if (this.shipmentPlanNumberConfigTable) {
      this.tableConfig = this.shipmentPlanNumberConfigTable
    } else {
      this.tableConfig = {}
    }
  }
}
