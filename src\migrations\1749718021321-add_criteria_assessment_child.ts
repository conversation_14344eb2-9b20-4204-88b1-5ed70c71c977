import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddCriteriaAssessmentChild1749718021321 implements MigrationInterface {
  name = 'AddCriteriaAssessmentChild1749718021321'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" DROP CONSTRAINT "FK_71c9f7ef0197019c21b302c7d94"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP CONSTRAINT "FK_5098f91ec52a61fdb326e2ac9e5"`)
    await queryRunner.query(`ALTER TABLE "template_site_assessment_item" DROP CONSTRAINT "FK_c76018c94d8472c57974801bae0"`)
    await queryRunner.query(`EXEC sp_rename "dbo.criteria_site_assessment_list_detail.criteriaSiteAssessmentId", "criteriaSiteAssessmentChildId"`)
    await queryRunner.query(`EXEC sp_rename "dbo.template_site_assessment_item.templateSiteAssessmentItemId", "templateSiteAssessmentId"`)
    await queryRunner.query(
      `CREATE TABLE "criteria_site_assessment_child" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c409b6b2886a08bb36849356bb0" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_edf2520d7a8d8a4faf572ce1724" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "name" nvarchar(max), "isToStore" bit CONSTRAINT "DF_aa54633754a1cecfcb975e07e81" DEFAULT 0, "maxScore" int CONSTRAINT "DF_00b0c1cb5e0ed26daf2ef7c2711" DEFAULT 0, "isSenSupplier" bit CONSTRAINT "DF_5a7d1d92d5bbb1cd4add91c5a1a" DEFAULT 0, "dataType" nvarchar(250), "document" nvarchar(max), "supplierReply" varchar(400), "supplierReplyList" varchar(100), "supplierReplyNumber" int, "evaluation" varchar(400), "evaluationNumber" int, "evaluationList" varchar(50), "fileUrl" varchar(250), "fileName" varchar(250), "scoreEvaluation" int, "criteriaSiteAssessmentId" uniqueidentifier NOT NULL, CONSTRAINT "PK_c409b6b2886a08bb36849356bb0" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "name"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP CONSTRAINT "DF_75c14bd7486d4443a93bc5e2794"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "isRequired"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP CONSTRAINT "DF_a2195f1ce0ffb06fd7c750381fa"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "isCalUp"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP CONSTRAINT "DF_a74ef2d5af877cb765e34204890"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "type"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP CONSTRAINT "DF_0cd78d16949e8a7f9a54743d897"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "percent"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "percentRule"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "percentDownRule"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP CONSTRAINT "DF_e59bef9cd1b0429df3cb852dda2"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "level"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "description"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "supplierReply"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "supplierReplyList"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "supplierReplyNumber"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "evaluation"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "evaluationNumber"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "evaluationList"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "parentId"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "scoreDLC"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "score"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "scoreEvaluation"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "requiredMin"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP CONSTRAINT "DF_2a9d4b74efc63267f2048eac1c2"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "isHighlight"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "hightlightValue"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "fileUrl"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "fileName"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "content" nvarchar(max) NOT NULL`)
    await queryRunner.query(
      `ALTER TABLE "criteria_site_assessment_list_detail" ADD CONSTRAINT "FK_377798c3b1108856e7b29b76cfb" FOREIGN KEY ("criteriaSiteAssessmentChildId") REFERENCES "criteria_site_assessment_child"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "criteria_site_assessment_child" ADD CONSTRAINT "FK_0ac89c24be90934f62d43fa5bb3" FOREIGN KEY ("criteriaSiteAssessmentId") REFERENCES "criteria_site_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "template_site_assessment_item" ADD CONSTRAINT "FK_00844053bd2d41022ccd70fef10" FOREIGN KEY ("templateSiteAssessmentId") REFERENCES "template_site_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "template_site_assessment_item" DROP CONSTRAINT "FK_00844053bd2d41022ccd70fef10"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment_child" DROP CONSTRAINT "FK_0ac89c24be90934f62d43fa5bb3"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" DROP CONSTRAINT "FK_377798c3b1108856e7b29b76cfb"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "content"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "fileName" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "fileUrl" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "hightlightValue" int`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "isHighlight" bit NOT NULL`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD CONSTRAINT "DF_2a9d4b74efc63267f2048eac1c2" DEFAULT 0 FOR "isHighlight"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "requiredMin" int`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "scoreEvaluation" int`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "score" int`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "scoreDLC" int`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "parentId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "evaluationList" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "evaluationNumber" int`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "evaluation" varchar(400)`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "supplierReplyNumber" int`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "supplierReplyList" varchar(100)`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "supplierReply" varchar(400)`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "description" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "level" int NOT NULL`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD CONSTRAINT "DF_e59bef9cd1b0429df3cb852dda2" DEFAULT 1 FOR "level"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "percentDownRule" bigint`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "percentRule" bigint`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "percent" float`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD CONSTRAINT "DF_0cd78d16949e8a7f9a54743d897" DEFAULT 0 FOR "percent"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "type" nvarchar(255) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD CONSTRAINT "DF_a74ef2d5af877cb765e34204890" DEFAULT 'string' FOR "type"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "isCalUp" bit NOT NULL`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD CONSTRAINT "DF_a2195f1ce0ffb06fd7c750381fa" DEFAULT 1 FOR "isCalUp"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "isRequired" bit NOT NULL`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD CONSTRAINT "DF_75c14bd7486d4443a93bc5e2794" DEFAULT 0 FOR "isRequired"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "name" varchar(250) NOT NULL`)
    await queryRunner.query(`DROP TABLE "criteria_site_assessment_child"`)
    await queryRunner.query(`EXEC sp_rename "dbo.template_site_assessment_item.templateSiteAssessmentId", "templateSiteAssessmentItemId"`)
    await queryRunner.query(`EXEC sp_rename "dbo.criteria_site_assessment_list_detail.criteriaSiteAssessmentChildId", "criteriaSiteAssessmentId"`)
    await queryRunner.query(
      `ALTER TABLE "template_site_assessment_item" ADD CONSTRAINT "FK_c76018c94d8472c57974801bae0" FOREIGN KEY ("templateSiteAssessmentItemId") REFERENCES "template_site_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "criteria_site_assessment" ADD CONSTRAINT "FK_5098f91ec52a61fdb326e2ac9e5" FOREIGN KEY ("parentId") REFERENCES "criteria_site_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "criteria_site_assessment_list_detail" ADD CONSTRAINT "FK_71c9f7ef0197019c21b302c7d94" FOREIGN KEY ("criteriaSiteAssessmentId") REFERENCES "criteria_site_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }
}
