import { MigrationInterface, QueryRunner } from 'typeorm'

export class Remove1749618038463 implements MigrationInterface {
  name = 'Remove1749618038463'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pr" ADD "lstFund" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "pr" ADD "lstFundGroup" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "pr_item" ADD "productHierarchyId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "pr_item" ADD "fundGroup" varchar(255)`)
    await queryRunner.query(
      `ALTER TABLE "pr_item" ADD CONSTRAINT "FK_5941af6edd4901556f664d64d9d" FOREIGN KEY ("productHierarchyId") REFERENCES "product_hierarchy"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_5941af6edd4901556f664d64d9d"`)
    await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "fundGroup"`)
    await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "productHierarchyId"`)
    await queryRunner.query(`ALTER TABLE "pr" DROP COLUMN "lstFundGroup"`)
    await queryRunner.query(`ALTER TABLE "pr" DROP COLUMN "lstFund"`)
  }
}
