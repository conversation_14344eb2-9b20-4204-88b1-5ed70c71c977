import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateEntity1752460939081 implements MigrationInterface {
  name = 'UpdateEntity1752460939081'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "business_template_plan_cost" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_bdd2a71361ff1a9b614096a4ccf" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_48927c91e85a9e2431be4389013" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "businessTemplatePlanId" uniqueidentifier, "name" varchar(255), "totalPrice" bigint, "lstRfqConfig" nvarchar(max), CONSTRAINT "PK_bdd2a71361ff1a9b614096a4ccf" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "shipmentPlanId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "configTable" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" DROP CONSTRAINT "FK_1b956422e3b4dbc517fd661af30"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" ALTER COLUMN "businessTemplatePlanId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_type" ALTER COLUMN "code" varchar(150)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ALTER COLUMN "code" varchar(150)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_setup" ALTER COLUMN "businessTemplatePlanTypeId" varchar(150)`)
    await queryRunner.query(
      `ALTER TABLE "business_template_plan_exchange" ADD CONSTRAINT "FK_1b956422e3b4dbc517fd661af30" FOREIGN KEY ("businessTemplatePlanId") REFERENCES "business_template_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "business_template_plan_cost" ADD CONSTRAINT "FK_c054ae0d7e169fb4a9092e0eaa0" FOREIGN KEY ("businessTemplatePlanId") REFERENCES "business_template_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "business_template_plan" ADD CONSTRAINT "FK_145ff542c8ce8c93ad641fdb798" FOREIGN KEY ("shipmentPlanId") REFERENCES "shipment_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP CONSTRAINT "FK_145ff542c8ce8c93ad641fdb798"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_cost" DROP CONSTRAINT "FK_c054ae0d7e169fb4a9092e0eaa0"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" DROP CONSTRAINT "FK_1b956422e3b4dbc517fd661af30"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_setup" ALTER COLUMN "businessTemplatePlanTypeId" varchar(150) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ALTER COLUMN "code" varchar(150) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_type" ALTER COLUMN "code" varchar(150) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" ALTER COLUMN "businessTemplatePlanId" uniqueidentifier NOT NULL`)
    await queryRunner.query(
      `ALTER TABLE "business_template_plan_exchange" ADD CONSTRAINT "FK_1b956422e3b4dbc517fd661af30" FOREIGN KEY ("businessTemplatePlanId") REFERENCES "business_template_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "configTable"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "shipmentPlanId"`)
    await queryRunner.query(`DROP TABLE "business_template_plan_cost"`)
  }
}
