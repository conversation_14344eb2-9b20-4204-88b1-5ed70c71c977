import { Injectable, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common'
import { ERROR_NOT_FOUND_DATA, enumData, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS, CREATE_SUCCESS } from '../../constants'
import { BannerClientRepository, SettingStringRepository } from '../../repositories'
import { In, Like } from 'typeorm'
import { BannerClientCreateDto, BannerClientUpdateDto } from './dto'
import { UserDto } from '../../dto'
import { coreHelper } from '../../helpers'

@Injectable()
export class BannerClientService {
  constructor(private readonly repo: BannerClientRepository, private settingStringRepo: SettingStringRepository) {}

  public async createData(user: UserDto, data: BannerClientCreateDto) {
    if (data.position === enumData.BannerClientPosition.Top.code) {
      const topBanner = await this.repo.findOne({
        where: { position: enumData.BannerClientPosition.Top.code },
      })
      if (topBanner) {
        throw new ConflictException('Banner top chỉ được tạo một duy nhất!')
      }
    }
    const newEntity = this.repo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.repo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: BannerClientUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    if (data.position === enumData.BannerClientPosition.Top.code && data.position !== entity.position) {
      const objCheckBannerTop = await this.repo.findOne({
        where: { position: enumData.BannerClientPosition.Top.code },
      })
      if (objCheckBannerTop) throw new ConflictException('Banner top chỉ được tạo một duy nhất!')
    }

    entity.name = data.name
    entity.position = data.position
    entity.atr = data.atr
    entity.type = data.type
    entity.url = data.url
    entity.description = data.description
    entity.companyId = user.companyId
    entity.domain = data.domain
    entity.updatedBy = user.id
    await this.repo.save(entity)

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: any) {
    const whereCon: any = {}

    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    let res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
    })

    let lstSetting: any = await this.settingStringRepo.find({
      where: { code: In([enumData.SettingString.KES_URL.code, enumData.SettingString.KTG_URL.code]) },
    })
    if (lstSetting.length == 0) {
      lstSetting = coreHelper.convertObjToArray(enumData.SettingString)
    }
    const dict: any = {}
    lstSetting.forEach((c) => (dict[c.code] = c))

    for (let item of res[0]) {
      item.urlName = dict[item.domain]?.valueString
    }
    return res
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    if (entity.position === enumData.BannerClientPosition.Top.code) throw new BadRequestException('Banner top không được phép ngưng hoạt động!')

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
