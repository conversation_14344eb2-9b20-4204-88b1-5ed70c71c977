import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, JoinColumn, ManyToOne } from 'typeorm'
import { CompanyEntity } from './company.entity'
import { MaterialEntity } from './material.entity'
import { PrItemEntity } from './prItem.entity'
import {
  BlockEntity,
  BusinessPlanEntity,
  ContractAppendixItemEntity,
  DepartmentEntity,
  EmployeeEntity,
  OfferServiceEntity,
  PrEntity,
  RequestQuoteEntity,
  ReservationNormEntity,
  RfqEntity,
  RoundUpContEntity,
  SiteAssessmentEntity,
} from '.'
import { PositionEntity } from './position.entity'
import { ComplaintEntity } from './complaint.entity'
import { PlantPurchaseOrgEntity } from './plantPurchasingOrg.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { SupplierPlantEntity } from './supplierPlant.entity'
import { ReservationEntity } from './reservation.entity'
import { MaterialPriceEntity } from './materialPrice.entity'
import { MaterialValtypeEntity } from './materialValtype.entity'
import { MaterialStorageLocationEntity } from './materialStorageLocation.entity'
import { RequestQuoteDetailEntity } from './requestQuoteDetail.entity'
import { ContractItemEntity } from './contractItem.entity'
import { BusinessTemplatePlanEntity } from './businessTemplatePlan.entity'
import { BusinessTemplateGroupPlanEntity } from './businessTemplateGroupPlan.entity'
// Nhà máy
@Entity('plant')
export class PlantEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyCode: string

  @ManyToOne(() => CompanyEntity, (p) => p.plants)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  companys: Promise<CompanyEntity>

  /** Lấy theo mã PurchasingOrganization, 1 plant - 1 purchasingOrganization & 1 purchasingOrganization - n plant*/
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  purchasingOrganization: string

  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  address: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  district: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  postCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  country: string

  /**Nguồn đăng kí nhà cung cấp(KES, KTG) */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  supplierSource: string

  @OneToMany(() => MaterialEntity, (p) => p.plant)
  materials: Promise<MaterialEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.plant)
  prItems: Promise<PrItemEntity[]>

  @OneToMany(() => OfferServiceEntity, (p) => p.plant)
  offerService: Promise<OfferServiceEntity[]>

  @OneToMany(() => EmployeeEntity, (p) => p.plant)
  employee: Promise<EmployeeEntity[]>

  @OneToMany(() => SiteAssessmentEntity, (p) => p.plant)
  siteAssessments: Promise<SiteAssessmentEntity[]>

  @OneToMany(() => PrEntity, (p) => p.plant)
  pr: Promise<PrEntity[]>

  @OneToMany(() => BlockEntity, (p) => p.plant)
  blocks: Promise<BlockEntity[]>

  @OneToMany(() => DepartmentEntity, (p) => p.plant)
  departments: Promise<DepartmentEntity[]>

  @OneToMany(() => PositionEntity, (p) => p.plant)
  positions: Promise<PositionEntity[]>

  @OneToMany(() => ComplaintEntity, (p) => p.plant)
  complaints: Promise<ComplaintEntity[]>

  @OneToMany(() => BusinessTemplatePlanEntity, (p) => p.plant)
  businessTemplatePlan: Promise<BusinessTemplatePlanEntity[]>

  @OneToMany(() => BusinessTemplateGroupPlanEntity, (p) => p.plant)
  businessTemplateGroupPlan: Promise<BusinessTemplateGroupPlanEntity[]>

  @OneToMany(() => PlantPurchaseOrgEntity, (p) => p.plant)
  plantPurchaseOrgs: Promise<PlantPurchaseOrgEntity[]>

  @OneToMany(() => RoundUpContEntity, (p) => p.plant)
  roundUpCont: Promise<RoundUpContEntity[]>

  @OneToMany(() => BusinessPlanEntity, (p) => p.plant)
  businessPlan: Promise<BusinessPlanEntity[]>

  @OneToMany(() => RecommendedPurchaseEntity, (p) => p.plant)
  recommendedPurchase: Promise<RecommendedPurchaseEntity[]>

  @OneToMany(() => SupplierPlantEntity, (p) => p.plant)
  supplierPlants: Promise<SupplierPlantEntity[]>

  @OneToMany(() => ReservationEntity, (p) => p.plant)
  reservations: Promise<ReservationEntity[]>

  @OneToMany(() => ReservationNormEntity, (p) => p.plant)
  norms: Promise<ReservationNormEntity[]>

  /**  Có đẩy dử liệu về sap ? */
  @Column({
    nullable: false,
    default: false,
  })
  isSap: boolean

  /**  Có tạo PO tự động ? */
  @Column({
    nullable: false,
    default: false,
  })
  isAutoPo: boolean

  @OneToMany(() => MaterialPriceEntity, (p) => p.plant)
  materialPrices: Promise<MaterialPriceEntity[]>

  @OneToMany(() => MaterialValtypeEntity, (p) => p.plant)
  materialValtypes: Promise<MaterialValtypeEntity[]>

  @OneToMany(() => MaterialStorageLocationEntity, (p) => p.plant)
  materialStorageLocations: Promise<MaterialStorageLocationEntity[]>

  @OneToMany(() => RequestQuoteDetailEntity, (p) => p.plant)
  requestQuoteDetails: Promise<RequestQuoteDetailEntity[]>

  @OneToMany(() => ContractItemEntity, (p) => p.plant)
  contractItems: Promise<ContractItemEntity[]>

  @OneToMany(() => RequestQuoteEntity, (p) => p.plant)
  requestQuotes: Promise<RequestQuoteEntity[]>

  @OneToMany(() => RfqEntity, (p) => p.plant)
  rfqs: Promise<RfqEntity[]>

  @OneToMany(() => ContractAppendixItemEntity, (p) => p.plant)
  contractAppendixItems: Promise<ContractAppendixItemEntity[]>
}
