import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateCol1752135917096 implements MigrationInterface {
  name = 'UpdateCol1752135917096'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "request_quote_supplier" ADD "status" nvarchar(20)`)
    await queryRunner.query(`ALTER TABLE "request_quote_supplier" ADD "timeConfirmParticipationQuotation" datetime`)
    await queryRunner.query(`ALTER TABLE "request_quote_supplier" ADD "timeRejectParticipationQuotation" datetime`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "request_quote_supplier" DROP COLUMN "timeRejectParticipationQuotation"`)
    await queryRunner.query(`ALTER TABLE "request_quote_supplier" DROP COLUMN "timeConfirmParticipationQuotation"`)
    await queryRunner.query(`ALTER TABLE "request_quote_supplier" DROP COLUMN "status"`)
  }
}
