import { Injectable } from '@nestjs/common'
import { v4 as uuidv4 } from 'uuid'
import { CREATE_SUCCESS, enumData, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { In, IsNull, Like, Not } from 'typeorm'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { BusinessTypeEntity } from '../../entities'
import { coreHelper } from '../../helpers'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'
import { BusinessTypeRepository } from '../../repositories/businessType.repository'
import { BusinessTypeCreateDto, BusinessTypeUpdateDto } from './dto'
import { BusinessTypeImportExcelDto } from './dto/businessTypeExcel.dto'
import { SupplierRepository } from '../../repositories'

@Injectable()
export class BusinessTypeService {
  constructor(
    private readonly repo: BusinessTypeRepository,
    private organizationalPositionService: OrganizationalPositionService,
    private readonly supplierRepo: SupplierRepository,
  ) {}

  public async find(user: UserDto) {
    const whereCon: any = { isDeleted: false }
    return await this.repo.find({ where: whereCon, order: { code: 'ASC' } })
  }

  public async createData(user: UserDto, data: BusinessTypeCreateDto) {
    const objCheckCode = await this.repo.findOne({
      where: { code: data.code },
      select: { id: true },
    })
    if (objCheckCode) throw new Error(ERROR_CODE_TAKEN)

    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BusinessTypeEntity)
      const newBusinessType = new BusinessTypeEntity()
      newBusinessType.id = uuidv4()
      newBusinessType.companyId = user.companyId
      newBusinessType.createdBy = user.id
      newBusinessType.code = data.code
      newBusinessType.name = data.name
      newBusinessType.description = data.description
      ;(newBusinessType.createdAt = new Date()), await repo.insert(newBusinessType)

      return { message: CREATE_SUCCESS }
    })
  }

  public async updateData(user: UserDto, data: BusinessTypeUpdateDto) {
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BusinessTypeEntity)

      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      entity.name = data.name
      entity.description = data.description
      entity.updatedBy = user.id
      entity.companyId = user.companyId
      entity.updatedAt = new Date()
      await repo.save(entity)

      return { message: UPDATE_SUCCESS }
    })
  }

  /** Hàm phân trang */
  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.BusinessType.code)
    if (dataRs.type === enumData.RoleData.All.code) {
      /* do nothing */
    }
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where?.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where?.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where?.code) whereCon.code = Like(`%${data.where.code}%`)

    const res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })

    const suppliers = await this.supplierRepo.find({ where: { isDeleted: false }, order: { code: 'ASC' } })

    const listBusinessId = new Set(suppliers.map((item: any) => item.businessTypeId))

    for (let item of res[0]) {
      if (listBusinessId.has(item.id)) {
        item.isActive = true
      }
    }
    return res
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id, updatedAt: new Date() })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async findDetail(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
    })
    return res
  }

  public async importData(user: UserDto, data: BusinessTypeImportExcelDto[]) {
    //check xem data có trùng nhau hoặc không có không
    if (data.length < 1) throw new Error('Vui lòng điền ít nhất 1 dòng dữ liệu!')
    {
      const dublicateArayCode = coreHelper.findDuplicates(data, 'code')
      if (dublicateArayCode.length > 0) throw new Error(`Danh sách mã loại hình doanh nghiệp trùng nhau ${dublicateArayCode.toString()}`)
    }

    //lọc lấy danh sách code
    const codes = data.map(function (obj) {
      return obj.code
    })

    // tìm ra những mã bị trùng dưới data base
    const dupCount = await this.repo.find({ where: { code: In(codes) } })
    if (dupCount.length > 0) {
      const dupCode = data.map(function (obj) {
        return obj.code
      })
      throw new Error(`Danh sách mã loại hình doanh nghiệp  trùng nhau [${dupCode.toString()}]`)
    }
    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const lstTask = []
      const repo = trans.getRepository(BusinessTypeEntity)
      const dictBusinessType: any = {}
      const set = new Set()
      for (const item of data) {
        item.code = item.code.toString()
        if (dictBusinessType[item.code]) continue
        if (set.has(item.code)) continue
        set.add(item.code)

        const businessType = new BusinessTypeEntity()
        businessType.createdBy = user.id
        businessType.createdAt = new Date()
        businessType.code = item.code
        businessType.name = item.name
        businessType.description = item.description
        lstTask.push(businessType)
      }
      const lstChild = coreHelper.splitArrayByParameters(lstTask, 2000)
      for (const chunk of lstChild) {
        await trans.insert(BusinessTypeEntity, chunk)
      }
    })
    return { message: CREATE_SUCCESS }
  }

  /** Hàm cập nhật trạng thái isDeleted */
  public async updateActiveStatus(user: UserDto, data: { id: string }) {
    const found = await this.repo.findOne({ where: { id: data.id } })
    if (!found) throw new Error(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BusinessTypeEntity)
      found.isDeleted = !found.isDeleted
      found.updatedAt = new Date()
      found.updatedBy = user.id
      await repo.save(found)
      return { message: UPDATE_SUCCESS }
    })
  }
}
