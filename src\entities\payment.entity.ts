import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, Index, ManyToOne, JoinColumn } from 'typeorm'
import { PaymentBillEntity } from './paymentBill.entity'
import { PaymentPoEntity } from './paymentPo.entity'
import { PaymentContractEntity } from './paymentContract.entity'
import { SupplierEntity } from './supplier.entity'
import { CurrencyEntity } from './currency.entity'

/** Thông tin hồ sơ thanh toán*/
@Entity('payment')
export class PaymentEntity extends BaseEntity {
  /**Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Mã HSTT */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Tên hồ sơ thanh toán */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  // File đính kèm
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  fileAttach: string

  // File đề nghị thanh toán
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  filePaymentRequest: string

  // File biên bảng nghiệm thu
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  fileAcceptanceReport: string

  /** Ghi chú */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  note: string

  /** nhà cung cấp */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.payments)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @OneToMany(() => PaymentBillEntity, (p) => p.payment)
  paymentBills: Promise<PaymentBillEntity[]>

  @OneToMany(() => PaymentPoEntity, (p) => p.payment)
  paymentPos: Promise<PaymentPoEntity[]>

  @OneToMany(() => PaymentContractEntity, (p) => p.payment)
  paymentContracts: Promise<PaymentContractEntity[]>

  /**Đơn vị tiền tệ */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currencyId: string
  @ManyToOne(() => CurrencyEntity, (currency) => currency.payments)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  /** Số tiền tạm ứng */
  @Column({
    type: 'bigint',
    default: 0,
    nullable: true,
  })
  moneyAdvance: number

  /** loại thanh toán */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  paymentType: string

  /**Thanh toán được tạo từ nhà cung cấp hay là từ trang admin: true ? Nhà cung cấp : trang Admin */
  @Column({
    nullable: true,
  })
  isSupplierCreate: boolean
}
