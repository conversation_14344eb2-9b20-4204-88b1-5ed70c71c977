import { BusinessTemplateGroupPlanEntity } from '../entities/businessTemplateGroupPlan.entity'
import { BusinessTemplatePlanEntity } from '../entities/businessTemplatePlan.entity'
import { BusinessTemplatePlanSetupEntity } from '../entities/businessTemplatePlanSetup.entity'
import { BusinessTemplatePlanTypeEntity } from '../entities/businessTemplatePlanType.entity'
import { CustomRepository } from '../typeorm'
import { BaseRepository } from './base.repository'

@CustomRepository(BusinessTemplatePlanEntity)
export class BusinessTemplatePlanRepository extends BaseRepository<BusinessTemplatePlanEntity> {}

@CustomRepository(BusinessTemplatePlanSetupEntity)
export class BusinessTemplatePlanSetupRepository extends BaseRepository<BusinessTemplatePlanSetupEntity> {}

@CustomRepository(BusinessTemplateGroupPlanEntity)
export class BusinessTemplateGroupPlanRepository extends BaseRepository<BusinessTemplateGroupPlanEntity> {}

@CustomRepository(BusinessTemplatePlanTypeEntity)
export class BusinessTemplatePlanTypeRepository extends BaseRepository<BusinessTemplatePlanTypeEntity> {
  genCode(): Promise<string> {
    // Implement logic to generate a unique code for the business template plan type
    return this.createQueryBuilder('type')
      .select('MAX(type.code)', 'maxCode')
      .getRawOne()
      .then((result) => {
        const maxCode = result.maxCode || '0'
        const newCodeNumber = parseInt(maxCode, 10) + 1
        return 'P' + newCodeNumber.toString()
      })
  }
}
