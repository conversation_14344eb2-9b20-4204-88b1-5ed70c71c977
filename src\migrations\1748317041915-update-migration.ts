import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateMigration1748317041915 implements MigrationInterface {
    name = 'UpdateMigration1748317041915'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "flow_approve_base" ADD "purOrgId" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP COLUMN "purOrgId"`);
    }

}
