import { Injectable, NotFoundException, BadRequestException, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'
import {
  ERROR_YOU_DO_NOT_HAVE_PERMISSION,
  ERROR_NOT_FOUND_DATA,
  ERROR_SUPPLIER_USED_TEMPLATE,
  UPDATE_SUCCESS,
  CREATE_SUCCESS,
  DELETE_SUCCESS,
  IMPORT_SUCCESS,
} from '../../constants'
import { EmailService } from '../email/email.service'
import {
  ServiceRepository,
  EmployeeRepository,
  BidTypeRepository,
  SettingStringRepository,
  BidRepository,
  BidEmployeeAccessRepository,
  ServiceTechRepository,
  ServiceTradeRepository,
  ServicePriceRepository,
  SupplierServiceRepository,
  ServiceCustomPriceRepository,
  BidCustomPriceRepository,
  BidTechRepository,
  BidTradeRepository,
  BidPriceRepository,
  BidPriceColRepository,
  BidSupplierRepository,
  SupplierRepository,
  BidPrItemRepository,
} from '../../repositories'
import { enumData } from '../../constants/enumData'
import { UserDto } from '../../dto'
import { In, IsNull } from 'typeorm'

import { BidEntity, BidHistoryEntity, BidTechEntity, BidTechListDetailEntity, BidTradeEntity, BidTradeListDetailEntity } from '../../entities'
import { BidTechCreateDto, BidTechUpdateDto, BidTradeCreateDto, BidTradeUpdateDto } from './dto2'
import { FlowApproveService } from '../flowApprove/flowApprove.service'

@Injectable()
export class BidTechTradeService {
  constructor(
    private readonly repo: BidRepository,
    private readonly emailService: EmailService,
    private readonly serviceTechRepo: ServiceTechRepository,
    private readonly bidTechRepo: BidTechRepository,
    private readonly serviceTradeRepo: ServiceTradeRepository,
    private readonly bidTradeRepo: BidTradeRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly bidPrItemRepository: BidPrItemRepository,
    private flowService: FlowApproveService,
  ) {}

  //#region bidTech

  /** Check quyền tạo thiết lập yêu cầu kỹ thuật cho gói thầu */
  async checkPermissionTechCreate(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatusCanEdit = [
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({
      // where: [{ id: bidId, parentId: IsNull(), isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
      where: [{ id: bidId }],
    })
    // const bidParent = await bid.bid
    if (bid) {
      if (lstStatusCanEdit.includes(bid.status)) {
        // result = await this.bidEmployeeAccessRepo.isTech(user, bid.id)
        // if (!result) {
        result = true
        message = 'Bạn không có quyền thiết lập yêu cầu kỹ thuật cho gói thầu.'
        // }
      } else {
        result = false
        message = 'Chỉ được phép thiết lập yêu cầu kỹ thuật khi chưa mở thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  async creatingTech(user: UserDto, bidId: string) {
    await this.repo.update(bidId, {
      statusTech: enumData.BidTechStatus.DangTao.code,
      status: enumData.BidStatus.DangCauHinhGoiThau.code,
      updatedBy: user.id,
    })
  }

  /** Lấy thông tin thiết lập yêu cầu kỹ thuật của lĩnh vực mời thầu */
  async loadTech(user: UserDto, bidItemId: string, templateId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // const bid = await this.repo.findOne({ where: { id: bidItemId, isDeleted: false } })
    const bid = await this.repo.findOne({ where: { id: bidItemId, isDeleted: false } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionTechCreate(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidItemId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    let lstServiceTech = await this.serviceTechRepo.find({
      where: { serviceId: templateId, parentId: IsNull(), isDeleted: false },
      relations: { childs: { serviceTechListDetails: true }, serviceTechListDetails: true },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    // Tạo danh sách yêu cầu kỹ thuật cho gói thầu theo cấu hình kỹ thuật
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)
      const bidTechListDetailRepo = manager.getRepository(BidTechListDetailEntity)
      for (const a of lstServiceTech) {
        const item = new BidTechEntity()
        item.companyId = user.companyId
        item.createdBy = user.id
        // item.bidId = bidItemId
        item.sort = a.sort
        item.bidId = bid.id
        // item.bidItemId = bid.id
        item.name = a.name
        item.isRequired = a.isRequired
        item.type = a.type
        item.percent = a.percent
        item.percentRule = a.percentRule
        item.isCalUp = a.isCalUp
        item.percentDownRule = a.percentDownRule
        item.level = a.level
        item.description = a.description
        item.parentId = a.parentId
        item.scoreDLC = a.scoreDLC
        item.requiredMin = a.requiredMin
        item.isHighlight = a.isHighlight
        item.hightlightValue = a.hightlightValue
        const bidTechEntity = await bidTechRepo.save(item)

        const lstChild = (await a.childs).filter((c) => !c.isDeleted)

        if (lstChild && lstChild.length > 0) {
          for (const b of lstChild) {
            const itemChild = new BidTechEntity()
            itemChild.companyId = user.companyId
            itemChild.createdBy = user.id
            // itemChild.bidId = bidItemId
            itemChild.sort = b.sort
            item.bidId = bid.id
            // item.bidItemId = bid.id
            itemChild.name = b.name
            itemChild.isRequired = b.isRequired
            itemChild.type = b.type
            itemChild.percent = b.percent
            itemChild.percentRule = b.percentRule
            itemChild.isCalUp = b.isCalUp
            itemChild.percentDownRule = b.percentDownRule
            itemChild.level = b.level
            itemChild.description = b.description
            itemChild.parentId = bidTechEntity.id
            itemChild.scoreDLC = b.scoreDLC
            itemChild.requiredMin = b.requiredMin
            itemChild.isHighlight = b.isHighlight
            itemChild.hightlightValue = b.hightlightValue
            const bidTechChildEntity = await bidTechRepo.save(itemChild)

            const lstDataTypeList = (await b.serviceTechListDetails).filter((c) => !c.isDeleted)
            if (lstDataTypeList && lstDataTypeList.length > 0) {
              for (const c of lstDataTypeList) {
                const itemListDetail = new BidTechListDetailEntity()
                itemListDetail.companyId = user.companyId
                itemListDetail.createdBy = user.id
                itemListDetail.bidTechId = bidTechChildEntity.id
                itemListDetail.name = c.name
                itemListDetail.value = c.value
                await bidTechListDetailRepo.save(itemListDetail)
              }
            }
          }
        }

        const lstDataTypeList = (await a.serviceTechListDetails).filter((c) => !c.isDeleted)
        if (lstDataTypeList && lstDataTypeList.length > 0) {
          for (const c of lstDataTypeList) {
            const itemListDetail = new BidTechListDetailEntity()
            itemListDetail.companyId = user.companyId
            itemListDetail.createdBy = user.id
            itemListDetail.bidTechId = bidTechEntity.id
            itemListDetail.name = c.name
            itemListDetail.value = c.value
            await bidTechListDetailRepo.save(itemListDetail)
          }
        }
      }
    })

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái kỹ thuật cho gói thầu thất bại.')
    })
  }

  /** Lưu template kỹ thuật cho gói thầu => Chuyển TechLead duyệt */
  async createTech(user: UserDto, bidId: string, data: { noteTech?: string; isSurvey?: boolean; skipApprove?: boolean }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionTechCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      statusTech: data.isSurvey || data.skipApprove ? enumData.BidTechStatus.DaDuyet.code : enumData.BidTechStatus.DaTao.code,
      noteTech: data.noteTech,
      updatedBy: user.id,
    })

    if (data.isSurvey || data.skipApprove) {
      const bid = await this.repo.findOne({ where: { id: bidId } })
      if (bid) {
        // bid.statusTech = enumData.BidTechStatus.DaDuyet.code
        // bid.noteTechLeader = data.noteTechLeader
        if (
          (bid.statusTrade === enumData.BidTradeStatus.DaTao.code || bid.statusTrade === enumData.BidTradeStatus.DaDuyet.code) &&
          (bid.statusPrice === enumData.BidPriceStatus.DaTao.code || bid.statusPrice === enumData.BidPriceStatus.DaDuyet.code)
        ) {
          bid.status = enumData.BidStatus.DangChonNCC.code
          // chưa chọn => đang chọn, để chọn Doanh nghiệp
          if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
            bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
          }
          // đã duyệt => đã chọn, để duyệt lại
          if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
            bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
          }
        }
        bid.updatedBy = user.id
        await this.repo.save(bid)
      }
    }

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TaoKyThuat.code
    bidHistory.save()

    // gửi email
    await this.emailService.GuiTechLeadDuyetKyThuat(bidId)
    return { message: 'Thiết lập yêu cầu kỹ thuật cho gói thầu thành công' }
  }

  /** Lấy thiết lập yêu cầu kỹ thuật của gói thầu */
  async getTech(user: UserDto, bidId: string, isMobile?: boolean) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid1(user, bidId, true)
    // lọc lại data

    res.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
      lsType: [enumData.FlowCode.BIDTECH.code],
    })

    res.isShowAcceptTech = res.objPermissionApprove[enumData.FlowCode.BIDTECH.code]
    const lstDataItem = []

    res.listTech = await this.bidTechRepo.getTech(user, res.id)

    res.sumPercent = 0
    for (const data1 of res.listTech) {
      if (data1.percent > 0) res.sumPercent += data1.percent

      const lstChild = data1.__childs__.filter((c) => c.type === enumData.DataType.List.code || c.type === enumData.DataType.Number.code)
      if (lstChild.length > 0) {
        data1.sumPercent = 0
        for (const child of lstChild) {
          child.bidItemId = data1.bidItemId
          child.bidId = data1.bidId
          if (child.percent > 0) data1.sumPercent += child.percent
        }
      }
    }

    if (isMobile) {
      let materialGroupName = `${res.__service__.code} ${res.__service__.name}`
      const list = res.listItem?.[0].listTech ?? []
      const tableInfo = []

      const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
        targetId: res?.id,
        entityName: BidEntity.name,
        type: enumData.FlowCode.BIDTECH.code,
      })

      for (let i = 0; i < list.length; i++) {
        const item = list[i]
        let order = String(`${i + 1}`)
        tableInfo.push({
          order,
          name: item?.name,
          percent: item?.percent,
          type: item?.type,
          isRequired: item?.isRequired ? 'Có' : 'Không',
          listDetail: item?.__bidTechListDetails__.map((item, index) => {
            order = `${order}.${index}`
            return {
              name: item?.name,
              value: item?.value,
            }
          }),
        })
      }

      return {
        id: res.id,
        materialGroupName,
        tableInfo,
        status: res.status ?? '',
        statusName: 'Chờ duyệt',
        approvalProgress,
        canApprove,
      }
    }

    /* Tìm ra danh sách cấp duyệt và comment và người duyệt */
    res.haveProgress = false
    res.approvalProgress = await this.flowService.getListApproveDetail(user, {
      targetId: res.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.BIDTECH.code,
    })
    res.showComment = false
    /* nếu như nhân viên hiện tại chưa duyệt và nằm trong luồng duyệt thì toggle bật showComment = true */
    for (const item of res.approvalProgress) {
      if (item.approveType == user.orgPositionId && !item.approved) {
        res.showComment = true
        break
      }
    }
    if (res.approvalProgress.length > 0) res.haveProgress = true
    res.isShowSendTech = res.statusTech === enumData.BidTechStatus.DaTao.code && res.isTech
    res.isShowAcceptTech = res.statusTech === enumData.BidTechStatus.ChoDuyet.code
    return res
  }

  /** Check quyền duyệt thiết lập yêu cầu kỹ thuật của gói thầu */
  async checkPermissionTechAccept(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    // const bid = await this.repo.findOne({ where: { id: bidId, parentId: IsNull(), isDeleted: false } })
    const bid = await this.repo.findOne({ where: { id: bidId, parentId: IsNull(), isDeleted: false } })

    if (bid) {
      if (bid.status === enumData.BidStatus.DangCauHinhGoiThau.code && bid.statusTech === enumData.BidTechStatus.ChoDuyet.code) {
        result = true
        // if (!result) {
        message = 'Bạn không có quyền xét duyệt thiết lập kỹ thuật cho gói thầu.'
        // }
      } else {
        result = false
        message = 'Gói thầu đã được xét duyệt thiết lập kỹ thuật.'
      }
    }

    return { hasPermission: result, message }
  }

  async sendCheckBidTech(user: UserDto, bidId: string) {
    await this.repo.update(bidId, { statusTech: enumData.BidTechStatus.ChoDuyet.code, updatedBy: user.id })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.YeuCauDuyetThongTinKiThuat.code
    bidHistory.save()

    // tạo quyền phê duyệt thiết lập yêu cầu kĩ thuật
    let flowType: string
    flowType = enumData.FlowCode.BIDTECH.code
    await this.flowService.setRoleRule(user, {
      targetId: bidId,
      entityName: BidEntity.name,
      flowType: flowType,
      companyId: user.orgCompanyId,
      // departmentId: user?.departmentId,
    })

    return { message: 'Đã gửi yêu cầu phê duyệt thiết lập yêu cầu kĩ thuật.' }
  }

  /** Duyệt thiết lập yêu cầu kỹ thuật của gói thầu */
  async techAccept(user: UserDto, bidId: string, data?: { comment?: string }) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: bidId,
      entityName: BidEntity.name,
      type: enumData.FlowCode.BIDTECH.code,
      comment: data.comment,
    })

    if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
      return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    } else {
      // if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
      const objPermission = await this.checkPermissionTechAccept(user, bidId)
      if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

      const bid = await this.repo.findOne({ where: { id: bidId } })
      if (bid) {
        bid.statusTech = enumData.BidTechStatus.DaDuyet.code
        // bid.noteTechLeader = data.noteTechLeader
        if (
          (bid.statusTrade === enumData.BidTradeStatus.DaTao.code || bid.statusTrade === enumData.BidTradeStatus.DaDuyet.code) &&
          (bid.statusPrice === enumData.BidPriceStatus.DaTao.code || bid.statusPrice === enumData.BidPriceStatus.DaDuyet.code)
        ) {
          bid.status = enumData.BidStatus.DangChonNCC.code
          // chưa chọn => đang chọn, để chọn Doanh nghiệp
          if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
            bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
          }
          // đã duyệt => đã chọn, để duyệt lại
          if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
            bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
          }
        }
        bid.updatedBy = user.id
        await this.repo.save(bid)
      }

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidId
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.DuyetTaoKyThuat.code
      bidHistory.save()

      // gửi email
      await this.emailService.ThongBaoDaDuyetKyThuat(bidId)

      return { message: 'Duyệt thiết lập yêu cầu kỹ thuật của gói thầu thành công.' }
    }
  }

  /** Từ chối thiết lập yêu cầu kỹ thuật của gói thầu */
  async techReject(user: UserDto, bidId: string, data?: { comment?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionTechAccept(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.flowService.rejectRule(user, {
      targetId: bidId,
      entityName: BidEntity.name,
      type: enumData.FlowCode.BIDTECH.code,
      comment: data.comment,
    })

    await this.repo.update(bidId, {
      statusTech: enumData.BidTechStatus.TuChoi.code,
      // noteTechLeader: data.noteTechLeader,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TuChoiTaoKyThuat.code
    bidHistory.save()

    // gửi email
    this.emailService.GuiTechTuChoiKyThuat(bidId)

    return { message: 'Từ chối thiết lập yêu cầu kỹ thuật của gói thầu thành công.' }
  }

  /** Lấy data cbb tiêu chí cấp 1 */
  async techGetData(user: UserDto, bidId: string) {
    if (bidId !== null) return
    const result = await this.bidTechRepo.find({ where: { bidItemId: bidId, level: 1, isDeleted: false } })
    return result
  }

  async techCreateData(user: UserDto, data: BidTechCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.repo.findOne({ where: { id: data.bidId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    // const objPermission = await this.checkPermissionTechCreate(user, data.bidId)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Tạo thêm ngoài lấy từ template
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)

      const item = new BidTechEntity()
      item.companyId = user.companyId
      item.createdBy = user.id
      item.bidId = bid.id
      // item.bidItemId = bid.id
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.parentId = data.parentId
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isHighlight = data.isHighlight
      item.hightlightValue = data.hightlightValue
      await bidTechRepo.save(item)
    })

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái kỹ thuật cho gói thầu thất bại.')
    })
  }

  async techUpdateData(user: UserDto, data: BidTechUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.bidTechRepo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionTechCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Sửa
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)

      const item = await bidTechRepo.findOne({ where: { id: data.id } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isHighlight = data.isHighlight
      item.hightlightValue = data.hightlightValue
      item.updatedBy = user.id
      await bidTechRepo.save(item)
    })

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.bidId || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái kỹ thuật cho gói thầu thất bại.')
    })
  }

  /** Xóa */
  async techDeleteData(user: UserDto, bidTechId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bidTech = await this.bidTechRepo.findOne({ where: { id: bidTechId } })
    if (!bidTech) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTech.bid
    const objPermission = await this.checkPermissionTechCreate(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bid.id)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xóa
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)
      const bidTechListDetailRepo = manager.getRepository(BidTechListDetailEntity)

      const bidTech = await bidTechRepo.findOne({ where: { id: bidTechId } })
      if (!bidTech) throw new Error(ERROR_NOT_FOUND_DATA)

      const lstChild = await bidTech.childs
      for (const bidTechChild of lstChild) {
        await bidTechListDetailRepo.delete({ bidTechId: bidTechChild.id })
      }
      await bidTechListDetailRepo.delete({ bidTechId })
      await bidTechRepo.delete({ parentId: bidTechId })
      await bidTechRepo.delete(bidTechId)

      return { message: DELETE_SUCCESS }
    })

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái kỹ thuật cho gói thầu thất bại.')
    })
  }

  /** Xoá tất cả */
  async techDeleteAllData(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bid = await this.repo.findOne({ where: { id: bidId, isDeleted: false } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const objPermission = await this.checkPermissionTechCreate(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bid.id)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xoá tất cả
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)
      const bidTechListDetailRepo = manager.getRepository(BidTechListDetailEntity)

      const lstBidTech = await bidTechRepo.find({ where: { bidItemId: bidId } })
      for (const bidTech of lstBidTech) {
        const lstBidTechChild = await bidTech.childs
        for (const bidTechChild of lstBidTechChild) {
          // xoá lst detail
          await bidTechListDetailRepo.delete({ bidTechId: bidTechChild.id })
        }
        // xoá lst
        await bidTechListDetailRepo.delete({ bidTechId: bidTech.id })
        // xoá con
        await bidTechRepo.delete({ parentId: bidTech.id })
        // xoá
        await bidTechRepo.delete(bidTech.id)
      }
    })

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái kỹ thuật cho gói thầu thất bại.')
    })
  }

  /** Import excel kỹ thuật */
  public async tech_import(user: UserDto, bidId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.techDeleteAllData(user, bidId)
    // import excel kỹ thuật
    await this.repo.manager.transaction(async (manager) => {
      const bidTechRepo = manager.getRepository(BidTechEntity)
      const bidTechListDetailRepo = manager.getRepository(BidTechListDetailEntity)

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objBidTechNew = new BidTechEntity()
        objBidTechNew.companyId = user.companyId
        objBidTechNew.createdBy = user.id
        objBidTechNew.bidId = bidId
        // objBidTechNew.bidItemId = bidId
        objBidTechNew.level = 1
        objBidTechNew.sort = item.sort || 0
        objBidTechNew.name = item.name
        objBidTechNew.percent = item.percent
        objBidTechNew.percentRule = item.percentRule
        objBidTechNew.requiredMin = item.requiredMin
        objBidTechNew.type = item.type
        objBidTechNew.isRequired = item.isRequired
        objBidTechNew.isHighlight = item.isHighlight
        objBidTechNew.hightlightValue = item.hightlightValue
        objBidTechNew.isCalUp = item.isCalUp
        objBidTechNew.percentDownRule = item.percentDownRule

        const objBidTech = await bidTechRepo.save(objBidTechNew)
        item.id = objBidTech.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidTechListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidTechId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await bidTechListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objBidTechNew = new BidTechEntity()
        objBidTechNew.companyId = user.companyId
        objBidTechNew.createdBy = user.id
        objBidTechNew.bidId = bidId
        // objBidTechNew.bidItemId = bidId
        objBidTechNew.level = 2
        objBidTechNew.sort = item.sort || 0
        objBidTechNew.name = item.name
        objBidTechNew.percent = item.percent
        objBidTechNew.percentRule = item.percentRule
        objBidTechNew.requiredMin = item.requiredMin
        objBidTechNew.type = item.type
        objBidTechNew.isRequired = item.isRequired
        objBidTechNew.isHighlight = item.isHighlight
        objBidTechNew.hightlightValue = item.hightlightValue
        objBidTechNew.isCalUp = item.isCalUp
        objBidTechNew.percentDownRule = item.percentDownRule
        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objBidTechNew.parentId = parent.id

        const objBidTech = await bidTechRepo.save(objBidTechNew)
        item.id = objBidTech.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidTechListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidTechId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await bidTechListDetailRepo.save(detailNew)
          }
        }
      }
    })

    return { message: IMPORT_SUCCESS }
  }

  public async bidTechListDetail_list(user: UserDto, bidTechId: string) {
    return await this.repo.manager.getRepository(BidTechListDetailEntity).find({
      where: { bidTechId },
      order: { value: 'DESC' },
    })
  }

  public async bidTechListDetail_create_data(user: UserDto, data: { bidTechId: string; name: string; value: number }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTech = await this.bidTechRepo.findOne({ where: { id: data.bidTechId } })
    if (!bidTech) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTech.bid
    const objPermission = await this.checkPermissionTechCreate(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bid.id)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.id || bid.id)

    const entity = new BidTechListDetailEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    entity.name = data.name
    entity.value = data.value
    entity.bidTechId = data.bidTechId
    await entity.save()

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async bidTechListDetail_update_data(user: UserDto, data: { id: string; bidTechId: string; name: string; value: number }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTech = await this.bidTechRepo.findOne({ where: { id: data.bidTechId } })
    if (!bidTech) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTech.bidItem
    const objPermission = await this.checkPermissionTechCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bid.bidId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.bidId || bid.id)

    const entity = await this.repo.manager.getRepository(BidTechListDetailEntity).findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async bidTechListDetail_delete_data(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTechListDetail = await this.repo.manager.getRepository(BidTechListDetailEntity).findOne({ where: { id } })
    if (!bidTechListDetail) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bidTech = await bidTechListDetail.bidTech
    const bid = await bidTech.bidItem
    const objPermission = await this.checkPermissionTechCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bid.bidId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTech => DangTao
    await this.creatingTech(user, bid.bidId || bid.id)

    await this.repo.manager.getRepository(BidTechListDetailEntity).delete(id)

    return { message: DELETE_SUCCESS }
  }

  //#endregion

  //#region  bidTrade

  /** Check quyền tạo thiết lập điều kiện thương mại cho gói thầu */
  async checkPermissionTradeCreate(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatusCanEdit = [
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({
      where: [{ id: bidId, isDeleted: false }],
    })
    if (bid) {
      if (lstStatusCanEdit.includes(bid.status)) {
        result = true
        // result = await this.bidEmployeeAccessRepo.isMPO(user, bid.id)
        if (!result) message = 'Bạn không có quyền thiết lập điều kiện thương mại cho gói thầu.'
      } else {
        result = false
        message = 'Chỉ được phép thiết lập điều kiện thương mại khi chưa mở thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Update statusTrade => DangTao */
  async creatingTrade(user: UserDto, bidId: string) {
    await this.repo.update(bidId, {
      statusTrade: enumData.BidTradeStatus.DangTao.code,
      status: enumData.BidStatus.DangCauHinhGoiThau.code,
      updatedBy: user.id,
    })
  }

  /** Lấy thông tin thiết lập điều kiện thương mại của lĩnh vực mời thầu */
  async loadTrade(user: UserDto, bidItemId: string, templateId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // const bid = await this.repo.findOne({ where: { id: bidItemId, isDeleted: false } })
    const bid = await this.repo.findOne({ where: { id: bidItemId, isDeleted: false } })

    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionTradeCreate(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidItemId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    let lstServiceTrade = await this.serviceTradeRepo.find({
      where: { serviceId: templateId, parentId: IsNull(), isDeleted: false },
      relations: { childs: { serviceTradeListDetails: true }, serviceTradeListDetails: true },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    // Tạo danh sách điều kiện thương mại cho gói thầu
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(BidTradeListDetailEntity)
      for (const a of lstServiceTrade) {
        const item = new BidTradeEntity()
        item.companyId = user.companyId
        item.createdBy = user.id
        item.bidId = bid.id
        // item.bidItemId = bid.id
        item.sort = a.sort
        item.name = a.name
        item.isRequired = a.isRequired
        item.type = a.type
        item.percent = a.percent
        item.percentRule = a.percentRule
        item.isCalUp = a.isCalUp
        item.percentDownRule = a.percentDownRule
        item.level = a.level
        item.description = a.description
        item.parentId = a.parentId
        item.scoreDLC = a.scoreDLC
        item.requiredMin = a.requiredMin
        const bidTradeEntity = await bidTradeRepo.save(item)

        const lstChild = (await a.childs).filter((c) => !c.isDeleted)
        if (lstChild && lstChild.length > 0) {
          for (const b of lstChild) {
            const itemChild = new BidTradeEntity()
            itemChild.companyId = user.companyId
            itemChild.createdBy = user.id
            // itemChild.bidId = bidItemId
            itemChild.bidItemId = bidItemId

            itemChild.sort = b.sort
            itemChild.name = b.name
            itemChild.isRequired = b.isRequired
            itemChild.type = b.type
            itemChild.percent = b.percent
            itemChild.percentRule = b.percentRule
            itemChild.isCalUp = b.isCalUp
            itemChild.percentDownRule = b.percentDownRule
            itemChild.level = b.level
            itemChild.description = b.description
            itemChild.parentId = bidTradeEntity.id
            itemChild.scoreDLC = b.scoreDLC
            itemChild.requiredMin = b.requiredMin
            const bidTradeChildEntity = await bidTradeRepo.save(itemChild)

            const lstDataTypeList = (await b.serviceTradeListDetails).filter((c) => !c.isDeleted)
            if (lstDataTypeList && lstDataTypeList.length > 0) {
              for (const c of lstDataTypeList) {
                const itemListDetail = new BidTradeListDetailEntity()
                itemListDetail.companyId = user.companyId
                itemListDetail.createdBy = user.id
                itemListDetail.bidTradeId = bidTradeChildEntity.id
                itemListDetail.name = c.name
                itemListDetail.value = c.value
                await bidTradeListDetailRepo.save(itemListDetail)
              }
            }
          }
        }

        const lstDataTypeList = (await a.serviceTradeListDetails).filter((c) => !c.isDeleted)
        for (const c of lstDataTypeList) {
          const itemListDetail = new BidTradeListDetailEntity()
          itemListDetail.companyId = user.companyId
          itemListDetail.createdBy = user.id
          itemListDetail.bidTradeId = bidTradeEntity.id
          itemListDetail.name = c.name
          itemListDetail.value = c.value
          await bidTradeListDetailRepo.save(itemListDetail)
        }
      }
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Tạo thiết lập điều kiện thương mại cho gói thầu */
  /**
   * gởi yêu cầu duyệt điều kiện thương mại
   */

  async sendTrade(user: UserDto, bidId: string, data: { noteTrade: string; isSurvey?: boolean }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionTradeCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    bid.statusTrade = enumData.BidTradeStatus.GuiDuyet.code
    bid.noteTrade = data.noteTrade

    bid.updatedBy = user.id
    await this.repo.save(bid)

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TaoThuongMai.code
    bidHistory.save()

    let flowType: string
    flowType = enumData.FlowCode.BIDTRADE.code
    await this.flowService.setRoleRule(user, {
      targetId: bidId,
      entityName: BidEntity.name,
      flowType: flowType,
      companyId: user.orgCompanyId,
      // departmentId: user?.departmentId,
    })

    return { message: 'Gửi duyệt thành công' }

    // return { message: 'Thiết lập điều kiện thương mại cho gói thầu thành công.' }
  }

  async createTrade(user: UserDto, bidId: string, data: { noteTrade: string; isSurvey?: boolean; skipApprove?: boolean }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionTradeCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    bid.statusTrade = data.isSurvey || data.skipApprove ? enumData.BidTradeStatus.DaDuyet.code : enumData.BidTradeStatus.DaTao.code
    bid.noteTrade = data.noteTrade
    if (bid) {
      bid.statusTrade = data.isSurvey || data.skipApprove ? enumData.BidTradeStatus.DaDuyet.code : enumData.BidTradeStatus.DaTao.code
      bid.noteTrade = data.noteTrade

      // đã duyệt => đã chọn, để duyệt lại
      if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
        bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
      }

      if (data.isSurvey || data.skipApprove)
        if (bid.statusTech === enumData.BidTechStatus.DaDuyet.code && bid.statusPrice === enumData.BidPriceStatus.DaDuyet.code) {
          bid.status = enumData.BidStatus.DangChonNCC.code
          // chưa chọn => đang chọn, để chọn Doanh nghiệp
          if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
            bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
          }
          // đã duyệt => đã chọn, để duyệt lại
          if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
            bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
          }
        }

      bid.updatedBy = user.id
      await this.repo.save(bid)
      // }

      // gửi email
      // await this.emailService.ThongBaoDaDuyetKyThuat(bidId)
    }

    bid.updatedBy = user.id
    await this.repo.save(bid)

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TaoThuongMai.code
    bidHistory.save()

    return { message: 'Tạo thành công' }
  }

  /** Duyệt thiết lập điều kiện thương mại của gói thầu */
  async tradeAccept(user: UserDto, bidId: string, data: { noteTrade?: string; noteMPOLeader?: string }) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: bidId,
      entityName: BidEntity.name,
      type: enumData.FlowCode.BIDTRADE.code,
    })

    if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
      return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    } else {
      // const objPermission = await this.checkPermissionTechAccept(user, bidId)
      // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
      const bid = await this.repo.findOne({ where: { id: bidId } })
      if (bid) {
        bid.statusTrade = enumData.BidTradeStatus.DaDuyet.code
        bid.statusPrice = enumData.BidPriceStatus.DaDuyet.code
        bid.noteTrade = data.noteTrade
        bid.noteMPOLeader = data.noteMPOLeader
        if (
          bid.statusTech === enumData.BidTechStatus.DaDuyet.code &&
          (bid.statusPrice === enumData.BidPriceStatus.DaTao.code || bid.statusPrice === enumData.BidPriceStatus.DaDuyet.code)
        ) {
          bid.status = enumData.BidStatus.DangChonNCC.code
          // chưa chọn => đang chọn, để chọn Doanh nghiệp
          if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
            bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
          }
          // đã duyệt => đã chọn, để duyệt lại
          if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
            bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
          }
        }

        bid.updatedBy = user.id
        await this.repo.save(bid)

        // Bid History
        const bidHistory = new BidHistoryEntity()
        bidHistory.companyId = user.companyId
        bidHistory.createdBy = user.id
        bidHistory.bidId = bidId
        bidHistory.employeeId = user.employeeId
        bidHistory.status = enumData.BidHistoryStatus.DuyetThuongMai.code
        bidHistory.save()
      }
      // gửi email
      // await this.emailService.ThongBaoDaDuyetKyThuat(bidId)

      return { message: 'Duyệt thiết lập điều kiện thương mại của gói thầu thành công.' }
    }
  }

  async tradeReject(user: UserDto, bidId: string, data: any) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: bidId,
      entityName: BidEntity.name,
      type: enumData.FlowCode.BIDTRADE.code,
    })

    if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
      return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    } else {
      // const objPermission = await this.checkPermissionTechAccept(user, bidId)
      // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

      const bid = await this.repo.findOne({ where: { id: bidId } })
      if (bid) {
        bid.statusTrade = enumData.BidTradeStatus.TuChoi.code
        bid.statusPrice = enumData.BidPriceStatus.TuChoi.code
        bid.updatedBy = user.id
        await this.repo.save(bid)
        // }

        // Bid History
        const bidHistory = new BidHistoryEntity()
        bidHistory.companyId = user.companyId
        bidHistory.createdBy = user.id
        bidHistory.bidId = bidId
        bidHistory.employeeId = user.employeeId
        bidHistory.status = enumData.BidHistoryStatus.TuChoiThuongMai.code
        bidHistory.save()
      }

      // gửi email
      // await this.emailService.ThongBaoDaDuyetKyThuat(bidId)

      return { message: 'Duyệt thiết lập điều kiện thương mại của gói thầu thành công.' }
    }
  }

  /** Lấy thiết lập điều kiện thương mại của gói thầu */
  async getTrade(user: UserDto, bidId: string, isMobile?: boolean) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid1(user, bidId)

    res.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
      lsType: [enumData.FlowCode.BIDTRADE.code],
    })
    res.canApprove = res.objPermissionApprove[enumData.FlowCode.BIDTRADE.code]

    // lọc lại data

    res.listTrade = await this.bidTradeRepo.getTrade(user, res.id)

    res.sumPercent = 0
    for (const data1 of res.listTrade) {
      if (data1.percent > 0) res.sumPercent += data1.percent

      const lstChild = data1.__childs__.filter((c) => c.type === enumData.DataType.List.code || c.type === enumData.DataType.Number.code)
      if (lstChild.length > 0) {
        data1.sumPercent = 0
        for (const child of lstChild) {
          if (child.percent > 0) data1.sumPercent += child.percent
        }
      }
    }

    if (isMobile) {
      const quote = res?.listItem.find((item) => item.isExmatgroup)

      return quote?.listTrade.map((item) => {
        return {
          id: item?.id,
          name: item?.name ?? '',
          percent: item?.percent ?? '',
          percentRule: item?.percentRule ?? '',
          type: item?.type ?? '',
          isRequired: item?.isRequired ? 'Có' : 'Không',
          __childs__: item?.__childs__.map((i) => {
            return {
              id: i?.id,
              name: i?.name ?? '',
              percent: i?.percent ?? '',
              percentRule: i?.percentRule ?? '',
              type: i?.type ?? '',
              isRequired: i?.isRequired ? 'Có' : 'Không',
            }
          }),
          __bidTradeListDetails__: item?.__bidTradeListDetails__.map((i) => {
            return {
              name: i?.name ?? '',
              type: i?.type ?? '',
              value: i?.value ?? '',
            }
          }),
        }
      })
    }

    return res
  }

  /** Lấy data cbb tiêu chí cấp 1 */
  async tradeGetData(user: UserDto, bidId: string) {
    return await this.bidTradeRepo.find({ where: { bidItemId: bidId, level: 1, isDeleted: false } })
  }

  /** Tạo thêm ngoài lấy từ template */
  async tradeCreateData(user: UserDto, data: BidTradeCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    // const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId, isDeleted: false } })
    const bid = await this.repo.findOne({ where: { id: data.bidId, isDeleted: false } })

    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionTradeCreate(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Tạo thêm ngoài lấy từ template
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)

      const item = new BidTradeEntity()
      item.companyId = user.companyId
      item.createdBy = user.id
      item.bidId = bid.id
      // item.bidItemId = bid.id
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.parentId = data.parentId
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      await bidTradeRepo.save(item)
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.id)
  }

  /** Cập nhật */
  async tradeUpdateData(user: UserDto, data: BidTradeUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    // const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId, isDeleted: false } })
    const bid = await this.repo.findOne({ where: { id: data.bidId, isDeleted: false } })

    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    // const objPermission = await this.checkPermissionTradeCreate(user, data.bidId)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Cập nhật
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)

      const item = await bidTradeRepo.findOne({ where: { id: data.id } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.percentRule = data.percentRule
      item.isCalUp = data.isCalUp
      item.percentDownRule = data.percentDownRule
      item.level = data.level
      item.description = data.description
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.updatedBy = user.id
      await bidTradeRepo.save(item)
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Xóa */
  async tradeDeleteData(user: UserDto, bidTradeId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bidTrade = await this.bidTradeRepo.findOne({ where: { id: bidTradeId } })
    if (!bidTrade) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTrade.bid
    const objPermission = await this.checkPermissionTradeCreate(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bid.bidId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xóa
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(BidTradeListDetailEntity)

      const bidTrade = await bidTradeRepo.findOne({ where: { id: bidTradeId } })
      if (!bidTrade) throw new Error(ERROR_NOT_FOUND_DATA)

      const lstChild = await bidTrade.childs
      for (const bidTradeChild of lstChild) {
        await bidTradeListDetailRepo.delete({ bidTradeId: bidTradeChild.id })
      }
      await bidTradeListDetailRepo.delete({ bidTradeId })
      await bidTradeRepo.delete({ parentId: bidTradeId })
      await bidTradeRepo.delete(bidTradeId)

      return { message: DELETE_SUCCESS }
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.id || bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Xoá tất cả */
  async tradeDeleteAllData(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // const bid = await this.bidPrItemRepository.findOne({ where: { id: bidId, isDeleted: false } })
    const bid = await this.repo.findOne({ where: { id: bidId, isDeleted: false } })

    const objPermission = await this.checkPermissionTradeCreate(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const flag = await this.checkPermissionMpoEditTemplate(user, bid.id)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xoá tất cả
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(BidTradeListDetailEntity)

      const lstBidTrade = await bidTradeRepo.find({ where: { bidItemId: bidId } })
      for (const bidTrade of lstBidTrade) {
        const lstBidTradeChild = await bidTrade.childs
        for (const bidTradeChild of lstBidTradeChild) {
          // xoá lst detail
          await bidTradeListDetailRepo.delete({ bidTradeId: bidTradeChild.id })
        }
        // xoá lst
        await bidTradeListDetailRepo.delete({ bidTradeId: bidTrade.id })
        // xoá con
        await bidTradeRepo.delete({ parentId: bidTrade.id })
        // xoá
        await bidTradeRepo.delete(bidTrade.id)
      }

      return { message: DELETE_SUCCESS }
    })

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.id).catch((err: any) => {
      throw new BadRequestException('Cập nhật trạng thái điều kiện thương mại cho gói thầu thất bại.')
    })
  }

  /** Import excel chào giá */
  public async trade_import(user: UserDto, bidId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.tradeDeleteAllData(user, bidId)
    await this.repo.manager.transaction(async (manager) => {
      const bidTradeRepo = manager.getRepository(BidTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(BidTradeListDetailEntity)

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objBidTradeNew = new BidTradeEntity()
        objBidTradeNew.companyId = user.companyId
        objBidTradeNew.createdBy = user.id
        // objBidTradeNew.bidId = bidId
        objBidTradeNew.bidItemId = bidId
        objBidTradeNew.level = 1
        objBidTradeNew.sort = item.sort || 0
        objBidTradeNew.name = item.name
        objBidTradeNew.percent = item.percent
        objBidTradeNew.percentRule = item.percentRule
        objBidTradeNew.type = item.type
        objBidTradeNew.isRequired = item.isRequired
        objBidTradeNew.isCalUp = item.isCalUp
        objBidTradeNew.percentDownRule = item.percentDownRule

        const objBidTrade = await bidTradeRepo.save(objBidTradeNew)
        item.id = objBidTrade.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidTradeListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidTradeId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await bidTradeListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objBidTradeNew = new BidTradeEntity()
        objBidTradeNew.companyId = user.companyId
        objBidTradeNew.createdBy = user.id
        // objBidTradeNew.bidId = bidId
        objBidTradeNew.bidItemId = bidId
        objBidTradeNew.level = 2
        objBidTradeNew.sort = item.sort || 0
        objBidTradeNew.name = item.name
        objBidTradeNew.percent = item.percent
        objBidTradeNew.percentRule = item.percentRule
        objBidTradeNew.type = item.type
        objBidTradeNew.isRequired = item.isRequired
        objBidTradeNew.isCalUp = item.isCalUp
        objBidTradeNew.percentDownRule = item.percentDownRule

        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objBidTradeNew.parentId = parent.id

        const objBidTrade = await bidTradeRepo.save(objBidTradeNew)
        item.id = objBidTrade.id

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidTradeListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidTradeId = item.id
            detailNew.name = detail.nameList
            detailNew.value = detail.valueList
            await bidTradeListDetailRepo.save(detailNew)
          }
        }
      }
    })

    return { message: IMPORT_SUCCESS }
  }

  public async bidTradeListDetail_list(user: UserDto, bidTradeId: string) {
    return await this.repo.manager.getRepository(BidTradeListDetailEntity).find({
      where: { bidTradeId },
      order: { value: 'DESC' },
    })
  }

  public async bidTradeListDetail_create_data(user: UserDto, data: { bidTradeId: string; name: string; value: number }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTrade = await this.bidTradeRepo.findOne({ where: { id: data.bidTradeId } })
    if (!bidTrade) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTrade.bid
    const objPermission = await this.checkPermissionTradeCreate(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bid.id)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.id || bid.id)

    const entity = new BidTradeListDetailEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    entity.name = data.name
    entity.value = data.value
    entity.bidTradeId = data.bidTradeId
    await entity.save()

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async bidTradeListDetail_update_data(user: UserDto, data: { id: string; bidTradeId: string; name: string; value: number }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTrade = await this.bidTradeRepo.findOne({ where: { id: data.bidTradeId } })
    if (!bidTrade) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidTrade.bid
    const objPermission = await this.checkPermissionTradeCreate(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bid.id)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.id || bid.id)

    const entity = await this.repo.manager.getRepository(BidTradeListDetailEntity).findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    entity.name = data.name
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async bidTradeListDetail_delete_data(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidTradeListDetail = await this.repo.manager.getRepository(BidTradeListDetailEntity).findOne({ where: { id } })
    if (!bidTradeListDetail) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bidTrade = await bidTradeListDetail.bidTrade
    const bid = await bidTrade.bid
    const objPermission = await this.checkPermissionTradeCreate(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bid.id)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusTrade => DangTao
    await this.creatingTrade(user, bid.id || bid.id)

    await this.repo.manager.getRepository(BidTradeListDetailEntity).delete(id)

    return { message: DELETE_SUCCESS }
  }

  //#endregion

  async checkPermissionMpoEditTemplate(user: UserDto, bidId: string) {
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid && bid.statusResetPrice == enumData.BidResetPriceStatus.DangTao.code) {
      return true
    }

    let result = true
    const lstBidSupplierStatus = [
      enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
      enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
      enumData.BidSupplierStatus.DangDanhGia.code,
      enumData.BidSupplierStatus.DaDanhGia.code,
    ]

    const lstBidSupplier = await this.bidSupplierRepo.find({
      where: { bidId, status: In(lstBidSupplierStatus) },
      select: { id: true },
    })

    if (lstBidSupplier.length > 0) {
      result = false
    }

    return result
  }
}
