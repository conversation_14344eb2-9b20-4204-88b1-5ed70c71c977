import { MigrationInterface, QueryRunner } from 'typeorm'

export class ShipmentCoditionChange1750670646767 implements MigrationInterface {
  name = 'ShipmentCoditionChange1750670646767'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "shipment_condition_change" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_1cabb819c4fb1e164454b52e26d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_64eec51b476d29189f5e3affce9" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "shipmentConditionID" nvarchar(255) NOT NULL, "old" nvarchar(255) NOT NULL, "new" nvarchar(255) NOT NULL, CONSTRAINT "PK_1cabb819c4fb1e164454b52e26d" PRIMARY KEY ("id"))`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "shipment_condition_change"`)
  }
}
