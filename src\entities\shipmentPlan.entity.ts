import { Column, Entity, OneToMany } from 'typeorm'
import { ShipmentConfigTemplateDetailEntity } from './shipmentConfigTemplateDetail.entity'
import { BaseEntity } from './base.entity'
import { ShipmentPlanNumberEntity } from './shipmentPlanNumber.entity'
import { RequestQuoteEntity } from './requestQuote.entity'
import { BusinessTemplatePlanEntity } from './businessTemplatePlan.entity'

@Entity('shipment_plan')
export class ShipmentPlanEntity extends BaseEntity {
  /* Tên mẫu cấu hình */
  @Column({
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  name: string

  /* Mô tả mẫu cấu hình */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /* Trạng thái của mẫu cấu hình */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string
  /* id phương án đã chốt */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  acceptedValueId: string

  @OneToMany(() => ShipmentPlanNumberEntity, (p) => p.shipmentPlan)
  shipmentPlanNumbers: Promise<ShipmentPlanNumberEntity[]>

  @OneToMany(() => RequestQuoteEntity, (p) => p.shipmentPlan)
  requestQuotes: Promise<RequestQuoteEntity[]>

  @OneToMany(() => BusinessTemplatePlanEntity, (p) => p.shipmentPlan)
  businessTemplatePlans: Promise<BusinessTemplatePlanEntity[]>
}
