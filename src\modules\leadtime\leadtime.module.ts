import { Module } from '@nestjs/common'


import { TypeOrmExModule } from '../../typeorm'
import { LeadtimeRepository } from '../../repositories'
import { LeadtimeController } from './leadtime.controller'
import { LeadtimeService } from './leadtime.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([LeadtimeRepository])],
  providers: [LeadtimeService],
  controllers: [LeadtimeController],
})
export class LeadtimeModule { }
