import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { SchemaConfigEntity } from './schemaConfig.entity'
import { SupplierListPricePOEntity } from './supplierListPricePo.entity'

@Entity('supplier_schema')
export class SupplierSchemaEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierEntity, (p) => p.supplierSchema)
  suppliers: Promise<SupplierEntity[]>


  @OneToMany(() => SchemaConfigEntity, (p) => p.supplierSchema)
  schemaConfig: Promise<SchemaConfigEntity[]>

  @OneToMany(() => SupplierListPricePOEntity, (p) => p.supplierSchema)
  listPricePos: Promise<SupplierListPricePOEntity[]>
}
