import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common'
import { GuideRepository } from '../../repositories/guide.repository'
import { GuideCreateDto } from './dto/guideCreate.dto'
import { GuideFindByTypeDto, GuideUpdateDto } from './dto/guideUpdate.dto'
import { UserDto } from '../../dto'
import { ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'

@Injectable()
export class GuideService {
  constructor(private readonly guideRepo: GuideRepository) {}

  public async addNewGuide(user: UserDto, data: GuideCreateDto) {
    // user: UserDto,
    const guide = {
      ...data,
      createdBy: user.id,
      updatedBy: user.id,
    }
    return await this.guideRepo.insert(guide)
  }

  public async getGuides() {
    return await this.guideRepo.findAndCount({ where: { isDeleted: false } })
  }

  public async findByType(guideTypeDto: GuideFindByTypeDto) {
    return await this.guideRepo.find({
      where: { guideType: guideTypeDto.guideType, isDeleted: false },
      // order: { createdAt: 'DESC' },
    })
  }

  public async findById(id: string) {
    const guide = await this.guideRepo.findOne({
      where: {
        id,
        isDeleted: false,
      },
    })
    if (!guide) {
      throw new NotFoundException(`Guide with ID ${id} not found`)
    }
    return guide
  }

  public async updateGuide(user: UserDto, data: GuideUpdateDto) {
    const existingGuide = await this.guideRepo.findOne({
      where: {
        id: data.id,
        // isDeleted: false,
      },
    })
    if (!existingGuide) {
      throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    }

    // Update the guide
    const updateData = {
      ...data,
      updatedBy: user.id,
      updatedAt: new Date(),
    }

    await this.guideRepo.update(data.id, updateData)

    // Return the updated guide
    return { message: UPDATE_SUCCESS }
  }

  public async updateActiveGuide(user: UserDto, id: string) {
    const existingGuide = await this.guideRepo.findOne({
      where: {
        id: id,
      },
      select: { id: true, isDeleted: false },
    })
    if (!existingGuide) {
      throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    }

    await this.guideRepo.update(id, {
      isDeleted: true,
      updatedBy: user.id,
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
