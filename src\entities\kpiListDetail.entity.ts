import { KpiDetailEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'

@Entity('kpi_list_detail')
export class KpiListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    nullable: true,
  })
  value: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  kpiDetailId: string
  @ManyToOne(() => KpiDetailEntity, (p) => p.kpiListDetails)
  @JoinColumn({ name: 'kpiDetailId', referencedColumnName: 'id' })
  kpiDetail: Promise<KpiDetailEntity>
}
