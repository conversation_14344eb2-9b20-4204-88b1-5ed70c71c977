import { Controller, UseGuards, Post, Body, Get, Param } from '@nestjs/common'
import { JwtAuthGuard } from '../common/guards'
import { BidAuctionService } from './bidAuction.service'
import { CurrentUser } from '../common/decorators'
import { UserDto } from '../../dto'
import { BidAuctionCreateDto } from './dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

/** Đấu giá */
@ApiBearerAuth()
@ApiTags('Bid')
@UseGuards(JwtAuthGuard)
@Controller('bid_auction')
export class BidAuctionController {
  constructor(private readonly service: BidAuctionService) {}

  @ApiOperation({ summary: 'L<PERSON>y danh sách hạng mục chào giá' })
  @Get('get_price/:bidId')
  public async getPrice(@CurrentUser() user: UserDto, @Param('bidId') bidId: string) {
    return await this.service.getPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy ds phiên đấu giá' })
  @Post('approve')
  public async approve(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.approve(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds phiên đấu giá' })
  @Post('reject')
  public async reject(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.reject(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết đấu giá' })
  @Post('find_detail')
  public async findDetail(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    return await this.service.findDetail(data, user)
  }

  @ApiOperation({ summary: 'Lấy ds NCC để mời tham gia đấu giá' })
  @Post('load_supplier_data')
  public async loadSupplierData(@CurrentUser() user: UserDto, @Body() data: { bidId: string; statusFile: string[]; name?: string }) {
    return await this.service.loadSupplierData(user, data)
  }

  @ApiOperation({ summary: 'Tạo đấu giá Item' })
  @Post('save_bid_auction')
  public async saveBidAuction(@CurrentUser() user: UserDto, @Body() data: BidAuctionCreateDto) {
    return await this.service.saveBidAuction(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp chấp nhận thắng đánh giá thầu' })
  @Post('accept_bid_auction_top_one')
  public async supplierAcceptBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.supplierAcceptBid(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp không chấp nhận thắng đánh giá thầu' })
  @Post('reject_bid_auction_top_one')
  public async supplierNotAcceptBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.supplierNotAcceptBid(user, data)
  }
}
