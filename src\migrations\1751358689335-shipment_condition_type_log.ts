import { MigrationInterface, QueryRunner } from 'typeorm'

export class ShipmentConditionTypeLog1751358689335 implements MigrationInterface {
  name = 'ShipmentConditionTypeLog1751358689335'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter1" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter2" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter3" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter4" bigint`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter4"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter3"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter2"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter1"`)
  }
}
