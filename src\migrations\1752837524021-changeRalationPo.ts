import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeRalationPo1752837524021 implements MigrationInterface {
    name = 'ChangeRalationPo1752837524021'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po_contract" DROP CONSTRAINT "FK_08d010a100efc61ef64d6f60e9c"`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP CONSTRAINT "FK_529f0b9999d77fe7a93eba692f1"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_f6ee1bd28f08ab4376251e84258"`);
        
       
        await queryRunner.query(`ALTER TABLE "pr" DROP COLUMN "poId"`);
       
        await queryRunner.query(`ALTER TABLE "contract" ADD "posId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "poContractId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "pr" ADD "posId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "pr" ADD "poPrId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "contractIds"`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "contractIds" nvarchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "prIds"`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD "prIds" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_bd5a0aadb506f49b0245881fd95" FOREIGN KEY ("posId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_8ab4e7565a0e9566ef537713579" FOREIGN KEY ("poContractId") REFERENCES "po_contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_4a1964e21f4038584bec464969b" FOREIGN KEY ("posId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_f76c836c62573749e880c2833cb" FOREIGN KEY ("poPrId") REFERENCES "po_pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_f76c836c62573749e880c2833cb"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_4a1964e21f4038584bec464969b"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_8ab4e7565a0e9566ef537713579"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_bd5a0aadb506f49b0245881fd95"`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "prIds"`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD "prIds" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "contractIds"`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "contractIds" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "pr" DROP COLUMN "poPrId"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP COLUMN "posId"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "poContractId"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "posId"`);
        await queryRunner.query(`ALTER TABLE "pr" ADD "poId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_f6ee1bd28f08ab4376251e84258" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD CONSTRAINT "FK_529f0b9999d77fe7a93eba692f1" FOREIGN KEY ("prIds") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD CONSTRAINT "FK_08d010a100efc61ef64d6f60e9c" FOREIGN KEY ("contractIds") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
