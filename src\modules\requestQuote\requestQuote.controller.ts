import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../common/guards'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { CurrentUser } from '../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { RequestQuoteService } from './requestQuote.service'
import { RequestQuoteCreateDto, RequestQuoteUpdateDto } from './dto'
@ApiBearerAuth()
@ApiTags('RequestQuote')
@UseGuards(JwtAuthGuard)
@Controller('request_quote')
export class RequestQuoteController {
  constructor(private readonly service: RequestQuoteService) {}

  @ApiOperation({ summary: 'Tạo mới một dữ liệu.' })
  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: RequestQuoteCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin của vùng với dữ liệu được cung cấp.' })
  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: RequestQuoteUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Danh sách vùng phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm chi tiết một PR' })
  @Post('find_detail')
  async loadDetail(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return this.service.loadDetail(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật hủy ' })
  @Post('update_cancel')
  public async updateCancel(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateCancel(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật chờ duyệt' })
  @Post('update_wait_approved')
  public async updateWaitApproved(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateWaitApproved(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật duyệt' })
  @Post('update_approved')
  public async updateApproved(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateApproved(user, data)
  }

  @ApiOperation({ summary: 'API gở duyệt khi từ chối PR' })
  @Post('update_remove_rule')
  public async updateRemoveRule(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateRemoveRule(user, data)
  }

  @ApiOperation({ summary: 'API từ chối duyệt' })
  @Post('update_reject_rule')
  public async updateRejectRule(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateRejectRule(user, data)
  }

  @ApiOperation({ summary: 'API gở duyệt khi từ chối PR' })
  @Post('update_revert_status')
  public async updateRevertStatus(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateRevertStatus(user, data)
  }

  @ApiOperation({ summary: 'Danh sách các cấp duyệt' })
  @Post('find_list_approved')
  public async findListApproved(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.findListApproved(user, data)
  }

  @ApiOperation({ summary: 'Danh sách vùng phân trang' })
  @Post('pagination_client')
  public async paginationClient(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationClient(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật duyệt' })
  @Post('update_supplier_approved')
  public async updateSuplierApproved(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateSuplierApproved(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật duyệt' })
  @Post('update_supplier_reject')
  public async updateSuplierReject(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateSuplierReject(user, data)
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm chi tiết một PR' })
  @Post('find_detail_supplier')
  async loadDetailBySupplier(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return this.service.loadDetailBySupplier(user, data)
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm chi tiết một PR' })
  @Post('find_list_incoterm')
  async findListIncoterm(@Body() data: FilterOneDto) {
    return this.service.findListIncoterm(data)
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm chi tiết một PR' })
  @Post('find_list_payment_term')
  async findListPaymentTerm(@Body() data: FilterOneDto) {
    return this.service.findListPaymentTerm(data)
  }
}
