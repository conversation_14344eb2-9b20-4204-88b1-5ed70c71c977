import { BaseEntity } from './base.entity'
import { Entity, Column } from 'typeorm'

@Entity('data_history')
export class DataHistoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  tableName: string

  @Column({
    type: 'varchar',

    nullable: false,
  })
  relationId: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  dataJson: any
}
