import { MigrationInterface, QueryRunner } from "typeorm";

export class GenColum1749893512664 implements MigrationInterface {
    name = 'GenColum1749893512664'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "quantityRemind" float CONSTRAINT "DF_0f190d7bd68862a9e2bbed95223" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "warehouseIssueSloc" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "reservationUomNormId" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "reservationUomNormId"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "warehouseIssueSloc"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "DF_0f190d7bd68862a9e2bbed95223"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "quantityRemind"`);
    }

}
