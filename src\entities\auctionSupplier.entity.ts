import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn, OneToMany } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
import { AuctionEntity } from './auction.entity'
import { BidSupplierEntity } from './bidSupplier.entity'
import { AuctionSupplierPriceEntity } from './auctionSupPrice.entity'
import { enumData } from '../constants'

/** Các NCC tham gia đấu giá */
@Entity('auction_supplier')
export class AuctionSupplierEntity extends BaseEntity {
  /** Lần đấu giá */
  @Column({ type: 'varchar', nullable: false })
  auctionId: string
  @ManyToOne(() => AuctionEntity, (p) => p.auctionSuppliers)
  @JoinColumn({ name: 'auctionId', referencedColumnName: 'id' })
  auction: Promise<AuctionEntity>

  /** NCC */
  @Column({ type: 'varchar', nullable: false })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.bidSupplier)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Giá đấu trước đó */
  @Column({ type: 'bigint', nullable: true })
  submitPriceOld?: number

  /** Giá đấu */
  @Column({ type: 'bigint', nullable: true })
  submitPrice?: number

  /** Ngày nộp đấu giá */
  @Column({ nullable: true, type: 'datetime' })
  submitDate?: Date

  /** Thứ hạng */
  @Column({ type: 'int', nullable: true })
  rank?: number

  /** Trạng thái */
  @Column({ type: 'varchar', length: 50, nullable: true, default: enumData.AuctionSupplierStatus.WAITING.code })
  status: string

  /** Thuộc về đấu giá ? */
  @Column({
    nullable: false,
    default: false,
  })
  isWinner: boolean

  @OneToMany(() => AuctionSupplierPriceEntity, (p) => p.auctionSupplier)
  auctionSupplierPrice: Promise<AuctionSupplierPriceEntity[]>
}
