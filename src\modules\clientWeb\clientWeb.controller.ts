import { Body, Controller, Get, Param, Post, Req, UseGuards, Request } from '@nestjs/common'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { SupplierCreateCustomPriceItemDto, SupplierCreatePriceItemDto, SupplierCreateTechItemDto, SupplierCreateTradeItemDto } from '../bid/dto'
import { BidAuctionSupplierSaveDto } from '../bidAuction/dto'
import { CurrentUser } from '../common/decorators'
import { JwtAuthGuard } from '../common/guards'
import { ClientWebService } from './clientWeb.service'
import { BidDealSupplierSaveDto } from '../bidDeal/dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { POUpdateDeliveryDateDto, POUpdateStatusDto } from '../po/dto'
import { InboundClientCreateDto } from '../inbound/dto/inboundClientCreate.dto'
import { ACCEPT_LANGUAGE } from '../../constants'
import { Request as IRequest } from 'express'

@ApiBearerAuth()
@ApiTags('Client')
@Controller('client_web')
export class ClientWebController {
  constructor(private service: ClientWebService) {}

  @ApiOperation({ summary: 'Kiểm tra quyền xem kết quả thẩm định' })
  @UseGuards(JwtAuthGuard)
  @Post('check_is_your_expertise')
  public async checkIsYourExpertise(@CurrentUser() user: UserDto, @Body() data: { supplierExpertiseId: string }) {
    return await this.service.checkIsYourExpertise(user, data)
  }

  @ApiOperation({ summary: 'Lấy dữ liệu mà MPO đề xuất cho nhà cung cấp' })
  @UseGuards(JwtAuthGuard)
  @Post('get_data_suggest')
  public async getDataSuggest(@CurrentUser() user: UserDto, @Body() data: { supplierExpertiseId: string }) {
    return await this.service.getDataSuggest(user, data)
  }

  @ApiOperation({ summary: 'Đồng ý cập nhật lại thông tin theo nội dung thẩm định' })
  @UseGuards(JwtAuthGuard)
  @Post('supplier_accept_change_data')
  public async supplierAcceptChangeData(@CurrentUser() user: UserDto, @Body() data: { supplierExpertiseId: string }) {
    return await this.service.supplierAcceptChangeData(user, data)
  }

  @ApiOperation({ summary: 'Danh sách gói thầu chưa đăng nhập' })
  // @UseGuards(JwtAuthGuard)
  @Post('paginationHomePage')
  public async paginationHomePage(@Req() req: Request, @Body() data: PaginationDto, @Request() request: IRequest) {
    return await this.service.paginationHomePage(req, data, request.headers[ACCEPT_LANGUAGE])
  }

  @ApiOperation({ summary: 'Danh sách gói thầu sau khi đăng nhập' })
  @UseGuards(JwtAuthGuard)
  @Post('paginationHomePageHadToken')
  public async paginationHomePageHadToken(@CurrentUser() user: UserDto, @Body() data: PaginationDto, @Request() req: IRequest) {
    return await this.service.paginationHomePageHadToken(user, data, req.headers[ACCEPT_LANGUAGE])
  }

  @ApiOperation({ summary: 'Lấy danh sách Item của gói thầu khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataBidding')
  public async loadDataBidding(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataBidding(data, user)
  }

  @ApiOperation({ summary: 'Chi tiết gói thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('bidDetailHadToken')
  public async bidDetailHadToken(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.bidDetailHadToken(data, user)
  }

  @ApiOperation({ summary: 'Kiểm tra hiển thị nút xác nhận tham gia gói thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('isDisplayBtnAcceptBid')
  public async isDisplayBtnAcceptBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.isDisplayBtnAcceptBid(user, data)
  }

  @ApiOperation({ summary: 'Kiểm tra hiển thị nút bổ sung hồ sơ không' })
  @UseGuards(JwtAuthGuard)
  @Post('isDisplayBtnBid')
  public async isDisplayBtnBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.isDisplayBtnBid(user, data)
  }

  @ApiOperation({ summary: 'Xác nhận tham gia gói thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('acceptBid')
  public async acceptBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.acceptBid(user, data)
  }

  @ApiOperation({ summary: 'Từ chối tham gia gói thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('rejectBid')
  public async rejectBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.rejectBid(user, data)
  }

  @ApiOperation({ summary: 'NCC nộp hồ sơ thầu cho Item' })
  @UseGuards(JwtAuthGuard)
  @Post('createBidSupplier')
  public async createBidSupplier(
    @CurrentUser() user: UserDto,
    @Body()
    data: {
      bidId: string
      techInfo: SupplierCreateTechItemDto[]
      tradeInfo: SupplierCreateTradeItemDto[]
      priceShipmentInfo: any[]
      priceInfo: SupplierCreatePriceItemDto[]
      customPriceInfo: SupplierCreateCustomPriceItemDto[]
    },
  ) {
    return await this.service.createBidSupplier(user, data)
  }

  @ApiOperation({ summary: 'NCC nộp hồ sơ thầu cho Item' })
  @UseGuards(JwtAuthGuard)
  @Post('checkIsShipment')
  public async checkIsShipment(
    @CurrentUser() user: UserDto,
    @Body()
    data: {
      bidId: string
    },
  ) {
    return await this.service.checkIsShipment(user, data)
  }

  @ApiOperation({ summary: 'NCC nộp hồ sơ thầu cho Item' })
  @UseGuards(JwtAuthGuard)
  @Post('createOfferSupplier')
  public async createOfferSupplier(
    @CurrentUser() user: UserDto,
    @Body()
    data: {
      fileAttach: string
      fileTech: string
      filePrice: string
      linkDrive: string
      note: string
      bidId: string
      fromAdmin: boolean
      tradeInfo: SupplierCreateTradeItemDto[]
      priceInfo: SupplierCreatePriceItemDto[]
      customPriceInfo: SupplierCreateCustomPriceItemDto[]
      priceShipmentInfo: any[]
    },
  ) {
    return await this.service.createOfferSupplier(user, data)
  }

  @ApiOperation({ summary: 'Check quyền truy cập gói thầu của Doanh nghiệp' })
  @UseGuards(JwtAuthGuard)
  @Post('checkPermissionLoadDataBid')
  public async checkPermissionLoadDataBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.checkPermissionLoadDataBid(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ kỹ thuật Item của NCC khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataBidTech')
  public async loadDataBidTech(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataBidTech(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ kỹ thuật Item của NCC khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataTradeShipment')
  public async loadDataTradeShipment(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.loadDataTradeShipment(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ ĐKTM Item của NCC khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataBidTrade')
  public async loadDataBidTrade(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataBidTrade(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ giá Item của NCC khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataBidPrice')
  public async loadDataBidPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataBidPrice(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ giá Item của NCC khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataOfferPrice')
  public async loadDataOfferPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataOfferPrice(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ giá Item của NCC khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataOfferPriceView')
  public async loadDataOfferPriceView(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataOfferPriceView(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ ĐKTM Item của NCC khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataOfferTrade')
  public async loadDataOfferTrade(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataOfferTrade(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ ĐKTM Item của NCC khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataOfferTradeView')
  public async loadDataOfferTradeView(@CurrentUser() user: UserDto, @Body() data: { bidId: string; supplierId?: string }) {
    return await this.service.loadDataOfferTradeView(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ cơ cấu giá Item của NCC khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataOfferCustomPrice')
  public async loadDataOfferCustomPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataOfferCustomPrice(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ cơ cấu giá Item của NCC khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataOfferCustomPriceView')
  public async loadDataOfferCustomPriceView(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataOfferCustomPriceView(user, data)
  }

  @ApiOperation({ summary: 'Lấy hồ sơ cơ cấu giá Item của NCC khi nộp hồ sơ thầu' })
  @UseGuards(JwtAuthGuard)
  @Post('loadDataBidCustomPrice')
  public async loadDataBidCustomPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.loadDataBidCustomPrice(user, data)
  }

  @ApiOperation({ summary: 'Lịch sử đấu thầu Doanh nghiệp' })
  @UseGuards(JwtAuthGuard)
  @Post('paginationBidHistory')
  public async paginationBidHistory(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationBidHistory(user, data)
  }

  @ApiOperation({ summary: 'Check quyền nộp giá bổ sung cho gói thầu của Doanh nghiệp' })
  @UseGuards(JwtAuthGuard)
  @Post('checkPermissionJoinResetPrice')
  public async checkPermissionJoinResetPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string }) {
    return await this.service.checkPermissionJoinResetPrice(user, data)
  }

  @ApiOperation({ summary: 'NCC nộp chào giá hiệu chỉnh' })
  @UseGuards(JwtAuthGuard)
  @Post('supplierSaveResetPrice')
  public async supplierSaveResetPrice(
    @CurrentUser() user: UserDto,
    @Body()
    data: {
      bidId: string
      dataInfo: { filePriceDetail?: string; fileTechDetail?: string }
      priceInfo: SupplierCreatePriceItemDto[]
    },
  ) {
    return await this.service.supplierSaveResetPrice(user, data)
  }

  @ApiOperation({ summary: 'Lịch sử chào giá gói thầu của NCC' })
  @UseGuards(JwtAuthGuard)
  @Get('get_bid_history_price_client/:bidid')
  public async getBidHistoryPrice(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service.getBidHistoryPrice(user, bidId)
  }

  //#region BidDeal

  @ApiOperation({ summary: 'Lấy thông tin đàm phán giá của NCC' })
  @UseGuards(JwtAuthGuard)
  @Get('get_bid_deal_supplier/:biddealid')
  public async getBidDealSupplier(@CurrentUser() user: UserDto, @Param('biddealid') bidDealId: string) {
    return await this.service.getBidDealSupplier(user, bidDealId)
  }

  @ApiOperation({ summary: 'Lấy thông tin đàm phán giá của NCC' })
  @UseGuards(JwtAuthGuard)
  @Get('get_offer_deal_supplier/:biddealid')
  public async getOfferDealSupplier(@CurrentUser() user: UserDto, @Param('biddealid') bidDealId: string) {
    return await this.service.getOfferDealSupplier(user, bidDealId)
  }

  @ApiOperation({ summary: 'Đề nghị đàm phán giá/ Chấp nhận giá đề nghị/ Lưu' })
  @UseGuards(JwtAuthGuard)
  @Post('accept_bid_deal_supplier')
  public async acceptBidDealSupplier(@CurrentUser() user: UserDto, @Body() data: BidDealSupplierSaveDto) {
    return await this.service.acceptBidDealSupplier(user, data)
  }

  @ApiOperation({ summary: 'Đề nghị đàm phán giá/ Chấp nhận giá đề nghị/ Lưu' })
  @UseGuards(JwtAuthGuard)
  @Post('accept_offer_deal_supplier')
  public async acceptOfferDealSupplier(@CurrentUser() user: UserDto, @Body() data: BidDealSupplierSaveDto) {
    return await this.service.acceptOfferDealSupplier(user, data)
  }

  @ApiOperation({ summary: 'Từ chối giá đề nghị' })
  @UseGuards(JwtAuthGuard)
  @Post('reject_bid_deal_supplier')
  public async rejectBidDealSupplier(@CurrentUser() user: UserDto, @Body() data: { bidDealId: string }) {
    return await this.service.rejectBidDealSupplier(user, data?.bidDealId || '')
  }

  @ApiOperation({ summary: 'Từ chối giá đề nghị' })
  @UseGuards(JwtAuthGuard)
  @Post('reject_offer_deal_supplier')
  public async rejectOfferDealSupplier(@CurrentUser() user: UserDto, @Body() data: { bidDealId: string }) {
    return await this.service.rejectOfferDealSupplier(user, data?.bidDealId || '')
  }

  @ApiOperation({ summary: 'Lấy kết quả đàm phán' })
  @UseGuards(JwtAuthGuard)
  @Get('check_result_message/:biddealid')
  public async checkResultMessage(@CurrentUser() user: UserDto, @Param('biddealid') bidDealId: string) {
    return await this.service.checkResultMessage(user, bidDealId)
  }

  @ApiOperation({ summary: 'Lấy kết quả đàm phán' })
  @UseGuards(JwtAuthGuard)
  @Get('check_result_message_offer/:biddealid')
  public async checkResultMessageOffer(@CurrentUser() user: UserDto, @Param('biddealid') bidDealId: string) {
    return await this.service.checkResultMessageOffer(user, bidDealId)
  }
  //#endregion

  //#region BidAuction

  @ApiOperation({ summary: 'Lấy thông tin đấu giá của NCC' })
  @UseGuards(JwtAuthGuard)
  @Get('get_bid_auction_supplier/:bidauctionid')
  public async getBidAuctionSupplier(@CurrentUser() user: UserDto, @Param('bidauctionid') bidAuctionId: string) {
    return await this.service.getBidAuctionSupplier(user, bidAuctionId)
  }

  @ApiOperation({ summary: 'NCC nộp đấu giá của mình' })
  @UseGuards(JwtAuthGuard)
  @Post('supplier_save_auction')
  public async supplierSaveAuction(@CurrentUser() user: UserDto, @Body() data: BidAuctionSupplierSaveDto) {
    await this.service.supplierSaveAuction(user, data)
  }
  //#endregion

  //#region FAQ
  @ApiOperation({ summary: 'Lấy danh sách FAQ theo danh mục FAQ' })
  @Get('get_faq/:id')
  public async getFAQHomePage(@CurrentUser() user: UserDto, @Param('id') id: string) {
    return await this.service.getFAQHomePage(user, id)
  }

  @ApiOperation({ summary: 'Lấy danh sách danh mục FAQ' })
  @Post('get_faq_category')
  public async getFAQCategoryHomePage(@CurrentUser() user: UserDto, @Request() req: IRequest) {
    return await this.service.getFAQCategoryHomePage(user, req.headers[ACCEPT_LANGUAGE])
  }

  @ApiOperation({ summary: 'Lấy danh sách câu hỏi theo danh mục' })
  @Post('find_faq')
  public async findFaq(@CurrentUser() user: UserDto, @Body() data: { categoryId?: string }) {
    return await this.service.findFaq(user, data)
  }
  //#endregion

  //#region PO
  @ApiOperation({ summary: 'Danh sách PO của nhà cung cấp' })
  @UseGuards(JwtAuthGuard)
  @Post('paginationSupplier')
  public async paginationSupplier(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationSupplier(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp cập nhật xác nhận giao hàng' })
  @UseGuards(JwtAuthGuard)
  @Post('update_status_po_delivery')
  public async updateStatusDelivery(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateStatusDelivery(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp cập nhật ngày giao hàng' })
  @UseGuards(JwtAuthGuard)
  @Post('update_delivery_date')
  public async updateDeliveryDate(@CurrentUser() user: UserDto, @Body() data: POUpdateDeliveryDateDto) {
    return await this.service.updateDeliveryDate(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp cập nhật ngày giao hàng' })
  @UseGuards(JwtAuthGuard)
  @Post('update_status_order')
  public async updateStatus(@CurrentUser() user: UserDto, @Body() data: POUpdateStatusDto) {
    return await this.service.updateStatus(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp cập nhật xác nhận giao hàng' })
  @UseGuards(JwtAuthGuard)
  @Post('update_status_po_confirm')
  public async updateStatusConfirm(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateStatusConfirm(user, data)
  }

  @ApiOperation({ summary: 'Nhà cung cấp cập nhật xác nhận giao hàng' })
  @UseGuards(JwtAuthGuard)
  @Post('update_status_po_refuse')
  public async updateStatusSupplierRefuse(@CurrentUser() user: UserDto, @Body() data: { id: string; reason: string }) {
    return await this.service.updateStatusSupplierRefuse(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết PO' })
  @UseGuards(JwtAuthGuard)
  @Post('find_detail_po')
  public async findDetailPO(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetailPO(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết PO' })
  @UseGuards(JwtAuthGuard)
  @Post('loadList_product_client')
  public async loadListProductClient(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.loadListProductClient(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái thực hiện PO' })
  @UseGuards(JwtAuthGuard)
  @Post('update_status_order_client')
  public async updateStatusOrderClient(@CurrentUser() user: UserDto, @Body() data: { id: string; orderStatus: string; reason: string }) {
    return await this.service.updateStatusOrderClient(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái thực hiện PO' })
  @UseGuards(JwtAuthGuard)
  @Post('find_po_client')
  public async findPoClient(@CurrentUser() user: UserDto, @Body() data: { contractId?: string; status?: any }) {
    return await this.service.findPoClient(user, data)
  }

  //#endregion

  /**Contract */
  @ApiOperation({ summary: 'Danh sách hợp đồng của nhà cung cấp' })
  @UseGuards(JwtAuthGuard)
  @Post('pagination_contract_supplier')
  public async paginationContractSupplier(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationContractSupplier(data, user)
  }

  @ApiOperation({ summary: 'Chi tiết hợp đồng' })
  @UseGuards(JwtAuthGuard)
  @Post('find_detail_contract')
  public async findDetailContract(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetailContract(user, data)
  }

  @ApiOperation({ summary: 'Load ds PR' })
  @UseGuards(JwtAuthGuard)
  @Post('find_client')
  public async findClient(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findClient(user, data)
  }

  @ApiOperation({ summary: 'Load ds Hợp đồng' })
  @UseGuards(JwtAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: { status?: string }) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Danh sách phụ lục hợp đồng' })
  @UseGuards(JwtAuthGuard)
  @Post('pagination_contract_appendix')
  public async paginationContractAppendix(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationContractAppendix(data, user)
  }

  @ApiOperation({ summary: 'Lấy List item' })
  @Post('load_list_item')
  public async loadListItem(@CurrentUser() user: UserDto, @Body() data: { contractId: string }) {
    return await this.service.loadListItem(user, data)
  }
  @ApiOperation({ summary: 'Chi tiết inbound' })
  @UseGuards(JwtAuthGuard)
  @Post('load_detail')
  public async loadDetail(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.loadDetail(user, data)
  }

  @ApiOperation({ summary: 'Thêm mới inbound' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data_inbound')
  public async createDataPoInbound(@CurrentUser() user: UserDto, @Body() data: InboundClientCreateDto) {
    return await this.service.createDataPoInbound(user, data)
  }

  /**Auction */
  @ApiOperation({ summary: 'Load ds đấu giá ' })
  @UseGuards(JwtAuthGuard)
  @Post('find_auction_client')
  public async findAuction(@CurrentUser() user: UserDto, @Body() data: { status?: string }) {
    return await this.service.findAuction(user, data)
  }

  /**Auction */
  @ApiOperation({ summary: 'Load ds nhà cung cấp select box' })
  @UseGuards(JwtAuthGuard)
  @Post('find_select_supplier_client')
  public async findSelectSuplier(@CurrentUser() user: UserDto, @Body() data: { status?: string }) {
    return await this.service.findSelectSuplier(user, data)
  }

  /**Recommenda puschase  */

  @ApiOperation({ summary: 'Lấy list pr của đề nghị mua hàng' })
  @UseGuards(JwtAuthGuard)
  @Post('recommended_purchase_load_list_pr')
  public async rePurloadListPr(@CurrentUser() user: UserDto, @Body() data: { recommendedPurchaseId: string }) {
    return await this.service.RePurloadListPr(user, data)
  }

  @ApiOperation({ summary: 'Lấy list ex mat group  của đề nghị mua hàng' })
  @UseGuards(JwtAuthGuard)
  @Post('recommended_purchase_load_list_ex_mat_group')
  public async rePurloadListExmatGroup(@CurrentUser() user: UserDto, @Body() data: { recommendedPurchaseId: string }) {
    return await this.service.RePurloadListExmatGroup(user, data)
  }

  @ApiOperation({ summary: 'Danh sách công ty' })
  @Post('find_data_company')
  public async findDataCompany(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.findDataCompany(user, data)
  }

  @ApiOperation({ summary: 'Danh sách công ty' })
  @Post('load_payment_method')
  public async findDataPaymentMethod(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.findDataPaymentMethod(user, data)
  }

  @ApiOperation({ summary: 'Danh sách công ty' })
  @Post('load_payment_term')
  public async findDataPaymentTerm(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.findDataPaymentTerm(user, data)
  }

  @ApiOperation({ summary: 'Danh sách công ty' })
  @Post('load_currency')
  public async findCurrency(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.findCurrency(user, data)
  }
}
