import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnContract1752164786777 implements MigrationInterface {
  name = 'AddColumnContract1752164786777'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "technicalSpec" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "incotermLocationEn" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "qualityEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "packaging" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "packagEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "marking" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "markingEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "portOfLoading" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "portOfDischargeEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "partialDelivery" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "partialDeliveryEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "transshipment" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "transshipmentEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "documentSendingDays" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "billOfLading" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "billOfLadingEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "onBehalfOf" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "onBehalfOfEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "signedInvoiceCount" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "packingListDetailCount" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "qualityCertificateCount" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "isShowLotNumber" bit CONSTRAINT "DF_eaf2c2037ad7b443951391ea9a4" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "showLotNumberEn" bit CONSTRAINT "DF_3357159f36e8fac2dc4cc5803d8" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "certificateOfOrigin" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "certificateOfOriginEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "authorizedLocation" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "originalCopyCount" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "copyCount" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "insuranceAgent" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "documentDeliveryTime" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "claimFilingTime" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "sellerClaimResponseTime" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "quantityClaimTime" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "warrantyTerms" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "warrantyTermsEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "forceMajeureNoticeTime" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "disputeResolutionPlace" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "disputeResolutionLocationEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "penaltyValue" bigint CONSTRAINT "DF_14d75c5032218212639a1bbd5c7" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "penaltyPaymentTime" int`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "otherTerms" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "otherTermsEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "deliveryPoint" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "deliveryPointEn" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "destination" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "destinationEn" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "freightPaymentTerm" nvarchar(100)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "freightPaymentTermEn" nvarchar(100)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "freightPaymentTermEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "freightPaymentTerm"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "destinationEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "destination"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "deliveryPointEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "deliveryPoint"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "otherTermsEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "otherTerms"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "penaltyPaymentTime"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_14d75c5032218212639a1bbd5c7"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "penaltyValue"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "disputeResolutionLocationEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "disputeResolutionPlace"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "forceMajeureNoticeTime"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "warrantyTermsEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "warrantyTerms"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "quantityClaimTime"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "sellerClaimResponseTime"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "claimFilingTime"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "documentDeliveryTime"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "insuranceAgent"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "copyCount"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "originalCopyCount"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "authorizedLocation"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "certificateOfOriginEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "certificateOfOrigin"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_3357159f36e8fac2dc4cc5803d8"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "showLotNumberEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_eaf2c2037ad7b443951391ea9a4"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "isShowLotNumber"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "qualityCertificateCount"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "packingListDetailCount"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "signedInvoiceCount"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "onBehalfOfEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "onBehalfOf"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "billOfLadingEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "billOfLading"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "documentSendingDays"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "transshipmentEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "transshipment"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "partialDeliveryEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "partialDelivery"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "portOfDischargeEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "portOfLoading"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "markingEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "marking"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "packagEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "packaging"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "qualityEn"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "incotermLocationEn"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "technicalSpec"`)
  }
}
