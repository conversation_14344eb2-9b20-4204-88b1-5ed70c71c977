import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateCodeIncoterm1752483284487 implements MigrationInterface {
    name = 'CreateCodeIncoterm1752483284487'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "code" varchar(50)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "code"`);
    }

}
