import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangePermissionApprove1748515161409 implements MigrationInterface {
  name = 'ChangePermissionApprove1748515161409'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "flow_approve_detail" ADD "employeePositionId" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "permission_approve" ADD "employeePositionId" varchar(50)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "permission_approve" DROP COLUMN "employeePositionId"`)
    await queryRunner.query(`ALTER TABLE "flow_approve_detail" DROP COLUMN "employeePositionId"`)
  }
}
