import { Injectable } from '@nestjs/common'
import { In, Like } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { IncotermConversionRepository, IncotermRepository } from '../../repositories'
import { IncotermConversionCreateDto, IncotermConversionUpdateDto } from './dto'
import { IncotermConversionEntity } from '../../entities'
import { customAlphabet } from 'nanoid'

@Injectable()
export class IncotermConversionService {
  constructor(private repo: IncotermConversionRepository, private incotermRepo: IncotermRepository) {}

  /** Lấy ds incoterm */
  public async find() {
    return await this.repo.find({ where: { isDeleted: false } })
  }

  /** <PERSON><PERSON>y ds incoterm có phân trang */
  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}

    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)

    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })

    return res
  }

  /** Tạo mới một incoterm với dữ liệu được cung cấp. */
  async createData(user: UserDto, data: IncotermConversionCreateDto[]) {
    if (data.length === 0) throw new Error(` Vui lòng thêm ít nhất một Item. Vui lòng kiểm tra lại`)

    const dictIncoterm: any = {}
    {
      const lstIncotem: any = await this.incotermRepo.find({ where: { isDeleted: false }, select: { id: true, code: true } })
      lstIncotem.forEach((c) => (dictIncoterm[c.id] = c))
    }

    for (let item of data) {
      if (item.incotermFormId) {
        if (!dictIncoterm[item.incotermFormId]) throw new Error(` Incoterm Form không tồn tại. Vui lòng kiểm tra lại`)
      }
      if (item.incotermToId) {
        if (!dictIncoterm[item.incotermToId]) throw new Error(` Incoterm To không tồn tại. Vui lòng kiểm tra lại`)
      }
    }

    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const repo = trans.getRepository(IncotermConversionEntity)

      for (let item of data) {
        const findEntity = await repo.findOne({ where: { incotermFormId: item.incotermFormId, incotermToId: item.incotermToId } })
        if (findEntity) {
          findEntity.updatedAt = new Date()
          findEntity.updatedBy = user.id
          findEntity.incotermFormId = item.incotermFormId
          findEntity.incotermToId = item.incotermToId
          findEntity.lstSettingCostCode = item.lstSettingCostCode
          findEntity.lstSettingCostId = item.lstSettingCostId
          findEntity.lstShipmentConditionTypeId = item.lstShipmentConditionTypeId
          findEntity.lstShipmentConditionTypeCode = item.lstShipmentConditionTypeCode
          await repo.save(findEntity)
        } else {
          const nanoid = customAlphabet('QWERTYUIOPASDFGHJKLZXCVBNM', 10)
          const entity = new IncotermConversionEntity()
          entity.code = nanoid()
          entity.createdAt = new Date()
          entity.createdBy = user.id
          entity.incotermFormId = item.incotermFormId
          entity.incotermToId = item.incotermToId
          entity.lstSettingCostCode = item.lstSettingCostCode
          entity.lstSettingCostId = item.lstSettingCostId

          entity.lstShipmentConditionTypeId = item.lstShipmentConditionTypeId
          entity.lstShipmentConditionTypeCode = item.lstShipmentConditionTypeCode
          await repo.insert(entity)
        }
      }
    })

    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật trạng thái kích hoạt của incoterm. */
  async updateActive(user: UserDto, id: string) {
    const entity = await this.repo.findOne({
      where: { id: id },
      select: { id: true, isDeleted: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const newIsDeleted = !entity.isDeleted
    await this.repo.update(id, {
      isDeleted: newIsDeleted,
      updatedBy: user.id,
      updatedAt: new Date(),
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async loadDataSelect(user: UserDto) {
    return await this.repo.find({
      where: { isDeleted: false },
      select: { id: true, code: true },
    })
  }
}
