import { GuideService } from './guide.service'
import { GuideController } from './guide.controller'

import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { GuideRepository } from '../../repositories/guide.repository'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([GuideRepository])],
  controllers: [GuideController],
  providers: [GuideService],
})
export class GuideModule {}
