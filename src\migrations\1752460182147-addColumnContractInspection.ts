import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnContractInspection1752460182147 implements MigrationInterface {
  name = 'AddColumnContractInspection1752460182147'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_inspection" ADD "name" nvarchar(300)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_inspection" DROP COLUMN "name"`)
  }
}
