import { Injectable, ConflictException } from '@nestjs/common'
import { CREATE_SUCCESS, enumData, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { BidTypeRepository } from '../../repositories'
import { In, Like } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { BidTypeCreateDto, BidTypeUpdateDto } from './dto'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'

@Injectable()
export class BidTypeService {
  constructor(private readonly repo: BidTypeRepository, private organizationalPositionService: OrganizationalPositionService) {}

  public async find(user: UserDto) {
    const whereCon: any = { isDeleted: false }
    return await this.repo.find({ where: whereCon, order: { name: '<PERSON><PERSON>' } })
  }

  public async createData(user: UserDto, data: BidTypeCreateDto) {
    const objCheckCode = await this.repo.findOne({ where: { code: data.code }, select: { id: true } })
    if (objCheckCode) throw new ConflictException(ERROR_CODE_TAKEN)

    const newEntity = this.repo.create(data)
    newEntity.companyId = user.companyId
    newEntity.createdBy = user.id
    await this.repo.save(newEntity)

    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: BidTypeUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.name = data.name
    entity.description = data.description
    entity.updatedBy = user.id
    await this.repo.save(entity)

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.MasterBidForm.code)
    if (dataRs.type === enumData.RoleData.All.code) {
      /* do nothing */
    }
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    return await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { name: 'ASC' },
    })
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
