import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateMigra1752481947732 implements MigrationInterface {
  name = 'UpdateMigra1752481947732'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "shipmentFeeConditionTypeId"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "lstShipmentFeeConditionTypeId" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "lstSettingCostCode" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "lstShipmentFeeConditionTypeCode" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "lstShipmentFeeConditionTypeCode"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "lstSettingCostCode"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP COLUMN "lstShipmentFeeConditionTypeId"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" ADD "shipmentFeeConditionTypeId" nvarchar(MAX)`)
  }
}
