import { <PERSON>umn, <PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>umn, <PERSON>To<PERSON>ne, OneToMany, PrimaryGeneratedColumn } from 'typeorm'
import { ShipmentFeeConditionsEntity } from './shipmentFeeConditions.entity'
import { ShipmentFeeConditionsListEntity } from './shipmentFeeConditionsList.entity'
import { BaseEntity } from './base.entity'
import { RequestQuoteFeeEntity } from './requestQuoteFee.entity'

@Entity('shipment_fee_conditions_to_list')
export class ShipmentFeeConditionsToListEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  shipmentFeeConditionsListId: string
  @ManyToOne(() => ShipmentFeeConditionsListEntity, (p) => p.shipmentFeeConditionsToList)
  @JoinColumn({ name: 'shipmentFeeConditionsListId', referencedColumnName: 'id' })
  shipmentFeeConditionsList: Promise<ShipmentFeeConditionsListEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  shipmentFeeConditionsId: string
  @ManyToOne(() => ShipmentFeeConditionsEntity, (p) => p.shipmentFeeConditionsToList)
  @JoinColumn({ name: 'shipmentFeeConditionsId', referencedColumnName: 'id' })
  shipmentFeeConditions: Promise<ShipmentFeeConditionsEntity>

  @OneToMany(() => RequestQuoteFeeEntity, (p) => p.shipmentFeeConditionsToList)
  requestQuoteFees: Promise<RequestQuoteFeeEntity[]>
}
