import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnContract1752204648283 implements MigrationInterface {
    name = 'AddColumnContract1752204648283'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "latestDeliveryDate" datetime`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "bankNumber" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "bankUsername" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "bankName" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "bankBranchName" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "swiftCode" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "iban" nvarchar(250)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "iban"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "swiftCode"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "bankBranchName"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "bankName"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "bankUsername"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "bankNumber"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "latestDeliveryDate"`);
    }

}
