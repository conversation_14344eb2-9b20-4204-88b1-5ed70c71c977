import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'

import { BillLookupController } from './billLookup.controller'
import { BillLookupService } from './billLookup.service'
import { BillLookupRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BillLookupRepository]), OrganizationalPositionModule],
  controllers: [BillLookupController],
  providers: [BillLookupService],
})
export class BillLookupModule {}
