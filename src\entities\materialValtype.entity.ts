import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { MaterialEntity } from './material.entity'
import { PlantEntity } from './plant.entity'
import { CurrencyEntity } from './currency.entity'

@Entity('material_val_type')
export class MaterialValtypeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  materialCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  plantCode: string

  /** valuation Type */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  valuationType: string

  @Column({
    nullable: true,
    type: 'datetime',
  })
  lastChange: Date

  // ABC Indicator
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  abcIndicator: string

  // Valuation Class
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  valuationClass: string

  // Price Control
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  priceControl: string

  @Column({
    type: 'float',
    nullable: true,
  })
  price: number

  /** Currency */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  currencyCode: string

  /** MRP Type */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  mRPType: string

  /** Currency */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currencyId: string

  @ManyToOne(() => CurrencyEntity, (p) => p.materialValtypes)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  @Column({
    type: 'float',
    nullable: true,
  })
  priceUnit: number

  @ManyToOne(() => MaterialEntity, (p) => p.materialValtypes)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @ManyToOne(() => PlantEntity, (p) => p.materialValtypes)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>
}
