import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnContract1752635021698 implements MigrationInterface {
  name = 'AddColumnContract1752635021698'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract" ADD "fileUrl" nvarchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "fileUrl"`)
  }
}
