import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsOptional, IsString, IsBoolean, IsNumber } from 'class-validator'

export class RfqCreateDto {
  @ApiProperty() id: string
  @ApiProperty({ required: false }) supplierCode?: string
  @ApiProperty({ required: false }) type?: string
  @ApiProperty({ required: false }) supplierId?: string
  @ApiProperty({ required: false }) rfqQuantity?: number
  @ApiProperty({ required: false }) deliveryDate?: Date
  @ApiProperty({ required: false }) deadLine?: Date
  @ApiProperty({ required: false }) materialCode?: string
  @ApiProperty({ required: false }) materialId?: string
  @ApiProperty({ required: false }) netPrice?: number
  @ApiProperty({ required: false }) targetId?: string
  @ApiProperty({ required: false }) entityName?: string
  @ApiProperty({ required: false }) dealId?: string
  @ApiProperty({ required: false }) isSynchronizing?: boolean
  @ApiProperty({ required: false }) sapCode?: string
  @ApiProperty({ required: false }) code?: string
  @ApiProperty({ required: false }) companyId?: string
  @ApiProperty({ required: false }) requestQuoteId?: string
  @ApiProperty() collNo: string
  @ApiProperty({ required: false }) currencyId?: string
  @ApiProperty({ required: false }) incotermId?: string
  @ApiProperty({ required: false }) paymentTermId?: string

  @ApiProperty({ required: false }) yourRef?: string
  @ApiProperty({ required: false }) salesPer?: string
  @ApiProperty({ required: false }) ourRef?: string
  @ApiProperty({ required: false }) telephone?: string
  @ApiProperty({ required: false }) headerText?: string
  @ApiProperty({ required: false }) headerNote?: string
  @ApiProperty({ required: false }) pricingTypes?: string
  @ApiProperty({ required: false }) deadlines?: string
  @ApiProperty({ required: false }) termsDelivery?: string
  @ApiProperty({ required: false }) shippingInstructions?: string
  @ApiProperty({ required: false }) termsPayment?: string
  @ApiProperty({ required: false }) incoLoc1?: string
  @ApiProperty({ required: false }) incoLoc2?: string
  @ApiProperty({ required: false }) incoVer?: string
  @ApiProperty({ required: false }) targVal?: string

  @ApiProperty({ required: false }) warrantyDate?: Date
  @ApiProperty({ required: false }) bindgDate?: Date
  @ApiProperty({ required: false }) applyDate?: Date
  @ApiProperty({ required: false }) valStart?: Date
  @ApiProperty({ required: false }) valEnd?: Date
  @ApiProperty({ required: false }) submitDate?: Date

  details: CreateOrUpdateRfqDetailsDto[]

  purchasingOrgId: string

  purchasingGroupId: string

  plantId: string

  quoteDate: Date

  lstFee: any
}

export class CreateOrUpdateRfqDetailsDto {
  @IsOptional()
  @IsString()
  rfqId?: string

  @IsOptional()
  @IsString()
  materialCode?: string

  @IsOptional()
  @IsString()
  materialId?: string

  @IsOptional()
  @IsBoolean()
  isSynchronizing?: boolean

  @IsOptional()
  @IsNumber()
  rfqQuantity?: number

  @IsOptional()
  @IsString()
  dealId?: string

  @IsOptional()
  @Type(() => Date)
  deliveryDate?: Date

  @IsOptional()
  @IsString()
  supplierId?: string

  @IsOptional()
  @IsBoolean()
  isFinal?: boolean

  @IsOptional()
  @IsString()
  itemNo?: string

  @IsOptional()
  @IsString()
  tracking?: string

  @IsOptional()
  @IsNumber()
  remind1?: number

  @IsOptional()
  @IsNumber()
  remind2?: number

  @IsOptional()
  @IsNumber()
  remind3?: number

  @IsOptional()
  @IsNumber()
  remindNo?: number

  @IsOptional()
  @IsNumber()
  netPrice?: number

  @IsOptional()
  @IsNumber()
  priceUnit?: number

  @IsOptional()
  @IsString()
  unitId?: string

  @IsOptional()
  @IsString()
  quotComment?: string

  @IsOptional()
  @IsString()
  infoUpdate?: string

  @IsOptional()
  @IsString()
  tax?: string

  @IsOptional()
  @IsString()
  itemText?: string

  @IsOptional()
  @IsString()
  materialPoText?: string

  @IsOptional()
  @IsString()
  additionalText?: string

  @IsOptional()
  @IsString()
  stockInformationText?: string

  @IsOptional()
  @IsString()
  orderCode?: string

  @IsOptional()
  @IsString()
  orderName?: string

  @IsOptional()
  @IsString()
  assetCode?: string

  @IsOptional()
  @IsString()
  assetDesc?: string

  id?: string

  externalMaterialGroupId?: string

  materialGroupId?: string

  quantity: number
}
