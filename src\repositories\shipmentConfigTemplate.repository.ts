import { ShipmentConfigTemplateEntity } from '../entities/shipmentConfigTemplate.entity'
import { ShipmentConfigTemplateDetailEntity } from '../entities/shipmentConfigTemplateDetail.entity'
import { CustomRepository } from '../typeorm'
import { BaseRepository } from './base.repository'

@CustomRepository(ShipmentConfigTemplateEntity)
export class ShipmentConfigTemplateRepository extends BaseRepository<ShipmentConfigTemplateEntity> {}

@CustomRepository(ShipmentConfigTemplateDetailEntity)
export class ShipmentConfigTemplateDetailRepository extends BaseRepository<ShipmentConfigTemplateDetailEntity> {}
