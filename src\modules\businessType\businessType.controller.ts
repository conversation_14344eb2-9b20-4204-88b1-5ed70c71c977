import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { BusinessTypeService } from './businessType.service'
import { JwtAuthGuard } from '../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { BusinessTypeCreateDto, BusinessTypeUpdateDto } from './dto'
import { BusinessTypeImportExcelDto } from './dto/businessTypeExcel.dto'

@ApiBearerAuth()
@ApiTags('BusinessType')
@Controller('business_type')
export class BusinessTypeController {
  constructor(private readonly service: BusinessTypeService) {}

  @ApiOperation({ summary: 'Lấy danh sách loại hình doanh nghiệp' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto) {
    return await this.service.find(user)
  }

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách loại hình doanh nghiệp phân trang' })
  @UseGuards(JwtAuthGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo loại hình doanh nghiệp' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BusinessTypeCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Chỉnh sửa loại hình doanh nghiệp' })
  @UseGuards(JwtAuthGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BusinessTypeUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết loại hình doanh nghiệp' })
  @UseGuards(JwtAuthGuard)
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'import excel loại hình doanh nghiệp' })
  @UseGuards(JwtAuthGuard)
  @Post('import_data')
  public async importData(@CurrentUser() user: UserDto, @Body() data: BusinessTypeImportExcelDto[]) {
    return await this.service.importData(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật trạng thái isDelete' })
  @UseGuards(JwtAuthGuard)
  @Post('update_active_status')
  public async updateActiveStatus(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateActiveStatus(user, data)
  }
}
