import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateDetails1750731973468 implements MigrationInterface {
  name = 'UpdateDetails1750731973468'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "shipment_config_template" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_bbe631919175726d8cca521e6a3" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_801bca0006a1ce5c4cb07ac8018" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "name" varchar(255) NOT NULL, "description" nvarchar(max), "status" varchar(50) NOT NULL, CONSTRAINT "PK_bbe631919175726d8cca521e6a3" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "shipment_config_template_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_af301d5ca87e089af8d302b5223" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3330b31fea1d84a589894018c7e" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "shipmentConfigTemplateId" uniqueidentifier, "shipmentConditionTypeId" uniqueidentifier, CONSTRAINT "PK_af301d5ca87e089af8d302b5223" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "shipment_config_template_detail" ADD CONSTRAINT "FK_f4333754b6d72fce2ca4019cd97" FOREIGN KEY ("shipmentConfigTemplateId") REFERENCES "shipment_config_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "shipment_config_template_detail" ADD CONSTRAINT "FK_7b810f98ea0e69b1c8144475b7d" FOREIGN KEY ("shipmentConditionTypeId") REFERENCES "shipment_condition_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_config_template_detail" DROP CONSTRAINT "FK_7b810f98ea0e69b1c8144475b7d"`)
    await queryRunner.query(`ALTER TABLE "shipment_config_template_detail" DROP CONSTRAINT "FK_f4333754b6d72fce2ca4019cd97"`)
    await queryRunner.query(`DROP TABLE "shipment_config_template_detail"`)
    await queryRunner.query(`DROP TABLE "shipment_config_template"`)
  }
}
