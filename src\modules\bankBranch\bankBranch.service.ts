import { Injectable } from '@nestjs/common'
import { FindOptionsWhere, In, Like, Not } from 'typeorm'
import { coreHelper } from '../../helpers'
import {
  CREATE_SUCCESS,
  enumData,
  ERROR_CODE_TAKEN,
  ERROR_NOT_FOUND_DATA,
  IMPORT_SUCCESS,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { BankBranchEntity, BankEntity, CountryEntity, RegionEntity } from '../../entities'
import { BankBranchRepository, BankRepository, CountryRepository, RegionRepository } from '../../repositories'
import { BankBranchCreateDto, BankBranchUpdateDto, BankBranchImportExcelDto } from './dto'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'

@Injectable()
export class BankBranchService {
  constructor(
    readonly repo: BankBranchRepository,
    readonly bankRepo: BankRepository,
    readonly countryRepo: CountryRepository,
    readonly regionRepo: RegionRepository,
    private organizationalPositionService: OrganizationalPositionService,
  ) {}

  /** Lấy ds Chi nhánh/Phòng giao dịch Ngân hàng */
  public async find() {
    return await this.repo.find({ where: { isDeleted: false }, order: { name: 'ASC' } })
  }

  /** Lấy ds Chi nhánh/Phòng giao dịch Ngân hàng có phân trang */
  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: FindOptionsWhere<BankBranchEntity> = {}
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.BankBranch.code)
    if (dataRs.type === enumData.RoleData.All.code) {
      /* do nothing */
    }
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where.name) {
      whereCon.name = Like(`%${data.where.name}%`)
    }
    if (data.where.code) {
      whereCon.code = Like(`%${data.where.code}%`)
    }
    if (data.where.bankId) {
      whereCon.bank = { id: data.where.bankId }
    }
    if (data.where.countryId) {
      whereCon.country = { id: data.where.countryId }
    }
    if (data.where.regionId) {
      whereCon.region = { id: data.where.regionId }
    }
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: { bank: true, country: true, region: true },
    })

    if (res[0].length == 0) return [[], 0]
    for (const item of res[0]) {
      item.bankCode = item.__bank__?.code
      item.bankName = item.__bank__?.name
      item.countryCode = item.__country__?.code
      item.countryName = item.__country__?.name
      item.regionCode = item.__region__?.code
      item.regionName = item.__region__?.name
      delete item.__bank__
      delete item.__country__
      delete item.__region__
    }

    return res
  }

  /** Tạo mới một Chi nhánh/Phòng giao dịch Ngân hàng với dữ liệu được cung cấp. */
  async createData(user: UserDto, data: BankBranchCreateDto) {
    const checkExistBank = await this.bankRepo.find({ where: { id: data.bankId }, select: { id: true, code: true } })
    if (!checkExistBank) throw new Error(ERROR_NOT_FOUND_DATA)

    const checkExistRegion = await this.regionRepo.exists({ where: { id: data.regionId }, select: { id: true } })
    if (!checkExistRegion) throw new Error(ERROR_NOT_FOUND_DATA)

    // const codePrefix = checkExistBank[0].code + '_' + data.areaCode

    // const objData = await this.repo.findOne({
    //   where: { code: Like(`${codePrefix}%`) },
    //   order: { code: 'DESC' },
    // })

    // let sortString = '001'
    // if (objData) {
    //   const lastSortString = objData.code.substring(codePrefix.length)
    //   const lastSort = parseInt(lastSortString, 10)

    //   sortString = ('000' + (lastSort + 1)).slice(-3)
    // }

    // const newCode = codePrefix + sortString

    const bankBranchNew = new BankBranchEntity()
    bankBranchNew.code = data.code
    bankBranchNew.name = data.name
    bankBranchNew.countryId = data.countryId
    bankBranchNew.bankId = data.bankId
    bankBranchNew.regionId = data.regionId
    bankBranchNew.address = data.address
    bankBranchNew.description = data.description
    bankBranchNew.swift = data.swift
    bankBranchNew.accountNumber = data.accountNumber
    bankBranchNew.bankBranch = data.bankBranch
    bankBranchNew.bankGroupType = data.bankGroupType
    bankBranchNew.areaCode = data.areaCode
    bankBranchNew.postbank = data.postbank
    bankBranchNew.createdAt = new Date()
    bankBranchNew.createdBy = user.id
    await this.repo.insert(bankBranchNew)

    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật thông tin của Chi nhánh/Phòng giao dịch Ngân hàng với dữ liệu được cung cấp. */
  async updateData(user: UserDto, data: BankBranchUpdateDto) {
    const checkExistRegion = await this.regionRepo.exists({ where: { id: data.regionId }, select: { id: true } })
    if (!checkExistRegion) throw new Error(ERROR_NOT_FOUND_DATA)

    const bankBranch = await this.repo.findOne({ where: { id: data.id } })
    if (!bankBranch) throw new Error(ERROR_NOT_FOUND_DATA)

    bankBranch.name = data.name
    bankBranch.countryId = data.countryId
    bankBranch.regionId = data.regionId
    bankBranch.bankId = data.bankId
    bankBranch.address = data.address
    bankBranch.description = data.description
    bankBranch.swift = data.swift
    bankBranch.accountNumber = data.accountNumber
    bankBranch.bankBranch = data.bankBranch
    bankBranch.bankGroupType = data.bankGroupType
    bankBranch.areaCode = data.areaCode
    bankBranch.postbank = data.postbank
    bankBranch.updatedBy = user.id
    bankBranch.updatedAt = new Date()

    await this.repo.update(bankBranch.id, bankBranch)

    return { message: UPDATE_SUCCESS }
  }

  /** Cập nhật trạng thái kích hoạt của Chi nhánh/Phòng giao dịch Ngân hàng. */
  async updateActive(user: UserDto, id: string) {
    const entity = await this.repo.findOne({
      where: { id: id },
      select: { id: true, isDeleted: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const newIsDeleted = !entity.isDeleted

    await this.repo.update(id, {
      isDeleted: newIsDeleted,
      updatedBy: user.id,
      updatedAt: new Date(),
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /**Hàm import excel */
  public async importData(user: UserDto, data: BankBranchImportExcelDto[]) {
    //check xem data có trùng nhau hoặc không có không
    if (data.length < 1) throw new Error('Vui lòng điền ít nhất 1 dòng dữ liệu!')

    // Check for duplicates within the imported data
    const duplicateArrayCode = coreHelper.findDuplicates(data, 'code')
    if (duplicateArrayCode.length > 0) {
      throw new Error(`Danh sách mã chi nhánh ngân hàng trùng nhau: ${duplicateArrayCode}`)
    }

    // Extract list of codes
    const codes = data.map((obj) => obj.code)

    for (const item of codes) {
      const existingDepartments = await this.repo.find({ where: { code: item } })
      if (existingDepartments.length > 0) {
        const existingCodes = existingDepartments.map((c) => c.code)
        throw new Error(`Danh sách chi nhánh ngân hàng đã tồn tại trong hệ thống: ${existingCodes}`)
      }
    }

    const lstTask = []
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BankBranchEntity)
      const countryRepo = trans.getRepository(CountryEntity)
      const regionRepo = trans.getRepository(RegionEntity)
      const bankRepo = trans.getRepository(BankEntity)

      const setCountryCode = new Set<string>()
      const setRegionCode = new Set<string>()
      const setBankCode = new Set<string>()
      for (const item of data) {
        item.code = item.code?.toString()
        if (item.countryCode) setCountryCode.add(item.countryCode)
        if (item.regionCode) setRegionCode.add(item.regionCode)
        if (item.bankCode) setBankCode.add(item.bankCode)
      }
      const dictBankBranch: any = {}
      {
        const lstBankBranch = await repo.find({ where: {} })
        lstBankBranch.forEach((c) => (dictBankBranch[c.code] = c.id))
      }
      const dictCountry = new Map()
      if (setCountryCode.size > 0) {
        const country = await countryRepo.find({ where: { code: In([...setCountryCode]), isDeleted: false } })
        country.forEach((c) => dictCountry.set(c.code, c))
      }

      // danh sách tỉnh thành phố
      const dictRegion = new Map()
      if (setRegionCode.size > 0) {
        const region = await regionRepo.find({ where: { code: In([...setRegionCode]), isDeleted: false } })
        region.forEach((c) => dictRegion.set(c.code, c))
      }

      const dictBank = new Map()
      if (setBankCode.size > 0) {
        const bank = await bankRepo.find({ where: { code: In([...setBankCode]), isDeleted: false } })
        bank.forEach((c) => dictBank.set(c.code, c))
      }

      const set = new Set()
      const usedCodes = new Set<string>()

      for (const item of data) {
        if (item.countryCode) item.countryCode = item.countryCode?.toString()
        if (item.regionCode) item.regionCode = item.regionCode?.toString()
        if (item.bankCode) item.bankCode = item.bankCode?.toString()

        const country = dictCountry.get(item.countryCode)
        const region = dictRegion.get(item.regionCode)
        const bank = dictBank.get(item.bankCode)

        let bankId = null
        if (item.bankCode) {
          if (!bank) throw new Error(`Mã ngân hàng [${item.bankCode}] không có trong hệ thống`)
          bankId = bank.id
        }
        let countryId = null
        if (item.countryCode) {
          if (!country) throw new Error(`Mã quốc gia [${item.countryCode}] không có trong hệ thống`)
          countryId = country.id
        }
        let regionId = null
        if (item.regionCode) {
          if (!region) throw new Error(`Mã tỉnh thành [${item.regionCode}] không có trong hệ thống!`)
          if (region.countryId != country?.id) {
            throw new Error(`[Mã tỉnh thành [${item.regionCode}] không tồn tại trong quốc gia [${item.countryCode}] ]`)
          }
          regionId = region.id
        }

        // const codePrefix = item.bankCode + '_' + item.regionCode

        // const objData: any = await repo.findOne({
        //   where: { code: Like(`${codePrefix}%`) },
        //   order: { code: 'DESC' },
        // })

        // let sortString = '001'
        // if (objData) {
        //   const lastSortString = objData.code.substring(codePrefix.length)
        //   const lastSort = parseInt(lastSortString, 10)

        //   sortString = ('000' + (lastSort + 1)).slice(-3)
        // }

        // let newCode = codePrefix + sortString

        // usedCodes.add(newCode)
        const bankBranchNew = new BankBranchEntity()
        bankBranchNew.createdBy = user.id
        bankBranchNew.createdAt = new Date()
        bankBranchNew.countryId = countryId
        bankBranchNew.regionId = regionId
        bankBranchNew.bankId = bankId
        bankBranchNew.code = item.code
        bankBranchNew.name = item.name
        bankBranchNew.address = item.address
        bankBranchNew.swift = item.swift
        bankBranchNew.accountNumber = item.accountNumber?.toString()
        bankBranchNew.bankBranch = item.bankBranch
        bankBranchNew.bankGroupType = item.bankGroupType
        bankBranchNew.areaCode = item.regionCode
        bankBranchNew.postbank = item.postbank
        lstTask.push(bankBranchNew)
      }

      const lstChild = coreHelper.splitArrayByParameters(lstTask, 2000)
      for (const chunk of lstChild) {
        await trans.insert(BankBranchEntity, chunk)
      }
    })
    return { message: CREATE_SUCCESS }
  }

  async loadDataSelect(user: UserDto, data: { countryId?: string; regionId?: string; bankId?: string }) {
    const whereCon: any = { isDeleted: false }
    if (data?.bankId) whereCon.bankId = data?.bankId
    if (data?.countryId) whereCon.countryId = data?.countryId
    if (data?.regionId) whereCon.regionId = data?.regionId
    return await this.repo.find({ where: whereCon, select: { id: true, code: true, name: true } })
  }

  /** Chi tiết kế hoạch */
  async findDetail(data: FilterOneDto) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: { country: true, region: true, bank: true },
    })
    res.countryName = res.__country__?.name
    res.bankName = res.__bank__?.name
    res.regionName = res.__region__?.name
    res.bankGroupTypeName = enumData.BankGroupType[res.bankGroupType]?.name

    delete res.__country__
    delete res.__region__
    delete res.__bank__

    return res
  }
}
