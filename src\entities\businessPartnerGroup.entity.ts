import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { SupplierNumberEntity, SupplierNumberRequestApproveEntity } from '.'

/** Business partner group */
@Entity('business_partner_group')
export class BusinessPartnerGroupEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'bigint',

    nullable: true,
  })
  startCode: number

  @Column({
    type: 'bigint',

    nullable: true,
  })
  endCode: number

  @OneToMany(() => SupplierEntity, (p) => p.businessPartnerGroup)
  supplier: Promise<SupplierEntity[]>

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.businessPartnerGroup)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>

  @OneToMany(() => SupplierNumberEntity, (p) => p.businessPartnerGroup)
  supplierNumbers: Promise<SupplierNumberEntity[]>
}
