import { BidSupplierShipmentValueEntity, ShipmentCostEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany, OneToOne } from 'typeorm'
import { ShipmentCostStageCostEntity } from './shipmentCostStageCost.entity'
import { BidShipmentPriceEntity } from './bidShipmentPrice.entity'
import { OfferSupplierShipmentValueEntity } from './offerSupplierShipmentValue.entity'

/** Thông tin shipment price*/
@Entity('shipment_cost_price')
export class ShipmentCostPriceEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  conditionType: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @Column({
    nullable: true,
  })
  amount: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  crcy: string

  @Column({
    nullable: true,
  })
  per: number

  @Column({
    nullable: true,
  })
  conditionValue: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  curr: string

  @Column({
    nullable: true,
  })
  cConDe: number

  @Column({
    nullable: true,
  })
  numCCo: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  shipmentCostId: string
  @ManyToOne(() => ShipmentCostEntity, (p) => p.prices)
  @JoinColumn({ name: 'shipmentCostId', referencedColumnName: 'id' })
  shipmentCost: Promise<ShipmentCostEntity>

  @OneToMany(() => BidShipmentPriceEntity, (p) => p.shipmentPrice)
  shipmentPrice: Promise<BidShipmentPriceEntity[]>

  @OneToMany(() => BidSupplierShipmentValueEntity, (p) => p.shipmentPrice)
  bidSupplierShipmentTechValue: Promise<BidSupplierShipmentValueEntity[]>

  @OneToMany(() => OfferSupplierShipmentValueEntity, (p) => p.shipmentPrice)
  offerSupplierShipmentTechValue: Promise<OfferSupplierShipmentValueEntity[]>

  /** Mối liên hệ với shipment cost stage cost*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentCostStageCostId: string

  @OneToOne(() => ShipmentCostStageCostEntity, (p) => p.shipmentCostPriceId)
  @JoinColumn({ name: 'shipmentCostStageCostId', referencedColumnName: 'id' })
  shipmentCostStageCost: Promise<ShipmentCostStageCostEntity>
}
