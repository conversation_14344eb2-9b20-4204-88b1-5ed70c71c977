import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeColums1752031630342 implements MigrationInterface {
  name = 'ChangeColums1752031630342'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "FK_1d3a1063b2a6c456a09f9de80e8"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "FK_4135ac52c6cb4b535b0e0245500"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "FK_d18a3e9ab08af307fbb7c619750"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" ALTER COLUMN "shipmentFeeConditionsId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" ALTER COLUMN "shipmentFeeConditionsListId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" ALTER COLUMN "shipmentFeeConditionsToListId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "request_quote_fee" ADD CONSTRAINT "FK_1d3a1063b2a6c456a09f9de80e8" FOREIGN KEY ("shipmentFeeConditionsId") REFERENCES "shipment_fee_conditions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_fee" ADD CONSTRAINT "FK_4135ac52c6cb4b535b0e0245500" FOREIGN KEY ("shipmentFeeConditionsListId") REFERENCES "shipment_fee_conditions_list"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_fee" ADD CONSTRAINT "FK_d18a3e9ab08af307fbb7c619750" FOREIGN KEY ("shipmentFeeConditionsToListId") REFERENCES "shipment_fee_conditions_to_list"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "FK_d18a3e9ab08af307fbb7c619750"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "FK_4135ac52c6cb4b535b0e0245500"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "FK_1d3a1063b2a6c456a09f9de80e8"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" ALTER COLUMN "shipmentFeeConditionsToListId" uniqueidentifier NOT NULL`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" ALTER COLUMN "shipmentFeeConditionsListId" uniqueidentifier NOT NULL`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" ALTER COLUMN "shipmentFeeConditionsId" uniqueidentifier NOT NULL`)
    await queryRunner.query(
      `ALTER TABLE "request_quote_fee" ADD CONSTRAINT "FK_d18a3e9ab08af307fbb7c619750" FOREIGN KEY ("shipmentFeeConditionsToListId") REFERENCES "shipment_fee_conditions_to_list"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_fee" ADD CONSTRAINT "FK_4135ac52c6cb4b535b0e0245500" FOREIGN KEY ("shipmentFeeConditionsListId") REFERENCES "shipment_fee_conditions_list"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_fee" ADD CONSTRAINT "FK_1d3a1063b2a6c456a09f9de80e8" FOREIGN KEY ("shipmentFeeConditionsId") REFERENCES "shipment_fee_conditions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }
}
