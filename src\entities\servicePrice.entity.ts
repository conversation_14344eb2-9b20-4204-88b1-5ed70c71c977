import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { ServiceEntity } from './service.entity'
import { ServicePriceListDetailEntity } from './servicePriceListDetail.entity'
import { BidPriceEntity } from './bidPrice.entity'
import { ServicePriceColValueEntity } from './servicePriceColValue.entity'

@Entity('service_price')
export class ServicePriceEntity extends BaseEntity {
  /** Số lượng */
  @Column({
    nullable: false,
    default: 0,
  })
  number: number

  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  /** Tên  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** <PERSON><PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** <PERSON><PERSON> cấu hình giá hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isSetup: boolean

  /** <PERSON><PERSON> template cơ cấu giá hay không */
  @Column({
    nullable: false,
    default: true,
  })
  isTemplate: boolean

  /** Kiểu dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unit: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',

    nullable: true,
  })
  currency: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  percent: number

  /** Cấp độ */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** Mô tả */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => ServicePriceEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: ServicePriceEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => ServicePriceEntity, (p) => p.parent)
  childs: Promise<ServicePriceEntity[]>

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Giá trị nhỏ nhất */
  @Column({
    nullable: true,
  })
  requiredMin: number

  /** Con - 1 công thức sẽ có thể có nhiều detail list */
  @OneToMany(() => ServicePriceListDetailEntity, (p) => p.servicePrice)
  servicePriceListDetails: Promise<ServicePriceListDetailEntity[]>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  serviceId: string
  /** 1 công thức chỉ có 1 dịch vụ cha */
  @ManyToOne(() => ServiceEntity, (p) => p.prices)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  @OneToMany(() => BidPriceEntity, (p) => p.servicePrice)
  bidPrices: Promise<BidPriceEntity[]>

  @OneToMany(() => ServicePriceColValueEntity, (p) => p.servicePrice)
  servicePriceColValues: Promise<ServicePriceColValueEntity[]>
}
