import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateComment1749115453456 implements MigrationInterface {
  name = 'UpdateComment1749115453456'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "comment" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0b0e4bbc8415ec426f87f3a88e2" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_5657ae1e2ec01ed4c59386e55b9" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "comment" nvarchar(255) CONSTRAINT "DF_aaa8cc745b2dd540f94fa2ca933" DEFAULT 0, "subId" varchar(255) NOT NULL, "entityName" varchar(255) NOT NULL, "employeeId" varchar(255) NOT NULL, CONSTRAINT "PK_0b0e4bbc8415ec426f87f3a88e2" PRIMARY KEY ("id"))`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "comment"`)
  }
}
