import { Injectable, NotFoundException, NotAcceptableException, UnauthorizedException } from '@nestjs/common'
import { ERROR_YOU_DO_NOT_HAVE_PERMISSION, ERROR_NOT_FOUND_DATA, UPDATE_SUCCESS } from '../../constants'
import { EmailService } from '../email/email.service'
import {
  ServiceRepository,
  BidRepository,
  BidCustomPriceRepository,
  BidTechRepository,
  BidTradeRepository,
  BidPriceRepository,
  BidPriceColRepository,
  BidSupplierRepository,
  BidSupplierPriceRepository,
  BidPrItemRepository,
  BidShipmentPriceRepository,
  BidDealRepository,
  BidAuctionRepository,
} from '../../repositories'
import * as moment from 'moment'
import { apiHelper, coreHelper } from '../../helpers'
import { enumData } from '../../constants/enumData'
import { PaginationDto, UserDto } from '../../dto'
import { In, IsNull, Like, Not, Raw } from 'typeorm'
import { SupplierCreateTechItemDto, SupplierCreateTradeItemDto, SupplierCreatePriceItemDto, SupplierCreateCustomPriceItemDto } from './dto'
import {
  BidEntity,
  BidSupplierCustomPriceValueEntity,
  BidSupplierEntity,
  BidSupplierPriceColValueEntity,
  BidSupplierPriceEntity,
  BidSupplierPriceValueEntity,
  BidSupplierShipmentValueEntity,
  BidSupplierTechValueEntity,
  BidSupplierTradeValueEntity,
  EmailTemplateEntity,
  EmployeeWarningEntity,
  UserEntity,
} from '../../entities'

import { BidPrItemEntity } from '../../entities/bidPrItem.entity'
import { BidExMatGroupRepository } from '../../repositories/bid.repository'

@Injectable()
export class BidClientService {
  constructor(
    private readonly repo: BidRepository,
    private readonly emailService: EmailService,
    private readonly serviceRepo: ServiceRepository,

    private readonly bidTechRepo: BidTechRepository,
    private readonly bidPriceShipmentRepo: BidShipmentPriceRepository,
    private readonly bidDealRepository: BidDealRepository,
    private readonly bidAuctionRepository: BidAuctionRepository,
    private readonly bidTradeRepo: BidTradeRepository,
    private readonly bidPriceRepo: BidPriceRepository,
    private readonly bidPriceColRepo: BidPriceColRepository,
    private readonly bidCustomPriceRepo: BidCustomPriceRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly bidPrItemRepository: BidPrItemRepository,
    private readonly bidExMatGroupRepository: BidExMatGroupRepository,
  ) {}

  //#region Trang Client

  async paginationHomePage(req: Request, data: PaginationDto, lan?: string) {
    const domain = await apiHelper.getDomain(req)
    return await this.paginationHomePageHadToken({ domain }, data, lan)
  }

  async paginationHomePageHadToken(user: any, data: PaginationDto, lan?: string) {
    const lstBidStatus = [
      enumData.BidStatus.DangNhanBaoGia.code, // Đang nhận  báo giá
      enumData.BidStatus.DangDanhGia.code, // Đanh đánh giá
      enumData.BidStatus.DangDuyetDanhGia.code, // Đang duyệt đánh giá
      enumData.BidStatus.HoanTatDanhGia.code, // Hoàn tất đánh giá thầu
      enumData.BidStatus.DangDamPhanGia.code, // Đang đàm phán giá
      enumData.BidStatus.DongDauGia.code, // Đóng đấu giá
      enumData.BidStatus.DangDauGia.code, // Đang đấu giá
      enumData.BidStatus.DongDamPhanGia.code, // Đóng đàm phán giá
      enumData.BidStatus.DongThau.code, // Đang duyệt kết quả
      enumData.BidStatus.DuyetNCCThangThau.code, // Đã duyệt Doanh nghiệp thắng thầu
      enumData.BidStatus.DangDuyetKetThucThau.code, // Đang duyệt kết thúc thầu
      enumData.BidStatus.HoanTat.code, // Hoàn tất
      enumData.BidStatus.Huy.code, // Huỷ
    ]
    const whereCommon: any = { parentId: IsNull(), status: In(lstBidStatus), isDeleted: false }
    if (data.where.serviceId) {
      const whereCommonS: any = { isLast: true, isDeleted: false }
      const lstService = await this.serviceRepo.find({
        where: [
          { ...whereCommonS, id: data.where.serviceId },
          { ...whereCommonS, parentId: data.where.serviceId },
          { ...whereCommonS, parent: { parentId: data.where.serviceId } },
          { ...whereCommonS, parent: { parent: { parentId: data.where.serviceId } } },
        ],
        select: { id: true },
      })
      if (lstService.length == 0) return [[], 0]
      const lstServiceId = lstService.map((c) => c.id)
      whereCommon.bidPrItem = { serviceId: In(lstServiceId) }
    }
    let whereCon = whereCommon
    if (data.where.externalMaterialGroupId) {
      whereCommon.exMatGroupId = data.where.externalMaterialGroupId
    }
    if (user.supplierId) {
      whereCon = [
        { ...whereCommon, isShowHomePage: true },
        { ...whereCommon, isShowHomePage: false, bidSuppliers: { supplierId: user.supplierId, isDeleted: false } },
      ]
    } else whereCon.isShowHomePage = true
    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip > 0 ? data.skip : 0,
      take: data.take > 1000 ? 10 : data.take,
      order: { startBidDate: 'DESC' },
      select: {
        id: true,
        code: true,
        name: true,
        status: true,
        submitEndDate: true,
        startBidDate: true,
        companyInvite: true,

        isShowHomePage: true,
      },
    })

    res[0] = await this.repo.translate(res[0], lan)
    if (res[0].length == 0) return res

    // Đang phát hành
    // Mặc định
    // Đã mở thầu
    const lstStatus1 = [
      enumData.BidStatus.DangDanhGia.code,
      enumData.BidStatus.DangDuyetDanhGia.code,
      enumData.BidStatus.HoanTatDanhGia.code,
      enumData.BidStatus.DangDamPhanGia.code,
      enumData.BidStatus.DongDamPhanGia.code,
      enumData.BidStatus.DangDauGia.code,
      enumData.BidStatus.DongDauGia.code,
      enumData.BidStatus.DongThau.code,
      enumData.BidStatus.DuyetNCCThangThau.code,
      enumData.BidStatus.DangDuyetKetThucThau.code,
    ]
    // Đóng thầu
    const lstStatus2 = [enumData.BidStatus.HoanTat.code, enumData.BidStatus.Huy.code]
    let lstBidSupplier: any[] = []
    if (user.supplierId) {
      const lstId = res[0].map((c) => c.id)
      lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: In(lstId), supplierId: user.supplierId, status: Not(In(lstStatus2)), isDeleted: false },
        select: { id: true, bidId: true },
      })
    }

    for (const item of res[0]) {
      if (user.supplierId) {
        item.isAllowViewDetail = lstBidSupplier.some((c) => c.bidId == item.id)
      }

      const date = new Date(item.startBidDate)
      item.day = date.getDate()
      item.month = date.getMonth() + 1
      item.begin_time = new Date(item.submitEndDate)
      item.end_time = new Date(item.startBidDate)

      item.submit = 0
      if (lstStatus1.includes(item.status)) item.submit = 1
      if (lstStatus2.includes(item.status)) item.submit = 2
    }

    return res
  }

  /** Lấy data để bidding */
  async loadDataBidding(data: { bidId: string }, user: UserDto) {
    if (!user.supplierId) throw new UnauthorizedException('Phiên đăng nhập hết hạn, vui lòng đăng nhập và thử lại!')

    const res: any = await this.bidSupplierRepo.findOne({
      where: { bidId: data.bidId, supplierId: user.supplierId, companyId: user.companyId },
      relations: { bid: { bidPrItem: { service: true } } },
    })
    if (!res) throw new Error('Bạn không được mời tham gia gói thầu, vui lòng kiểm tra lại!')

    const setType = new Set()
    setType.add(enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code)
    setType.add(enumData.BidSupplierStatus.DangDanhGia.code)
    setType.add(enumData.BidSupplierStatus.DaDanhGia.code)

    const bid = res.__bid__
    delete res.__bid__
    const lstChild = await bid.__bidPrItem__.filter((c) => !c.isDeleted)
    res.bidCode = bid.code
    res.bidName = bid.name
    res.listItem = []
    for (const item of lstChild) {
      const bidSupplierItem = await this.bidSupplierRepo.findOne({
        where: { bidId: item.bidId, supplierId: user.supplierId, companyId: user.companyId },
        select: { id: true, status: true },
      })
      if (!bidSupplierItem) continue

      const dataItem: any = {
        id: item.id,
        isExmatgroup: item.isExmatgroup,
        itemName: item.__service__?.code || 'Shipment' + ' - ' + item.__service__?.name || 'Thầu shipment',
        quantityItem: item.quantityItem,
        bidSupplierId: bidSupplierItem.id,
        isSubmitBid: false,
        statusName: 'Chưa bổ sung hồ sơ thầu',
      }
      if (setType.has(bidSupplierItem.status)) {
        dataItem.isSubmitBid = true
        dataItem.statusName = 'Đã bổ sung hồ sơ thầu'
      }
      // nếu như không có Pr thì lọc ra
      // const havePr = await bid.bidPr
      // nếu như không có Pr thì nó là bid theo exmatgr
      // if (havePr.length === 0) {
      //nếu như là bid theo exmatgr thì kiểm tra nó là isExmatgr thì push
      if (item.isExmatgroup) {
        res.listItem.push(dataItem)
      }
      // } else {
    }

    return res
  }

  async bidDetailHadToken(data: { bidId: string }, user: UserDto) {
    if (!user.supplierId) throw new UnauthorizedException('Phiên đăng nhập hết hạn, vui lòng đăng nhập và thử lại!')

    const res: any = await this.repo.findOne({
      // where: { id: data.bidId, isDeleted: false },
      where: { id: data.bidId, isDeleted: false },
      relations: { bidType: true, masterBidGuarantee: true, bidPrItem: { service: true } },
    })
    if (!res) throw new Error('Gói thầu không tồn tại, vui lòng kiểm tra lại link mời thầu!')

    if (res.status == enumData.BidStatus.HoanTat.code || res.status == enumData.BidStatus.Huy.code) {
      throw new Error('Gói thầu đã đóng!')
    }
    /* từ gói thâu tìm ra danh sách offer  */

    const offer = await this.bidDealRepository.findOne({
      where: { bidId: res.id, parentId: IsNull(), status: enumData.BidDealStatus.DangDamPhan.code },
    })

    /* tìm ra bidAuction */
    const bidAuction = await this.bidAuctionRepository.findOne({
      where: { bidId: res.id, parentId: IsNull(), status: enumData.BidAuctionStatus.DangDauGia.code },
    })

    if (offer) res.offerId = offer.id
    if (bidAuction) res.auctionId = bidAuction.id
    const bidSupplier = await this.bidSupplierRepo.findOne({
      // where: { supplierId: user.supplierId, bidId: res.id, companyId: user.companyId },
      where: { supplierId: user.supplierId, bidId: res.id, companyId: user.companyId },

      select: { id: true },
    })
    if (!bidSupplier) throw new Error('Bạn không được mời tham gia gói thầu, vui lòng kiểm tra lại!')

    res.bidTypeName = res.__bidType__?.name
    res.masterBidGuaranteeName = res.__masterBidGuarantee__?.name

    delete res.__bidType__
    delete res.__masterBidGuarantee__

    res.__bidPrItem__ = res.__bidPrItem__ || []

    res.listItem = []

    for (const item of res.__bidPrItem__) {
      res.listItem.push({
        itemName: item.shortText,
        quantityItem: item.quantityItem,
      })
    }

    // const havePr = await res.bidPr
    // // if (havePr.length === 0) {
    // const lstDataItem = []
    // res.listItem = []
    // for (const item of res.__bidPrItem__) {
    //   if (item.isExmatgroup) {
    //     const service = await item.service
    //     item.itemName = service?.code + ' - ' + service?.name
    //     lstDataItem.push(item)
    //   }
    // }
    // res.listItem = lstDataItem
    // }

    // delete res.__bidPrItem__

    return res
  }

  /** Hàm kiểm tra xem có hiển thị nút bổ sung hồ sơ không */
  async isDisplayBtnBid(bidId: string = '', user: UserDto) {
    // Kiểm tra gói thầu có đang nhận giá hay không
    const bid = await this.repo.findOne({
      where: { id: bidId, status: enumData.BidStatus.DangNhanBaoGia.code, companyId: user.companyId },
      select: { id: true, submitEndDate: true },
    })
    // Nếu không thì không hiện nút
    if (!bid) return false

    // Nếu hết hạn gửi hồ sơ thì không hiện nút
    const today = new Date()
    if (today > bid.submitEndDate) return false

    // Kiểm tra đã xác nhận tham gia thầu chưa
    const bidSupplier = await this.bidSupplierRepo.findOne({
      where: {
        supplierId: user.supplierId,
        bidId: bid.id,
        status: enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
        companyId: user.companyId,
      },
      select: { id: true },
    })
    if (!bidSupplier) return false

    return true
  }

  async isDisplayBtnAcceptBid(bidId: string = '', user: UserDto) {
    const bid = await this.repo.findOne({ where: { id: bidId, status: enumData.BidStatus.DangNhanBaoGia.code, companyId: user.companyId } })
    if (!bid) return false

    const today = new Date()
    if (today > bid.acceptEndDate) return false

    // Nếu gói thầu không công khai thì kiểm tra thêm Doanh nghiệp có được mời không!, nếu không thì không hiện
    if (!bid.isShowHomePage) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: { supplierId: user.supplierId, bidId: bid.id, status: enumData.BidSupplierStatus.DaThongBaoMoiThau.code, companyId: user.companyId },
      })
      if (!bidSupplier) return false
    }

    const lstStatus = [
      enumData.BidSupplierStatus.DaDuocChon.code,
      enumData.BidSupplierStatus.DaXacNhanKhongThamGiaThau.code,
      enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
      enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
      enumData.BidSupplierStatus.DangDanhGia.code,
      enumData.BidSupplierStatus.DaDanhGia.code,
    ]
    // Kiểm tra Doanh nghiệp đã xác nhận hoặc từ chối chưa, nếu có thì không hiện btn
    const bidSupplier = await this.bidSupplierRepo.findOne({
      where: { supplierId: user.supplierId, bidId: bid.id, status: In(lstStatus), companyId: user.companyId },
    })
    if (bidSupplier) return false

    return true
  }

  async acceptBid(bidId: string = '', user: UserDto) {
    const bid = await this.repo.findOne({ where: { id: bidId, status: In([enumData.BidStatus.DangNhanBaoGia.code]), companyId: user.companyId } })

    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    const today = new Date()
    if (today > bid.acceptEndDate) {
      throw new Error('Hết hạn xác nhận tham gia đấu thầu.')
    }

    const check = await this.isDisplayBtnAcceptBid(bidId, user)

    if (!check) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // Nếu gói thầu hạn chế thì cập nhật trạng thái
    if (!bid.isShowHomePage) {
      await this.bidSupplierRepo.update(
        { bidId, supplierId: user.supplierId },
        {
          status: enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
          statusTech: enumData.BidSupplierTechStatus.DangBoSung.code,
          statusTrade: enumData.BidSupplierTradeStatus.DangBoSung.code,
          statusPrice: enumData.BidSupplierPriceStatus.DangBoSung.code,
          updatedBy: user.id,
        },
      )
    } else {
      // Nếu gói thầu công khai thì kiểm tra xem đã được mời chưa
      const check = await this.bidSupplierRepo.findOne({ where: { bidId, supplierId: user.supplierId, companyId: user.companyId } })

      // Nếu đã được mời thì cập nhật
      if (check) {
        await this.bidSupplierRepo.update(
          { bidId, supplierId: user.supplierId },
          {
            status: enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
            statusTech: enumData.BidSupplierTechStatus.DangBoSung.code,
            statusTrade: enumData.BidSupplierTradeStatus.DangBoSung.code,
            statusPrice: enumData.BidSupplierPriceStatus.DangBoSung.code,
            updatedBy: user.id,
          },
        )
      } else {
        // Nếu chưa được mời thì thêm mới
        const entity = this.bidSupplierRepo.create({
          companyId: user.companyId,
          createdBy: user.id,
          bidId,
          supplierId: user.supplierId,
          status: enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
          statusFile: enumData.BidSupplierFileStatus.ChuaKiemTra.code,
          statusTech: enumData.BidSupplierTechStatus.DangBoSung.code,
          statusTrade: enumData.BidSupplierTradeStatus.DangBoSung.code,
          statusPrice: enumData.BidSupplierPriceStatus.DangBoSung.code,
        })
        await this.bidSupplierRepo.save(entity)
      }
    }

    await this.emailService.GuiMpoNccXacNhanThamGiaThau(bidId, user.supplierId)

    return { message: UPDATE_SUCCESS }
  }

  async rejectBid(bidId: string = '', user: UserDto) {
    const bid = await this.repo.findOne({ where: { id: bidId, status: In([enumData.BidStatus.DangNhanBaoGia.code]), companyId: user.companyId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    const today = new Date()
    if (today > bid.acceptEndDate) {
      throw new Error('Hết hạn xác nhận tham gia đấu thầu.')
    }

    const check = await this.isDisplayBtnAcceptBid(bidId, user)

    if (!check) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // Nếu gói thầu hạn chế thì cập nhật trạng thái
    if (!bid.isShowHomePage) {
      await this.bidSupplierRepo.update(
        { bidId, supplierId: user.supplierId },
        {
          status: enumData.BidSupplierStatus.DaXacNhanKhongThamGiaThau.code,
          statusTech: enumData.BidSupplierTechStatus.KhongXacNhan.code,
          statusTrade: enumData.BidSupplierTradeStatus.KhongXacNhan.code,
          statusPrice: enumData.BidSupplierPriceStatus.KhongXacNhan.code,
          updatedBy: user.id,
        },
      )
    } else {
      // Nếu gói thầu công khai thì kiểm tra xem đã được mời chưa
      const check = await this.bidSupplierRepo.findOne({ where: { bidId, supplierId: user.supplierId, companyId: user.companyId } })

      // Nếu đã được mời thì cập nhật
      if (check) {
        await this.bidSupplierRepo.update(
          { bidId, supplierId: user.supplierId },
          {
            status: enumData.BidSupplierStatus.DaXacNhanKhongThamGiaThau.code,
            statusTech: enumData.BidSupplierTechStatus.KhongXacNhan.code,
            statusTrade: enumData.BidSupplierTradeStatus.KhongXacNhan.code,
            statusPrice: enumData.BidSupplierPriceStatus.KhongXacNhan.code,
            updatedBy: user.id,
          },
        )
      } else {
        // Nếu chưa được mời thì thêm mới
        const entity = this.bidSupplierRepo.create({
          companyId: user.companyId,
          createdBy: user.id,
          bidId,
          supplierId: user.supplierId,
          status: enumData.BidSupplierStatus.DaXacNhanKhongThamGiaThau.code,
          statusTech: enumData.BidSupplierTechStatus.KhongXacNhan.code,
          statusTrade: enumData.BidSupplierTradeStatus.KhongXacNhan.code,
          statusPrice: enumData.BidSupplierPriceStatus.KhongXacNhan.code,
        })
        await this.bidSupplierRepo.save(entity)
      }
    }

    await this.emailService.GuiMpoNccTuChoiThamGiaThau(bidId, user.supplierId)

    return { message: UPDATE_SUCCESS }
  }

  async createBidSupplier(
    user: UserDto,
    data: {
      bidId: string
      techInfo: SupplierCreateTechItemDto[]
      tradeInfo: SupplierCreateTradeItemDto[]
      priceInfo: any[]
      priceShipmentInfo: any[]

      customPriceInfo: SupplierCreateCustomPriceItemDto[]
    },
  ) {
    if (!user.supplierId) throw new UnauthorizedException('Phiên đăng nhập hết hạn, vui lòng đăng nhập và thử lại!')

    await this.createBidSupplierFromAdmin(user, { ...data, supplierId: user.supplierId })
  }

  async checkIsShipment(
    user: UserDto,
    data: {
      bidId: string
    },
  ) {
    const bid = await this.repo.findOne({ where: { id: data.bidId } })
    if (bid.biddingTypeCode === enumData.BiddingType.SHIPPING.code) {
      return true
    } else {
      return false
    }
  }

  async createBidSupplierFromAdmin(
    user: UserDto,
    data: {
      bidId: string
      supplierId: string
      techInfo: SupplierCreateTechItemDto[]
      tradeInfo: SupplierCreateTradeItemDto[]
      priceShipmentInfo: any[]
      priceInfo: any[]
      customPriceInfo: any[]
    },
  ) {
    const bidItem = await this.bidExMatGroupRepository.findOne({
      where: [{ bidId: data.bidId }, { id: data.bidId }],
    })
    if (!bidItem) throw new Error('Item không còn tồn tại!')

    const bid = await bidItem.bid

    const today = new Date()
    if (today > bid.submitEndDate) throw new Error('Hết hạn nộp hồ sơ thầu.')

    await this.repo.manager.transaction(async (manager) => {
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      // const bid = await bidItem.bid
      const bidSupplier = await bidSupplierRepo.findOne({
        where: { bidId: bidItem.bidId, supplierId: data.supplierId, isDeleted: false },
      })
      if (!bidSupplier) throw new Error(ERROR_NOT_FOUND_DATA)

      await bidSupplierRepo.update(bidSupplier.id, {
        status: enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
        statusTech: enumData.BidSupplierTechStatus.DangDanhGia.code,
        statusTrade: enumData.BidSupplierTradeStatus.DangDanhGia.code,
        statusPrice: enumData.BidSupplierPriceStatus.DangDanhGia.code,
        updatedBy: user.id,
      })
      //#region Save Tech
      const bidSupplierTechValueRepo = manager.getRepository(BidSupplierTechValueEntity)
      // Xóa hồ sơ nộp thầu cũ
      await bidSupplierTechValueRepo.delete({ bidSupplierId: bidSupplier.id })
      for (let index = 0; index < data.techInfo.length; index++) {
        const item = data.techInfo[index]

        if (item.value || item.__childs__?.length > 0) {
          const bidTechValue = new BidSupplierTechValueEntity()
          bidTechValue.companyId = user.companyId
          bidTechValue.createdBy = user.id
          bidTechValue.bidSupplierId = bidSupplier.id
          bidTechValue.bidTechId = item.bidTechId
          bidTechValue.value = item.value
          await bidSupplierTechValueRepo.save(bidTechValue)

          const lengthC = item.__childs__.length
          for (let i = 0; i < lengthC; i++) {
            const itemC = item.__childs__[i]
            if (itemC.value) {
              let bidTechValueC = new BidSupplierTechValueEntity()
              bidTechValueC.companyId = user.companyId
              bidTechValueC.createdBy = user.id
              bidTechValueC.bidSupplierId = bidSupplier.id
              bidTechValueC.bidTechId = itemC.bidTechId
              bidTechValueC.value = itemC.value
              await bidSupplierTechValueRepo.save(bidTechValueC)
            }
          }
        }
      }
      //#endregion

      //#region Save Trade
      const bidSupplierTradeValueRepo = manager.getRepository(BidSupplierTradeValueEntity)
      // Xóa hồ sơ nộp thầu cũ
      await bidSupplierTradeValueRepo.delete({ bidSupplierId: bidSupplier.id })

      for (let index = 0; index < data.tradeInfo.length; index++) {
        const item = data.tradeInfo[index]
        if (item.value || item.__childs__?.length > 0) {
          const bidTradeValue = new BidSupplierTradeValueEntity()
          bidTradeValue.companyId = user.companyId
          bidTradeValue.createdBy = user.id
          bidTradeValue.bidSupplierId = bidSupplier.id
          bidTradeValue.bidTradeId = item.bidTradeId
          bidTradeValue.value = item.value
          await bidSupplierTradeValueRepo.save(bidTradeValue)

          const lengthC = item.__childs__.length
          for (let i = 0; i < lengthC; i++) {
            const itemC = item.__childs__[i]
            if (itemC.value) {
              let bidTradeValueC = new BidSupplierTradeValueEntity()
              bidTradeValueC.companyId = user.companyId
              bidTradeValueC.createdBy = user.id
              bidTradeValueC.bidSupplierId = bidSupplier.id
              bidTradeValueC.bidTradeId = itemC.bidTradeId
              bidTradeValueC.value = itemC.value
              await bidSupplierTradeValueRepo.save(bidTradeValueC)
            }
          }
        }
      }
      //#endregion

      if (bid.biddingTypeCode !== enumData.BiddingType.SHIPPING.code) {
        //#region Save Price
        for (const exMat of data.priceInfo) {
          const bidSupplierPriceValueRepo = manager.getRepository(BidSupplierPriceValueEntity)
          const bidSupplierPriceColValueRepo = manager.getRepository(BidSupplierPriceColValueEntity)
          const bidSupplierPriceRepo = new BidSupplierPriceRepository(BidSupplierPriceEntity, manager)
          const lstBidPriceCol = (await exMat.bidPriceCol).filter((c) => !c.isDeleted)
          // Xóa lần nộp giá cuối
          await bidSupplierPriceRepo.delete({ bidSupplierId: bidSupplier.id })
          // Xóa hồ sơ nộp thầu cũ
          await bidSupplierPriceValueRepo.delete({ bidSupplierId: bidSupplier.id })
          // Xóa hồ sơ nộp thầu cũ
          await bidSupplierPriceColValueRepo.delete({ bidSupplierId: bidSupplier.id })

          exMat.priceInfo = exMat.listOfData
          // lv1
          for (let index = 0; index < exMat.priceInfo.length; index++) {
            const item = exMat.priceInfo[index] as any
            item.submitDate = new Date()
            item.submitType = 0
            item.level = 1

            for (const col of lstBidPriceCol) {
              if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
                const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, item)
                if (value != null) {
                  const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                  bidSupplierPriceColValue.companyId = user.companyId
                  bidSupplierPriceColValue.createdBy = user.id
                  bidSupplierPriceColValue.value = value
                  bidSupplierPriceColValue.bidPriceId = item.id
                  bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                  bidSupplierPriceColValue.bidPriceColId = col.id
                  await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                  item[col.id] = value
                }
              } else {
                if (item[col.id]) {
                  const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                  bidSupplierPriceColValue.companyId = user.companyId
                  bidSupplierPriceColValue.createdBy = user.id
                  bidSupplierPriceColValue.value = item[col.id]
                  bidSupplierPriceColValue.bidPriceId = item.id
                  bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                  bidSupplierPriceColValue.bidPriceColId = col.id
                  await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                }
              }
            }

            if (exMat.fomular && exMat.fomular.length > 0) {
              item.value = await coreHelper.calFomular(exMat.fomular, lstBidPriceCol, item)
            }

            if (item.value) {
              const bidPriceValue = new BidSupplierPriceValueEntity()
              bidPriceValue.companyId = user.companyId
              bidPriceValue.createdBy = user.id
              bidPriceValue.bidSupplierId = bidSupplier.id
              bidPriceValue.value = item.value
              bidPriceValue.bidPriceId = item.id
              bidPriceValue.name = item.name
              bidPriceValue.unit = item.unit
              bidPriceValue.currency = item.currency
              bidPriceValue.number = item.number
              await bidSupplierPriceValueRepo.save(bidPriceValue)

              await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, item)
            }

            // lv2
            if (item.__childs__?.length > 0) {
              var priceInfoLv2 = item.__childs__
              for (let index2 = 0; index2 < priceInfoLv2.length; index2++) {
                const itemLv2 = priceInfoLv2[index2] as any
                itemLv2.submitDate = today
                itemLv2.submitType = 0
                itemLv2.level = 2

                for (const col of lstBidPriceCol) {
                  if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
                    const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, itemLv2)
                    if (value != null) {
                      const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                      bidSupplierPriceColValue.companyId = user.companyId
                      bidSupplierPriceColValue.createdBy = user.id
                      bidSupplierPriceColValue.value = value
                      bidSupplierPriceColValue.bidPriceId = itemLv2.id
                      bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                      bidSupplierPriceColValue.bidPriceColId = col.id
                      await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                      itemLv2[col.id] = value
                    }
                  } else {
                    if (itemLv2[col.id]) {
                      const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                      bidSupplierPriceColValue.companyId = user.companyId
                      bidSupplierPriceColValue.createdBy = user.id
                      bidSupplierPriceColValue.value = itemLv2[col.id]
                      bidSupplierPriceColValue.bidPriceId = itemLv2.id
                      bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                      bidSupplierPriceColValue.bidPriceColId = col.id
                      await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                    }
                  }
                }

                if (exMat.fomular && exMat.fomular.length > 0) {
                  itemLv2.value = await coreHelper.calFomular(exMat.fomular, lstBidPriceCol, itemLv2)
                }

                if (itemLv2.value) {
                  const bidPriceValue = new BidSupplierPriceValueEntity()
                  bidPriceValue.companyId = user.companyId
                  bidPriceValue.createdBy = user.id
                  bidPriceValue.bidSupplierId = bidSupplier.id
                  bidPriceValue.value = itemLv2.value
                  bidPriceValue.bidPriceId = itemLv2.id
                  bidPriceValue.name = itemLv2.name
                  bidPriceValue.unit = itemLv2.unit
                  bidPriceValue.currency = itemLv2.currency
                  bidPriceValue.number = itemLv2.number
                  await bidSupplierPriceValueRepo.save(bidPriceValue)

                  await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, itemLv2)
                }

                // lv3
                if (itemLv2.__childs__?.length > 0) {
                  var priceInfoLv3 = itemLv2.__childs__
                  for (let index3 = 0; index3 < priceInfoLv3.length; index3++) {
                    const itemLv3 = priceInfoLv3[index3] as any
                    itemLv3.submitDate = today
                    itemLv3.submitType = 0
                    itemLv3.level = 3

                    for (const col of lstBidPriceCol) {
                      if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
                        const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, itemLv3)
                        if (value != null) {
                          const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                          bidSupplierPriceColValue.companyId = user.companyId
                          bidSupplierPriceColValue.createdBy = user.id
                          bidSupplierPriceColValue.value = value
                          bidSupplierPriceColValue.bidPriceId = itemLv3.id
                          bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                          bidSupplierPriceColValue.bidPriceColId = col.id
                          await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                          itemLv3[col.id] = value
                        }
                      } else {
                        if (itemLv3[col.id]) {
                          const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                          bidSupplierPriceColValue.companyId = user.companyId
                          bidSupplierPriceColValue.createdBy = user.id
                          bidSupplierPriceColValue.value = itemLv3[col.id]
                          bidSupplierPriceColValue.bidPriceId = itemLv3.id
                          bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                          bidSupplierPriceColValue.bidPriceColId = col.id
                          await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                        }
                      }
                    }

                    if (exMat.fomular && exMat.fomular.length > 0) {
                      itemLv3.value = await coreHelper.calFomular(exMat.fomular, lstBidPriceCol, itemLv3)
                    }

                    if (itemLv3.value) {
                      const bidPriceValue = new BidSupplierPriceValueEntity()
                      bidPriceValue.companyId = user.companyId
                      bidPriceValue.createdBy = user.id
                      bidPriceValue.bidSupplierId = bidSupplier.id
                      bidPriceValue.value = itemLv3.value
                      bidPriceValue.bidPriceId = itemLv3.id
                      bidPriceValue.name = itemLv3.name
                      bidPriceValue.unit = itemLv3.unit
                      bidPriceValue.currency = itemLv3.currency
                      bidPriceValue.number = itemLv3.number
                      await bidSupplierPriceValueRepo.save(bidPriceValue)

                      await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, itemLv3)
                    }
                  }
                }
              }
            }
          }
          //#endregion

          //#region Save CustomPrice
        }

        for (const exGr of data.customPriceInfo) {
          const bidSupplierCustomPriceValueRepo = manager.getRepository(BidSupplierCustomPriceValueEntity)
          // Xóa hồ sơ nộp thầu cũ
          await bidSupplierCustomPriceValueRepo.delete({ bidSupplierId: bidSupplier.id })
          exGr.customPriceInfo = exGr.listOfData
          for (let index = 0; index < exGr.customPriceInfo.length; index++) {
            const item = exGr.customPriceInfo[index]
            if (item.value) {
              let bidCustomPriceValue = new BidSupplierCustomPriceValueEntity()
              bidCustomPriceValue.companyId = user.companyId
              bidCustomPriceValue.createdBy = user.id
              bidCustomPriceValue.bidSupplierId = bidSupplier.id
              bidCustomPriceValue.value = item.value
              bidCustomPriceValue.name = item.name
              bidCustomPriceValue.unit = item.unit
              bidCustomPriceValue.currency = item.currency
              bidCustomPriceValue.number = item.number
              bidCustomPriceValue.sort = item.sort
              await bidSupplierCustomPriceValueRepo.save(bidCustomPriceValue)
            }
          }
        }
        //#endregion
      } else {
        //#region Save CustomPrice

        const bidSupplierPriceShipmentRepoRepo = manager.getRepository(BidSupplierShipmentValueEntity)
        // Xóa hồ sơ nộp thầu cũ
        await bidSupplierPriceShipmentRepoRepo.delete({ bidSupplierId: bidSupplier.id })

        for (let index = 0; index < data.priceShipmentInfo.length; index++) {
          const item = data.priceShipmentInfo[index]
          if (item.value) {
            let bidShipmentPriceValue = new BidSupplierShipmentValueEntity()
            bidShipmentPriceValue.companyId = user.companyId
            bidShipmentPriceValue.createdBy = user.id
            bidShipmentPriceValue.bidSupplierId = bidSupplier.id
            bidShipmentPriceValue.value = item.value
            bidShipmentPriceValue.shipmentPriceId = item.shipmentPriceId

            await bidSupplierPriceShipmentRepoRepo.save(bidShipmentPriceValue)
          }
        }
        //#endregion
      }
      // throw new Error('Luư thành công , Hi hi')
    })

    await this.emailService.GuiMpoNccNopHoSoThau(data.bidId, data.supplierId)

    return { message: UPDATE_SUCCESS }
  }

  async checkPermissionLoadDataBid(bidId: string, user: UserDto) {
    return await this.isDisplayBtnBid(bidId, user)
  }

  async loadDataBidTech(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const bidSupplierTechValueRepo = this.repo.manager.getRepository(BidSupplierTechValueEntity)
    const arrayWhere: any[] = [{ bidId: data.bidId }]

    const listTech: any[] = await this.bidTechRepo.find({
      where: arrayWhere,
      relations: { bidTechListDetails: true, childs: { bidTechListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })
    const bideItemRepo = this.repo.manager.getRepository(BidEntity)
    const bid = await bideItemRepo.findOne({ where: [{ id: data.bidId }] })
    const supplierId = user.supplierId || data.supplierId
    if (supplierId && listTech.length > 0 && bid) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: { bidId: data.bidId, supplierId, isDeleted: false },
        select: { id: true, status: true },
      })
      if (!bidSupplier) return listTech

      // nếu đã nộp thầu => show các data đã nộp
      if (bidSupplier.status == enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code) {
        const dicValue: any = {}
        {
          const lstTechValue = await bidSupplierTechValueRepo.find({
            where: { bidSupplierId: bidSupplier.id, isDeleted: false },
          })
          lstTechValue.forEach((c) => (dicValue[c.bidTechId] = c.value))
        }

        for (const data1 of listTech) {
          data1.value = dicValue[data1.id] || ''
          for (const data2 of data1.__childs__) {
            data2.value = dicValue[data2.id] || ''
          }
        }
      }
    }

    return listTech
  }

  async loadDataTradeShipment(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const bidPriceShipmentValueRepo = this.repo.manager.getRepository(BidSupplierShipmentValueEntity)
    /* tìm ra gói thầu */
    const bideItemRepo = this.repo.manager.getRepository(BidPrItemEntity)
    const bidItem = await bideItemRepo.findOne({ where: { id: data.bidId } })

    const listPrice: any[] = await this.bidPriceShipmentRepo.find({
      where: { bidId: bidItem.bidId, isDeleted: false },
      relations: { shipmentPrice: true },
    })

    const supplierId = user.supplierId || data.supplierId
    if (supplierId && listPrice.length > 0 && bidItem) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: { bidId: bidItem.bidId, supplierId, isDeleted: false },
        select: { id: true, status: true },
      })
      for (const data1 of listPrice) {
        data1.colz = data1.__shipmentPrice__.jsonPrice
        const jsonParse = JSON.parse(data1.colz)
        data1.conditionType = jsonParse.conditionType
        data1.description = jsonParse.description
        data1.amount = jsonParse.amount
        data1.crcy = jsonParse.crcy
        data1.per = jsonParse.per
        data1.conditionValue = jsonParse.conditionValue
        data1.curr = jsonParse.curr
        data1.numCCo = jsonParse.numCCo
        data1.cConDe = jsonParse.cConDe
      }
      if (!bidSupplier) return listPrice

      // nếu đã nộp thầu => show các data đã nộp
      if (bidSupplier.status == enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code) {
        const dicValue: any = {}
        {
          const lstTechValue = await bidPriceShipmentValueRepo.find({
            where: { bidSupplierId: bidSupplier.id, isDeleted: false },
          })
          lstTechValue.forEach((c) => (dicValue[c.shipmentPriceId] = c.value))
        }

        for (const data1 of listPrice) {
          data1.value = dicValue[data1.id] || ''
          for (const data2 of data1.__childs__) {
            data2.value = dicValue[data2.id] || ''
          }
        }
      }
    }

    return listPrice
  }

  async loadDataBidTrade(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const bidSupplierTradeValueRepo = this.repo.manager.getRepository(BidSupplierTradeValueEntity)
    const bideItemRepo = this.repo.manager.getRepository(BidPrItemEntity)
    let listTrade: any[] = await this.bidTradeRepo.find({
      where: [
        { bidId: data.bidId, isDeleted: false },
        { bidItemId: data.bidId, isDeleted: false },
      ],
      relations: { bidTradeListDetails: true, childs: { bidTradeListDetails: true } },
      order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
    })

    const bid = await bideItemRepo.findOne({ where: [{ bidId: data.bidId }, { id: data.bidId }] })
    // if (listTrade.length === 0) listTrade = await bid.trades
    const supplierId = user.supplierId || data.supplierId
    if (supplierId && listTrade.length > 0 && bid) {
      const bidSupplier = await this.bidSupplierRepo.findOne({
        where: [
          { bidId: bid.bidId, supplierId, isDeleted: false },
          { bidItemId: bid.bidId, supplierId, isDeleted: false },
        ],
        select: { id: true, status: true },
      })
      if (!bidSupplier) return listTrade

      // nếu đã nộp thầu => show các data đã nộp
      if (bidSupplier.status == enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code) {
        const dicValue: any = {}
        {
          const lstTradeValue = await bidSupplierTradeValueRepo.find({
            where: { bidSupplierId: bidSupplier.id, isDeleted: false },
          })
          lstTradeValue.forEach((c) => (dicValue[c.bidTradeId] = c.value))
        }

        for (const data1 of listTrade) {
          data1.value = dicValue[data1.id] || ''
          for (const data2 of data1.__childs__) {
            data2.value = dicValue[data2.id] || ''
          }
        }
      }
    }

    return listTrade
  }

  async loadDataBidPrice(user: UserDto, data: { bidId: string; supplierId?: string }) {
    /* tìm ra danh sách exMat của Bid */
    const bidExGr: any = await this.bidExMatGroupRepository.find({
      where: [{ bidId: data.bidId, isDeleted: false }],
    })

    let rs = []

    for (const item of bidExGr) {
      const service = await this.serviceRepo.findOne({ where: { externalMaterialGroupId: item.externalMaterialGroupId.toLowerCase() } })

      item.serviceName = service.name
      const res1: any[] = await this.bidPriceRepo.find({
        // where: { bidItemId: data.bidId, companyId: user.companyId },
        where: [{ bidExgroupId: item.id }, { bidId: data.bidId }],
        relations: {
          bidPriceListDetails: true,
          bidPriceColValue: true,
          childs: { bidPriceListDetails: true, bidPriceColValue: true, childs: { bidPriceListDetails: true, bidPriceColValue: true } },
        },
        order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } } },
      })
      const res2 = await this.bidPriceColRepo.getBidPriceColAll(user, item.id)

      const bideItemRepo = this.repo.manager.getRepository(BidPrItemEntity)

      const supplierId = user.supplierId || data.supplierId
      if (supplierId && res1.length > 0) {
        const bidSupplier = await this.bidSupplierRepo.findOne({
          // where: { bidItemId: bid.bidId, supplierId, isDeleted: false },
          where: [{ bidId: item.bidId, supplierId, isDeleted: false }],
          select: { id: true, status: true },
        })
        if (!bidSupplier) return res1

        const getDataCell = (row: any, col: any, lstValue: any[] = []) => {
          row[col.id] = ''
          if (col.colType === enumData.ColType.MPO.code) {
            if (row.__bidPriceColValue__?.length > 0) {
              const cell = row.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
              if (cell) row[col.id] = cell.value
            }
          } else {
            const cell = lstValue.find((c) => c.bidPriceColId === col.id && c.bidPriceId === row.id)
            if (cell) row[col.id] = cell.value
          }
        }

        // Show các data đã nộp
        const lstBidSupplierPriceColValue = await bidSupplier.bidSupplierPriceColValue
        const dicValue: any = {}
        {
          const lstPriceValue = await bidSupplier.bidSupplierPriceValue
          lstPriceValue.forEach((c) => (dicValue[c.bidPriceId] = c.value))
        }

        for (const data1 of res1) {
          data1.value = dicValue[data1.id] || ''
          for (const col of res2) {
            getDataCell(data1, col, lstBidSupplierPriceColValue)
          }
          for (const data2 of data1.__childs__) {
            data2.value = dicValue[data2.id] || ''
            for (const col of res2) {
              getDataCell(data2, col, lstBidSupplierPriceColValue)
            }
            for (const data3 of data2.__childs__) {
              data3.value = dicValue[data3.id] || ''
              for (const col of res2) {
                getDataCell(data3, col, lstBidSupplierPriceColValue)
              }
            }
          }
        }
      }

      rs.push({ listOfData: res1, bidPriceCol: res2, bid: item })
    }
    return rs
  }

  async loadDataBidCustomPrice(user: UserDto, data: { bidId: string; supplierId?: string }) {
    const bidSupplierCustomPriceValueRepo = this.repo.manager.getRepository(BidSupplierCustomPriceValueEntity)
    const bideItemRepo = this.repo.manager.getRepository(BidPrItemEntity)

    const bidExGr: any = await this.bidExMatGroupRepository.find({
      where: [{ bidId: data.bidId, isDeleted: false }],
    })

    const rsData = []

    for (const exGr of bidExGr) {
      const service = await this.serviceRepo.findOne({ where: { externalMaterialGroupId: exGr.externalMaterialGroupId.toLowerCase() } })

      exGr.serviceName = service.name

      let res: any[] = await this.bidCustomPriceRepo.find({
        // where: ,
        where: [{ bidExgroupId: exGr.id }],

        order: { sort: 'ASC', createdAt: 'ASC' },
      })

      const supplierId = user.supplierId || data.supplierId
      if (supplierId) {
        const bidSupplier = await this.bidSupplierRepo.findOne({
          where: [
            { bidId: bidExGr.bidId, supplierId, isDeleted: false },
            // { bidId: bid.id, supplierId, isDeleted: false },
          ],
          // where: { bidId: bid.bidId, supplierId, isDeleted: false },

          select: { id: true, status: true },
        })
        if (!bidSupplier) return res

        // nếu đã nộp thầu => show các data đã nộp
        if (bidSupplier.status == enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code) {
          res = await bidSupplierCustomPriceValueRepo.find({
            where: { bidSupplierId: bidSupplier.id, isDeleted: false },
            order: { sort: 'ASC', createdAt: 'ASC' },
          })
          console.log(res)
        }
      }
      rsData.push({ ...exGr, listOfData: res })
    }
    return rsData
  }

  /** Lịch sử đấu thầu Doanh nghiệp */
  async paginationBidHistory(user: UserDto, data: PaginationDto) {
    const whereCon: any = { supplierId: user.supplierId, isDeleted: false, bid: { parentId: IsNull() } }
    if (data.where.textFilter) {
      whereCon.bid = [
        { code: Like(`%${data.where.textFilter}%`), parentId: IsNull() },
        { name: Like(`%${data.where.textFilter}%`), parentId: IsNull() },
      ]
    }
    const res: any[] = await this.bidSupplierRepo.findAndCount({
      where: whereCon,
      relations: { supplier: true, bid: true },
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })

    if (res[0].length == 0) return res

    // Đang phát hành
    const lstStatus1 = [
      enumData.BidStatus.DangDanhGia.code,
      enumData.BidStatus.DangDuyetDanhGia.code,
      enumData.BidStatus.HoanTatDanhGia.code,
      enumData.BidStatus.DangDamPhanGia.code,
      enumData.BidStatus.DongDamPhanGia.code,
      enumData.BidStatus.DangDauGia.code,
      enumData.BidStatus.DongDauGia.code,
      enumData.BidStatus.DongThau.code,
      enumData.BidStatus.DuyetNCCThangThau.code,
      enumData.BidStatus.DangDuyetKetThucThau.code,
    ]
    // Đóng thầu
    const lstStatus2 = [enumData.BidStatus.HoanTat.code, enumData.BidStatus.Huy.code]
    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    for (const item of res[0]) {
      item.bidCode = item.__bid__.code
      item.bidName = item.__bid__.name
      item.bidStatus = item.__bid__.status
      item.companyInvite = item.__bid__.companyInvite
      item.addressSubmit = item.__bid__.addressSubmit
      delete item.__bid__

      item.supplierName = item.__supplier__.name
      delete item.__supplier__

      item.statusName = dicStatus[item.status]
      item.statusCode = item.status
      item.bidStatusName = 'Đang phát hành'
      if (lstStatus1.includes(item.bidStatus)) {
        item.bidStatusName = 'Đã mở thầu'
      }
      if (lstStatus2.includes(item.bidStatus)) {
        item.bidStatusName = 'Đóng thầu'
      }
      if (item.bidStatus == enumData.BidStatus.HoanTat.code) {
        item.isShowResult = true
      }

      item.lstItemSuccess = await this.bidSupplierRepo.find({
        where: { supplierId: item.supplierId, bid: { parentId: item.bidId, isDeleted: false }, isDeleted: false, isSuccessBid: true },
        relations: { bid: { service: true } },
        select: { id: true, bidId: true, bid: { id: true, quantityItem: true, service: { code: true, name: true } } },
      })
      if (item.lstItemSuccess.length > 0) item.isSuccessBid = true
      for (const itemS of item.lstItemSuccess) {
        itemS.itemName = itemS.__bid__.__service__.code + ' - ' + itemS.__bid__.__service__.name
        itemS.quantityItem = itemS.__bid__.quantityItem
        delete itemS.__bid__
      }
    }

    return res
  }

  async checkPermissionJoinResetPrice(bidId: string, user: UserDto) {
    const bidSupplier: any = await this.bidSupplierRepo.findOne({
      where: { bidId, supplierId: user.supplierId, companyId: user.companyId },
      relations: { bid: true },
      select: { id: true, statusResetPrice: true, bid: { id: true, statusResetPrice: true, resetPriceEndDate: true } },
    })
    if (!bidSupplier) return false

    const bid = bidSupplier.__bid__
    const todate = new Date()
    if (bid.resetPriceEndDate && bid.resetPriceEndDate < todate) return false

    if (
      bid.statusResetPrice == enumData.BidResetPriceStatus.DaTao.code &&
      bidSupplier.statusResetPrice == enumData.BidSupplierResetPriceStatus.YeuCauBoSung.code
    ) {
      return true
    }

    return false
  }

  /** Doanh nghiệp nộp chào giá bổ sung cho gói thầu */
  async supplierSaveResetPrice(
    user: UserDto,
    data: {
      bidId: string
      dataInfo: { filePriceDetail?: string; fileTechDetail?: string }
      priceInfo: SupplierCreatePriceItemDto[]
    },
  ) {
    if (!user.supplierId) throw new NotFoundException('Không có quyền truy cập')

    const bid = await this.bidPrItemRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error('Gói thầu không tồn tại, vui lòng kiểm tra lại link yêu cầu nộp chào giá bổ sung!')

    const today = new Date()
    const bidParent = await bid.bid
    if (bidParent.resetPriceEndDate && bidParent.resetPriceEndDate < today) {
      throw new Error('Hết hạn nộp chào giá bổ sung cho gói thầu!')
    }
    const check = await this.checkPermissionJoinResetPrice(data.bidId, user)
    if (!check) throw new Error('Bạn không có quyền truy cập hoặc đã ngưng nộp giá bổ sung cho gói thầu')

    await this.repo.manager.transaction(async (manager) => {
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      const bidSupplier = await bidSupplierRepo.findOne({ where: { bidId: data.bidId, supplierId: user.supplierId, companyId: user.companyId } })
      if (!bidSupplier) throw new Error(ERROR_NOT_FOUND_DATA)

      await bidSupplierRepo.update(bidSupplier.id, {
        statusResetPrice: enumData.BidSupplierResetPriceStatus.DaBoSung.code,
        filePriceDetail: data.dataInfo.filePriceDetail,
        fileTechDetail: data.dataInfo.fileTechDetail,
        updatedBy: user.id,
      })

      //#region Save Price

      const bidSupplierPriceValueRepo = manager.getRepository(BidSupplierPriceValueEntity)
      const bidSupplierPriceColValueRepo = manager.getRepository(BidSupplierPriceColValueEntity)
      const bidSupplierPriceRepo = new BidSupplierPriceRepository(BidSupplierPriceEntity, manager)
      const lstBidPriceCol = (await bid.bidPriceCols).filter((c) => !c.isDeleted)
      await bidSupplierPriceRepo.delete({ bidSupplierId: bidSupplier.id })

      // lv1
      for (let index = 0; index < data.priceInfo.length; index++) {
        const item = data.priceInfo[index] as any
        item.submitDate = today
        item.submitType = 0
        item.level = 1

        for (const col of lstBidPriceCol) {
          if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
            const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, item)
            if (value != null) {
              const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
              bidSupplierPriceColValue.companyId = user.companyId
              bidSupplierPriceColValue.createdBy = user.id
              bidSupplierPriceColValue.value = value
              bidSupplierPriceColValue.bidPriceId = item.bidPriceId
              bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
              bidSupplierPriceColValue.bidPriceColId = col.id
              await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
              item[col.id] = value
            }
          } else {
            if (item[col.id]) {
              const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
              bidSupplierPriceColValue.companyId = user.companyId
              bidSupplierPriceColValue.createdBy = user.id
              bidSupplierPriceColValue.value = item[col.id]
              bidSupplierPriceColValue.bidPriceId = item.bidPriceId
              bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
              bidSupplierPriceColValue.bidPriceColId = col.id
              await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
            }
          }
        }

        if (bid.fomular && bid.fomular.length > 0) {
          item.value = await coreHelper.calFomular(bid.fomular, lstBidPriceCol, item)
        }

        if (item.value) {
          const bidPriceValue = new BidSupplierPriceValueEntity()
          bidPriceValue.companyId = user.companyId
          bidPriceValue.createdBy = user.id
          bidPriceValue.bidSupplierId = bidSupplier.id
          bidPriceValue.value = item.value
          bidPriceValue.bidPriceId = item.bidPriceId
          bidPriceValue.name = item.name
          bidPriceValue.unit = item.unit
          bidPriceValue.currency = item.currency
          bidPriceValue.number = item.number
          await bidSupplierPriceValueRepo.save(bidPriceValue)

          await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, item)
        }

        // lv2
        if (item.__childs__?.length > 0) {
          var priceInfoLv2 = item.__childs__
          for (let index2 = 0; index2 < priceInfoLv2.length; index2++) {
            const itemLv2 = priceInfoLv2[index2] as any
            itemLv2.submitDate = today
            itemLv2.submitType = 0
            itemLv2.level = 2

            for (const col of lstBidPriceCol) {
              if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
                const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, itemLv2)
                if (value != null) {
                  const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                  bidSupplierPriceColValue.companyId = user.companyId
                  bidSupplierPriceColValue.createdBy = user.id
                  bidSupplierPriceColValue.value = value
                  bidSupplierPriceColValue.bidPriceId = itemLv2.bidPriceId
                  bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                  bidSupplierPriceColValue.bidPriceColId = col.id
                  await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                  itemLv2[col.id] = value
                }
              } else {
                if (itemLv2[col.id]) {
                  const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                  bidSupplierPriceColValue.companyId = user.companyId
                  bidSupplierPriceColValue.createdBy = user.id
                  bidSupplierPriceColValue.value = itemLv2[col.id]
                  bidSupplierPriceColValue.bidPriceId = itemLv2.bidPriceId
                  bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                  bidSupplierPriceColValue.bidPriceColId = col.id
                  await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                }
              }
            }

            if (bid.fomular && bid.fomular.length > 0) {
              itemLv2.value = await coreHelper.calFomular(bid.fomular, lstBidPriceCol, itemLv2)
            }

            if (itemLv2.value) {
              const bidPriceValue = new BidSupplierPriceValueEntity()
              bidPriceValue.companyId = user.companyId
              bidPriceValue.createdBy = user.id
              bidPriceValue.bidSupplierId = bidSupplier.id
              bidPriceValue.value = itemLv2.value
              bidPriceValue.bidPriceId = itemLv2.bidPriceId
              bidPriceValue.name = itemLv2.name
              bidPriceValue.unit = itemLv2.unit
              bidPriceValue.currency = itemLv2.currency
              bidPriceValue.number = itemLv2.number
              await bidSupplierPriceValueRepo.save(bidPriceValue)

              await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, itemLv2)
            }

            // lv3
            if (itemLv2.__childs__?.length > 0) {
              var priceInfoLv3 = itemLv2.__childs__
              for (let index3 = 0; index3 < priceInfoLv3.length; index3++) {
                const itemLv3 = priceInfoLv3[index3] as any
                itemLv3.submitDate = today
                itemLv3.submitType = 0
                itemLv3.level = 3

                for (const col of lstBidPriceCol) {
                  if (col.type === enumData.DataType.Number.code && col.fomular?.length > 0) {
                    const value = await coreHelper.calFomular(col.fomular, lstBidPriceCol, itemLv3)
                    if (value != null) {
                      const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                      bidSupplierPriceColValue.companyId = user.companyId
                      bidSupplierPriceColValue.createdBy = user.id
                      bidSupplierPriceColValue.value = value
                      bidSupplierPriceColValue.bidPriceId = itemLv3.bidPriceId
                      bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                      bidSupplierPriceColValue.bidPriceColId = col.id
                      await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                      itemLv3[col.id] = value
                    }
                  } else {
                    if (itemLv3[col.id]) {
                      const bidSupplierPriceColValue = new BidSupplierPriceColValueEntity()
                      bidSupplierPriceColValue.companyId = user.companyId
                      bidSupplierPriceColValue.createdBy = user.id
                      bidSupplierPriceColValue.value = itemLv3[col.id]
                      bidSupplierPriceColValue.bidPriceId = itemLv3.bidPriceId
                      bidSupplierPriceColValue.bidSupplierId = bidSupplier.id
                      bidSupplierPriceColValue.bidPriceColId = col.id
                      await bidSupplierPriceColValueRepo.save(bidSupplierPriceColValue)
                    }
                  }
                }

                if (bid.fomular && bid.fomular.length > 0) {
                  itemLv3.value = await coreHelper.calFomular(bid.fomular, lstBidPriceCol, itemLv3)
                }

                if (itemLv3.value) {
                  const bidPriceValue = new BidSupplierPriceValueEntity()
                  bidPriceValue.companyId = user.companyId
                  bidPriceValue.createdBy = user.id
                  bidPriceValue.bidSupplierId = bidSupplier.id
                  bidPriceValue.value = itemLv3.value
                  bidPriceValue.bidPriceId = itemLv3.bidPriceId
                  bidPriceValue.name = itemLv3.name
                  bidPriceValue.unit = itemLv3.unit
                  bidPriceValue.currency = itemLv3.currency
                  bidPriceValue.number = itemLv3.number
                  await bidSupplierPriceValueRepo.save(bidPriceValue)

                  await bidSupplierPriceRepo.saveBidSupplierPrice(user, bidSupplier, itemLv3)
                }
              }
            }
          }
        }
      }
      //#endregion
    })

    // email gửi cho nhân viên phụ trách, MPOLeader mua hàng khi có nhà cung cấp nộp chào giá bổ sung
    await this.emailService.GuiMpoNccNopChaoGiaBosung(data.bidId, user.supplierId)

    return { message: UPDATE_SUCCESS }
  }

  /** Cảnh báo khi gần hết hạn thiết lập và đánh giá Gói Thầu */
  public async autoCreateWarningComingExpirySettingEvalution() {
    return this.repo.manager.transaction(async (manager) => {
      try {
        const dataType = enumData.DataWarningType.Bid.code
        const warningType = enumData.WarningType.Bid_Expiry_Setting_Evalution
        await manager.getRepository(EmployeeWarningEntity).delete({
          dataId: Not(IsNull()),
          dataType: dataType,
          warningType: warningType.code,
        })

        const dFrom = new Date(new Date().setHours(0, 0, 0, 0))
        let dTo = new Date(new Date().setDate(new Date().getDate() + 7))
        dTo = new Date(new Date(dTo).setHours(23, 59, 59, 59))

        const condition: any = new Object()
        condition.isDeleted = false
        condition.status = Not(In([enumData.BidStatus.Huy.code, enumData.BidStatus.HoanTat.code, enumData.BidStatus.DongThau.code]))

        const where1 = {
          ...condition,
          ...{
            timeTechDate: Raw(
              (alias) =>
                `DATE(${alias}) BETWEEN DATE("${moment(dFrom).format('YYYY-MM-DD HH:MM:SS')}") AND DATE("${moment(dTo).format(
                  'YYYY-MM-DD HH:MM:SS',
                )}")`,
            ),
          },
        }
        const where2 = {
          ...condition,
          ...{
            timePriceDate: Raw(
              (alias) =>
                `DATE(${alias}) BETWEEN DATE("${moment(dFrom).format('YYYY-MM-DD HH:MM:SS')}") AND DATE("${moment(dTo).format(
                  'YYYY-MM-DD HH:MM:SS',
                )}")`,
            ),
          },
        }
        const where3 = {
          ...condition,
          ...{
            timeCheckTechDate: Raw(
              (alias) =>
                `DATE(${alias}) BETWEEN DATE("${moment(dFrom).format('YYYY-MM-DD HH:MM:SS')}") AND DATE("${moment(dTo).format(
                  'YYYY-MM-DD HH:MM:SS',
                )}")`,
            ),
          },
        }
        const where4 = {
          ...condition,
          ...{
            timeCheckPriceDate: Raw(
              (alias) =>
                `DATE(${alias}) BETWEEN DATE("${moment(dFrom).format('YYYY-MM-DD HH:MM:SS')}") AND DATE("${moment(dTo).format(
                  'YYYY-MM-DD HH:MM:SS',
                )}")`,
            ),
          },
        }

        const lstBid = await manager.getRepository(BidEntity).find({
          where: [where1, where2, where3, where4],
        })
        if (lstBid.length > 0) {
          let html = warningType.default
          let subject = warningType.name
          const template = await manager.getRepository(EmailTemplateEntity).findOne({ where: { code: warningType.code, isDeleted: false } })
          if (template) {
            html = template.description
            subject = template.name
          }
          let lstUserId = lstBid.map((s: any) => s.createdBy)
          lstUserId = Array.from(new Set(lstUserId))
          const lstUser = await manager.getRepository(UserEntity).find({
            where: { id: In(lstUserId), isDeleted: false },
            relations: ['employee'],
          })

          for await (const bid of lstBid) {
            let createdBy = lstUser.find((s: any) => s.id == bid.createdBy)
            if (createdBy && createdBy.employeeId) {
              let emp = await createdBy.employee
              let title = ''
              if (dFrom.getTime() <= new Date(bid.timeTechDate).getTime() && new Date(bid.timeTechDate).getTime() <= dTo.getTime()) {
                title = 'Thiết Lập Yêu Cầu Kỹ Thuật Và Năng Lực'
              } else if (dFrom.getTime() <= new Date(bid.timePriceDate).getTime() && new Date(bid.timePriceDate).getTime() <= dTo.getTime()) {
                title = 'Thiết Lập Các Hạng Mục Báo Giá, Cơ Cấu Giá Và Điều Kiện Thương Mại'
              } else if (dFrom.getTime() <= new Date(bid.timeCheckTechDate).getTime() && new Date(bid.timeCheckTechDate).getTime() <= dTo.getTime()) {
                title = 'Đánh Giá Yêu Cầu Kỹ Thuật Và Năng Lực'
              } else if (
                dFrom.getTime() <= new Date(bid.timeCheckPriceDate).getTime() &&
                new Date(bid.timeCheckPriceDate).getTime() <= dTo.getTime()
              ) {
                title = 'Đánh Giá Các Hạng Mục Báo Giá, Cơ Cấu Giá Và Điều Kiện Thương Mại'
              }

              const subject_text = coreHelper.stringInject(subject, [title, bid.code])
              // let link = `&nbsp; <button onclick="showDataDetail('${dataType}', '${pp.id}')">Xem Chi Tiết</button>`

              let content = coreHelper.stringInject(`<html><head></head><body>${html}</body></html>`, [emp.name, bid.name, bid.code])

              const w1 = new EmployeeWarningEntity()
              w1.warningType = warningType.code
              w1.dataType = dataType
              w1.dataId = bid.id
              w1.message = subject_text || ''
              w1.messageFull = content || ''
              w1.employeeId = createdBy.employeeId
              await manager.getRepository(EmployeeWarningEntity).save(w1)
            }
          }
        }
      } catch (error) {
        throw error
      }
    })
  }

  //#endregion
}
