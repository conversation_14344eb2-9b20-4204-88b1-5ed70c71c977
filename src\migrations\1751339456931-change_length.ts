import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeLength1751339456931 implements MigrationInterface {
    name = 'ChangeLength1751339456931'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "totalValue"`);
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "totalValue" decimal(20,10) CONSTRAINT "DF_fa9be7f0107fc0431114f8fde56" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP CONSTRAINT "DF_fa9be7f0107fc0431114f8fde56"`);
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "totalValue"`);
        await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "totalValue" bigint`);
    }

}
