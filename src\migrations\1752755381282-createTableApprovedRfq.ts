import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableApprovedRfq1752755381282 implements MigrationInterface {
    name = 'CreateTableApprovedRfq1752755381282'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "rfq_approved" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_09963c156e92abd47a08039cc4d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3352c3a3d63ddec453fc8c76324" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "code" varchar(100), "lstSupplierId" varchar(max), "lstRfqId" varchar(max), "requestQuoteId" varchar(255), "status" nvarchar(20), CONSTRAINT "PK_09963c156e92abd47a08039cc4d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "isChoose" bit CONSTRAINT "DF_1fed31fbf0f9e5b3249345481d3" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "isWiner" bit CONSTRAINT "DF_35b2dc01460dba1d0447bb05343" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "scoreSystem" float`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "score" float`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "ranking" float`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "totalPrice" float`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "exchange" decimal(20,3) CONSTRAINT "DF_e5e9f3b536c046c7aeeab7a2547" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "dateExchange" datetime`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "dateExchange"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "DF_e5e9f3b536c046c7aeeab7a2547"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "exchange"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "totalPrice"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "ranking"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "score"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "scoreSystem"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "DF_35b2dc01460dba1d0447bb05343"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "isWiner"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "DF_1fed31fbf0f9e5b3249345481d3"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "isChoose"`);
        await queryRunner.query(`DROP TABLE "rfq_approved"`);
    }

}
