import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ComplaintEntity } from './complaint.entity'
import { ComplaintFixEntity } from './complaintFix.entity'
import { ComplaintPreventionEntity } from './complaintPrevention.entity'

/** Khiếu nại - item */
@Entity('complaint_item')
export class ComplaintItemEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  complaintId: string
  @ManyToOne(() => ComplaintEntity, (p) => p.complaintItems)
  @JoinColumn({ name: 'complaintId', referencedColumnName: 'id' })
  complaint: Promise<ComplaintEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  itemCode: string

  /**Short text */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  shortText: string

  /**Coding  enumData (CodingComplaint)*/
  @Column({ type: 'varchar', length: 30, nullable: true })
  codingType: string
  /**Coding  enumData */
  @Column({ type: 'varchar', length: 30, nullable: true })
  choiceType: string

  /** mô tả  */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  description: string

  /** chi tiết mô tả  */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  note: string

  /** File đính kèm */
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  fileAttached: string

  /** Khắc phục */
  @OneToMany(() => ComplaintFixEntity, (p) => p.complaintItem)
  complaintFixs: Promise<ComplaintFixEntity[]>

  /** Khắc phục */
  @OneToMany(() => ComplaintPreventionEntity, (p) => p.complaintItem)
  complaintPreventions: Promise<ComplaintPreventionEntity[]>
}
