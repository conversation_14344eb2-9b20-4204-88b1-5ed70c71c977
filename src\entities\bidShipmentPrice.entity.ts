import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BidEntity } from './bid.entity'
import { BaseEntity } from './base.entity'
import { ShipmentCostPriceEntity } from './shipmentCostPrice.entity'

@Entity('bid_shipment_price')
export class BidShipmentPriceEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.shipments)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPriceId: string
  @ManyToOne(() => ShipmentCostPriceEntity, (p) => p.shipmentPrice)
  @JoinColumn({ name: 'shipmentPriceId', referencedColumnName: 'id' })
  shipmentPrice: Promise<ShipmentCostPriceEntity>

  @Column({
    nullable: false,
  })
  value: number
}
