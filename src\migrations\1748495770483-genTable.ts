import { MigrationInterface, QueryRunner } from "typeorm";

export class GenTable1748495770483 implements MigrationInterface {
    name = 'GenTable1748495770483'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "currency_exchange" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_408ce7731cf7527a37ad3848d2b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_720d5170dae0b50385b254361f7" DEFAULT 0, "companyId" varchar(255), "currencyId" uniqueidentifier NOT NULL, "exchange" decimal(20,3) CONSTRAINT "DF_01fba1699ae9a99fbe826a8b2f2" DEFAULT 0, "source" varchar(250), "date" datetime, CONSTRAINT "PK_408ce7731cf7527a37ad3848d2b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "product_hierarchy" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_6265494e9bb9a762a00a87f9db6" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e96b78f86c1eb18dc162fe9189b" DEFAULT 0, "companyId" varchar(255), "name" varchar(250), "code" varchar(250) NOT NULL, "level" int NOT NULL, "isLast" bit NOT NULL CONSTRAINT "DF_929c1eac5d54a0c9288d8247499" DEFAULT 0, "parentId" uniqueidentifier, CONSTRAINT "PK_6265494e9bb9a762a00a87f9db6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "material" ADD "maximumStockLevel" decimal(20,3) CONSTRAINT "DF_ca6bd4b5370a07ef15dc4457f49" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "material" ADD "productHierarchyId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "material" ADD "procumentType" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "roundingValue" decimal(20,3) CONSTRAINT "DF_0e5f9261a6752beff0e6257ea16" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "currency_exchange" ADD CONSTRAINT "FK_d71a18d698f9ca06f8f57c4a44f" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_c4bea1a21b0fe9984c832ba367e" FOREIGN KEY ("productHierarchyId") REFERENCES "product_hierarchy"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_hierarchy" ADD CONSTRAINT "FK_8d88ee68c60077f86b50fa4616e" FOREIGN KEY ("parentId") REFERENCES "product_hierarchy"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_hierarchy" DROP CONSTRAINT "FK_8d88ee68c60077f86b50fa4616e"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_c4bea1a21b0fe9984c832ba367e"`);
        await queryRunner.query(`ALTER TABLE "currency_exchange" DROP CONSTRAINT "FK_d71a18d698f9ca06f8f57c4a44f"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_0e5f9261a6752beff0e6257ea16"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "roundingValue"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "procumentType"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "productHierarchyId"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_ca6bd4b5370a07ef15dc4457f49"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "maximumStockLevel"`);
        await queryRunner.query(`DROP TABLE "product_hierarchy"`);
        await queryRunner.query(`DROP TABLE "currency_exchange"`);
    }

}
