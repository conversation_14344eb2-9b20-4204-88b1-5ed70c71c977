import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { TicketEvaluationKpiEntity } from './ticketEvaluationKpi.entity'
import { TicketEvaluationKpiListDetailEntity } from './ticketEvaluationKpiListDetail.entity'

/** phiếu đánh giá template kpi */
@Entity('ticket_evaluation_kpi_detail')
export class TicketEvaluationKpiDetailEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** <PERSON><PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** Cách tính điểm theo loại càng cao càng tốt */
  @Column({
    nullable: false,
    default: true,
  })
  isCalUp: boolean

  /** <PERSON><PERSON>u dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  percent: number

  /** % điều kiện đạt tỉ trọng */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentRule: number

  /** % điều kiện liệt tỉ trọng khi tính theo chiều giảm dần */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentDownRule: number

  /** Cấp độ */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** Mô tả */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Điểm chấm tay */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  evaluate: number

  /** Đánh giá điều chỉnh*/
  @Column({
    type: 'varchar',
    length: 400,
    nullable: true,
  })
  evaluationString: string

  /** Đánh giá điều chỉnh (number)*/
  @Column({
    type: 'int',
    nullable: true,
  })
  evaluationNumber: string

  /** Đánh giá điều chỉnh List*/
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  evaluationList: string

  /** File đính kèm */
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  attachedFile: string

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => TicketEvaluationKpiDetailEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: TicketEvaluationKpiDetailEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => TicketEvaluationKpiDetailEntity, (p) => p.parent)
  childs: Promise<TicketEvaluationKpiDetailEntity[]>

  /** list detail */
  @OneToMany(() => TicketEvaluationKpiListDetailEntity, (p) => p.ticketEvaluationKpiDetail)
  ticketEvaluationKpiListDetails: Promise<TicketEvaluationKpiListDetailEntity[]>

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Giá trị nhỏ nhất */
  @Column({
    nullable: true,
  })
  requiredMin: number

  /** Thuộc tính của tiêu chí thể hệ Doanh nghiệp sẽ được highlight màu xanh nếu đạt giá trị X khi xếp hạng năng lực */
  @Column({
    nullable: false,
    default: false,
  })
  isHighlight: boolean

  @Column({
    nullable: true,
  })
  hightlightValue: number

  /** Kiểm tra lại */
  @Column({
    nullable: true,
    default: false,
  })
  isReason: boolean

  /** Lý do yêu cầu */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  reason: string

  /** id kpi mua hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  ticketEvaluationKpiId: string
  @ManyToOne(() => TicketEvaluationKpiEntity, (p) => p.ticketEvaluationKpiDetails)
  @JoinColumn({ name: 'ticketEvaluationKpiId', referencedColumnName: 'id' })
  ticketEvaluationKpi: Promise<TicketEvaluationKpiEntity>

  // lưu lại id để mapping data với kpiDetailId
  @Column({
    type: 'varchar',
    nullable: true,
  })
  kpiDetailId: string

  /** Đã Lưu lại yêu cầu kiểm tra lại chưa nếu có yêu cầu kiểm tra lại cho tiêu chí này */
  @Column({
    nullable: true,
    default: false,
  })
  isReChecked: boolean
}
