import { ApiProperty } from '@nestjs/swagger'
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class TemplateConfigDetailDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  colLetter: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  headerLabel: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  type: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  mappingField: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  fieldDataType: string

  @ApiProperty()
  @IsString()
  @IsOptional()
  formulaExpression?: string

  @ApiProperty()
  @IsString()
  @IsOptional()
  apiEndpoint?: string

  @ApiProperty()
  @IsString()
  @IsOptional()
  apiQueryParams?: string
}

export class TemplateConfigDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  code: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  type: string

  @ApiProperty()
  @IsString()
  @IsOptional()
  apiEndpoint?: string

  @ApiProperty()
  @IsString()
  @IsOptional()
  apiQueryParams?: string

  @ApiProperty()
  @IsString()
  @IsOptional()
  desc?: string

  @ApiProperty({ type: [TemplateConfigDetailDto] })
  @IsArray()
  configs: TemplateConfigDetailDto[]
}

export class UpdateTemplateConfigDto extends TemplateConfigDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  id: string
}
