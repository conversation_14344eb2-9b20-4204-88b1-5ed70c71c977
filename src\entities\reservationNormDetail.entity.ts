import { Enti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { MaterialEntity } from './material.entity'
import { UomEntity } from './uom.entity'
import { BaseEntity } from './base.entity'
import { ReservationNormEntity } from './reservationNorm.entity'

/** Chi tiết */
@Entity('reservation_norm_detail')
export class ReservationNormDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  code: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.reservationNormDetails)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  // Định mức theo baseUnit
  @Column({ nullable: true, default: 0, type: 'float' })
  baseUnitNorm: number

  // Đ<PERSON>nh mức còn lại theo baseunit
  @Column({ nullable: true, default: 0, type: 'float' })
  baseUnitQuantityRemind: number

  // Đơn vị tính
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.reservationNormUnitDetails)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  // Định mức
  @Column({ nullable: true, default: 0, type: 'float' })
  norm: number

  // Định mức còn lại
  @Column({ nullable: true, default: 0, type: 'float' })
  quantityRemind: number

  @Column({ nullable: true, type: 'datetime' })
  startDate: Date

  @Column({ nullable: true, type: 'datetime' })
  expireDate: Date

  @Column({
    type: 'varchar',
    nullable: true,
  })
  reservationNormId: string
  @ManyToOne(() => ReservationNormEntity, (p) => p.reservationNormDetails)
  @JoinColumn({ name: 'reservationNormId', referencedColumnName: 'id' })
  reservationNorm: Promise<ReservationNormEntity>

  // BaseUnit
  @Column({
    type: 'varchar',
    nullable: true,
  })
  baseUnitId: string
  @ManyToOne(() => UomEntity, (p) => p.reservationNormDetails)
  @JoinColumn({ name: 'baseUnitId', referencedColumnName: 'id' })
  baseUnit: Promise<UomEntity>

  @Column({
    nullable: true,
    default: 0,
    type: 'float',
  })
  month: number
}
