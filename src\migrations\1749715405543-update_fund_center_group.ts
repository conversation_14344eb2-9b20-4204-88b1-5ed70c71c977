import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateFundCenterGroup1749715405543 implements MigrationInterface {
    name = 'UpdateFundCenterGroup1749715405543'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "fund_center_group" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ebfb29bac61d77e6d535748a254" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_49d05337d098e07446744276959" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(max), CONSTRAINT "PK_ebfb29bac61d77e6d535748a254" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "fund_center_group"`);
    }

}
