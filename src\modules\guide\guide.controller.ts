import { Body, Controller, Get, Post, Put, Req, Param, Delete, UseGuards } from '@nestjs/common'
import { GuideService } from './guide.service'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { GuideCreateDto } from './dto/guideCreate.dto'
import { FilterOneDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { GuideFindByTypeDto, GuideUpdateDto } from './dto/guideUpdate.dto'
import { JwtAuthGuard } from '../common/guards'

@ApiBearerAuth()
@ApiTags('Instruction Guides')
@Controller('guides')
@UseGuards(JwtAuthGuard)
export class GuideController {
  constructor(private readonly guideService: GuideService) {}

  @ApiOperation({ summary: 'Thêm link HDSD mới' })
  @Post('add-guide-link')
  public async addGuides(@CurrentUser() user: UserDto, @Body() data: GuideCreateDto) {
    // @CurrentUser() user: UserDto,
    return await this.guideService.addNewGuide(user, data)
  }

  @ApiOperation({ summary: 'Lấy tất cả links' })
  @Post('guide-links')
  public async getAllLinks() {
    return this.guideService.getGuides()
  }

  @ApiOperation({ summary: 'Tìm guide theo loại' })
  @Post('get-by-type')
  public async findByType(@Body() data: GuideFindByTypeDto) {
    return await this.guideService.findByType(data)
  }

  @ApiOperation({ summary: 'Lấy guide theo ID' })
  @Get(':id')
  public async getGuideById(@Param('id') id: string) {
    return await this.guideService.findById(id)
  }

  @ApiOperation({ summary: 'Chỉnh sửa link guide' })
  @Post('edit-guide-link')
  public async editGuideLink(@CurrentUser() user: UserDto, @Body() data: GuideUpdateDto) {
    return await this.guideService.updateGuide(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái của HDSD' })
  @Post('update-active-guide')
  public async updateActiveGuide(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.guideService.updateActiveGuide(user, data.id)
  }
}
