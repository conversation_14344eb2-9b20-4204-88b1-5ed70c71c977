import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { MaterialEntity } from './material.entity'
import { BusinessPlanEntity } from './businessPlan.entity'
import { BusinessPlanTemplateEntity } from './businessPlanTemplate.entity'
import { BusinessPlanTemplateColEntity } from './businessPlanTemplateCol.entity'
import { PrEntity } from './pr.entity'

@Entity('business_plan_col_value')
export class BusinessPlanColValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.businessPlanColValues)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.businessPlanColValues)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanId: string
  @ManyToOne(() => BusinessPlanEntity, (p) => p.businessPlanColValues)
  @JoinColumn({ name: 'businessPlanId', referencedColumnName: 'id' })
  businessPlan: Promise<BusinessPlanEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanTemplateId: string
  @ManyToOne(() => BusinessPlanTemplateEntity, (p) => p.businessPlanColValues)
  @JoinColumn({ name: 'businessPlanTemplateId', referencedColumnName: 'id' })
  businessPlanTemplate: Promise<BusinessPlanTemplateEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanTemplateColId: string
  @ManyToOne(() => BusinessPlanTemplateColEntity, (p) => p.businessPlanColValues)
  @JoinColumn({ name: 'businessPlanTemplateColId', referencedColumnName: 'id' })
  businessPlanTemplateCol: Promise<BusinessPlanTemplateColEntity>

  /** Quantity */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  quantity: number
}
