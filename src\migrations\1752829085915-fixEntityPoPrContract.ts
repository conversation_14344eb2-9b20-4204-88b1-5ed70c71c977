import { MigrationInterface, QueryRunner } from "typeorm";

export class FixEntityPoPrContract1752829085915 implements MigrationInterface {
    name = 'FixEntityPoPrContract1752829085915'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po_contract" DROP CONSTRAINT "FK_6a4db1ae62b6adab3addeecb9c9"`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP CONSTRAINT "FK_667faf8aa18019a0be5969f00a3"`);
        await queryRunner.query(`EXEC sp_rename "ktg-dev.dbo.po_contract.contractId", "contractIds"`);
        await queryRunner.query(`EXEC sp_rename "ktg-dev.dbo.po_pr.prId", "prIds"`);
        await queryRunner.query(`ALTER TABLE "po" ADD "auctionId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po" ADD "prId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po" ADD "contractId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD CONSTRAINT "FK_08d010a100efc61ef64d6f60e9c" FOREIGN KEY ("contractIds") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD CONSTRAINT "FK_529f0b9999d77fe7a93eba692f1" FOREIGN KEY ("prIds") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_20b22b68714456b5d4fcd03e1a5" FOREIGN KEY ("auctionId") REFERENCES "auction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_20b22b68714456b5d4fcd03e1a5"`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP CONSTRAINT "FK_529f0b9999d77fe7a93eba692f1"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP CONSTRAINT "FK_08d010a100efc61ef64d6f60e9c"`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "contractId"`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "prId"`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "auctionId"`);
        await queryRunner.query(`EXEC sp_rename "ktg-dev.dbo.po_pr.prIds", "prId"`);
        await queryRunner.query(`EXEC sp_rename "ktg-dev.dbo.po_contract.contractIds", "contractId"`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD CONSTRAINT "FK_667faf8aa18019a0be5969f00a3" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD CONSTRAINT "FK_6a4db1ae62b6adab3addeecb9c9" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
