import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { FAQCategoryEntity } from './faqCategory.entity'

@Entity({ name: 'faq' })
export class FAQEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  title: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  categoryId: string
  @ManyToOne(() => FAQCategoryEntity, (p) => p.faqs)
  @JoinColumn({ name: 'categoryId', referencedColumnName: 'id' })
  category: Promise<FAQCategoryEntity>
}
