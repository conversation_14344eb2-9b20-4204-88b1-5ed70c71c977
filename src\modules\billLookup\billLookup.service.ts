import { Injectable } from '@nestjs/common'
import { v4 as uuidv4 } from 'uuid'
import * as moment from 'moment'
import {
  CREATE_SUCCESS,
  enumData,
  enumLanguage,
  ERROR_CODE_TAKEN,
  ERROR_NOT_FOUND_DATA,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../constants'
import { In, IsNull, Like, Not } from 'typeorm'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { BillLookupEntity } from '../../entities'
import { coreHelper } from '../../helpers'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'
import { BillLookupCreateDto, BillLookupUpdateDto } from './dto'
import { BillLookupImportExcelDto } from './dto/billLookupExcel.dto'
import { BillLookupRepository } from '../../repositories'

@Injectable()
export class BillLookupService {
  constructor(private readonly repo: BillLookupRepository, private organizationalPositionService: OrganizationalPositionService) {}

  public async find(user: UserDto, lan: string = enumLanguage.LanguageType.EN.code) {
    const whereCon: any = { isDeleted: false }
    const res = await this.repo.find({ where: whereCon, order: { code: 'DESC' } })
    return await this.repo.translate(res, lan)
  }

  async codeDefault() {
    const code = `TC`
    const objData = await this.repo.findOne({
      where: { code: Like(`%${code}%`) },
      order: { code: 'DESC' },
    })
    let sortString = '0000'
    if (objData) {
      sortString = objData.code.substring(code.length, code.length + 4)
    }
    const lastSort = parseInt(sortString, 10)
    sortString = ('0000' + (lastSort + 1)).slice(-4)

    return code + sortString
  }

  public async createData(user: UserDto, data: BillLookupCreateDto) {
    const newCode = await this.codeDefault()
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BillLookupEntity)
      const newBillLookup = new BillLookupEntity()
      newBillLookup.id = uuidv4()
      newBillLookup.companyId = user.companyId
      newBillLookup.createdBy = user.id
      newBillLookup.code = newCode
      newBillLookup.name = data.name
      newBillLookup.link = data.link
      newBillLookup.abbreviation = data.abbreviation

      newBillLookup.description = data.description
      newBillLookup.createdAt = new Date()
      await repo.insert(newBillLookup)

      //#region "Language"
      const createLanData: any = newBillLookup
      createLanData.nameEN = data.nameEN
      createLanData.abbreviationEN = data.abbreviationEN
      createLanData.descriptionEN = data.descriptionEN

      const languageObj = await coreHelper.checkENValue([createLanData], BillLookupEntity.name)

      await this.repo.saveOrUpdateTranslate(languageObj)
      //#endregion "Language"

      return { message: CREATE_SUCCESS }
    })
  }

  public async updateData(user: UserDto, data: BillLookupUpdateDto) {
    return await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BillLookupEntity)

      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      entity.name = data.name
      entity.link = data.link
      entity.abbreviation = data.abbreviation
      entity.description = data.description
      entity.updatedBy = user.id
      entity.companyId = user.companyId
      entity.updatedAt = new Date()
      const updatedData = await repo.save(entity)

      //#region "Language"
      const createLanData: any = updatedData
      createLanData.nameEN = data.nameEN
      createLanData.abbreviationEN = data.abbreviationEN
      createLanData.descriptionEN = data.descriptionEN

      const languageObj = await coreHelper.checkENValue([createLanData], BillLookupEntity.name)

      await this.repo.saveOrUpdateTranslate(languageObj)
      //#endregion "Language"

      return { message: UPDATE_SUCCESS }
    })
  }

  /** Hàm phân trang */
  public async pagination(user: UserDto, data: PaginationDto, lan?: string) {
    const whereCon: any = {}
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.BillLookup.code)
    if (dataRs.type === enumData.RoleData.All.code) {
      /* do nothing */
    }
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where?.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where?.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where?.code) whereCon.code = Like(`%${data.where.code}%`)

    const res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })

    return [await this.repo.translate(res[0], lan), res[1]]
  }

  public async updateIsDelete(user: UserDto, data: { id: string }) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    await this.repo.update(data.id, { isDeleted: !entity.isDeleted, updatedBy: user.id, updatedAt: new Date() })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async findDetail(user: UserDto, data: { id: string }, lan?: string) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
    })
    return res
  }

  public async importData(user: UserDto, data: BillLookupImportExcelDto[]) {
    const code = 'TC'
    const objData = await this.repo.findOne({
      where: { code: Like(`%${code}%`) },
      order: { code: 'DESC' },
    })
    let sortString = '0'
    if (objData) {
      sortString = objData.code.substring(code.length, code.length + 4)
    }
    const lastSort = parseInt(sortString)

    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const lstTask = []
      let i = 0
      for (const item of data) {
        i++
        sortString = ('000' + (lastSort + i)).slice(-4)
        const codeDefault = code + sortString
        const BillLookup = new BillLookupEntity()
        BillLookup.createdBy = user.id
        BillLookup.createdAt = new Date()
        BillLookup.code = codeDefault
        BillLookup.name = item.name
        BillLookup.link = item.link
        BillLookup.abbreviation = item.abbreviation
        BillLookup.description = item.description
        lstTask.push(BillLookup)
      }
      const lstChild = coreHelper.splitArrayByParameters(lstTask, 2000)
      for (const chunk of lstChild) {
        await trans.insert(BillLookupEntity, chunk)
      }
    })
    return { message: CREATE_SUCCESS }
  }

  /** Hàm cập nhật trạng thái isDeleted */
  public async updateActiveStatus(user: UserDto, data: { id: string }) {
    const found = await this.repo.findOne({ where: { id: data.id } })
    if (!found) throw new Error(ERROR_NOT_FOUND_DATA)

    return this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BillLookupEntity)
      found.isDeleted = !found.isDeleted
      found.updatedAt = new Date()
      found.updatedBy = user.id
      await repo.save(found)
      return { message: UPDATE_SUCCESS }
    })
  }
}
