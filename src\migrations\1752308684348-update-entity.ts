import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateEntity1752308684348 implements MigrationInterface {
  name = 'UpdateEntity1752308684348'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "business_plan_template_cost_list" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_546815384ae4780c14430263160" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b327978b10ac7c36ad1bdf4de30" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "costId" uniqueidentifier NOT NULL, "type" varchar(50), "value" float, "valueVanilla" float, CONSTRAINT "PK_546815384ae4780c14430263160" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(`ALTER TABLE "business_plan_template" ADD "shipmentPlanNumberConfigTable" nvarchar(max)`)
    await queryRunner.query(
      `ALTER TABLE "business_plan_template_cost_list" ADD CONSTRAINT "FK_ab7d4d8b8e5810c5f0c5d49435e" FOREIGN KEY ("costId") REFERENCES "cost"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_plan_template_cost_list" DROP CONSTRAINT "FK_ab7d4d8b8e5810c5f0c5d49435e"`)
    await queryRunner.query(`ALTER TABLE "business_plan_template" DROP COLUMN "shipmentPlanNumberConfigTable"`)
    await queryRunner.query(`DROP TABLE "business_plan_template_cost_list"`)
  }
}
