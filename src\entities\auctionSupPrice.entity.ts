import { AuctionSupplierEntity } from './auctionSupplier.entity'
import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { UomEntity } from './uom.entity'
import { PrItemEntity } from './prItem.entity'
import { BidPrItemEntity } from './bidPrItem.entity'
import { AuctionEntity } from './auction.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'

/** Các NCC tham gia đấu giá */
@Entity('auction_supplier_price')
export class AuctionSupplierPriceEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: true })
  auctionSupplierId: string
  @ManyToOne(() => AuctionSupplierEntity, (p) => p.auctionSupplierPrice)
  @JoinColumn({ name: 'auctionSupplierId', referencedColumnName: 'id' })
  auctionSupplier: Promise<AuctionSupplierEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.auctionSupplierPrice)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitName: string

  /** số lượng cần đặt hàng */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  /** Ngân sách */
  @Column({
    nullable: true,
    type: 'bigint',
  })
  maxPrice: number

  /** Giá đấu trước đó */
  @Column({ type: 'bigint', nullable: true })
  submitPriceOld?: number

  /** Giá đấu */
  @Column({ type: 'bigint', nullable: true })
  submitPrice?: number

  /** Ngày nộp đấu giá */
  @Column({ nullable: true, type: 'datetime' })
  submitDate?: Date

  /* Bid Pr item */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemId: string
  @ManyToOne(() => PrItemEntity, (p) => p.auctionSupplierPrice)
  @JoinColumn({ name: 'prItemId', referencedColumnName: 'id' })
  prItem: Promise<PrItemEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidItemId: string
  @ManyToOne(() => BidPrItemEntity, (p) => p.auctionSupplierPrice)
  @JoinColumn({ name: 'bidItemId', referencedColumnName: 'id' })
  bidItem: Promise<BidPrItemEntity>

  /** Thuộc về đấu giá ? */
  @Column({
    nullable: false,
    default: false,
  })
  belongAuction: boolean

  @Column({
    nullable: false,
    default: false,
  })
  isMaterial: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  auctionId: string
  @ManyToOne(() => AuctionEntity, (p) => p.auctionSupplierPrice)
  @JoinColumn({ name: 'auctionId', referencedColumnName: 'id' })
  auction: Promise<AuctionEntity>

  @Column({ type: 'varchar', nullable: true })
  exMatGroupId?: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.capacities)
  @JoinColumn({ name: 'exMatGroupId', referencedColumnName: 'id' })
  exMatGroup?: Promise<ExternalMaterialGroupEntity>
}
