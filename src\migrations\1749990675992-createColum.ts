import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateColum1749990675992 implements MigrationInterface {
    name = 'CreateColum1749990675992'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_val_type" ADD "mRPType" varchar(50)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_val_type" DROP COLUMN "mRPType"`);
    }

}
