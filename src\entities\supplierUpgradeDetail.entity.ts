import { BaseEntity } from './base.entity'
import { Entity, Column, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { EvaluationHistoryPurchaseEntity, SupplierUpgradeEntity } from '.'

/** <PERSON><PERSON><PERSON> nâng cấp nhà cung cấp chi tiết */
@Entity('supplier_upgrade_detail')
export class SupplierUpgradeDetailEntity extends BaseEntity {
  /** Mối liên hệ với NCC */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  evaluationHistoryPurchaseId: string

  @ManyToOne(() => EvaluationHistoryPurchaseEntity, (p) => p.supplierUpgradeDetails)
  @JoinColumn({ name: 'evaluationHistoryPurchaseId', referencedColumnName: 'id' })
  evaluationHistoryPurchase: Promise<EvaluationHistoryPurchaseEntity>

  /** Mối liên hệ với lệnh nâng cấp */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierUpgradeId: string

  @ManyToOne(() => SupplierUpgradeEntity, (p) => p.supplierUpgradeDetails)
  @JoinColumn({ name: 'supplierUpgradeId', referencedColumnName: 'id' })
  supplierUpgrade: Promise<SupplierUpgradeEntity>
}
