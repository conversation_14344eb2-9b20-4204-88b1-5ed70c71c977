import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BankRepository } from '../../repositories'
import { BankService } from './bank.service'
import { BankController } from './bank.controller'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BankRepository]), OrganizationalPositionModule],
  controllers: [BankController],
  providers: [BankService],
  exports: [BankService],
})
export class BankModule {}
