import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, JoinColumn, ManyToOne } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { ServiceEntity } from './service.entity'

@Entity('service_access')
export class ServiceAccessEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.serviceAccess)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.serviceAccess)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>
}
