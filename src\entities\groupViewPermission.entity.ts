import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rt, <PERSON>umn, <PERSON><PERSON>ty, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'

export type Permission = {
  code: string
  name: string
  path: string
  view: boolean
  edit: boolean
  delete: boolean
  add: boolean
  watchAnother: boolean
  editAnother: boolean
}

@Entity('group_view_permission')
export class GroupViewPermissionEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: true,
  })
  name: string

  /* danh sách code của nhóm quyền view  */

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value: Permission[]) {
        // Convert array of Permission objects to JSON string
        return JSON.stringify(value ?? [])
      },
      from(value: string) {
        // Convert JSON string back to array of Permission objects
        try {
          return value ? JSON.parse(value) : []
        } catch {
          return []
        }
      },
    },
  })
  viewPermissionCode: Permission[]
}
