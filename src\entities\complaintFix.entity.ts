import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ComplaintEntity } from './complaint.entity'
import { ComplaintItemEntity } from './complaintItem.entity'

/** <PERSON>hắ<PERSON> phục khi<PERSON>u nại */
@Entity('complaint_fix')
export class ComplaintFixEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  complaintId: string
  @ManyToOne(() => ComplaintEntity, (p) => p.complaintFixs)
  @JoinColumn({ name: 'complaintId', referencedColumnName: 'id' })
  complaint: Promise<ComplaintEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  complaintItemId: string
  @ManyToOne(() => ComplaintItemEntity, (p) => p.complaintFixs)
  @JoinColumn({ name: 'complaintItemId', referencedColumnName: 'id' })
  complaintItem: Promise<ComplaintItemEntity>

  /**Code Group enumData: CodeGroup */
  @Column({ type: 'varchar', length: 50, nullable: true })
  codeGroup: string

  /**Task Group enumData: TaskGroup */
  @Column({ type: 'varchar', length: 50, nullable: true })
  taskGroup: string

  /**Task Code Text */
  @Column({ type: 'varchar', length: 250, nullable: true })
  taskCodeText: string

  /**Task Text) */
  @Column({ type: 'varchar', length: 250, nullable: true })
  taskText: string

  /**Nhân viên phụ trách */
  @Column({ type: 'varchar', length: 250, nullable: true })
  employeeUser: string

  /**Planned start */
  @Column({ nullable: true, type: 'datetime' })
  startDate: Date

  /**Planned end */
  @Column({ nullable: true, type: 'datetime' })
  endDate: Date
}
