import { EntityManager, EntityTarget } from 'typeorm'
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity'

export class TypeOrmUtils {
  /**
   * Insert theo batch, dùng EntityManager
   */
  static async batchInsert<T>(manager: EntityManager, entity: EntityTarget<T>, data: T[], maxParams = 2000) {
    if (data.length === 0) return

    // Tính số trường (fields) trong 1 bản ghi đầu tiên
    const fieldsCount = Object.keys(data[0]).length

    // Tính batch size tối đa dựa trên maxParams và fieldsCount
    const batchSize = Math.floor(maxParams / fieldsCount) || 1 // tránh 0 batch size

    const chunks = this.chunkArray(data, batchSize)

    for (const chunk of chunks) {
      await manager.insert(entity, chunk as QueryDeepPartialEntity<T>[])
    }
  }

  private static chunkArray<T>(arr: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < arr.length; i += chunkSize) {
      chunks.push(arr.slice(i, i + chunkSize))
    }
    return chunks
  }
}
