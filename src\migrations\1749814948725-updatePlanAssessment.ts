import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdatePlanAssessment1749814948725 implements MigrationInterface {
  name = 'UpdatePlanAssessment1749814948725'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "plan_site_assessment" ADD "reason" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "plan_site_assessment" DROP COLUMN "reason"`)
  }
}
