import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeMigra1749832598665 implements MigrationInterface {
  name = 'ChangeMigra1749832598665'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "material_uom" ADD "materialCode" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "material_uom" DROP CONSTRAINT "FK_98f3a2d6faf7da88cea55534c99"`)
    await queryRunner.query(`ALTER TABLE "material_uom" ALTER COLUMN "materialId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "material_uom" ADD CONSTRAINT "FK_98f3a2d6faf7da88cea55534c99" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )

    // Check if the column exists first
    const columnExists = await queryRunner.hasColumn('site_assessment', 'count')

    if (!columnExists) {
      await queryRunner.query('ALTER TABLE "site_assessment" ADD "count" bigint')
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "material_uom" DROP CONSTRAINT "FK_98f3a2d6faf7da88cea55534c99"`)
    await queryRunner.query(`ALTER TABLE "material_uom" ALTER COLUMN "materialId" uniqueidentifier NOT NULL`)
    await queryRunner.query(
      `ALTER TABLE "material_uom" ADD CONSTRAINT "FK_98f3a2d6faf7da88cea55534c99" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(`ALTER TABLE "material_uom" DROP COLUMN "materialCode"`)
    await queryRunner.query('ALTER TABLE "site_assessment" DROP COLUMN "count"')
  }
}
