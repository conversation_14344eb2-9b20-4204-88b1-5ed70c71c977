import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateRole1749274460522 implements MigrationInterface {
  name = 'UpdateRole1749274460522'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "group_permission" ADD "companyIdTemp" varchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "companyIdTemp"`)
  }
}
