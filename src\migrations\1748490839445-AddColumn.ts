import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumn1748490839445 implements MigrationInterface {
    name = 'AddColumn1748490839445'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service_hierarchy" ADD "materialId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "cost" DROP COLUMN "description"`);
        await queryRunner.query(`ALTER TABLE "cost" ADD "description" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "service_hierarchy" ADD CONSTRAINT "FK_0aac594eeaf5b59f42d53fc6753" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service_hierarchy" DROP CONSTRAINT "FK_0aac594eeaf5b59f42d53fc6753"`);
        await queryRunner.query(`ALTER TABLE "cost" DROP COLUMN "description"`);
        await queryRunner.query(`ALTER TABLE "cost" ADD "description" varchar(MAX) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "service_hierarchy" DROP COLUMN "materialId"`);
    }

}
