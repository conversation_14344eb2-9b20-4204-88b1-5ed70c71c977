import { BaseEntity as Base, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, BeforeInsert, BeforeUpdate } from 'typeorm'

export abstract class BaseEntity extends Base {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'datetime',
    nullable: true,
  })
  createdAt: Date

  @BeforeInsert()
  updateInsertDates() {
    this.createdAt = new Date()
  }

  /** id của userEntity */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  createdBy: string

  @Column({
    type: 'datetime',
    nullable: true,
  })
  updatedAt: Date

  @BeforeUpdate()
  updateUpdateDates() {
    this.updatedAt = new Date()
  }

  /** id của userEntity */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  updatedBy: string

  @Column({
    name: 'isDeleted',
    nullable: false,
    default: false,
  })
  isDeleted: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId?: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId?: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId?: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prTypeCodeRef?: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  poTypeCodeRef?: string
}
