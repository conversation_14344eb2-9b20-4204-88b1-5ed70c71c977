import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CompanyEntity } from './company.entity'
import { PurchasingOrgEntity } from './purchasingOrg.entity'
import { PurchasingGroupEntity } from './purchasingGroup.entity'
import { EmployeeEntity } from './employee.entity'
import { MediaFileEntity } from './mediaFile.entity'
import { PrEntity } from './pr.entity'
import { ShipmentPlanEntity } from './shipmentPlan.entity'
import { RequestQuoteDetailEntity } from './requestQuoteDetail.entity'
import { RequestQuoteFeeEntity } from './requestQuoteFee.entity'
import { RequestQuoteSupplierEntity } from './requestQuoteSupplier.entity'
import { RfqEntity } from './rfq.entity'
import { PlantEntity } from './plant.entity'

/** Yêu cầu báo giá */
@Entity('request_quote')
export class RequestQuoteEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  /** Tên */
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string

  @ManyToOne(() => CompanyEntity, (p) => p.requestQuotes)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string

  @ManyToOne(() => PurchasingOrgEntity, (p) => p.requestQuotes)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string

  @ManyToOne(() => PurchasingGroupEntity, (p) => p.requestQuotes)
  @JoinColumn({ name: 'purchasingGroupId', referencedColumnName: 'id' })
  purchasingGroup: Promise<PurchasingGroupEntity>

  /** Id Của plant */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.requestQuotes)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  // mục đích báo giá (enumData PurposeQuote)
  @Column({
    type: 'nvarchar',
    length: 10,
    nullable: true,
  })
  purpose: string

  // Loại báo giá (enumData Type Quote)
  @Column({
    type: 'nvarchar',
    length: 10,
    nullable: true,
  })
  typeQuote: string

  // Kỳ báo giá (enumData Quotation Period)
  @Column({
    type: 'nvarchar',
    length: 10,
    nullable: true,
  })
  quotationPeriod: string

  // Thời gian bắt đầu nhận báo giá
  @Column({
    nullable: true,
    type: 'datetime',
  })
  timeStartReceiving: Date

  // Thời gian kết thúc nhận báo giá
  @Column({
    nullable: true,
    type: 'datetime',
  })
  timeEndReceiving: Date

  /**  Số lượng báo giá tối thiếu */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  @OneToMany(() => MediaFileEntity, (p) => p.requestQuote)
  mediaFiles: Promise<MediaFileEntity[]>

  // Thời gian xác nhận tham gia báo giá
  @Column({
    nullable: true,
    type: 'datetime',
  })
  timeConfirmParticipationQuotation: Date

  /** Ghi chú */
  @Column({
    length: 'max',
    nullable: true,
  })
  description: string

  // Nguồn tham chiếu (enumData ReferenceQuote)
  @Column({
    type: 'nvarchar',
    length: 10,
    nullable: true,
  })
  referenceQuote: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId: string

  @ManyToOne(() => PrEntity, (p) => p.requestQuotes)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPlanId: string

  @ManyToOne(() => ShipmentPlanEntity, (p) => p.requestQuotes)
  @JoinColumn({ name: 'shipmentPlanId', referencedColumnName: 'id' })
  shipmentPlan: Promise<ShipmentPlanEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  lstIncotermId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  lstPaymentTermId: string

  @OneToMany(() => RequestQuoteDetailEntity, (p) => p.requestQuote)
  requestQuoteDetails: Promise<RequestQuoteDetailEntity[]>

  // Hình thức báo giá (enumData Quotation form)
  @Column({
    type: 'nvarchar',
    length: 10,
    nullable: true,
  })
  quotationForm: string

  // Hình thức báo giá (enumData Quotation form)
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  status: string

  /**  Hiệu lực báo giá */
  @Column({
    nullable: true,
  })
  quotationValidity: number

  @OneToMany(() => RequestQuoteFeeEntity, (p) => p.requestQuote)
  requestQuoteFees: Promise<RequestQuoteFeeEntity[]>

  @OneToMany(() => RequestQuoteSupplierEntity, (p) => p.requestQuote)
  requestQuoteSuppliers: Promise<RequestQuoteSupplierEntity[]>

  /* shipment conditionTypeid compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactId: string[]

  /* shipment conditionType code compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactCode: string[]

  /* shipment conditionType price compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactValue: string[]

  @OneToMany(() => RfqEntity, (p) => p.requestQuote)
  rfqs: Promise<RfqEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @ManyToOne(() => PlantEntity, (p) => p.requestQuotes)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  requestQuoteParentId: string
}
