import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn, OneToMany } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { RecommendedPurchaseTemplateColEntity } from './recommendedPurchaseTemplateCol.entity'
import { RecommendedPurchaseHistoryEntity } from './recommendedPurchaseHistory.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { RecommendedPurchaseColValueEntity } from './recommendedPurchaseColValue.entity'

/** Đề nghị mua hàng */
@Entity('recommended_purchase_template')
export class RecommendedPurchaseTemplateEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: false,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /** Layout làm tròn cont (RecommendedPurchaseTemplate) */
  @Column({
    length: 20,
    nullable: true,
  })
  type: string

  /** nv tạo */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.recommendedPurchaseTemplates)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @OneToMany(() => RecommendedPurchaseTemplateColEntity, (p) => p.recommendedPurchaseTemplate)
  recommendedPurchaseTemplateCols: Promise<RecommendedPurchaseTemplateColEntity[]>

  @OneToMany(() => RecommendedPurchaseHistoryEntity, (p) => p.recommendedPurchaseTemplate)
  histories: Promise<RecommendedPurchaseHistoryEntity[]>

  @OneToMany(() => RecommendedPurchaseEntity, (p) => p.recommendedPurchaseTemplate)
  recommendedPurchases: Promise<RecommendedPurchaseEntity[]>

  @OneToMany(() => RecommendedPurchaseColValueEntity, (p) => p.recommendedPurchaseTemplate)
  recommendedPurchaseColValues: Promise<RecommendedPurchaseColValueEntity[]>
}
