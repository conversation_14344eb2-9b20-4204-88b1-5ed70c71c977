import { MigrationInterface, QueryRunner } from 'typeorm'

export class AdColumnSupplierNumber1749701219996 implements MigrationInterface {
  name = 'AdColumnSupplierNumber1749701219996'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "role_supplier" ADD "supplierNumberRequestApproveId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "role_fi_supplier" ADD "supplierNumberRequestApproveId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "role_supplier" ADD CONSTRAINT "FK_0e9adb3ac8abe70aeca5cd545ab" FOREIGN KEY ("supplierNumberRequestApproveId") REFERENCES "supplier_number_request_approve"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "role_fi_supplier" ADD CONSTRAINT "FK_c3b0562dac2bd66c9179768ea5f" FOREIGN KEY ("supplierNumberRequestApproveId") REFERENCES "supplier_number_request_approve"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "role_fi_supplier" DROP CONSTRAINT "FK_c3b0562dac2bd66c9179768ea5f"`)
    await queryRunner.query(`ALTER TABLE "role_supplier" DROP CONSTRAINT "FK_0e9adb3ac8abe70aeca5cd545ab"`)
    await queryRunner.query(`ALTER TABLE "role_fi_supplier" DROP COLUMN "supplierNumberRequestApproveId"`)
    await queryRunner.query(`ALTER TABLE "role_supplier" DROP COLUMN "supplierNumberRequestApproveId"`)
  }
}
