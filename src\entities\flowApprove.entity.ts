import { <PERSON>tity, Column, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CompanyEntity } from './company.entity'
import { FlowApproveDetailEntity } from './flowApproveDetail.entity'
import { DepartmentEntity } from './department.entity'

@Entity({ name: 'flow_approve' })
export class FlowApproveEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  flowCode: string

  /* Type */

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: 'None',
  })
  type: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string
  @ManyToOne(() => CompanyEntity, (p) => p.flowApprovies)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  companys: Promise<CompanyEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  departmentId: string
  @ManyToOne(() => DepartmentEntity, (p) => p.flowApprovies)
  @JoinColumn({ name: 'departmentId', referencedColumnName: 'id' })
  departments: Promise<DepartmentEntity>

  @OneToMany(() => FlowApproveDetailEntity, (p) => p.flowApprove)
  flowApprovieDetails: Promise<FlowApproveDetailEntity[]>
}
