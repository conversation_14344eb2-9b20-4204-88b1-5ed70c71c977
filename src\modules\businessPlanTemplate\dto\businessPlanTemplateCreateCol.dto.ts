import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class BusinessPlanTemplateCreateColDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  fomular: string

  isSystemData: boolean

  dataMapping: string

  isEdited: boolean

  colType: string

  isRequired: boolean

  sort: number

  businessPlanTemplateCode: string

  type: string
}
