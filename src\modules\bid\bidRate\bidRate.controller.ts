import { Controller, UseGuards, Post, Body, Get, Param } from '@nestjs/common'
import { BidRateService } from './bidRate.service'
import { JwtAuthGuard } from '../../common/guards'
import { CurrentUser } from '../../common/decorators'
import { PaginationDto, UserDto } from '../../../dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { BidRateService2 } from './bidRate2.service'

/** Đ<PERSON>h giá thầu */
@ApiBearerAuth()
@ApiTags('Bid')
@UseGuards(JwtAuthGuard)
@Controller('bidRates')
export class BidRateController {
  constructor(private readonly service: BidRateService, private readonly service2: BidRateService2) {}

  @ApiOperation({ summary: 'Danh sách gói thầu module đánh giá thầu phân trang' })
  @Post('rate_data')
  public async rateData(@CurrentUser() user: UserDto, @Body() data: { lstData: any; type: any; bidSupplierId: any }) {
    return await this.service.rateData(user, data)
  }

  @ApiOperation({ summary: 'Danh sách gói thầu module đánh giá thầu phân trang' })
  @Post('lock_data')
  public async lockData(@CurrentUser() user: UserDto, @Body() data: { bidSupplierId: string; type: string }) {
    return await this.service.lockRateData(user, data)
  }

  @ApiOperation({ summary: 'Danh sách gói thầu module đánh giá thầu phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  //#region bidTechRate

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp tham gia thầu và tính điểm' })
  @Get('load_tech_rate/:id')
  public async loadTechRate(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.loadTechRate(user, bidId)
  }
  @ApiOperation({ summary: 'Lấy danh sách bidTech và điểm cao nhất tương ứng' })
  @Post('load_best_tech_value')
  public async loadBestTechValue(@CurrentUser() user: UserDto, @Body() data: { bidId: string; bidSupplierId: string }) {
    return await this.service.loadBestTechValue(user, data)
  }
  @ApiOperation({ summary: 'Tạo đánh giá kỹ thuật' })
  @Post('create_tech_rate')
  public async createTechRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.createTechRate(user, data)
  }
  @ApiOperation({ summary: 'Duyệt đánh giá kỹ thuật' })
  @Post('approve_tech_rate')
  public async approveTechRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[]; comment: string }) {
    return await this.service.approveTechRate(user, data)
  }
  @ApiOperation({ summary: 'Từ chối đánh giá kỹ thuật' })
  @Post('reject_tech_rate')
  public async rejectTechRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[]; comment: string }) {
    return await this.service.rejectTechRate(user, data)
  }

  //#endregion

  //#region bidTradeRate

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp tham gia thầu và tính điểm' })
  @Get('load_trade_rate/:id')
  public async loadTradeRate(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.loadTradeRate(user, bidId)
  }
  @ApiOperation({ summary: 'Lấy danh sách sách bidTrade và điểm cao nhất tương ứng' })
  @Post('load_best_trade_value')
  public async loadBestTradeValue(@CurrentUser() user: UserDto, @Body() data: { bidId: string; bidSupplierId: string }) {
    return await this.service.loadBestTradeValue(user, data)
  }
  @ApiOperation({ summary: 'Tạo đánh giá thương mại' })
  @Post('create_trade_rate')
  public async createTradeRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.createTradeRate(user, data)
  }
  @ApiOperation({ summary: 'Duyệt đánh giá thương mại' })
  @Post('approve_trade_rate')
  public async approveTradeRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[]; comment: string }) {
    return await this.service.approveTradeRate(user, data)
  }
  @ApiOperation({ summary: 'Từ chối đánh giá thương mại' })
  @Post('reject_trade_rate')
  public async rejectTradeRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[]; comment: string }) {
    return await this.service.rejectTradeRate(user, data)
  }

  //#endregion

  //#region bidPriceRate

  @ApiOperation({ summary: 'Lấy ds Doanh nghiệp tham gia thầu và tính điểm' })
  @Get('load_price_rate/:id')
  public async loadPriceRate(@CurrentUser() user: UserDto, @Param('id') bidId: string) {
    return await this.service.loadPriceRate(user, bidId)
  }
  @ApiOperation({ summary: 'Lấy danh sách sách bidPrice và điểm cao nhất tương ứng' })
  @Post('load_best_price_value')
  public async loadBestPriceValue(@CurrentUser() user: UserDto, @Body() data: { bidId: string; bidSupplierId: string }) {
    return await this.service.loadBestPriceValue(user, data)
  }
  @ApiOperation({ summary: 'Tạo đánh giá giá' })
  @Post('create_price_rate')
  public async createPriceRate(@CurrentUser() user: UserDto, @Body() data: { id: string; listItem: any[] }) {
    return await this.service.createPriceRate(user, data)
  }

  //#endregion

  //#region Phân tích giá

  @ApiOperation({ summary: 'Xếp hạng theo giá thấp nhất (Giá theo từng hạng mục)' })
  @Post('load_rank_by_min_price')
  public async loadRankByMinPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstId: string[] }) {
    return await this.service2.loadRankByMinPrice(user, data)
  }

  @ApiOperation({ summary: 'Xếp hạng theo tổng giá dạng 1 (Giá theo từng Doanh nghiệp)' })
  @Post('load_rank_by_sum_price')
  public async loadRankBySumPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstId: string[] }) {
    return await this.service2.loadRankBySumPrice(user, data)
  }

  @ApiOperation({ summary: 'Xếp hạng theo tổng giá dạng 2 (Giá theo từng Doanh nghiệp) (Mỗi dòng 1 Doanh nghiệp)' })
  @Post('load_supplier_rank_by_sum_price')
  public async loadSupplierRankBySumPrice(@CurrentUser() user: UserDto, @Body() data: { bidId: string; lstId: string[] }) {
    return await this.service2.loadSupplierRankBySumPrice(user, data)
  }

  //#endregion

  @ApiOperation({ summary: 'Báo cáo kết quả đánh giá' })
  @Get('get_data_report_rate_bid/:bidid')
  public async getDataReportRateBid(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service2.getDataReportRateBid(user, bidId)
  }

  @ApiOperation({ summary: 'In kết quả đánh giá' })
  @Get('get_data_print_rate_bid/:bidid')
  public async getDataPrintRateBid(@CurrentUser() user: UserDto, @Param('bidid') bidId: string) {
    return await this.service2.getDataPrintRateBid(user, bidId)
  }

  //#region Phê duyệt kết thúc thầu

  @ApiOperation({ summary: 'Gửi yêu cầu phê duyệt kết thúc thầu' })
  @Post('send_request_finish_bid')
  public async sendRequestFinishBid(@CurrentUser() user: UserDto, @Body() data: { id: string; fileScan: string; noteFinishBidMPO: string }) {
    return await this.service2.sendRequestFinishBid(user, data)
  }

  @ApiOperation({ summary: 'Phê duyệt kết thúc thầu' })
  @Post('approve_finish_bid')
  public async approveFinishBid(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service2.approveFinishBid(user, data)
  }

  //#endregion

  //#region Đàm phán/ Đấu giá

  @ApiOperation({ summary: 'DS item khi Đàm phán/ Đấu giá' })
  @Post('item_pagination')
  public async itemPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service2.itemPagination(user, data)
  }

  //#endregion

  //#region Truy vấn thông tin gói thầu

  @ApiOperation({ summary: 'Danh sách gói thầu đã hoàn tất' })
  @Post('result_pagination')
  public async resultPagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.resultPagination(user, data)
  }
  //#endregion
}
