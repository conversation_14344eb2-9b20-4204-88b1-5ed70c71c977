import { <PERSON>umn, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { DepartmentEntity } from './department.entity'
import { ComplaintEntity } from './complaint.entity'

/** Khiếu nại - phòng ban */
@Entity('complaint_department')
export class ComplaintDepartmentEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  departmentId: string
  @ManyToOne(() => DepartmentEntity, (p) => p.complaintDepartments)
  @JoinColumn({ name: 'departmentId', referencedColumnName: 'id' })
  department: Promise<DepartmentEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  complaintId: string
  @ManyToOne(() => ComplaintEntity, (p) => p.complaintDepartments)
  @JoinColumn({ name: 'complaintId', referencedColumnName: 'id' })
  complaint: Promise<ComplaintEntity>
}
