import { Column, Entity, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { TransportationPlanDetailEntity } from './transportationPlanDetail.entity'

@Entity('transportation_plan')
export class TransportationPlanEntity extends BaseEntity {
  /* id của shipment */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  shipmentId: string

  /* tổng giá trị  */
  /** Giá đấu trước đó */
  @Column({ type: 'bigint', nullable: true })
  submitPriceOld?: number

  @OneToMany(() => TransportationPlanDetailEntity, (p) => p.shipmentConditionType)
  transportationPlanDetails: Promise<TransportationPlanDetailEntity[]>
}
