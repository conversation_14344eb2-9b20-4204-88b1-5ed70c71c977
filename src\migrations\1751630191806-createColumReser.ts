import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateColumReser1751630191806 implements MigrationInterface {
    name = 'CreateColumReser1751630191806'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "itemDelete" varchar(1)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "itemComplete" varchar(1)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "itemComplete"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "itemDelete"`);
    }

}
