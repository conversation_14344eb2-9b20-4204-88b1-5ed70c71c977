import { MigrationInterface, QueryRunner } from "typeorm";

export class ShipmentConditionTypeLog1751335660150 implements MigrationInterface {
    name = 'ShipmentConditionTypeLog1751335660150'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "shipment_condition_type_log" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c69cedaac950f23f7ab981beabc" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_091a20757af296acca6ed4f27de" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "shipmentConditionTypeTemplateId" uniqueidentifier, "dateFrom" datetime, "dateTo" datetime, "price" bigint, "formatPrice" bigint, "code" varchar(50) NOT NULL, "name" varchar(100) NOT NULL, CONSTRAINT "PK_c69cedaac950f23f7ab981beabc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD CONSTRAINT "FK_60eeeb84ae247d609015fee0fae" FOREIGN KEY ("shipmentConditionTypeTemplateId") REFERENCES "shipment_condition_type_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP CONSTRAINT "FK_60eeeb84ae247d609015fee0fae"`);
        await queryRunner.query(`DROP TABLE "shipment_condition_type_log"`);
    }

}
