import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { LeadTimeDetailEntity } from './leadtimeDetail.entity'
import { MaterialGroupEntity } from './materialGroup.entity'

/** C<PERSON>u hình leadtime */
@Entity('leadtime')
export class LeadTimeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string

  @ManyToOne(() => MaterialGroupEntity, (p) => p.leadtime)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>

  @Column({
    nullable: true,
    type: 'datetime',
  })
  year: Date

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstMaterialGroupId: string

  @OneToMany(() => LeadTimeDetailEntity, (p) => p.leadtime)
  leadtimeDetails: Promise<LeadTimeDetailEntity[]>

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string
}
