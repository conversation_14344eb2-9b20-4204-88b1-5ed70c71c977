import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { BidSupplierEntity } from './bidSupplier.entity'

@Entity('bid_supplier_custom_price_value')
export class BidSupplierCustomPriceValueEntity extends BaseEntity {
  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unit: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currency: string

  /** Số lượng */
  @Column({
    nullable: false,
    default: 0,
  })
  number: number

  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  /** Tên  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidSupplierId: string
  @ManyToOne(() => BidSupplierEntity, (p) => p.bidSupplierCustomPriceValue)
  @JoinColumn({ name: 'bidSupplierId', referencedColumnName: 'id' })
  bidSupplier: Promise<BidSupplierEntity>
}
