import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSupplierTransient1752049631501 implements MigrationInterface {
    name = 'AddSupplierTransient1752049631501'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier" ADD "isTransient" bit CONSTRAINT "DF_7ba128543c59eab3c3048f2acb8" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_7ba128543c59eab3c3048f2acb8"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "isTransient"`);
    }

}
