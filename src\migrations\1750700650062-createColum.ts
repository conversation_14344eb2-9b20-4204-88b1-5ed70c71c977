import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateColum1750700650062 implements MigrationInterface {
  name = 'CreateColum1750700650062'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "budget_receipt_item" ADD "fund" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "pr_item" ADD "ciname" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "ciname"`)
    await queryRunner.query(`ALTER TABLE "budget_receipt_item" DROP COLUMN "fund"`)
  }
}
