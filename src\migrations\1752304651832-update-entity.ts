import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateEntity1752304651832 implements MigrationInterface {
  name = 'UpdateEntity1752304651832'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "business_template_plan_exchange" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_d082b61497a1a42324629c59c08" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_9a87c90c989a15f49540774e953" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "businessTemplatePlanId" uniqueidentifier NOT NULL, "fromCurrency" varchar(10), "fromCurrencyId" varchar(10), "toCurrency" varchar(10), "toCurrencyId" varchar(10), "exchangeRate" decimal(10,2), "exchangeRateInverse" decimal(10,2), CONSTRAINT "PK_d082b61497a1a42324629c59c08" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "plantId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "businessTemplatePlanTypeId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "loanTerm" int`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "currency" varchar(10)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "incotermId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "business_template_plan_exchange" ADD CONSTRAINT "FK_1b956422e3b4dbc517fd661af30" FOREIGN KEY ("businessTemplatePlanId") REFERENCES "business_template_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "business_template_plan" ADD CONSTRAINT "FK_3a8a5c7f5c31add414fc5202219" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "business_template_plan" ADD CONSTRAINT "FK_652917d8226d4f9b5d673d33aaa" FOREIGN KEY ("businessTemplatePlanTypeId") REFERENCES "business_template_plan_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "business_template_plan" ADD CONSTRAINT "FK_aba25599797663379750efdf9af" FOREIGN KEY ("incotermId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP CONSTRAINT "FK_aba25599797663379750efdf9af"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP CONSTRAINT "FK_652917d8226d4f9b5d673d33aaa"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP CONSTRAINT "FK_3a8a5c7f5c31add414fc5202219"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan_exchange" DROP CONSTRAINT "FK_1b956422e3b4dbc517fd661af30"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "incotermId"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "currency"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "loanTerm"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "businessTemplatePlanTypeId"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "plantId"`)
    await queryRunner.query(`DROP TABLE "business_template_plan_exchange"`)
  }
}
