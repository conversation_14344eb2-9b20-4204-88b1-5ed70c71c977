import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { BidPriceEntity } from './bidPrice.entity'
import { BidDealSupplierEntity } from './bidDealSupplier.entity'

@Entity('bid_deal_supplier_price_value')
export class BidDealSupplierPriceValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidDealSupplierId: string
  @ManyToOne(() => BidDealSupplierEntity, (p) => p.bidDealSupplierPriceValue)
  @JoinColumn({ name: 'bidDealSupplierId', referencedColumnName: 'id' })
  bidDealSupplier: Promise<BidDealSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidDealSupplierPriceValue)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>
}
