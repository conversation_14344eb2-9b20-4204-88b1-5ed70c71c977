import { <PERSON>ti<PERSON>, Column, <PERSON>To<PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierServiceEntity } from './supplierService.entity'

/** Sản phẩm và dịch vụ NCC sản xuất/kinh doanh ở lĩnh vực kinh doanh */
@Entity('supplier_product_service')
export class SupplierProductServiceEntity extends BaseEntity {
  /**  Tên */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: false,
  })
  name: string

  // Năng lực cung cấp/Tháng
  @Column({
    nullable: true,
    default: 0,
  })
  supplyCapacityPerMonth: number

  /** Lĩnh vực kinh doanh NCC  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierServiceId: string
  @ManyToOne(() => SupplierServiceEntity, (p) => p.productServices)
  @JoinColumn({ name: 'supplierServiceId', referencedColumnName: 'id' })
  supplierService: Promise<SupplierServiceEntity>
}
