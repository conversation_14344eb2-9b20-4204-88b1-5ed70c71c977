import { Injectable, NestMiddleware, BadRequestException } from '@nestjs/common'
import { Request, Response, NextFunction } from 'express'

@Injectable()
export class SqlInjectionMiddleware implements NestMiddleware {
  private readonly blacklistRegex = [
    /\bselect\b/i,
    /\binsert\b/i,
    /\bupdate\b/i,
    /\bdelete\b/i,
    /\bdrop\b/i,
    /\bunion\b/i,
    /\bexec\b/i,
    /\bdeclare\b/i,
    /\balter\b/i,
    /\bbegin\b/i,
    /\bcast\b/i,
    /\bcreate\b/i,
    /\bcursor\b/i,
    /\bfetch\b/i,
    /\bkill\b/i,
    /\bopen\b/i,
    /\bsys\b/i,
    /\bsysobjects\b/i,
    /\bsyscolumns\b/i,
    /\btable\b/i,
    /\binformation_schema\b/i,
    /--/,
    /;/,
    /\/\*/,
    /\*\//,
    /'/,
    /"/,
  ]

  use(req: Request, res: Response, next: NextFunction) {
    const containsSqlInjection = (value: any): boolean => {
      if (!value) return false
      if (typeof value !== 'string') return false

      return this.blacklistRegex.some((regex) => regex.test(value))
    }

    const checkObject = (obj: any) => {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const val = obj[key]
          if (typeof val === 'string') {
            if (containsSqlInjection(val)) {
              throw new BadRequestException(`SQL Injection detected in field '${key}'`)
            }
          } else if (typeof val === 'object' && val !== null) {
            checkObject(val)
          }
        }
      }
    }

    try {
      checkObject(req.query)
      // checkObject(req.body)
    } catch (e) {
      return res.status(400).json({ message: e.message })
    }

    next()
  }
}
