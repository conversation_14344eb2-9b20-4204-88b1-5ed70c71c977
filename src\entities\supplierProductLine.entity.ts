import { Entity, Column, <PERSON>T<PERSON><PERSON>ne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierServiceEntity } from './supplierService.entity'
import { Transform } from 'class-transformer'

/** Nhà xưởng và thiết bị ở lĩnh vực kinh doanh */
@Entity('supplier_product_line')
export class SupplierProductLineEntity extends BaseEntity {
  /**  Bước quy trình sản xuất */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  step: string

  /**Tên thiết bị */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  equipmentName: string

  /**Số lượng thiết bị */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  @Column({ type: 'datetime', nullable: true })
  @Transform(({ value }) => (value ? new Date(value) : null))
  commissioningYear: Date

  /**Công suất thiết kế */
  @Column({
    nullable: true,
    default: 0,
  })
  designCapacity: number

  /**Công suất thực tế */
  @Column({
    nullable: true,
    default: 0,
  })
  actualCapacity: number

  /** Lĩnh vực kinh doanh NCC  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierServiceId: string
  @ManyToOne(() => SupplierServiceEntity, (p) => p.supplierProductLines)
  @JoinColumn({ name: 'supplierServiceId', referencedColumnName: 'id' })
  supplierService: Promise<SupplierServiceEntity>
}
