import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeColums1752032074961 implements MigrationInterface {
    name = 'ChangeColums1752032074961'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" ADD "externalMaterialGroupId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" ADD "serviceId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" ADD CONSTRAINT "FK_ed1315055da0984f49be863a4d7" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" DROP CONSTRAINT "FK_ed1315055da0984f49be863a4d7"`);
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" DROP COLUMN "serviceId"`);
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" DROP COLUMN "externalMaterialGroupId"`);
    }

}
