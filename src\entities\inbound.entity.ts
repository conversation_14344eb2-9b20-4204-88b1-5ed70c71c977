import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne, OneToMany, Index } from 'typeorm'
import { POEntity, EmployeeEntity, WarehouseEntity, InboundItemEntity, InboundContainerEntity } from '.'
import { ShipmentInboundEntity } from './shipmentInbound.entity'
import { InboundDocumentHandoverEntity } from './inboundDocumentHandover.entity'
import { ShipmentItemEntity } from './shipmentItem.entity'

/** Thông tin inbound*/
@Entity('inbound')
export class InboundEntity extends BaseEntity {
  /**Trạng thái  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Mã inbound */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  /** Tên inbound */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  name: string

  @Index({ unique: true })
  /** inbound number*/
  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  inboundNumber: string

  /** id nguồn tham chiếu */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  referenceId: string

  /** Ngày giao hàng */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  /** Thời gian về kho dự kiến */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  dateArrivalWarehouse: Date

  /** Nhân viên chịu trách nhiệm */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeInchargeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.inbounds)
  @JoinColumn({ name: 'employeeInchargeId', referencedColumnName: 'id' })
  employeeIncharge: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.inbounds)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  /**Kho hàng dự kiến nhận */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  expectWarehouseId: string

  /**Inbound được tạo từ nhà cung cấp hay là từ trang admin: true ? Nhà cung cấp : trang Admin */
  @Column({
    nullable: true,
    default: false,
  })
  isSupplierCreate: boolean

  /** Thời gian dự kiến về cảng */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  dateArrivalPort: Date

  @ManyToOne(() => WarehouseEntity, (p) => p.inbounds)
  @JoinColumn({ name: 'expectWarehouseId', referencedColumnName: 'id' })
  expectWarehouse: Promise<WarehouseEntity>

  @OneToMany(() => InboundItemEntity, (p) => p.inbound)
  inboundItems: Promise<InboundItemEntity[]>

  @OneToMany(() => InboundContainerEntity, (p) => p.inbound)
  inboundContainers: Promise<InboundContainerEntity[]>

  @OneToMany(() => ShipmentInboundEntity, (p) => p.inbound)
  shipmentInbounds: Promise<ShipmentInboundEntity[]>

  @OneToMany(() => InboundDocumentHandoverEntity, (p) => p.inbound)
  documentHandovers: Promise<InboundDocumentHandoverEntity[]>

  @OneToMany(() => ShipmentItemEntity, (p) => p.inbound)
  shipmentItems: Promise<InboundDocumentHandoverEntity[]>
}
