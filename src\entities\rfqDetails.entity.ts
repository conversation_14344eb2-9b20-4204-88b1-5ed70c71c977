import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { MaterialEntity } from './material.entity'
import { RfqEntity } from './rfq.entity'
import { UomEntity } from './uom.entity'
import { ExternalMaterialGroupEntity, MaterialGroupEntity } from '.'

@Entity('rfq_details')
export class RfqDetailsEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  rfqId: string
  @ManyToOne(() => RfqEntity, (p) => p.rfqDetails)
  @JoinColumn({ name: 'rfqId', referencedColumnName: 'id' })
  rfq: Promise<RfqEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.rfqDetails)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @Column({
    nullable: true,
    default: false,
  })
  isSynchronizing: boolean

  /** số lượng cần đặt hàng */
  @Column({
    nullable: true,
    default: 0,
  })
  rfqQuantity: number

  /* id của lần đàm phán giá nào */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  dealId: string

  /** Thời gian giao hàng */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string

  @Column({
    nullable: true,
    default: false,
  })
  isFinal: boolean

  /** Item Line */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  itemNo: string

  // TrackingNo
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  tracking: string

  /** 1st Rem./Exped. */
  @Column({
    nullable: true,
  })
  remind1: number

  /** 2nd Rem./Exped. */
  @Column({
    nullable: true,
  })
  remind2: number

  /** 3rd Rem./Exped. */
  @Column({
    nullable: true,
  })
  remind3: number

  /** No. Exped. */
  @Column({
    nullable: true,
  })
  remindNo: number

  //Net Order Price
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  netPrice: number

  // Price unit
  @Column({
    nullable: true,
  })
  priceUnit: number

  // Order Price Unit (Purchasing)
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.rfqDetails)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  /** quo_cmt  Nhận xét nội bộ */
  @Column({
    type: 'nvarchar',
    nullable: true,
    length: 1000,
  })
  quotComment: string

  /** Info_update (A) */
  @Column({
    type: 'nvarchar',
    nullable: true,
    length: 10,
  })
  infoUpdate: string

  /** tax Tax Code */
  @Column({
    type: 'nvarchar',
    nullable: true,
    length: 500,
  })
  tax: string

  /** Ghi chú item texta */
  @Column({
    type: 'nvarchar',
    nullable: true,
    length: 1000,
  })
  itemText: string

  /** Material PO text Ghi chú item textb */
  @Column({
    type: 'nvarchar',
    nullable: true,
    length: 1000,
  })
  materialPoText: string

  /** Additional text for item textc */
  @Column({
    type: 'nvarchar',
    nullable: true,
    length: 1000,
  })
  additionalText: string

  /** Stock Information Text item textd */
  @Column({
    type: 'nvarchar',
    nullable: true,
    length: 1000,
  })
  stockInformationText: string

  /** Order */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  orderCode: string

  /** Order */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  orderName: string

  /** asset */
  @Column({
    length: 500,
    nullable: true,
  })
  assetCode: string

  /** asset Desc */
  @Column({
    length: 500,
    nullable: true,
  })
  assetDesc: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.rfqDetails)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string

  @ManyToOne(() => MaterialGroupEntity, (p) => p.rfqDetails)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>
}
