import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { MaterialEntity } from './material.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { RecommendedPurchaseTemplateEntity } from './recommendedPurchaseTemplate.entity'
import { RecommendedPurchaseTemplateColEntity } from './recommendedPurchaseTemplateCol.entity'

@Entity('recommended_purchase_col_value')
export class RecommendedPurchaseColValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.recommendedPurchaseColValues)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseId: string
  @ManyToOne(() => RecommendedPurchaseEntity, (p) => p.recommendedPurchaseColValues)
  @JoinColumn({ name: 'recommendedPurchaseId', referencedColumnName: 'id' })
  recommendedPurchase: Promise<RecommendedPurchaseEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseTemplateId: string
  @ManyToOne(() => RecommendedPurchaseTemplateEntity, (p) => p.recommendedPurchaseColValues)
  @JoinColumn({ name: 'recommendedPurchaseTemplateId', referencedColumnName: 'id' })
  recommendedPurchaseTemplate: Promise<RecommendedPurchaseTemplateEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseTemplateColId: string
  @ManyToOne(() => RecommendedPurchaseTemplateColEntity, (p) => p.recommendedPurchaseColValues)
  @JoinColumn({ name: 'recommendedPurchaseTemplateColId', referencedColumnName: 'id' })
  recommendedPurchaseTemplateCol: Promise<RecommendedPurchaseTemplateColEntity>

  /** Quantity */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  quantity: number
}
