import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { JwtAuthGuard } from '../common/guards'
import { CurrentUser } from '../common/decorators'
import { BankService } from './bank.service'
import { BankCreateDto, BankCreateExcelDto, BankUpdateDto } from './dto'

@ApiBearerAuth()
@ApiTags('Bank')
@Controller('bank')
export class BankController {
  constructor(private readonly service: BankService) {}

  @ApiOperation({ summary: '<PERSON><PERSON>y danh sách ' })
  @UseGuards(JwtAuthGuard)
  @Post('find')
  public async find(@CurrentUser() user: UserDto) {
    return await this.service.find(user)
  }

  @ApiOperation({ summary: 'Danh sách phân trang' })
  @UseGuards(JwtAuthGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới một với dữ liệu được cung cấp.' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: BankCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin của với dữ liệu được cung cấp.' })
  @UseGuards(JwtAuthGuard)
  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: BankUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái kích hoạt của.' })
  @UseGuards(JwtAuthGuard)
  @Post('update_active')
  async updateActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Tạo mới một với danh sách dữ liệu danh được cung cấp.' })
  @UseGuards(JwtAuthGuard)
  @Post('import_data')
  async importData(@CurrentUser() user: UserDto, @Body() data: BankCreateExcelDto[]) {
    return await this.service.importData(user, data)
  }

  @ApiOperation({ summary: 'Load data select' })
  @Post('load_data_select')
  async loadDataSelect(@CurrentUser() user: UserDto) {
    return await this.service.loadDataSelect(user)
  }
}
