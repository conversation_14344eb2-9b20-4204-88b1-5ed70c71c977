import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, Index } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
import { BusinessTemplatePlanEntity } from './businessTemplatePlan.entity'
import { BusinessTemplateGroupPlanEntity } from './businessTemplateGroupPlan.entity'

/** Thông tin loại hình doanh nghiệp*/
@Entity('business_template_plan_type')
export class BusinessTemplatePlanTypeEntity extends BaseEntity {
  /** Mã */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  code: string

  /** name */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  name: string

  /**Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        return JSON.stringify(value)
      },
      from(value) {
        return value ? JSON.parse(value) : {}
      },
    },
  })
  configTable: any

  @OneToMany(() => BusinessTemplatePlanEntity, (p) => p.businessTemplatePlanType)
  businessTemplatePlans: Promise<BusinessTemplatePlanEntity[]>

  @OneToMany(() => BusinessTemplateGroupPlanEntity, (p) => p.businessTemplatePlanType)
  businessTemplateGroupPlan: Promise<BusinessTemplateGroupPlanEntity[]>
}
