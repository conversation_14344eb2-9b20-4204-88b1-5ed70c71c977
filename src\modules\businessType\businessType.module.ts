import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'
import { BusinessTypeController } from './businessType.controller'
import { BusinessTypeService } from './businessType.service'
import { BusinessTypeRepository } from '../../repositories/businessType.repository'
import { SupplierRepository } from '../../repositories'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BusinessTypeRepository, SupplierRepository]), OrganizationalPositionModule],
  controllers: [BusinessTypeController],
  providers: [BusinessTypeService],
})
export class BusinessTypeModule {}
