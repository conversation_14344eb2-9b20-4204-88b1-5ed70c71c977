import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { MaterialEntity } from './material.entity'
import { UomEntity } from './uom.entity'
import { ContractInspectionEntity } from './contractInspection.entity'

/** item của nghiệm hợp đồng */
@Entity({ name: 'contract_inspection_item' })
export class ContractInspectionItemEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** material */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.contractInspectionItems)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /**Short text */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  shortText: string

  /** S<PERSON> lượng hợp đồng */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  /** Số lượng nghiệm thu */
  @Column({
    nullable: true,
    default: 0,
  })
  inspectionQuantity: number

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.contractInspectionItems)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  /**Đơn giá */
  @Column({
    nullable: false,
  })
  price: number

  /**Total price */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  totalPrice: number

  /** Nghiệp thu hợp đồng*/
  @Column({
    type: 'varchar',
    nullable: false,
  })
  contractInspectionId: string
  @ManyToOne(() => ContractInspectionEntity, (p) => p.contractInspectionItems)
  @JoinColumn({ name: 'contractInspectionId', referencedColumnName: 'id' })
  contractInspection: Promise<ContractInspectionEntity>
}
