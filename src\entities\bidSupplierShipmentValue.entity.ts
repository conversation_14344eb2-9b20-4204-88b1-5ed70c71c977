import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { BidSupplierEntity } from './bidSupplier.entity'
import { BidTechEntity } from './bidTech.entity'
import { ShipmentCostPriceEntity } from './shipmentCostPrice.entity'

@Entity('bid_supplier_shipment_value')
export class BidSupplierShipmentValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidSupplierId: string
  @ManyToOne(() => BidSupplierEntity, (p) => p.bidSupplierShipmentTechValue)
  @JoinColumn({ name: 'bidSupplierId', referencedColumnName: 'id' })
  bidSupplier: Promise<BidSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPriceId: string
  @ManyToOne(() => ShipmentCostPriceEntity, (p) => p.bidSupplierShipmentTechValue)
  @JoinColumn({ name: 'shipmentPriceId', referencedColumnName: 'id' })
  shipmentPrice: Promise<ShipmentCostPriceEntity>
}
