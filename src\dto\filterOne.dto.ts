import { ApiProperty } from '@nestjs/swagger'

/** Interface Id */
export class FilterOneDto {
  @ApiProperty({ description: 'Id của đối tượng', example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  id?: string
  @ApiProperty({ description: 'code của đối tượng' })
  code?: string

  @ApiProperty({ description: 'status của đối tượng' })
  status?: string

  @ApiProperty({ description: 'prParentId của đối tượng' })
  prParentId?: string

  level?: number

  @ApiProperty({ description: 'chỉ lấy những Pr tổng hợp hoặc PR chưa gộp' })
  getNotCreateGroup?: boolean

  isMobile?: boolean

  purchasingOrgId?: string

  externalMaterialGroupId?: string

  lstPr?: string[]

  roundUpContTemplateId?: string

  recommendedPurchaseTemplateId?: string

  businessPlanTemplateId?: string

  procedureId?: string

  materialId?: string

  supplierId?: string

  poId?: string

  isNotChild?: boolean

  businessPartnerGroupId?: string

  companyId?: string
}
