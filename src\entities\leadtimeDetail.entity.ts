import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'

import { LeadTimeEntity } from './leadtime.entity'
import { MaterialEntity } from './material.entity'
import { MaterialGroupEntity } from './materialGroup.entity'

@Entity('leadtime_detail')
export class LeadTimeDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string

  @ManyToOne(() => MaterialGroupEntity, (p) => p.leadtimeDetails)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  leadtimeId: string

  @ManyToOne(() => LeadTimeEntity, (p) => p.leadtimeDetails)
  @JoinColumn({ name: 'leadtimeId', referencedColumnName: 'id' })
  leadtime: Promise<LeadTimeEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string

  @ManyToOne(() => MaterialEntity, (p) => p.leadtimeDetails)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @Column({ nullable: true, type: 'datetime' })
  startDate: Date

  @Column({ nullable: true, type: 'datetime' })
  expireDate: Date

  /** Rouding (tròn kiện cân đối MRP)*/
  @Column({
    type: 'float',
    nullable: true,
  })
  rouding: number

  /** MOQ MUA HÀNG */
  @Column({
    type: 'float',
    nullable: true,
  })
  moqToPull: number

  /** MOQ MUA HÀNG */
  @Column({
    type: 'float',
    nullable: true,
  })
  moqBuy: number

  /**  Quy cách đóng gói */
  @Column({
    type: 'float',
    nullable: true,
  })
  packagingSpecifications: number

  /** đề nghị mua hàng */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfRecommendedPurchase: number

  /** Hợp đồng */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfContract: number

  /** TG NCC chuẩn bị nguyên liệu */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfSupplierPrepare: number

  /** NCC sản xuất */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfSupplierProduction: number

  /** TG từ NCC sx xong đến cảng */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfSupplierProductionToPort: number

  /** TG vận chuyển từ cảng NCC về VN */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfTransportSupplierToVietNam: number

  /** TG vận chuyển từ cảng VN về kho KT */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfTransportVietNamToWarehouse: number

  /** Kiểm tra chất lượng + Nhập kho */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfQualityCheckAndReceiving: number

  /** Tổng leadtime mua hàng */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfLeadtimePurchase: number

  /** Tổng leadtime kéo hàng */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfLeadtimeDelivery: number

  /** Quý */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  quarter: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialCode: string
}
