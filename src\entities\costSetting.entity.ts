import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { CostEntity } from './cost.entity'

/** Thông tin shipment cost type*/
@Entity('cost_setting')
export class CostSettingEntity extends BaseEntity {
  /** Mã */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  code: string

  /** Tên */
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => CostEntity, (p) => p.costSetting)
  costs: Promise<CostEntity[]>
}
