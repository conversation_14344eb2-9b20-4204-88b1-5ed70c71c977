import { Injectable } from '@nestjs/common'
import * as AWS from 'aws-sdk'
import { ConfigService } from '@nestjs/config'
import { customAlphabet } from 'nanoid'
import { coreHelper } from '../../helpers'
const nanoid = customAlphabet('0123456789abcdefghijklmnopqrstuvwxyz', 5)
import * as moment from 'moment'
import { ShipmentRepository, TransportationPlanDetailRepository, TransportationPlanRepository } from '../../repositories'
import { ShipmentConditionTypeTemplateRepository, ShipmentConditionTypeTemplateValueRepository } from '../../repositories/shipmentTemplate.repository'
import { In, LessThanOrEqual, MoreThanOrEqual } from 'typeorm'

@Injectable()
export class TransportationPlanService {
  constructor(
    private readonly transportationPlanRepository: TransportationPlanRepository,
    private readonly transportationPlanDetailRepository: TransportationPlanDetailRepository,
    private readonly shipmentRepository: ShipmentRepository,
    private readonly shipmentConditionTypeTemplateRepository: ShipmentConditionTypeTemplateRepository,
    private readonly shipmentConditionTypeTemplateValueRepository: ShipmentConditionTypeTemplateValueRepository,
  ) {}

  /* hàm kiểm tra shipment có map với những điều kiện trong bảng giá đã thiết lập */
  public async checkShipmentMapWithPrice(shipmentId: string, lstConditionType: string[]) {
    /* kiểm tra shipment có tồn tại không */
    const shipment = await this.shipmentRepository.findOne({
      where: { id: shipmentId, isDeleted: false },
      select: { id: true },
    })
    if (!shipment) {
      throw new Error('Không tìm thấy shipment!')
    }
    /* tìm danh sách record dựa theo lstConditionType truyền vào*/
    const lstTemplateRecord = await this.shipmentConditionTypeTemplateRepository.find({
      where: {
        shipmentConditionTypeId: In(lstConditionType),
        dateFrom: LessThanOrEqual(new Date()),
        dateTo: MoreThanOrEqual(new Date()),
        isDeleted: false,
      },
    })
    if (!lstTemplateRecord || lstTemplateRecord.length === 0 || lstTemplateRecord.length !== lstConditionType.length) {
      throw new Error('Không có bảng giá hoặc bảng giá đã bị hủy!')
    }
    /* for qua danh sách template record  */
    for (const templateRecord of lstTemplateRecord) {
      /* tìm danh sách giá trị của bảng giá */
      // for qua danh sách key của bảng giá
      templateRecord.conditionCodeCompact.forEach(async (key) => {})
    }
    return {}
  }
}
