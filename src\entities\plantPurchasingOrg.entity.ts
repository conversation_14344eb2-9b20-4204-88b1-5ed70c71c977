import { <PERSON><PERSON><PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PlantEntity } from './plant.entity'
import { PurchasingOrgEntity } from './purchasingOrg.entity'

@Entity('plant_purchasing_org')
export class PlantPurchaseOrgEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  plantId: string

  // @Column({
  //   type: 'varchar',
  //   nullable: false,
  // })
  // purchasingOrgId: string

  @ManyToOne(() => PlantEntity, (p) => p.plantPurchaseOrgs)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @ManyToOne(() => PurchasingOrgEntity, (p) => p.plantPurchaseOrgs)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>
}
