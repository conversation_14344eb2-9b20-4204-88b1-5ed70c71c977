import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { SiteAssessmentEntity } from '.'
import { CriteriaSiteAssessmentChildEntity } from './criteriaSiteAssessmentChild.entity'

// Tiêu chí đánh giá hiện trường
@Entity('criteria_site_assessment')
export class CriteriaSiteAssessmentEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: false,
  })
  content: string

  @Column({
    type: 'float',
    default: 0,
  })
  weight: number

  /** <PERSON><PERSON>h giá hiện trường */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  siteAssessmentId: string
  @ManyToOne(() => SiteAssessmentEntity, (p) => p.criteriaSiteAssessments)
  @JoinColumn({ name: 'siteAssessmentId', referencedColumnName: 'id' })
  siteAssessment: Promise<SiteAssessmentEntity>

  /** <PERSON>h sách tiêu chí con */
  @OneToMany(() => CriteriaSiteAssessmentChildEntity, (p) => p.criteriaSiteAssessment)
  criteriaSiteAssessmentChilds: Promise<CriteriaSiteAssessmentChildEntity[]>
}
