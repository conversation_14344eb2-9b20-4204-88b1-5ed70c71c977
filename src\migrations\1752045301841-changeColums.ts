import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeColums1752045301841 implements MigrationInterface {
  name = 'ChangeColums1752045301841'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "DF_160bf556376d13ebd195fc18a29"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" ADD CONSTRAINT "DF_160bf556376d13ebd195fc18a29" DEFAULT 0 FOR "checked"`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "DF_160bf556376d13ebd195fc18a29"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" ADD CONSTRAINT "DF_160bf556376d13ebd195fc18a29" DEFAULT 1 FOR "checked"`)
  }
}
