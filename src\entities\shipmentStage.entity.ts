import { BidEntity, BidSupplierEntity, OfferEntity, OfferSupplierEntity, ShipmentEntity, SupplierEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany, OneToOne } from 'typeorm'

/** Thông tin shipment stage*/
@Entity('shipment_stage')
export class ShipmentStageEntity extends BaseEntity {
  /** json bảng giá */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  jsonState: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentId: string
  @ManyToOne(() => ShipmentEntity, (p) => p.stages)
  @JoinColumn({ name: 'shipmentId', referencedColumnName: 'id' })
  shipment: Promise<ShipmentEntity>

  /**Nguồn tham chiếu */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  referenceSource: string

  /** tham chiếu gói thầu */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.stages)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  /** bid supplier */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidSupplierId: string
  @ManyToOne(() => BidSupplierEntity, (p) => p.stages)
  @JoinColumn({ name: 'bidSupplierId', referencedColumnName: 'id' })
  bidSupplier: Promise<BidSupplierEntity>

  /** tham chiếu báo giá */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string
  @ManyToOne(() => OfferEntity, (p) => p.stages)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  /** offer supplier */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerSupplierId: string
  @ManyToOne(() => OfferSupplierEntity, (p) => p.stages)
  @JoinColumn({ name: 'offerSupplierId', referencedColumnName: 'id' })
  offerSupplier: Promise<OfferSupplierEntity>
}
