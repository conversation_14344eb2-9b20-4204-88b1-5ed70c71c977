import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateMigration1753073674426 implements MigrationInterface {
  name = 'UpdateMigration1753073674426'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_config_template" ADD "shipmentFeeConditionTypeCompactId" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "shipment_config_template" ADD "shipmentFeeConditionTypeCompactCode" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "shipment_config_template" ADD "shipmentFeeConditionTypeCompactValue" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "shipment_config_template" ADD "configTable" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_config_template" DROP COLUMN "configTable"`)
    await queryRunner.query(`ALTER TABLE "shipment_config_template" DROP COLUMN "shipmentFeeConditionTypeCompactValue"`)
    await queryRunner.query(`ALTER TABLE "shipment_config_template" DROP COLUMN "shipmentFeeConditionTypeCompactCode"`)
    await queryRunner.query(`ALTER TABLE "shipment_config_template" DROP COLUMN "shipmentFeeConditionTypeCompactId"`)
  }
}
