import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>any } from 'typeorm'
import { BaseEntity } from './base.entity'
import { MaterialEntity } from './material.entity'
import { PlantEntity } from './plant.entity'
import { ContractItemEntity } from './contractItem.entity'
import { ContractAppendixItemEntity } from './contractAppendixItem.entity'

@Entity('material_storage_location')
export class MaterialStorageLocationEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  materialCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  plantCode: string

  // Storage location
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  storageLocation: string

  // Current period
  @Column({
    type: 'float',
    nullable: true,
  })
  currentPeriod: number

  // DF stor. loc. level
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  dfStorLocLevel: string

  //Year current period
  @Column({
    type: 'float',
    nullable: true,
  })
  yearCurrentPeriod: number

  // Maintenance status
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  maintenanceStatus: string

  // Unrestricted
  @Column({
    type: 'float',
    nullable: true,
  })
  unrestricted: number

  @ManyToOne(() => MaterialEntity, (p) => p.materialStorageLocations)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @ManyToOne(() => PlantEntity, (p) => p.materialStorageLocations)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @OneToMany(() => ContractItemEntity, (p) => p.materialStorageLocation)
  contractItems: Promise<ContractItemEntity[]>

  @OneToMany(() => ContractAppendixItemEntity, (p) => p.materialStorageLocation)
  contractAppendixItemUons: Promise<ContractAppendixItemEntity[]>
}
