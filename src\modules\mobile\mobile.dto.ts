/**
 * <PERSON><PERSON><PERSON><PERSON> t<PERSON>h của một yêu cầu phê duy<PERSON> (screen)
 */
export type RequirementDTO = {
  group: string
  groupName: string
  total: number
  name: string
  screen: string
  params: any
}

/**
 * <PERSON>h sách yêu cầu phê duyệt của các màn hình (screen)
 */
export type ListRequirementDTO = Array<RequirementDTO>

/**
 * Group requirement để hiển thị collapse
 */
export type CollapseRequirementDTO = {
  group: string
  groupName: string
  totalList: number
  list: ListRequirementDTO
}

/**
 * List of Group requirement để hiển thị collapse
 */
export type ListCollapseRequirementDTO = Array<CollapseRequirementDTO>

/**
 * Data mock
 */

// export const MoreStackRequirement: Array<RequirementDTO> = [
//   {
//     group: "MoreStack",
//     groupName: "Duyệt hợp đồng",
//     total: 1,
//     name: "<PERSON><PERSON>",
//     screen: "PO",
//     params: {},
//   },
//   {
//     group: "MoreStack",
//     groupName: "<PERSON><PERSON><PERSON><PERSON> hợp đồng",
//     total: 2,
//     name: "<PERSON><PERSON> s<PERSON><PERSON> hợp đồng",
//     screen: "Contract",
//     params: {},
//   },
//   {
//     group: "MoreStack",
//     groupName: "Duyệt hợp đồng",
//     total: 3,
//     name: "Phụ lục hợp đồng",
//     screen: "Annex",
//     params: {},
//   },
// ];

// export const BiddingStackRequirement: Array<RequirementDTO> = [
//   {
//     group: "BiddingStack",
//     groupName: "Đấu thầu",
//     total: 1,
//     name: "Duyệt gói thầu tạm",
//     screen: "Template",
//     params: {},
//   },
//   {
//     group: "BiddingStack",
//     groupName: "Đấu thầu",
//     total: 2,
//     name: "Duyệt thiết lập yêu cầu kỹ thuật",
//     screen: "InfoRequirement",
//     params: {},
//   },
//   {
//     group: "BiddingStack",
//     groupName: "Đấu thầu",
//     total: 3,
//     name: "Duyệt thiết lập bảng giá, cơ cấu giá, ĐKTM, NCC",
//     screen: "PriceRequirement",
//     params: {},
//   },
//   {
//     group: "BiddingStack",
//     groupName: "Đấu thầu",
//     total: 4,
//     name: "Duyệt nhà cung cấp",
//     screen: "Contractor",
//     params: {},
//   },
//   {
//     group: "BiddingStack",
//     groupName: "Đấu thầu",
//     total: 5,
//     name: "Duyệt đánh giá yêu cầu kĩ thuật",
//     screen: "EvaluationInfo",
//     params: {},
//   },
//   {
//     group: "BiddingStack",
//     groupName: "Đấu thầu",
//     total: 0,
//     name: "Duyệt đánh giá bảng giá, cơ cấu giá, ĐKTM",
//     screen: "EvaluationPrice",
//     params: {},
//   },
//   {
//     group: "BiddingStack",
//     groupName: "Đấu thầu",
//     total: 0,
//     name: "Duyệt danh sách NCC thắng thầu",
//     screen: "WinningContractor",
//     params: {},
//   },
// ];

// export const SupplierStackRequirement: Array<RequirementDTO> = [
//   {
//     group: "SupplierStack",
//     groupName: "Duyệt nhà cung cấp",
//     total: 13,
//     name: "Duyệt hồ sơ pháp lý",
//     screen: "SupplierLegal",
//     params: {},
//   },
//   {
//     group: "SupplierStack",
//     groupName: "Duyệt nhà cung cấp",
//     total: 13,
//     name: "Duyệt hồ sơ năng lực",
//     screen: "SupplierCapacity",
//     params: {},
//   },
//   {
//     group: "SupplierStack",
//     groupName: "Duyệt nhà cung cấp",
//     total: 0,
//     name: "Duyệt đánh giá Lịch sử mua hàng",
//     screen: "SupplierPurchaseHistory",
//     params: {},
//   },
//   {
//     group: "SupplierStack",
//     groupName: "Duyệt nhà cung cấp",
//     total: 8,
//     name: "Duyệt yêu cầu điều chỉnh hồ sơ pháp lý",
//     screen: "SupplierLegalAdjustment",
//     params: {},
//   },
//   {
//     group: "SupplierStack",
//     groupName: "Duyệt nhà cung cấp",
//     total: 0,
//     name: "Duyệt yêu cầu điều chỉnh hồ sơ năng lực",
//     screen: "SupplierCapacityAdjustment",
//     params: {},
//   },
//   {
//     group: "SupplierStack",
//     groupName: "Duyệt nhà cung cấp",
//     total: 3,
//     name: "Duyệt mở/khoá Nhà cung cấp",
//     screen: "SupplierLock",
//     params: {},
//   },
//   {
//     group: "SupplierStack",
//     groupName: "Duyệt nhà cung cấp",
//     total: 2,
//     name: "Duyệt mở/khoá Lĩnh vực kinh doanh",
//     screen: "SupplierSectorLock",
//     params: {},
//   },
//   {
//     group: "SupplierStack",
//     groupName: "Duyệt nhà cung cấp",
//     total: 1,
//     name: "Duyệt tạo mã SAP",
//     screen: "SupplierSap",
//     params: {},
//   },
// ];

// export const PRStackRequirement: Array<RequirementDTO> = [
//   {
//     group: "PRStack",
//     groupName: "Duyệt PR",
//     total: 27,
//     name: "PR",
//     screen: "PR",
//     params: {},
//   },
// ];

// export const HomeStackRequirement: Array<RequirementDTO> = [
//   ...MoreStackRequirement,
//   ...BiddingStackRequirement,
//   ...SupplierStackRequirement,
//   ...PRStackRequirement,
// ];
