import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTemplateSiteAssment1749633119890 implements MigrationInterface {
  name = 'AddTemplateSiteAssment1749633119890'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "template_site_assessment" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c8bcef2f176fe7a8d44e13d0448" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_838f488d7604ec6e637e890bb78" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "name" nvarchar(250), "code" nvarchar(100) NOT NULL, "status" nvarchar(250), CONSTRAINT "PK_c8bcef2f176fe7a8d44e13d0448" PRIMARY KEY ("id"))`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "template_site_assessment"`)
  }
}
