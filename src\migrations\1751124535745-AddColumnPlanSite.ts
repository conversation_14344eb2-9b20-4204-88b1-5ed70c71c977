import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnPlanSite1751124535745 implements MigrationInterface {
  name = 'AddColumnPlanSite1751124535745'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "plan_site_assessment" ADD "orgCompanyId" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "plan_site_assessment" DROP COLUMN "orgCompanyId"`)
  }
}
