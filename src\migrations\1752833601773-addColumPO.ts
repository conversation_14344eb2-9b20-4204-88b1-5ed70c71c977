import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumPO1752833601773 implements MigrationInterface {
    name = 'AddColumPO1752833601773'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po" ADD "totalPO" varchar(250)`);
        await queryRunner.query(`ALTER TABLE "po" ADD "usedBudget" varchar(250)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "usedBudget"`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "totalPO"`);
    }

}
