import { IsString, IsOptional, IsArray, IsDate, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'

export class BusinessPlanDetailResponseDto {
  @IsString()
  id: string

  @IsString()
  code: string

  @IsOptional()
  loanTerm?: number

  @IsOptional()
  @IsString()
  incotermId?: string

  @IsOptional()
  @IsString()
  description?: string

  @IsOptional()
  @IsString()
  referenceType?: string

  @IsOptional()
  @IsString()
  shipmentPlanId?: string

  @IsOptional()
  @IsString()
  shipmentPlanIncotermId?: string

  @IsOptional()
  @IsString()
  shipmentPlanPriceId?: string

  @IsOptional()
  tableConfig?: any

  @IsArray()
  lstCostConfig: any[]

  @IsArray()
  lstCurrencyExchage: any[]

  @IsArray()
  lstCostValue: any[]
}

export class BusinessTemplatePlanDetailResponseDto {
  @IsString()
  id: string

  @IsString()
  code: string

  @IsString()
  plantId: string

  @IsString()
  businessTemplatePlanTypeId: string

  @IsString()
  purchasingOrgId: string

  @IsString()
  status: string

  @IsDate()
  createdAt: Date

  @IsString()
  createdBy: string

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessPlanDetailResponseDto)
  lstBusinessPlan: BusinessPlanDetailResponseDto[]
}
