import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
import { ContractAppendixItemEntity, SiteAssessmentEntity } from '.'
import { ContractItemEntity } from './contractItem.entity'

@Entity('factory_supplier')
export class FactorySupplierEntity extends BaseEntity {
  // Tên nhà máy sản xuất
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  // Mã nhà máy sản xuất
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  // Địa chỉ nhà máy sản xuất
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  address: string

  // Số điện thoại
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  phone: string

  //  Số fax
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  fax: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.factorySuppliers)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** DS đánh giá hiện trường */
  @OneToMany(() => SiteAssessmentEntity, (p) => p.factorySupplier)
  siteAssessments: Promise<SiteAssessmentEntity[]>

  /** Danh sách item hợp đồng */
  @OneToMany(() => ContractItemEntity, (p) => p.factorySupplier)
  contractItems: Promise<ContractItemEntity[]>

  /** Danh sách item phụ lục hợp đồng */
  @OneToMany(() => ContractAppendixItemEntity, (p) => p.factorySupplier)
  contractAppendixItems: Promise<ContractAppendixItemEntity[]>
}
