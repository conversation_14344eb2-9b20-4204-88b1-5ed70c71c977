import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { CurrentUser } from '../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { JwtAuthGuard } from '../common/guards'
import { LeadtimeService } from './leadtime.service'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { LeadtimeCreateDto, LeadtimeUpdateDto } from './dto'

@ApiBearerAuth()
@ApiTags('leadtime')
@UseGuards(JwtAuthGuard)
@Controller('leadtime')
export class LeadtimeController {
  constructor(private readonly service: LeadtimeService) {}
  @ApiOperation({ summary: 'Tạo mới một incoterm với dữ liệu được cung cấp.' })
  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: LeadtimeCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin của incoterm với dữ liệu được cung cấp.' })
  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: LeadtimeUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Danh sách incoterm phân trang' })
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto) {
    return await this.service.pagination(data)
  }

  @ApiOperation({ summary: 'Chi tiết khiếu nại' })
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật trạng thái ' })
  @Post('update_active')
  async updateActiveStatus(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return this.service.updateActiveStatus(user, data)
  }
}
