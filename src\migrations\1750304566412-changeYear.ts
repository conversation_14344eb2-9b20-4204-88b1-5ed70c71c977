import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeYear1750304566412 implements MigrationInterface {
    name = 'ChangeYear1750304566412'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_norm_detail" ADD "month" float CONSTRAINT "DF_8a7f5d3d2192df8095a5c180466" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_norm_detail" DROP CONSTRAINT "DF_8a7f5d3d2192df8095a5c180466"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm_detail" DROP COLUMN "month"`);
    }

}
