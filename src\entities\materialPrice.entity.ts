import { <PERSON>ti<PERSON>, Column, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { MaterialEntity } from './material.entity'
import { PlantEntity } from './plant.entity'
import { CurrencyEntity } from './currency.entity'


@Entity('material_price')
export class MaterialPriceEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  materialId: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  plantId: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  materialCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  plantCode: string

  @ManyToOne(() => MaterialEntity, (p) => p.materialPrices)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @ManyToOne(() => PlantEntity, (p) => p.materialPrices)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /** Giá */
  @Column({
    type: 'float',
    nullable: true,
  })
  price: number

  @Column({
    nullable: true,
    type: 'datetime',
  })
  lastChange: Date

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  currencyCode: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  currencyId: string
  @ManyToOne(() => CurrencyEntity, (p) => p.materialPrices)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  valuationClass: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  priceControl: string

  /** valuation Type */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  valuationType: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  mrpType: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  abcIndicator: string



  /** Giá */
  @Column({
    type: 'float',
    nullable: true,
  })
  priceUnit: number
}
