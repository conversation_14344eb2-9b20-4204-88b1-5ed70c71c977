import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

@Entity('template_table')
export class TemplateConfigEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  type: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  apiEndpoint: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  apiQueryParams: string

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  desc: string

  @Column({
    nullable: true,
    default: false,
  })
  synchronize: boolean

  /** dataJson */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  configs: string
}
