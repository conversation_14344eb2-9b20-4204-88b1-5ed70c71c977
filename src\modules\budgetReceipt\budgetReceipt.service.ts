import { Injectable } from '@nestjs/common'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { CREATE_SUCCESS, DELETE_SUCCESS, enumData, ERROR_NOT_FOUND_DATA, UPDATE_SUCCESS } from '../../constants'
import {
  BudgetReceiptEntity,
  BudgetReceiptHistoryEntity,
  BudgetReceiptItemEntity,
  POEntity,
  POHistoryEntity,
  PrEntity,
  PrHistoryEntity,
} from '../../entities'
import { Between, Equal, In, Like, Not } from 'typeorm'

import { v4 as uuidv4 } from 'uuid'
import * as moment from 'moment'
import { BudgetReceiptRepository, CompanyRepository, EmployeeRepository, PORepository, PrRepository } from '../../repositories'
import { BudgetReceiptCreateDto, BudgetReceiptSyncDto, BudgetReceiptUpdateDto } from './dto'
import { coreHelper } from '../../helpers'
import { CurrencyRepository } from '../../repositories/currency.repository'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'

@Injectable()
export class BudgetReceiptService {
  constructor(
    private readonly repo: BudgetReceiptRepository,
    private employeeRepo: EmployeeRepository,
    private companyRepo: CompanyRepository,
    private currencyRepo: CurrencyRepository,
    private organizationalPositionService: OrganizationalPositionService,
  ) {}

  public async find(user: UserDto, data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.code) whereCon.code = Like(`%${data.code}%`)
    if (data.name) whereCon.name = Like(`%${data.name}%`)
    return await this.repo.find({ where: whereCon, order: { createdAt: 'DESC' } })
  }

  async genCodeBudget() {
    const curDate = new Date()
    const fd = coreHelper.getFirstDayOfMonth(curDate)
    const ld = coreHelper.getLastDayOfMonth(curDate)

    let count = await this.repo.count({
      where: { createdAt: Between(fd, ld) },
    })

    const month = moment(curDate).format('MM')
    const year = moment(curDate).format('YY')

    let code: string
    let isDuplicate: boolean

    do {
      let countText = `000${count + 1}`
      countText = countText.slice(countText.length - 4, countText.length)
      code = `FMBB${month}${year}${countText}`

      // Kiểm tra mã đã tồn tại
      isDuplicate = await this.repo.exists({
        where: { code },
      })

      if (isDuplicate) {
        count++
      }
    } while (isDuplicate)

    return { code }
  }

  public async createData(user: UserDto, data: BudgetReceiptCreateDto) {
    if (data.employeeId) {
      const foundEmp = await this.employeeRepo.findOne({ where: { id: data.employeeId, isDeleted: false } })
      if (!foundEmp) throw new Error(ERROR_NOT_FOUND_DATA + ' [Nhân viên] ')
    }
    if (data.companyId) {
      const foundCompany = await this.companyRepo.findOne({ where: { id: data.companyId, isDeleted: false } })
      if (!foundCompany) throw new Error(ERROR_NOT_FOUND_DATA + ' [Công ty] ')
    }

    let foundCurrency: any
    if (data.currencyId) {
      foundCurrency = await this.currencyRepo.findOne({ where: { id: data.currencyId, isDeleted: false } })
      if (!foundCurrency) throw new Error(ERROR_NOT_FOUND_DATA + ' [Đơn vị tiền] ')
    }

    if (data.prId) {
      let checkExist = await this.repo.exists({
        where: { prId: data.prId, status: Not(In([enumData.BudgetStatus.APPROVED.code, enumData.BudgetStatus.REJECT.code])), isDeleted: false },
      })
      if (checkExist) throw new Error(`PR đã tồn tại trong phiếu điều chỉnh ngân sách`)
    }

    if (data.poId) {
      let checkExist = await this.repo.exists({
        where: { poId: data.poId, status: Not(In([enumData.BudgetStatus.APPROVED.code, enumData.BudgetStatus.REJECT.code])), isDeleted: false },
      })
      if (checkExist) throw new Error(`PO đã tồn tại trong phiếu điều chỉnh ngân sách`)
    }

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BudgetReceiptEntity)
      const prBudgetItemRepo = trans.getRepository(BudgetReceiptItemEntity)
      const prBudgetHistoryRepo = trans.getRepository(BudgetReceiptHistoryEntity)
      const poRepo = trans.getRepository(POEntity)
      const prRepo = trans.getRepository(PrEntity)
      const prHistoryRepo = trans.getRepository(PrHistoryEntity)
      const poHistoryRepo = trans.getRepository(POHistoryEntity)

      const foundCode = await repo.findOne({ where: { code: data.code } })
      if (foundCode) throw new Error(`Mã [${data.code}] đã được sử dụng. Vui lòng refresh lại trang`)

      if (data.poId) {
        const po = await poRepo.findOne({ where: { id: data.poId, isDeleted: false } })
        if (!po) throw new Error(`Po không tồn tại. Vui lòng kiểm tra lại`)

        // Từ chối các ngân sách đang pending -> tạo ngân sách mới
        const lstStatusActive = [enumData.BudgetStatus.WAIT_EPAY.code, enumData.BudgetStatus.NEW.code]
        await repo.update(
          {
            prId: data.prId,
            status: In(lstStatusActive),
          },
          { status: enumData.BudgetStatus.REJECT.code },
        )
      }

      if (data.prId) {
        const pr = await prRepo.findOne({ where: { id: data.prId, isDeleted: false } })
        if (!pr) throw new Error(`PR không tồn tại. Vui lòng kiểm tra lại`)

        // Từ chối các ngân sách đang pending -> tạo ngân sách mới
        const lstStatusActive = [enumData.BudgetStatus.WAIT_EPAY.code, enumData.BudgetStatus.NEW.code]
        await repo.update(
          {
            poId: data.poId,
            status: In(lstStatusActive),
          },
          { status: enumData.BudgetStatus.REJECT.code },
        )
      }

      // Tạo ngân sách PMS
      const newBudget = new BudgetReceiptEntity()
      newBudget.prId = data.prId
      newBudget.poId = data.poId
      newBudget.code = data.code
      newBudget.date = data.date
      newBudget.dateBudget = data.dateBudget
      newBudget.companyId = data.companyId
      newBudget.isMultiCompany = data.isMultiCompany
      newBudget.proposeType = data.proposeType
      newBudget.receiptType = data.receiptType
      newBudget.status = enumData.BudgetStatus.NEW.code
      newBudget.employeeId = data.employeeId
      newBudget.departmentQLNS = data.departmentQLNS
      newBudget.currency = foundCurrency?.code
      newBudget.currencyId = data.currencyId
      newBudget.interpretation = data.interpretation
      newBudget.fileAttachmentUrl = data.fileAttachmentUrl

      newBudget.createdBy = user.employeeId
      newBudget.createdAt = new Date()
      newBudget.id = uuidv4()
      newBudget.totalMoneyAvailable = 0
      newBudget.totalMoneyPropose = 0
      await repo.insert(newBudget)

      for (let item of data.items) {
        const newItem = new BudgetReceiptItemEntity()
        newItem.budgetReceiptId = newBudget.id
        newItem.isIncrease = item.isIncrease
        newItem.funCenter = item.funCenter
        newItem.CICode = item.CICode
        newItem.CIName = item.CIName
        newItem.moneyAvailable = item.moneyAvailable
        newItem.moneyPropose = item.moneyPropose
        newItem.baseAdjust = item.baseAdjust
        newItem.reasonAdjust = item.reasonAdjust
        newItem.createdBy = user.id
        newItem.createdAt = new Date()
        newItem.id = uuidv4()
        newBudget.totalMoneyAvailable += +item.moneyAvailable || 0
        newBudget.totalMoneyPropose += +item.moneyPropose || 0
        await prBudgetItemRepo.insert(newItem)
      }

      const newHistory = new BudgetReceiptHistoryEntity()
      newHistory.budgetReceiptId = newBudget.id
      newHistory.createdByName = user.username
      newHistory.createdAt = new Date()
      newHistory.createdBy = user.id
      newHistory.description = 'Tài Khoản [' + user.username + '] vừa tạo đề xuất điều chỉnh ngân sách'
      await prBudgetHistoryRepo.insert(newHistory)

      // Update trạng thái ngân sách PR
      if (data.prId) {
        await prRepo.update({ id: data.prId }, { budgetStatus: enumData.BudgetStatus.NEW.code })
        const newHistory = new PrHistoryEntity()
        newHistory.prId = data.prId
        newHistory.createdByName = user.username
        newHistory.createdAt = new Date()
        newHistory.createdBy = user.id
        newHistory.description = 'Tài Khoản [' + user.username + '] vừa tạo đề xuất điều chỉnh ngân sách'
        await prHistoryRepo.insert(newHistory)
      }
      if (data.poId) {
        await poRepo.update({ id: data.poId }, { budgetStatus: enumData.BudgetStatus.NEW.code })
        const newHistory = new POHistoryEntity()
        newHistory.poId = data.poId
        newHistory.createdAt = new Date()
        newHistory.createdBy = user.id
        newHistory.description = 'Tài Khoản [' + user.username + '] vừa tạo đề xuất điều chỉnh ngân sách'
        await poHistoryRepo.insert(newHistory)
      }
    })
    return { message: CREATE_SUCCESS }
  }

  public async createDataBudGetPo(user: UserDto, data: BudgetReceiptCreateDto) {
    if (data.employeeId) {
      const foundEmp = await this.employeeRepo.findOne({ where: { id: data.employeeId, isDeleted: false } })
      if (!foundEmp) throw new Error(ERROR_NOT_FOUND_DATA + ' [Nhân viên] ')
    }
    if (data.companyId) {
      const foundCompany = await this.companyRepo.findOne({ where: { id: data.companyId, isDeleted: false } })
      if (!foundCompany) throw new Error(ERROR_NOT_FOUND_DATA + ' [Công ty] ')
    }

    let foundCurrency: any
    if (data.currencyId) {
      foundCurrency = await this.currencyRepo.findOne({ where: { id: data.currencyId, isDeleted: false } })
      if (!foundCurrency) throw new Error(ERROR_NOT_FOUND_DATA + ' [Đơn vị tiền] ')
    }

    if (data.prId) {
      let checkExist = await this.repo.exists({
        where: { prId: data.prId, status: Not(In([enumData.BudgetStatus.APPROVED.code, enumData.BudgetStatus.REJECT.code])), isDeleted: false },
      })
      if (checkExist) throw new Error(`PR đã tồn tại trong phiếu điều chỉnh ngân sách`)
    }

    if (data.poId) {
      let checkExist = await this.repo.exists({
        where: { poId: data.poId, status: Not(In([enumData.BudgetStatus.APPROVED.code, enumData.BudgetStatus.REJECT.code])), isDeleted: false },
      })
      if (checkExist) throw new Error(`PO đã tồn tại trong phiếu điều chỉnh ngân sách`)
    }

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BudgetReceiptEntity)
      const prBudgetItemRepo = trans.getRepository(BudgetReceiptItemEntity)
      const poBudgetHistoryRepo = trans.getRepository(BudgetReceiptHistoryEntity)
      const poRepo = trans.getRepository(POEntity)
      const poHistoryRepo = trans.getRepository(POHistoryEntity)

      const foundCode = await repo.findOne({ where: { code: data.code } })
      if (foundCode) throw new Error(`Mã [${data.code}] đã được sử dụng. Vui lòng refresh lại trang`)

      if (data.poId) {
        const po = await poRepo.findOne({ where: { id: data.poId, isDeleted: false } })
        if (!po) throw new Error(`Po không tồn tại. Vui lòng kiểm tra lại`)

        // Từ chối các ngân sách đang pending -> tạo ngân sách mới
        const lstStatusActive = [enumData.BudgetStatus.WAIT_EPAY.code, enumData.BudgetStatus.NEW.code]
        await repo.update(
          {
            prId: data.prId,
            status: In(lstStatusActive),
          },
          { status: enumData.BudgetStatus.REJECT.code },
        )
      }

      // Tạo ngân sách PMS
      const newBudget = new BudgetReceiptEntity()
      const curDate = new Date()
      const fd = coreHelper.getFirstDayOfMonth(curDate)
      const ld = coreHelper.getLastDayOfMonth(curDate)

      let count = await repo.count({
        where: { createdAt: Between(fd, ld) },
      })

      const month = moment(curDate).format('MM')
      const year = moment(curDate).format('YY')

      let code: string
      let isDuplicate: boolean

      do {
        let countText = `000${count + 1}`
        countText = countText.slice(countText.length - 4, countText.length)
        code = `ĐCNS${month}${year}${countText}`

        // Kiểm tra mã đã tồn tại
        isDuplicate = await repo.exists({
          where: { code },
        })

        if (isDuplicate) {
          count++
        }
      } while (isDuplicate)
      newBudget.poId = data.poId
      newBudget.code = code
      newBudget.date = new Date()
      newBudget.dateBudget = new Date()
      newBudget.status = enumData.BudgetStatus.NEW.code
      newBudget.createdBy = user.employeeId
      newBudget.employeeId = user.employeeId
      newBudget.receiptType = enumData.ReceiptBudgetType.PO.code
      newBudget.createdAt = new Date()
      newBudget.id = uuidv4()
      newBudget.totalMoneyPropose = 10000

      await repo.insert(newBudget)

      for (let item of data.items) {
        const newBudgetItem = new BudgetReceiptItemEntity()
        newBudgetItem.budgetReceiptId = newBudget.id
        newBudgetItem.isIncrease = true
        newBudgetItem.funCenter = item.fundCenter
        newBudgetItem.CICode = item.ci
        newBudgetItem.CIName = item.ciName
        newBudgetItem.fp = item.fp
        newBudgetItem.fc = item.fc
        newBudgetItem.ci = item.ci
        newBudgetItem.budgetPeriod = item.budgetperiod
        newBudgetItem.moneyAvailable = 0
        //  newBudgetItem.moneyPropose = item.budget - item.total
        newBudgetItem.baseAdjust = enumData.BaseAdjust.PRICE.code
        newBudgetItem.reasonAdjust = `Hệ thống xin ngân sách do tạo PO thiếu ngân sách`
        newBudgetItem.createdBy = user.id
        newBudgetItem.createdAt = new Date()
        newBudgetItem.id = uuidv4()
        newBudget.totalMoneyAvailable += +0 || 0
        //  newBudget.totalMoneyPropose += +item.budget - item.total || 0

        await prBudgetItemRepo.insert(newBudgetItem)
      }

      const newHistory = new BudgetReceiptHistoryEntity()
      newHistory.budgetReceiptId = newBudget.id
      newHistory.createdByName = user.username
      newHistory.createdAt = new Date()
      newHistory.createdBy = user.id
      newHistory.description = 'Tài Khoản [' + user.username + '] vừa tạo đề xuất điều chỉnh ngân sách'
      await poBudgetHistoryRepo.insert(newHistory)

      if (data.poId) {
        await poRepo.update({ id: data.poId }, { budgetStatus: enumData.BudgetStatus.NEW.code })
        const newHistory = new POHistoryEntity()
        newHistory.poId = data.poId
        newHistory.createdAt = new Date()
        newHistory.createdBy = user.id
        newHistory.description = 'Tài Khoản [' + user.username + '] vừa tạo đề xuất điều chỉnh ngân sách'
        await poHistoryRepo.insert(newHistory)
      }
    })
    return { message: CREATE_SUCCESS }
  }

  public async updateData(user: UserDto, data: BudgetReceiptUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    if (data.employeeId) {
      const foundEmp = await this.employeeRepo.findOne({ where: { id: data.employeeId, isDeleted: false } })
      if (!foundEmp) throw new Error(ERROR_NOT_FOUND_DATA + ' [Nhân viên] ')
    }
    if (data.companyId) {
      const foundCompany = await this.companyRepo.findOne({ where: { id: data.companyId, isDeleted: false } })
      if (!foundCompany) throw new Error(ERROR_NOT_FOUND_DATA + ' [Công ty] ')
    }

    let foundCurrency: any
    if (data.currencyId) {
      foundCurrency = await this.currencyRepo.findOne({ where: { id: data.currencyId, isDeleted: false } })
      if (!foundCurrency) throw new Error(ERROR_NOT_FOUND_DATA + ' [Đơn vị tiền] ')
    }

    if (data.prId) {
      let checkExist = await this.repo.exists({
        where: {
          prId: data.prId,
          status: Not(In([enumData.BudgetStatus.APPROVED.code, enumData.BudgetStatus.REJECT.code])),
          isDeleted: false,
          id: Not(Equal(data.id)),
        },
      })
      if (checkExist) throw new Error(`PR đã tồn tại trong phiếu điều chỉnh ngân sách`)
    }

    if (data.poId) {
      let checkExist = await this.repo.exists({
        where: {
          poId: data.poId,
          status: Not(In([enumData.BudgetStatus.APPROVED.code, enumData.BudgetStatus.REJECT.code])),
          isDeleted: false,
          id: Not(Equal(data.id)),
        },
      })
      if (checkExist) throw new Error(`PO đã tồn tại trong phiếu điều chỉnh ngân sách`)
    }

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BudgetReceiptEntity)
      const prBudgetItemRepo = trans.getRepository(BudgetReceiptItemEntity)
      const prBudgetHistoryRepo = trans.getRepository(BudgetReceiptHistoryEntity)
      const poRepo = trans.getRepository(POEntity)
      const prRepo = trans.getRepository(PrEntity)
      const prHistoryRepo = trans.getRepository(PrHistoryEntity)
      const poHistoryRepo = trans.getRepository(POHistoryEntity)

      if (data.poId) {
        const po = await poRepo.findOne({ where: { id: data.poId, isDeleted: false } })
        if (!po) throw new Error(`Po không tồn tại. Vui lòng kiểm tra lại`)
      }

      if (data.prId) {
        const pr = await prRepo.findOne({ where: { id: data.prId, isDeleted: false } })
        if (!pr) throw new Error(`PR không tồn tại. Vui lòng kiểm tra lại`)
      }

      entity.prId = data.prId
      entity.poId = data.poId
      entity.code = data.code
      entity.date = data.date
      entity.dateBudget = data.dateBudget
      entity.companyId = data.companyId
      entity.isMultiCompany = data.isMultiCompany
      entity.proposeType = data.proposeType
      entity.receiptType = data.receiptType
      entity.employeeId = data.employeeId
      entity.departmentQLNS = data.departmentQLNS
      entity.currency = foundCurrency?.code
      entity.currencyId = data.currencyId
      entity.interpretation = data.interpretation
      entity.fileAttachmentUrl = data.fileAttachmentUrl
      entity.createdBy = user.id
      entity.createdAt = new Date()
      entity.totalMoneyAvailable = 0
      entity.totalMoneyPropose = 0
      await repo.save(entity)
      await prBudgetItemRepo.delete({ budgetReceiptId: entity.id })
      for (let item of data.items) {
        const newItem = new BudgetReceiptItemEntity()
        newItem.budgetReceiptId = entity.id
        newItem.isIncrease = item.isIncrease
        newItem.funCenter = item.funCenter
        newItem.CICode = item.CICode
        newItem.CIName = item.CIName
        newItem.moneyAvailable = item.moneyAvailable
        newItem.moneyPropose = item.moneyPropose
        newItem.baseAdjust = item.baseAdjust
        newItem.reasonAdjust = item.reasonAdjust
        newItem.createdBy = user.id
        newItem.createdAt = new Date()
        newItem.id = uuidv4()
        entity.totalMoneyAvailable += +item.moneyAvailable || 0
        entity.totalMoneyPropose += +item.moneyPropose || 0
        await prBudgetItemRepo.insert(newItem)
      }

      const newHistory = new BudgetReceiptHistoryEntity()
      newHistory.budgetReceiptId = entity.id
      newHistory.createdByName = user.username
      newHistory.createdAt = new Date()
      newHistory.createdBy = user.id
      newHistory.description = 'Tài Khoản [' + user.username + '] vừa tạo đề xuất điều chỉnh ngân sách'
      await prBudgetHistoryRepo.insert(newHistory)

      // Update trạng thái ngân sách PR
      if (data.prId) {
        await prRepo.update({ id: data.prId }, { budgetStatus: enumData.BudgetStatus.NEW.code })
        const newHistory = new PrHistoryEntity()
        newHistory.prId = data.prId
        newHistory.createdByName = user.username
        newHistory.createdAt = new Date()
        newHistory.createdBy = user.id
        newHistory.description = 'Tài Khoản [' + user.username + '] vừa cập nhật đề xuất điều chỉnh ngân sách'
        await prHistoryRepo.insert(newHistory)
      }
      if (data.poId) {
        await poRepo.update({ id: data.poId }, { budgetStatus: enumData.BudgetStatus.NEW.code })
        const newHistory = new POHistoryEntity()
        newHistory.poId = data.poId
        newHistory.createdAt = new Date()
        newHistory.createdBy = user.id
        newHistory.description = 'Tài Khoản [' + user.username + '] vừa nhật đề xuất điều chỉnh ngân sách'
        await poHistoryRepo.insert(newHistory)
      }
    })

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.Budget.code)
    if (dataRs.type === enumData.RoleData.All.code) {
      /* do nothing */
    }
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.poId) whereCon.poId = data.where.poId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.prId) whereCon.prId = data.where.prId
    if (data.where.status) whereCon.status = data.where.status
    if (data.where.currencyId) whereCon.currencyId = data.where.currencyId
    if (data.where.employeeId) whereCon.employeeId = data.where.employeeId
    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: { po: true, pr: true, currencyValue: true, employee: { department: true } },
      order: { createdAt: 'DESC' },
    })
    if (res[0].length == 0) return [[], 0]
    for (const item of res[0]) {
      item.poName = item?.__po__?.name
      item.poCode = item?.__po__?.code
      item.prCode = item?.__pr__?.code
      item.currencyName = item?.__currencyValue__?.name
      item.currencyCode = item?.__currencyValue__?.code
      item.statusName = enumData.BudgetStatus[item.status].name
      item.statusColor = enumData.BudgetStatus[item.status].color
      item.statusBgColor = enumData.BudgetStatus[item.status].backgroundColor
      item.statusBorderColor = enumData.BudgetStatus[item.status].statusBorderColor
      item.employeeName = item?.__employee__?.name
      delete item._po__
      delete item.__contract__
      delete item.__pr__
      delete item.__employee__
      delete item.__currencyValue__
      delete item.__po__
    }
    return res
  }

  async findDetail(user: UserDto, data: FilterOneDto) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: {
        histories: true,
        po: true,
        pr: true,
        currencyValue: true,
        employee: { department: true },
        items: true,
        company: true,
      },
      order: { histories: { createdAt: 'DESC' } },
    })
    if (!res) throw new Error(`Phiều điều chỉnh ngân sách không tồn tại. Vui lòng kiểm tra lại`)
    res.companyNote = res?.__company__?.code + ' - ' + res?.__company__?.name
    res.poName = res?.__po__?.name
    res.poCode = res?.__po__?.code
    res.prCode = res?.__pr__?.code
    res.currencyName = res?.__currencyValue__?.name
    res.currencyCode = res?.__currencyValue__?.code
    res.statusName = enumData.BudgetStatus[res.status].name
    res.statusColor = enumData.BudgetStatus[res.status].color
    res.statusBgColor = enumData.BudgetStatus[res.status].backgroundColor
    res.statusBorderColor = enumData.BudgetStatus[res.status].statusBorderColor
    res.employeeName = res?.__employee__?.__department__?.name + ' - ' + res?.__employee__?.name
    res.lstHistories = res.__histories__
    res.lstItem = res.__items__
    delete res.__po__
    delete res.__contract__
    delete res.__pr__
    delete res._employee__
    delete res.__histories__
    delete res.__items__
    delete res.__currencyValue__
    delete res.__employee__
    delete res.__company__
    return res
  }

  async loadListBudget() {
    const lstBudgetSync: any = await this.repo.find({
      where: { status: enumData.BudgetStatus.NEW.code, isDeleted: false },
      relations: { items: true, po: true, pr: true, currencyValue: true, employee: { department: true } },
    })
    if (lstBudgetSync.length === 0) return []
    for (let item of lstBudgetSync) {
      item.lstItem = item.__items__
      item.poName = item?.__po__?.name
      item.poCode = item?.__po__?.code
      item.prCode = item?.__pr__?.code
      item.currencyName = item?.__currencyValue__?.name
      item.currencyCode = item?.__currencyValue__?.code
      item.statusName = enumData.BudgetStatus[item.status].name
      item.statusColor = enumData.BudgetStatus[item.status].color
      item.employeeName = item?.__employee__?.__department__?.name + ' - ' + item?.__employee__?.name
      delete item._po__
      delete item.__contract__
      delete item.__pr__
      delete item._employee__
      delete item.__items__
    }
    return lstBudgetSync
  }

  async updateStatusSync(data: BudgetReceiptSyncDto[]) {
    if (data.length === 0) return
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BudgetReceiptEntity)
      const prBudgetHistoryRepo = trans.getRepository(BudgetReceiptHistoryEntity)
      const poRepo = trans.getRepository(POEntity)
      const prRepo = trans.getRepository(PrEntity)
      const prHistoryRepo = trans.getRepository(PrHistoryEntity)
      const poHistoryRepo = trans.getRepository(POHistoryEntity)

      for (let item of data) {
        const budget = await repo.findOne({ where: { code: Like(`%${item.code}%`) } })
        if (!budget) throw new Error(`Mã phiếu điều chỉnh ngân sách [${item.code}]  không tồn tại. Vui lòng kiểm tra lại`)
        await repo.update(
          {
            id: budget.id,
          },
          {
            status: item.status,
            updatedAt: new Date(),
            updatedBy: 'System',
          },
        )

        const newHistory = new BudgetReceiptHistoryEntity()
        newHistory.budgetReceiptId = budget.id
        newHistory.createdByName = 'System'
        newHistory.createdAt = new Date()
        newHistory.createdBy = 'System'
        newHistory.description = ` Hệ thống vừa cập nhật trạng thái của phiếu điều chỉnh ngân sách [${enumData.BudgetStatus[item?.status]?.name}] `
        await prBudgetHistoryRepo.insert(newHistory)
        if (budget.prId) {
          await prRepo.update({ id: budget.prId }, { budgetStatus: item.status })
          const newHistory = new PrHistoryEntity()
          newHistory.prId = budget.prId
          newHistory.createdByName = 'System'
          newHistory.createdAt = new Date()
          newHistory.createdBy = 'System'
          newHistory.description = ` Hệ thống vừa cập nhật trạng thái của phiếu điều chỉnh ngân sách [${enumData.BudgetStatus[item?.status]?.name}] `
          await prHistoryRepo.insert(newHistory)
        }
        if (budget.poId) {
          await poRepo.update({ id: budget.poId }, { budgetStatus: item.status })
          const newHistory = new POHistoryEntity()
          newHistory.poId = budget.poId
          newHistory.createdAt = new Date()
          newHistory.createdBy = 'System'
          newHistory.description = ` Hệ thống vừa cập nhật trạng thái của phiếu điều chỉnh ngân sách [${enumData.BudgetStatus[item?.status]?.name}] `
          await poHistoryRepo.insert(newHistory)
        }
      }
    })
    return { message: UPDATE_SUCCESS }
  }

  public async updateDelete(user: UserDto, data: FilterOneDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BudgetReceiptEntity)
      const poRepo = trans.getRepository(POEntity)
      const prRepo = trans.getRepository(PrEntity)
      const prBudgetItemRepo = trans.getRepository(BudgetReceiptItemEntity)
      const prBudgetHistoryRepo = trans.getRepository(BudgetReceiptHistoryEntity)
      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
      if (entity.prId) {
        await prRepo.update({ id: entity.prId }, { budgetStatus: null, updatedAt: new Date(), updatedBy: user.id })
      }
      if (entity.poId) {
        await poRepo.update({ id: entity.poId }, { budgetStatus: null, updatedAt: new Date(), updatedBy: user.id })
      }
      await prBudgetHistoryRepo.delete({ budgetReceiptId: entity.id })
      await prBudgetItemRepo.delete({ budgetReceiptId: entity.id })
      await repo.delete({ id: entity.id })
    })

    return { message: DELETE_SUCCESS }
  }

  public async updateSend(user: UserDto, data: FilterOneDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BudgetReceiptEntity)
      const poRepo = trans.getRepository(POEntity)
      const prRepo = trans.getRepository(PrEntity)

      const prBudgetHistoryRepo = trans.getRepository(BudgetReceiptHistoryEntity)
      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.BudgetStatus.NEW.code) {
        throw new Error(` Trạng thái không hợp lệ. Vui lòng kiểm tra lại`)
      }

      entity.status = enumData.BudgetStatus.WAIT_EPAY.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await repo.save(entity)
      if (entity.prId) {
        await prRepo.update({ id: entity.prId }, { budgetStatus: enumData.BudgetStatus.WAIT_EPAY.code, updatedAt: new Date(), updatedBy: user.id })
      }
      if (entity.poId) {
        await poRepo.update({ id: entity.poId }, { budgetStatus: enumData.BudgetStatus.WAIT_EPAY.code, updatedAt: new Date(), updatedBy: user.id })
      }

      const newHistory = new BudgetReceiptHistoryEntity()
      newHistory.budgetReceiptId = entity.id
      newHistory.createdByName = user.username
      newHistory.createdAt = new Date()
      newHistory.createdBy = user.id
      newHistory.description = `Tài khoản [${user.username}] vừa cập nhật gửi duyệt cho phiếu điều chỉnh ngân sách có mã là [${entity.code}]`
      await prBudgetHistoryRepo.insert(newHistory)
    })

    return { message: UPDATE_SUCCESS }
  }

  public async updateApproved(user: UserDto, data: FilterOneDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BudgetReceiptEntity)
      const poRepo = trans.getRepository(POEntity)
      const prRepo = trans.getRepository(PrEntity)

      const prBudgetHistoryRepo = trans.getRepository(BudgetReceiptHistoryEntity)
      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.BudgetStatus.WAIT_EPAY.code) {
        throw new Error(` Trạng thái không hợp lệ. Vui lòng kiểm tra lại`)
      }

      entity.status = enumData.BudgetStatus.APPROVED.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      if (entity.prId) {
        const prItem = await prRepo.findOne({ where: { id: entity.prId, isDeleted: false } })
        if (!prItem) throw new Error(` PR không tồn tại . Vui lòng kiểm tra lại`)
        if (prItem.status === enumData.PRStatus.H.code && prItem.status === enumData.PRStatus.S.code) {
          throw new Error(`Trạng thái PR không phù hợp . Vui lòng kiểm tra lại`)
        }
        entity.totalMoneyRemaining = entity.totalMoneyApproved - prItem.budgetMissing
      }

      await repo.save(entity)
      if (entity.prId) {
        await prRepo.update(
          { id: entity.prId },
          { status: enumData.PRStatus.S.code, budgetStatus: enumData.BudgetStatus.APPROVED.code, updatedAt: new Date(), updatedBy: user.id },
        )
      }
      if (entity.poId) {
        await poRepo.update(
          { id: entity.poId },
          {
            status: enumData.PurchaseOrderStatus.SAVED.code,
            budgetStatus: enumData.BudgetStatus.APPROVED.code,
            updatedAt: new Date(),
            updatedBy: user.id,
          },
        )
      }

      const newHistory = new BudgetReceiptHistoryEntity()
      newHistory.budgetReceiptId = entity.id
      newHistory.createdByName = user.username
      newHistory.createdAt = new Date()
      newHistory.createdBy = user.id
      newHistory.description = `Tài khoản [${user.username}] vừa cập nhật duyệt cho phiếu điều chỉnh ngân sách có mã là [${entity.code}]`
      await prBudgetHistoryRepo.insert(newHistory)
    })

    return { message: UPDATE_SUCCESS }
  }

  public async updateReject(user: UserDto, data: FilterOneDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(BudgetReceiptEntity)
      const poRepo = trans.getRepository(POEntity)
      const prRepo = trans.getRepository(PrEntity)

      const prBudgetHistoryRepo = trans.getRepository(BudgetReceiptHistoryEntity)
      const entity = await repo.findOne({ where: { id: data.id } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      if (entity.status !== enumData.BudgetStatus.WAIT_EPAY.code) {
        throw new Error(` Trạng thái không hợp lệ. Vui lòng kiểm tra lại`)
      }

      entity.status = enumData.BudgetStatus.REJECT.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await repo.save(entity)
      if (entity.prId) {
        await prRepo.update({ id: entity.prId }, { budgetStatus: enumData.BudgetStatus.REJECT.code, updatedAt: new Date(), updatedBy: user.id })
      }
      if (entity.poId) {
        await poRepo.update(
          { id: entity.poId },
          {
            budgetStatus: enumData.BudgetStatus.REJECT.code,
            updatedAt: new Date(),
            updatedBy: user.id,
          },
        )
      }

      const newHistory = new BudgetReceiptHistoryEntity()
      newHistory.budgetReceiptId = entity.id
      newHistory.createdByName = user.username
      newHistory.createdAt = new Date()
      newHistory.createdBy = user.id
      newHistory.description = `Tài khoản [${user.username}] vừa cập nhật từ chối cho phiếu điều chỉnh ngân sách có mã là [${entity.code}]`
      await prBudgetHistoryRepo.insert(newHistory)
    })

    return { message: UPDATE_SUCCESS }
  }
}
