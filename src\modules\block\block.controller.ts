import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { CurrentUser } from '../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { JwtAuthGuard } from '../common/guards'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { BlockService } from './block.service'
import { BlockCreateDto, BlockUpdateDto, BlockExcelDto, AddDepartmentBlockDto } from './dto'
import { UpdateCompanyBlockDto } from './dto/updateCompanyBlock.dto'

@ApiBearerAuth()
@ApiTags('Block')
@UseGuards(JwtAuthGuard)
@Controller('block')
export class BlockController {
  constructor(private readonly service: BlockService) {}

  @ApiOperation({ summary: 'Lấy danh sách khối' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: '<PERSON><PERSON>y một khối' })
  @Post('find_one')
  public async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }

  @ApiOperation({ summary: 'Danh sách khối phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo khối' })
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BlockCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật khối' })
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BlockUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động khối' })
  @Post('update_active')
  public async updateActive(@Body() data: { id: string }, @CurrentUser() user: UserDto) {
    const warehouse = await this.service.updateIsDelete(data, user)
    return warehouse
  }

  @ApiOperation({ summary: 'Chi tiết khối' })
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'Hàm thêm mới bằng excel' })
  @Post('create_data_excel')
  async createDataExcel(@CurrentUser() user: UserDto, @Body() data: BlockExcelDto[]) {
    return this.service.createDataExcel(user, data)
  }

  @ApiOperation({ summary: 'Hàm thêm mới bằng excel' })
  @Post('update_company_block')
  async updateCompanyBlock(@CurrentUser() user: UserDto, @Body() data: UpdateCompanyBlockDto) {
    return this.service.updateCompanyBlock(user, data)
  }

  @ApiOperation({ summary: 'Thêm phòng ban khối' })
  @Post('add_department')
  public async addDepartmentCompany(@CurrentUser() user: UserDto, @Body() data: AddDepartmentBlockDto) {
    return await this.service.addDepartmentCompany(user, data)
  }

  @ApiOperation({ summary: 'Xóa phòng ban khối' })
  @Post('remove_department')
  public async removeDepartmentCompany(@CurrentUser() user: UserDto, @Body() data: AddDepartmentBlockDto) {
    return await this.service.removeDepartmentCompany(user, data)
  }
}
