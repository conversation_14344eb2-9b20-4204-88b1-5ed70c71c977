import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

/** Interface tạo mới một Chi nhánh/Phòng giao dịch Ngân hàng. */
export class BankBranchCreateDto {
  @ApiProperty({ description: 'Tê<PERSON> nh<PERSON>/Phòng giao dịch Ngân hàng.' })
  @IsOptional()
  @IsString()
  name: string

  @ApiProperty({ description: 'Mã chi nhánh ngân hàng' })
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty({ description: 'Id quốc gia.' })
  @IsNotEmpty()
  @IsString()
  countryId: string

  @ApiProperty({ description: 'Id Ngân hàng' })
  @IsNotEmpty()
  @IsString()
  bankId: string

  @ApiProperty({ description: 'Id thành phố.' })
  @IsNotEmpty()
  regionId: string

  @ApiProperty({ description: 'Đ<PERSON><PERSON> chỉ.' })
  address?: string

  @ApiProperty({ description: 'SWIFT/BIC.' })
  swift: string

  @ApiProperty({ description: 'Số tài khoản ngân hàng.' })
  accountNumber: string

  @ApiProperty({ description: 'Bank branch.' })
  @IsOptional()
  @IsString()
  bankBranch: string

  @ApiProperty({ description: 'Nhóm ngân hàng enumData: BankGroupType.' })
  @IsOptional()
  @IsString()
  bankGroupType: string

  @ApiProperty({ description: 'Mã vùng.' })
  areaCode: string

  @ApiProperty({ description: 'Postbank acct' })
  @IsOptional()
  postbank: boolean

  @ApiProperty({ description: 'Mô tả về Chi nhánh/Phòng giao dịch Ngân hàng.' })
  @IsOptional()
  @IsString()
  description?: string
}
