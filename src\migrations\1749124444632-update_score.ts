import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateScore1749124444632 implements MigrationInterface {
  name = 'UpdateScore1749124444632'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "comment" ADD "targetId" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "comment" DROP COLUMN "targetId"`)
  }
}
