import { MigrationInterface, QueryRunner } from "typeorm";

export class ProfitCenter1752117293468 implements MigrationInterface {
    name = 'ProfitCenter1752117293468'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "profit_center" DROP COLUMN "profitCenterCode"`);
        await queryRunner.query(`ALTER TABLE "profit_center" DROP COLUMN "profitCenterDescription"`);
        await queryRunner.query(`ALTER TABLE "profit_center" ADD "code" nvarchar(20) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "profit_center" ADD "description" nvarchar(max) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "profit_center" DROP COLUMN "description"`);
        await queryRunner.query(`ALTER TABLE "profit_center" DROP COLUMN "code"`);
        await queryRunner.query(`ALTER TABLE "profit_center" ADD "profitCenterDescription" nvarchar(MAX) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "profit_center" ADD "profitCenterCode" nvarchar(7) NOT NULL`);
    }

}
