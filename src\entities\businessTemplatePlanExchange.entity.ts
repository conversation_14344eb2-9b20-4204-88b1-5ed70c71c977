import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BusinessTemplatePlanEntity } from './businessTemplatePlan.entity'
import { BaseEntity } from './base.entity'

@Entity('business_template_plan_exchange')
export class BusinessTemplatePlanExchangeEntity extends BaseEntity {
  // id của template plan
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessTemplatePlanId: string
  @ManyToOne(() => BusinessTemplatePlanEntity, (c) => c.businessTemplatePlanExchange)
  @JoinColumn({ name: 'businessTemplatePlanId', referencedColumnName: 'id' })
  businessTemplatePlan: Promise<BusinessTemplatePlanEntity>
  /* từ đơn vị tiền tệ */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  fromCurrency: string

  /* từ đơn vị tiền tệ */
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  fromCurrencyId: string
  /* đến đơn vị tiền tệ */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  toCurrency: string

  /* đến đơn vị tiền tệ */
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  toCurrencyId: string
  /* tỷ giá nhân */
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  exchangeRate: number
  // tỉ giá chỉa
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  exchangeRateInverse: number
}
