import { MigrationInterface, QueryRunner } from "typeorm";

export class GenCoum1751565998810 implements MigrationInterface {
    name = 'GenCoum1751565998810'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "leadtime" DROP CONSTRAINT "FK_4060c31a644ded6f510b8525c10"`);
        await queryRunner.query(`ALTER TABLE "leadtime" DROP COLUMN "materialId"`);
        await queryRunner.query(`ALTER TABLE "leadtime" DROP COLUMN "materialGroupId"`);
        await queryRunner.query(`ALTER TABLE "leadtime" ADD "materialGroupId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "leadtime" ADD CONSTRAINT "FK_584661bc1baf872b3a815acdd05" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "leadtime" DROP CONSTRAINT "FK_584661bc1baf872b3a815acdd05"`);
        await queryRunner.query(`ALTER TABLE "leadtime" DROP COLUMN "materialGroupId"`);
        await queryRunner.query(`ALTER TABLE "leadtime" ADD "materialGroupId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "leadtime" ADD "materialId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "leadtime" ADD CONSTRAINT "FK_4060c31a644ded6f510b8525c10" FOREIGN KEY ("materialId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
