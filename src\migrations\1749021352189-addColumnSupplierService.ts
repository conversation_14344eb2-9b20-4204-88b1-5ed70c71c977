import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnSupplierService1749021352189 implements MigrationInterface {
    name = 'AddColumnSupplierService1749021352189'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "supplier_product_service" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_848a756a816b658c5ec4be42849" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_85099a1a89b37673823f5f192c5" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(250) NOT NULL, "supplyCapacityPerMonth" int CONSTRAINT "DF_18a6190e76dff08ed8264060358" DEFAULT 0, "supplierServiceId" uniqueidentifier, CONSTRAINT "PK_848a756a816b658c5ec4be42849" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_service_factory" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_d67d76abfd5557f6926fea01482" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3d771b9c3745b01f45beb0042d2" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(250), "totalAreaM2" float, "avgWorkHour" float, "officeAreaM2" float, "productionAreaM2" float, "rawMaterialAreaM2" float, "finishedGoodsAreaM2" float, "supplierServiceId" uniqueidentifier, CONSTRAINT "PK_d67d76abfd5557f6926fea01482" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_product_line" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_dbad358ef2838b0ff50a1e94a7c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f3030559a16aefce7d542f401f4" DEFAULT 0, "companyId" varchar(255), "step" nvarchar(250), "equipmentName" nvarchar(250), "quantity" int CONSTRAINT "DF_06b683e3ee3f15c3aaf4fd299e0" DEFAULT 0, "commissioningYear" datetime, "designCapacity" int CONSTRAINT "DF_e27f0bdbfd3b55a5e76a7b06bb2" DEFAULT 0, "actualCapacity" int CONSTRAINT "DF_81575a954e1781cd352a989aac1" DEFAULT 0, "supplierServiceId" uniqueidentifier, CONSTRAINT "PK_dbad358ef2838b0ff50a1e94a7c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_certificate" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_7a4968a6cdb9d864cc41f158894" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_09a357d7d96e373ec0fe8b15146" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(250), "fileAttachment" nvarchar(max), "supplierServiceId" uniqueidentifier, CONSTRAINT "PK_7a4968a6cdb9d864cc41f158894" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "isISO" bit CONSTRAINT "DF_50c98770d058d0c27fee511fdb5" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier_product_service" ADD CONSTRAINT "FK_8d74f2228e1b3a815b713c1c090" FOREIGN KEY ("supplierServiceId") REFERENCES "supplier_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_service_factory" ADD CONSTRAINT "FK_9ae383a903822454c7d4d38a8b6" FOREIGN KEY ("supplierServiceId") REFERENCES "supplier_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_product_line" ADD CONSTRAINT "FK_5f26523a89d7197973a3d623fab" FOREIGN KEY ("supplierServiceId") REFERENCES "supplier_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_certificate" ADD CONSTRAINT "FK_9f4fc7e028e51907dc13493e8eb" FOREIGN KEY ("supplierServiceId") REFERENCES "supplier_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier_certificate" DROP CONSTRAINT "FK_9f4fc7e028e51907dc13493e8eb"`);
        await queryRunner.query(`ALTER TABLE "supplier_product_line" DROP CONSTRAINT "FK_5f26523a89d7197973a3d623fab"`);
        await queryRunner.query(`ALTER TABLE "supplier_service_factory" DROP CONSTRAINT "FK_9ae383a903822454c7d4d38a8b6"`);
        await queryRunner.query(`ALTER TABLE "supplier_product_service" DROP CONSTRAINT "FK_8d74f2228e1b3a815b713c1c090"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP CONSTRAINT "DF_50c98770d058d0c27fee511fdb5"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "isISO"`);
        await queryRunner.query(`DROP TABLE "supplier_certificate"`);
        await queryRunner.query(`DROP TABLE "supplier_product_line"`);
        await queryRunner.query(`DROP TABLE "supplier_service_factory"`);
        await queryRunner.query(`DROP TABLE "supplier_product_service"`);
    }

}
