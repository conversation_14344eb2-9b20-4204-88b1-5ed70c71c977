import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColum1752314385837 implements MigrationInterface {
  name = 'AddColum1752314385837'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "externalMaterialGroupId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "materialGroupId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "rfq_details" ADD CONSTRAINT "FK_dcbc7c737e649cd678d945828e4" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "rfq_details" ADD CONSTRAINT "FK_a7c1e88b4eede3e4c9f75b2c3e6" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP CONSTRAINT "FK_a7c1e88b4eede3e4c9f75b2c3e6"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP CONSTRAINT "FK_dcbc7c737e649cd678d945828e4"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "materialGroupId"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "externalMaterialGroupId"`)
  }
}
