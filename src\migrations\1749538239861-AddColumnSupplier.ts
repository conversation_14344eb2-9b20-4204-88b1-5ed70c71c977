import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnSupplier1749538239861 implements MigrationInterface {
  name = 'AddColumnSupplier1749538239861'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier" ADD "isHO" bit CONSTRAINT "DF_f3fc3fe2c53657cc08a243fc5e7" DEFAULT 0`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_f3fc3fe2c53657cc08a243fc5e7"`)
    await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "isHO"`)
  }
}
