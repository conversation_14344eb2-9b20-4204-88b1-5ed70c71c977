import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeColum1747987333809 implements MigrationInterface {
    name = 'ChangeColum1747987333809'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier" ALTER COLUMN "isInternal" bit`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier" ALTER COLUMN "isInternal" bit NOT NULL`);
    }

}
