import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNumber, IsArray, IsNotEmpty } from 'class-validator'

export class BidAuctionCreateDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  bidId: string

  @ApiProperty()
  @IsNotEmpty()
  endDate: Date

  dateStart: Date

  dateStartPlus: Date

  timePlus: number

  timePlusType: string

  timeApprove: number

  timeApproveType: string

  step: number

  description: string

  lstFile: any[]

  @ApiProperty()
  @IsArray()
  lstSupplierChoose: any[]

  @ApiProperty()
  @IsArray()
  lstPrice: BidAuctionPriceCreateDto[]
}

export class BidAuctionPriceCreateDto {
  @ApiProperty()
  @IsString()
  bidPriceId: string

  number: number
  name: string
  unit: string

  @ApiProperty()
  @IsNumber()
  maxPrice: number
}

export class BidAuctionSupplierSaveDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  id: string

  @ApiProperty()
  @IsArray()
  listChild: BidAuctionItemDto[]
}

class BidAuctionItemDto {
  @ApiProperty()
  id: string
  @ApiProperty()
  bidId: string
  @ApiProperty()
  lstAuctionPrice: any[]
}
