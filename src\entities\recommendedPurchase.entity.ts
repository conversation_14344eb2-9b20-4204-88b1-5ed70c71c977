import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { PaymentMethodEntity } from './paymentMethod.entity'
import { IncotermEntity } from './incoterm.entity'
import { MediaFileEntity } from './mediaFile.entity'
import { CompanyEntity } from './company.entity'
import { PlantEntity } from './plant.entity'
import { RecommendedPurchaseTemplateEntity } from './recommendedPurchaseTemplate.entity'
import { RecommendedPurchaseColValueEntity } from './recommendedPurchaseColValue.entity'
import { RecommendedPurchaseHistoryEntity } from './recommendedPurchaseHistory.entity'
import { OfferEntity } from './offer.entity'
import { BusinessPlanEntity } from './businessPlan.entity'
import { RecommendedPurchaseSettingValueEntity } from './recommendedPurchaseSettingValue.entity'
import { POEntity } from './po.entity'
import { ContractEntity } from './contract.entity'
import { BidEntity } from './bid.entity'
import { ShipmentCostEntity } from './shipmentCost.entity'
import { RecommendedPurchaseShipmentCostPriceEntity } from './recommendedPurchaseShipmentCostPrice.entity'
import { RecommendedPurchaseShipmenStageEntity } from './recommendedPurchaseShipmentStage.entity'
import { RecommendedPurchaseRfqEntity } from './recommendedPurchaseRfq.entity'

/** Đề nghị mua hàng */
@Entity('recommended_purchase')
export class RecommendedPurchaseEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.recommendedPurchase)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseTemplateId: string
  @ManyToOne(() => RecommendedPurchaseTemplateEntity, (p) => p.recommendedPurchases)
  @JoinColumn({ name: 'recommendedPurchaseTemplateId', referencedColumnName: 'id' })
  recommendedPurchaseTemplate: Promise<RecommendedPurchaseTemplateEntity>

  /** Phương thức thanh toán */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentMethodId: string
  @ManyToOne(() => PaymentMethodEntity, (p) => p.recommendedPurchase)
  @JoinColumn({ name: 'paymentMethodId', referencedColumnName: 'id' })
  paymentMethod: Promise<PaymentMethodEntity>

  /** Điều kiện giao hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermId: string
  @ManyToOne(() => IncotermEntity, (p) => p.recommendedPurchase)
  @JoinColumn({ name: 'incotermId', referencedColumnName: 'id' })
  incoterm: Promise<IncotermEntity>

  /** incoterm Description */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  incotermDescription: string

  /** địa điểm giao hàng */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  deliveryAddress: string

  /**  Thời gian dự kiến về kho */
  @Column({
    type: 'datetime',
    nullable: true,
  })
  estimatedTime: Date

  /** Nguồn mua */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  purchasingSource: string

  /** Nguồn bán */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  sellingSource: string

  /** Mục đích*/
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  purpose: string

  /** Đề xuất */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  propose: string

  /** Nhà cung cấp đề xuất */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  intermediarySupplier: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string
  @ManyToOne(() => CompanyEntity, (p) => p.recommendedPurchase)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  @OneToMany(() => MediaFileEntity, (p) => p.recommendedPurchase)
  mediaFiles: Promise<MediaFileEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.recommendedPurchase)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @OneToMany(() => RecommendedPurchaseColValueEntity, (p) => p.recommendedPurchase)
  recommendedPurchaseColValues: Promise<RecommendedPurchaseColValueEntity[]>

  @OneToMany(() => RecommendedPurchaseHistoryEntity, (p) => p.recommendedPurchase)
  histories: Promise<RecommendedPurchaseHistoryEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string
  @ManyToOne(() => OfferEntity, (p) => p.recommendedPurchases)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanId: string
  @ManyToOne(() => BusinessPlanEntity, (p) => p.recommendedPurchase)
  @JoinColumn({ name: 'businessPlanId', referencedColumnName: 'id' })
  businessPlan: Promise<BusinessPlanEntity>

  @OneToMany(() => RecommendedPurchaseSettingValueEntity, (p) => p.recommendedPurchase)
  recommendedPurchaseSettingValue: Promise<RecommendedPurchaseSettingValueEntity[]>

  @OneToMany(() => POEntity, (p) => p.recommendedPurchase)
  po: Promise<POEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.recommendedPurchase)
  contract: Promise<ContractEntity[]>

  /** Mã Nguồn tham chiếu  */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  referenceSource: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.recommendedPurchase)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentCostId: string
  @ManyToOne(() => ShipmentCostEntity, (p) => p.recommendedPurchase)
  @JoinColumn({ name: 'shipmentCostId', referencedColumnName: 'id' })
  shipmentCost: Promise<ShipmentCostEntity>

  @OneToMany(() => RecommendedPurchaseShipmentCostPriceEntity, (p) => p.recommendedPurchase)
  recommendedPurchaseShipmentCostPrices: Promise<RecommendedPurchaseShipmentCostPriceEntity[]>

  @OneToMany(() => RecommendedPurchaseShipmenStageEntity, (p) => p.recommendedPurchase)
  recommendedPurchaseShipmentStages: Promise<RecommendedPurchaseShipmenStageEntity[]>

  @OneToMany(() => RecommendedPurchaseRfqEntity, (p) => p.recommendedPurchase)
  recommendedPurchaseRfq: Promise<RecommendedPurchaseRfqEntity[]>
}
