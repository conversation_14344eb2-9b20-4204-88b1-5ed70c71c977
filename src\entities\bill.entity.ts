import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { POEntity } from './po.entity'
import { ContractEntity } from './contract.entity'
import { CurrencyEntity } from './currency.entity'
import { MediaFileEntity } from './mediaFile.entity'
import { BillHistoryEntity } from './billHistory.entity'
import { PaymentBillEntity } from './paymentBill.entity'
import { BillLookupEntity } from './billLookup.entity'
/** C<PERSON>u hình hóa đơn */
@Entity('bill')
export class BillEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  /** <PERSON>ô tả hóa đơn */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /** File xml */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  fileXml: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Tình trạng thanh toán */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  paymentStatus: string

  /** nhà cung cấp */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.bills)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** PO */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.bills)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.bills)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  /** Trị giá hóa đơn */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  invoiceValue: number

  /** tổng trị giá hóa đơn */
  @Column({
    type: 'float',
    nullable: true,
  })
  totalInvoiceValue: number

  /** Thuế VAT */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  vat: number

  @OneToMany(() => BillHistoryEntity, (p) => p.bill)
  histories: Promise<BillHistoryEntity[]>

  /** Nguồn tham chiếu hóa đơn(Theo hợp đồng hoặc theo PO)*/
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  referencesInvoice: string

  /** Đơn vị tiền tệ*/
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  currencyName: string

  @OneToMany(() => PaymentBillEntity, (p) => p.bill)
  paymentBills: Promise<PaymentBillEntity[]>

  /**Lưu id của công ty được chọn từ hệ thống hóa đơn bizzi */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  bizziCompanyId: string

  /** File đính kèm PDF */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  fileAttach: string

  /**Mã tra cứu hóa đơn điện tử */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  billLookupCode: string

  /**Tra cứu hóa đơn */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  billLookupId: string
  @ManyToOne(() => BillLookupEntity, (p) => p.bills)
  @JoinColumn({ name: 'billLookupId', referencedColumnName: 'id' })
  billLookup: Promise<BillLookupEntity>
}
