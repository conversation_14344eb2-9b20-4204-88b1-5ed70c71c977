import { <PERSON>ti<PERSON>, Column, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PurchasingOrgEntity } from './purchasingOrg.entity'
import { CompanyEntity } from './company.entity'
import { PurchasingGroupEntity } from './purchasingGroup.entity'
import { IncotermEntity } from './incoterm.entity'
import { CurrencyEntity } from './currency.entity'
import { SupplierPlantEntity } from './supplierPlant.entity'
import { SupplierNumberRequestApproveEntity } from './supplierNumberRequestApprove.entity'

/**vai trò role supplier : nhập thông tin phân bổ plant */
@Entity('role_supplier')
export class RoleSupplierEntity extends BaseEntity {
  /** Tổ chức mua hàng*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string

  @ManyToOne(() => PurchasingOrgEntity, (p) => p.roleSuppliers)
  @JoinC<PERSON>umn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  /** Công ty */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string

  @ManyToOne(() => CompanyEntity, (p) => p.roleSuppliers)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  /** Nhóm mua hàng*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string

  @ManyToOne(() => PurchasingGroupEntity, (p) => p.roleSuppliers)
  @JoinColumn({ name: 'purchasingGroupId', referencedColumnName: 'id' })
  purchasingGroup: Promise<PurchasingGroupEntity>

  /** Điều kiện thanh toán quốc tế */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermId: string

  @ManyToOne(() => IncotermEntity, (p) => p.roleSuppliers)
  @JoinColumn({ name: 'incotermId', referencedColumnName: 'id' })
  incoterm: Promise<IncotermEntity>

  /** Đơn vị tiền tệ*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currencyId: string

  @ManyToOne(() => CurrencyEntity, (p) => p.roleSuppliers)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  /** Supplier plant*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierPlantId: string

  @ManyToOne(() => SupplierPlantEntity, (p) => p.roleSuppliers)
  @JoinColumn({ name: 'supplierPlantId', referencedColumnName: 'id' })
  supplierPlant: Promise<SupplierPlantEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierNumberRequestApproveId: string

  @ManyToOne(() => SupplierNumberRequestApproveEntity, (p) => p.roleSuppliers)
  @JoinColumn({ name: 'supplierNumberRequestApproveId', referencedColumnName: 'id' })
  supplierNumberRequestApprove: Promise<SupplierNumberRequestApproveEntity>
}
