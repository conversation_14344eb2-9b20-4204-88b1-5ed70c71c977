import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateColum1749796920449 implements MigrationInterface {
  name = 'CreateColum1749796920449'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pr_item" ADD "unloadedQuantity" int CONSTRAINT "DF_5888e5eabc10b7e053330dd9d1b" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "pr_item" ADD "quantityPO" int CONSTRAINT "DF_a257ed292871d0ccba23a4fcd31" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "material" ADD "currencyCode" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "currencyId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "material" ADD "abcIndicator" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "maintenanceStatus" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "currentPeriod" float`)
    await queryRunner.query(`ALTER TABLE "material" ADD "unrestricted" float`)
    await queryRunner.query(`ALTER TABLE "material" ADD "dfStorLocLevel" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "countryOrigin" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "doNotCost" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "materialRelatedOrigin" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "materialIsCosted" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "material" ADD "inspectionText" varchar(250)`)
    await queryRunner.query(
      `ALTER TABLE "material" ADD CONSTRAINT "FK_d93c478fea2d9a98bf7c161cecd" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_d93c478fea2d9a98bf7c161cecd"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "inspectionText"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "materialIsCosted"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "materialRelatedOrigin"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "doNotCost"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "countryOrigin"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "dfStorLocLevel"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "unrestricted"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "currentPeriod"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "maintenanceStatus"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "abcIndicator"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "currencyId"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "currencyCode"`)
    await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "DF_a257ed292871d0ccba23a4fcd31"`)
    await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "quantityPO"`)
    await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "DF_5888e5eabc10b7e053330dd9d1b"`)
    await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "unloadedQuantity"`)
  }
}
