import { MigrationInterface, QueryRunner } from 'typeorm'

export class BusinessTemplatePlan1752833527934 implements MigrationInterface {
  name = 'BusinessTemplatePlan1752833527934'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "shipmentNumberPlanId" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "shipmentNumberPlanId"`)
  }
}
