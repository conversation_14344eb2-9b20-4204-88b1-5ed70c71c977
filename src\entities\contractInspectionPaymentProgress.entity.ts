import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PaymentMethodEntity } from './paymentMethod.entity'
import { ContractInspectionEntity } from './contractInspection.entity'

/** Tiến độ thanh toán trong nghiệm thu hợp đồng */
@Entity({ name: 'contract_inspection_payment_progress' })
export class ContractInspectionPaymentProgressEntity extends BaseEntity {
  /** Mã tiến độ */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  /** Tên tiến độ */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** % tiến độ */
  @Column({
    default: 0,
  })
  percent: number

  /** Số tiền cần thanh toán của tiến độ */
  @Column({
    nullable: true,
    type: 'float',
    default: 0,
  })
  money: number

  /** Ph<PERSON><PERSON><PERSON> thứ<PERSON> thanh toán */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentMethodId: string
  @ManyToOne(() => PaymentMethodEntity, (p) => p.contractInspectionPaymentProgresses)
  @JoinColumn({ name: 'paymentMethodId', referencedColumnName: 'id' })
  paymentMethod: Promise<PaymentMethodEntity>

  /** Chứng từ yêu cầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  requiredDocument: string

  /** Thời gian thanh toán cũ */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  oldTime: Date

  /** Thời gian thanh toán mới */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  newTime: Date

  /** Tiến độ thanh toán có được chọn? */
  @Column({
    nullable: true,
    default: false,
  })
  isChoose: boolean

  /** nghiệm thu hợp đồng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractInspectionId: string
  @ManyToOne(() => ContractInspectionEntity, (p) => p.paymentPlans)
  @JoinColumn({ name: 'contractInspectionId', referencedColumnName: 'id' })
  contractInspection: Promise<ContractInspectionEntity>
}
