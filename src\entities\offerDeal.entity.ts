import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { OfferDealSupplierEntity } from './offerDealSupplier.entity'
import { OfferDealPriceEntity } from './offerDealPrice.entity'
import { OfferEntity } from './offer.entity'

@Entity('offer_deal')
export class OfferDealEntity extends BaseEntity {
  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Thời điểm kết thúc đàm phán giá */
  @Column({
    nullable: false,
    type: 'datetime',
  })
  endDate: Date

  /** Gửi giá đàm phán hoặc không gửi giá trong thông báo đàm phàn với nhà cung cấp */
  @Column({
    nullable: false,
    default: false,
  })
  isSendDealPrice: boolean

  @Column({
    type: 'varchar',
    nullable: false,
  })
  offerId: string
  @ManyToOne(() => OfferEntity, (p) => p.offerDeals)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  /** Id của đàm phán cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId?: string
  /** Cha */
  @ManyToOne(() => OfferDealEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: Promise<OfferDealEntity>

  /** Con - 1 đàm phán sẽ có thể có nhiều đàm phán con */
  @OneToMany(() => OfferDealEntity, (p) => p.parent)
  childs: Promise<OfferDealEntity[]>

  /** Có bắt buộc File chi tiết giá */
  @Column({
    nullable: true,
    default: false,
  })
  isRequireFilePriceDetail: boolean

  /** Có bắt buộc File chi tiết kỹ thuật */
  @Column({
    nullable: true,
    default: false,
  })
  isRequireFileTechDetail: boolean

  @OneToMany(() => OfferDealSupplierEntity, (p) => p.offerDeal)
  offerDealSupplier: Promise<OfferDealSupplierEntity[]>

  @OneToMany(() => OfferDealPriceEntity, (p) => p.offerDeal)
  offerDealPrices: Promise<OfferDealPriceEntity[]>
}
