import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsDate, IsNotEmpty, IsString, IsOptional, IsArray, IsNumber, IsBoolean } from 'class-validator'
export class BidUpdateDto {
  /** ID gói thầu */
  @ApiProperty()
  // @IsString()
  id: string

  /** Có hiển thị ở trang chủ không */
  @ApiProperty()
  @IsBoolean()
  isShowHomePage: boolean

  /** Có gửi thông báo mởi thầu Doanh nghiệp qua email không */
  @ApiProperty()
  @IsBoolean()
  isSendEmailInviteBid: boolean

  /** Tự động chọn NCC thắng thầu và kết thúc thầu */
  @ApiProperty()
  @IsBoolean()
  isAutoBid: boolean

  isGetFromPr: boolean

  /** Tên gói thầu */
  @ApiProperty()
  // @IsString()
  name: string

  pmOrder: string

  businessPlanId: string

  refType: string

  nameEN: string

  exMatGroupId: any

  isLoadFromBusinessPlan?: boolean

  biddingTypeCode: string

  shipmentId: string

  /** YCMH */
  @ApiPropertyOptional()
  prId?: string

  /** Mã hệ thống tự sinh */
  @ApiPropertyOptional()
  code: string

  /** Tỉ trọng điểm đánh giá kỹ thuật */
  @ApiPropertyOptional()
  percentTech: number

  /** Tỉ trọng điểm đánh giá ĐKTM */
  @ApiPropertyOptional()
  percentTrade: number

  /** Tỉ trọng điểm đánh giá bảng giá */
  @ApiPropertyOptional()
  percentPrice: number

  /** Bản vẽ kỹ thuật hoặc hình ảnh minh hoạ */
  @ApiPropertyOptional()
  fileDrawing: string

  /** Phạm vi công việc */
  @ApiPropertyOptional()
  fileJD: string

  /** Tiêu chuẩn đánh giá KPI */
  @ApiPropertyOptional()
  fileKPI: string

  /** Các quy định về nội quy gói thầu: an toàn, an ninh, VSTP,... */
  @ApiPropertyOptional()
  fileRule: string

  /** Tài liệu mẫu (mẫu báo giá, mẫu hợp đồng,...) */
  @ApiPropertyOptional()
  fileDocument: string

  /** Khác */
  @ApiPropertyOptional()
  fileAnother: string

  /** Mô tả nội dung mời thầu */
  @ApiProperty()
  // @IsString()
  serviceInvite: string

  /** Ngày hết hạn xác nhận tham gia đấu thầu */
  @ApiProperty()
  acceptEndDate: Date

  /** Ngày hết hạn nộp hồ sơ thầu */
  @ApiProperty()
  submitEndDate: Date

  /** Địa chỉ nộp hồ sơ thầu */
  @ApiPropertyOptional()
  @IsOptional()
  // @IsString()
  addressSubmit?: string

  /** Công ty mời thầu */
  @ApiProperty()
  // @IsString()
  companyInvite: string

  companyInviteId: string

  /** Các địa điểm thực hiện gói thầu */
  @ApiPropertyOptional()
  @IsOptional()
  // @IsString()
  listAddress?: string

  plantId?: string

  /** Thời gian đăng tải hệ thống tự sinh */
  @ApiPropertyOptional()
  publicDate: Date

  /** Hình thức đấu thầu */
  @ApiProperty()
  // @IsString()
  bidTypeId: string

  /** Hiệu lực hợp đồng (tháng) */
  @ApiProperty()
  @IsNumber()
  timeserving: number

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @ApiPropertyOptional()
  scoreDLC: number

  /** Thời điểm mở thầu hệ thống tự sinh */
  @ApiPropertyOptional()
  startBidDate: Date

  /** Số tiền bảo lãnh dự thầu (VNĐ) */
  @ApiPropertyOptional()
  moneyGuarantee: number

  /** Thời hạn bảo lãnh dự thầu (tháng) */
  @ApiPropertyOptional()
  timeGuarantee: number

  /** Hình thức bảo lãnh dự thầu */
  @ApiPropertyOptional()
  masterBidGuaranteeId: string

  /** Thời hạn thiết lập yêu cầu kỹ thuật, năng lực */
  @ApiProperty()
  timeTechDate: Date

  /** Thời hạn thiết lập các hạng mục báo giá, cơ cấu giá và điều kiện thương mại */
  @ApiProperty()
  timePriceDate: Date

  /** Thời hạn đánh giá yêu cầu kỹ thuật, năng lực */
  @ApiProperty()
  timeCheckTechDate: Date

  /** Thời hạn đánh giá các hạng mục báo giá, cơ cấu giá và điều kiện thương mại */
  @ApiProperty()
  timeCheckPriceDate: Date

  /** Danh sách nhân viên có quyền */

  /** Nhân viên MPO */
  @ApiProperty()
  // @IsString()
  mpoId: string

  lstPriceShipment: any[]

  // /** Admin MPO */
  // @ApiProperty()
  // @IsNotEmpty()
  // @IsString()
  // mpoLeadId: string

  /** Nhân viên Kỹ thuật */
  @ApiProperty()
  techId: string

  @ApiProperty()
  techMemberId: string

  // /** Admin kỹ thuật */
  // @ApiProperty()
  // @IsNotEmpty()
  // @IsString()
  // techLeadId: string

  /** Nhân viên trong hội đồng */
  @ApiProperty()
  @IsArray()
  anotherRoleIds: string[]

  /** Các thành viên khác */
  @ApiPropertyOptional()
  otherRoleIds: string[]

  /** Danh sách Item gói thầu */
  @ApiProperty()
  listItem: any[]

  /** Nếu có thay đổi liên quan Item thì true */
  @ApiPropertyOptional()
  isChangeItem: boolean

  /** Nếu có thay đổi liên quan HĐ thầu thì true */
  @ApiPropertyOptional()
  isChangeEmployee: boolean
}
