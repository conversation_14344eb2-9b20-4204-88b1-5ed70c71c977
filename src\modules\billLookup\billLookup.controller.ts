import { Controller, UseGuards, Post, Body, Request } from '@nestjs/common'
import { BillLookupService } from './billLookup.service'
import { JwtAuthGuard } from '../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { BillLookupCreateDto, BillLookupUpdateDto } from './dto'
import { BillLookupImportExcelDto } from './dto/billLookupExcel.dto'
import { ACCEPT_LANGUAGE } from '../../constants'
import { Request as IRequest } from 'express'

@ApiBearerAuth()
@ApiTags('BillLookup')
@Controller('bill_lookup')
export class BillLookupController {
  constructor(private readonly service: BillLookupService) {}

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách tra cứu hóa đơn' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Request() req: IRequest) {
    return await this.service.find(user, req.headers[ACCEPT_LANGUAGE])
  }

  @ApiOperation({ summary: 'Lấy danh sách tra cứu hóa đơn phân trang' })
  @UseGuards(JwtAuthGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto, @Request() req: IRequest) {
    return await this.service.pagination(user, data, req.headers[ACCEPT_LANGUAGE])
  }

  @ApiOperation({ summary: 'Tạo tra cứu hóa đơn' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BillLookupCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Chỉnh sửa tra cứu hóa đơn' })
  @UseGuards(JwtAuthGuard)
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BillLookupUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết tra cứu hóa đơn' })
  @UseGuards(JwtAuthGuard)
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'import excel tra cứu hóa đơn' })
  @UseGuards(JwtAuthGuard)
  @Post('import_data')
  public async importData(@CurrentUser() user: UserDto, @Body() data: BillLookupImportExcelDto[]) {
    return await this.service.importData(user, data)
  }

  @ApiOperation({ summary: 'Hàm cập nhật trạng thái isDelete' })
  @UseGuards(JwtAuthGuard)
  @Post('update_active_status')
  public async updateActiveStatus(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateActiveStatus(user, data)
  }
}
