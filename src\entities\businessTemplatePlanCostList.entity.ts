import { Column, <PERSON>ti<PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CostEntity } from './cost.entity'
import { BusinessTemplatePlanEntity } from './businessTemplatePlan.entity'

@Entity('business_plan_template_cost_list')
export class BusinessPlanTemplateCostListEntity extends BaseEntity {
  // id của plan
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessTemplatePlanId: string
  @ManyToOne(() => BusinessTemplatePlanEntity, (p) => p.businessPlanTemplateCostLists)
  @JoinColumn({ name: 'businessTemplatePlan', referencedColumnName: 'id' })
  businessTemplatePlan: Promise<BusinessTemplatePlanEntity>

  // costId
  @Column({
    type: 'varchar',
    nullable: false,
  })
  costId: string
  @ManyToOne(() => CostEntity, (p) => p.businessPlanTemplateCostLists)
  @JoinColumn({ name: 'costId', referencedColumnName: 'id' })
  cost: Promise<CostEntity>

  /* Type */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  type: string

  // value
  @Column({
    type: 'float',
    nullable: true,
  })
  value: number

  // value
  @Column({
    type: 'float',
    nullable: true,
  })
  valueVanilla: number

  /* Type */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  typeConfig: string
}
