import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { POEntity } from './po.entity'
import { PoAcceptanceEmployeeEntity } from './poAcceptanceEmployee.entity'

/** <PERSON>hiệm thu PO*/
@Entity({ name: 'po_acceptance' })
export class PoAcceptanceEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.members)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  /** Số biên bản nghiệm thu */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  acceptanceNumber: string

  /** Đ<PERSON>i tượng nghiệm thu */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  acceptanceObject: string

  /** Đ<PERSON>a điểm bàn giao */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  address: string

  /** Thời gian bàn giao */
  @Column({
    type: 'datetime',
    nullable: true,
  })
  handoverTime: Date

  /** Ghi chú */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  descriptionAcceptance: string

  /** Kết quả nghiệm thu */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  acceptanceResults: string

  /** File đính kèm */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  attachedFile: string

  @OneToMany(() => PoAcceptanceEmployeeEntity, (p) => p.poAcceptance)
  poAcceptanceEmployees: Promise<PoAcceptanceEmployeeEntity[]>
}
