import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ShipmentFeeConditionsEntity } from './shipmentFeeConditions.entity'
import { RequestQuoteEntity } from './requestQuote.entity'
import { ShipmentFeeConditionsListEntity } from './shipmentFeeConditionsList.entity'
import { ShipmentFeeConditionsToListEntity } from './shipmentFeeConditionsToList.entity'

/** Yêu cầu báo giá */
@Entity('request_quote_fee')
export class RequestQuoteFeeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  requestQuoteId: string
  @ManyToOne(() => RequestQuoteEntity, (p) => p.requestQuoteFees)
  @JoinColumn({ name: 'requestQuoteId', referencedColumnName: 'id' })
  requestQuote: Promise<RequestQuoteEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentFeeConditionsId: string
  @ManyToOne(() => ShipmentFeeConditionsEntity, (p) => p.requestQuoteFees)
  @JoinColumn({ name: 'shipmentFeeConditionsId', referencedColumnName: 'id' })
  shipmentFeeConditions: Promise<ShipmentFeeConditionsEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentFeeConditionsListId: string
  @ManyToOne(() => ShipmentFeeConditionsListEntity, (p) => p.requestQuoteFees)
  @JoinColumn({ name: 'shipmentFeeConditionsListId', referencedColumnName: 'id' })
  shipmentFeeConditionsList: Promise<ShipmentFeeConditionsListEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentFeeConditionsToListId: string
  @ManyToOne(() => ShipmentFeeConditionsToListEntity, (p) => p.requestQuoteFees)
  @JoinColumn({ name: 'shipmentFeeConditionsToListId', referencedColumnName: 'id' })
  shipmentFeeConditionsToList: Promise<ShipmentFeeConditionsToListEntity>

  /** Có bắt buộc nhập hay không */
  @Column({
    nullable: true,
    default: false,
  })
  checked: boolean
}
