import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { CostSettingRepository } from '../../repositories'
import { CostSettingController } from './costSetting.controller'
import { CostSettingService } from './costSetting.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([CostSettingRepository])],
  controllers: [CostSettingController],
  providers: [CostSettingService],
  exports: [CostSettingService],
})
export class CostSettingModule {}
