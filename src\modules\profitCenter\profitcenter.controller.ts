/*
https://docs.nestjs.com/controllers#controllers
*/

import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ProfitCenterService } from './profitcenter.service'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { JwtAuthGuard } from '../common/guards'
import { UserDto } from '../../dto'
import { ProfitCenterCreationDto } from './dto/profitCenterCreationDto'
import { ProfitCenterUpdateDto } from './dto/profitCenterUpdateDto'
import { CurrentUser } from '../common/decorators'

@ApiBearerAuth()
@ApiTags('Profit Center')
@UseGuards(JwtAuthGuard)
@Controller('profit_center')
export class ProfitCenterController {
  constructor(private profitCenterService: ProfitCenterService) {}

  @ApiOperation({ summary: 'Tạo Profit Center mới' })
  @Post('get_data')
  async getProfitCenters() {
    return await this.profitCenterService.getAllProfitCenters()
  }

  @ApiOperation({ summary: 'Tạo Profit Center mới' })
  @Post('create_data')
  async addNewProfitCenter(@CurrentUser() user: UserDto, @Body() data: ProfitCenterCreationDto) {
    return await this.profitCenterService.addNewProfitCenter(user, data)
  }

  @ApiOperation({ summary: 'Chỉnh sửa/ Cập nhật Profit Center' })
  @Post('update_data')
  async updateProfitCenter(@CurrentUser() user: UserDto, @Body() data: ProfitCenterUpdateDto) {
    return await this.profitCenterService.updateProfitCenter(user, data)
  }

  @ApiOperation({ summary: 'Xóa Profit Center' })
  @Post('delete_data')
  async deleteProfitCenter(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.profitCenterService.updateActiveProfitCenter(user, data.id)
  }
}
