import { MigrationInterface, QueryRunner } from 'typeorm'

export class Guide1750928920079 implements MigrationInterface {
  name = 'Guide1750928920079'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "guide" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_fe92b4af32150e0580d37eacaef" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7437c36a619f13f75e8ba0d520e" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "guideName" nvarchar(max) NOT NULL, "guideLink" nvarchar(max) NOT NULL, CONSTRAINT "PK_fe92b4af32150e0580d37eacaef" PRIMARY KEY ("id"))`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "guide"`)
  }
}
