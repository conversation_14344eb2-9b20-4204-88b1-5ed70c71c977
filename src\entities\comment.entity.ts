import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

@Entity('comment')
export class CommentEntity extends BaseEntity {
  // Điểm đạt được qua phiếu đánh giá
  @Column({
    nullable: true,
  })
  comment: string

  /* id của đối tượng được đánh giá*/
  @Column({ type: 'varchar', nullable: true })
  targetId: string

  /* id của đối tượng được đánh giá*/
  @Column({ type: 'varchar', nullable: true })
  supplierId: string

  /* id phụ để xác định trong group*/
  @Column({ type: 'varchar', nullable: true })
  subId: string

  /* bảng được đánh giá*/
  @Column({ type: 'varchar', nullable: true })
  entityName: string

  /* nhà cung cấp được đánh giá */
  @Column({ type: 'varchar', nullable: true })
  employeeId: string
}
