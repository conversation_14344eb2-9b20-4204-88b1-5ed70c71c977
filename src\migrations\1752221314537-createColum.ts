import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateColum1752221314537 implements MigrationInterface {
  name = 'CreateColum1752221314537'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "rfq" ADD "shipmentFeeConditionTypeCompactId" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "shipmentFeeConditionTypeCompactCode" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "shipmentFeeConditionTypeCompactValue" varchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "shipmentFeeConditionTypeCompactValue"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "shipmentFeeConditionTypeCompactCode"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "shipmentFeeConditionTypeCompactId"`)
  }
}
