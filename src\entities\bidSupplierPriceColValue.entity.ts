import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { BidSupplierEntity } from './bidSupplier.entity'
import { BidPriceEntity } from './bidPrice.entity'
import { BidPriceColEntity } from './bidPriceCol.entity'

@Entity('bid_supplier_price_col_value')
export class BidSupplierPriceColValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidSupplierId: string
  @ManyToOne(() => BidSupplierEntity, (p) => p.bidSupplierPriceColValue)
  @JoinColumn({ name: 'bidSupplierId', referencedColumnName: 'id' })
  bidSupplier: Promise<BidSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidSupplierPriceColValue)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidPriceColId: string
  @ManyToOne(() => BidPriceColEntity, (p) => p.bidSupplierPriceColValue)
  @JoinColumn({ name: 'bidPriceColId', referencedColumnName: 'id' })
  bidPriceCol: Promise<BidPriceColEntity>
}
