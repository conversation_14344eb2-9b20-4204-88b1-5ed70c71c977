import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ShipmentConditionTypeEntity } from './shipmentCondictionType.entity'
import { ShipmentConditionTypeTemplateValueEntity } from './shipmentCondictionTypeTemplateValue.entity'
import { TransportationPlanDetailEntity } from './transportationPlanDetail.entity'
import { ShipmentConditionTypeLogEntity } from './shipmentConditionTypeLog.entity'

@Entity('shipment_condition_type_template')
export class ShipmentConditionTypeTemplateEntity extends BaseEntity {
  /*condition type*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentConditionTypeId: string
  @ManyToOne(() => ShipmentConditionTypeEntity, (p) => p.shipmentConditionTypeTemplates)
  @JoinColumn({ name: 'shipmentConditionTypeId', referencedColumnName: 'id' })
  shipmentConditionType: Promise<ShipmentConditionTypeEntity>

  /* date from */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  dateFrom: Date

  /* date to*/
  @Column({
    nullable: true,
    type: 'datetime',
  })
  dateTo: Date

  /* loại */
  @Column({
    length: 50,
    nullable: false,
  })
  type: string

  /* giá */
  @Column({ type: 'bigint', nullable: true })
  price: number

  /* id của bảng giá năm */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  priceYearId: string

  @Column({ type: 'bigint', nullable: true })
  priceYear: number

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** giá 12 tháng */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  jsonPrice: string

  /* năm */
  @Column({
    type: 'int',
    nullable: true,
  })
  year: number

  // quý 1
  @Column({
    type: 'bigint',
    nullable: true,
  })
  priceQuarter1: number

  // quý 2
  @Column({
    type: 'bigint',
    nullable: true,
  })
  priceQuarter2: number

  // quý 3
  @Column({
    type: 'bigint',
    nullable: true,
  })
  priceQuarter3: number

  // quý 4
  @Column({
    type: 'bigint',
    nullable: true,
  })
  priceQuarter4: number

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  conditionCodeCompact: string[]
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  conditionIdCompact: string[]

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  conditionValueIdCompact: string[]

  @OneToMany(() => ShipmentConditionTypeTemplateValueEntity, (p) => p.shipmentConditionTypeTemplate)
  shipmentConditionTypeTemplateValues: Promise<ShipmentConditionTypeTemplateValueEntity[]>

  @OneToMany(() => TransportationPlanDetailEntity, (p) => p.shipmentConditionType)
  transportationPlanDetails: Promise<TransportationPlanDetailEntity[]>

  @OneToMany(() => ShipmentConditionTypeLogEntity, (p) => p.shipmentConditionTypeTemplate)
  shipmentConditionTypeLog: Promise<ShipmentConditionTypeLogEntity[]>
}
