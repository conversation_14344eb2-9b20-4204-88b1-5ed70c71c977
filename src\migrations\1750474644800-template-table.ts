import { MigrationInterface, QueryRunner } from "typeorm";

export class TemplateTable1750474644800 implements MigrationInterface {
    name = 'TemplateTable1750474644800'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "template_table" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a715865068840479f3320af50f2" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7b07e4e8f418b0e20def4456eef" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "code" varchar(50) NOT NULL, "name" nvarchar(250), "type" nvarchar(250), "apiEndpoint" nvarchar(250), "apiQueryParams" nvarchar(250), "desc" varchar(max), "synchronize" bit CONSTRAINT "DF_8183f4a97bffe2d57a27309bdcc" DEFAULT 0, "configs" nvarchar(max), CONSTRAINT "PK_a715865068840479f3320af50f2" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "template_table"`);
    }

}
