import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BillRepository, CompanyRepository, PlantRepository } from '../../repositories'
import { BillService } from './bill.service'
import { Bill<PERSON>ontroller } from './bill.controller'
import { HttpModule } from '@nestjs/axios'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BillRepository, CompanyRepository, PlantRepository]), HttpModule, OrganizationalPositionModule],
  controllers: [BillController],
  providers: [BillService],
})
export class BillModule {}
