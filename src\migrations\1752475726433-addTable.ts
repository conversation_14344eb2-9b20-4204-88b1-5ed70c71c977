import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTable1752475726433 implements MigrationInterface {
  name = 'AddTable1752475726433'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "incoterm_conversion" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ac2ee0490f8a813ebfe21870ccf" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2afa3631cb7469e31f7deab98ba" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "incotermToId" uniqueidentifier, "incotermFormId" uniqueidentifier, "lstSettingCostId" nvarchar(max), "shipmentFeeConditionTypeId" nvarchar(max), CONSTRAINT "PK_ac2ee0490f8a813ebfe21870ccf" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(`ALTER TABLE "incoterm" ADD "sort" int`)
    await queryRunner.query(
      `ALTER TABLE "incoterm_conversion" ADD CONSTRAINT "FK_fd8565fd0f9dcc71bed0c6f946b" FOREIGN KEY ("incotermToId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "incoterm_conversion" ADD CONSTRAINT "FK_c0be46abbd2a583ef628e61f9f0" FOREIGN KEY ("incotermFormId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP CONSTRAINT "FK_c0be46abbd2a583ef628e61f9f0"`)
    await queryRunner.query(`ALTER TABLE "incoterm_conversion" DROP CONSTRAINT "FK_fd8565fd0f9dcc71bed0c6f946b"`)
    await queryRunner.query(`ALTER TABLE "incoterm" DROP COLUMN "sort"`)
    await queryRunner.query(`DROP TABLE "incoterm_conversion"`)
  }
}
