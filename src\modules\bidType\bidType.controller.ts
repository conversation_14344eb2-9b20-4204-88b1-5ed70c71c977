import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { BidTypeService } from './bidType.service'
import { BidTypeCreateDto, BidTypeUpdateDto } from './dto'
import { JwtAuthGuard } from '../common/guards'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@ApiTags('Bid Type')
/** <PERSON><PERSON>nh thức đấu thầu */
@Controller('bidTypes')
export class BidTypeController {
  constructor(private readonly service: BidTypeService) {}

  @ApiOperation({ summary: 'L<PERSON>y danh sách hình thức đấu thầu' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto) {
    return await this.service.find(user)
  }

  @ApiOperation({ summary: '<PERSON><PERSON> sách hình thức đấu thầu phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo hình thức đấu thầu' })
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BidTypeCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật hình thức đấu thầu' })
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BidTypeUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động hình thức đấu thầu' })
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data)
  }
}
