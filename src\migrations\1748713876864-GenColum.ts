import { MigrationInterface, QueryRunner } from "typeorm";

export class GenColum1748713876864 implements MigrationInterface {
    name = 'GenColum1748713876864'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_plant" ADD "planningStrategyGroup" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD "originGroup" varchar(50)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_plant" DROP COLUMN "originGroup"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP COLUMN "planningStrategyGroup"`);
    }

}
