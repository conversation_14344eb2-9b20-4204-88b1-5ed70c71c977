import { MigrationInterface, QueryRunner } from 'typeorm'

export class PrItem1749544487233 implements MigrationInterface {
  name = 'PrItem1749544487233'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "budget_receipt" ADD "fmDoc" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "pr_item" ADD "fund" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "fund"`)
    await queryRunner.query(`ALTER TABLE "budget_receipt" DROP COLUMN "fmDoc"`)
  }
}
