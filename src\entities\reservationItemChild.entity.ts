import { Entity, Column, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ReservationEntity } from './reservation.entity'
import { MaterialEntity } from './material.entity'
import { ReservationItemEntity } from './reservationItem.entity'

@Entity('reservation_item_child')
export class ReservationItemChildEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  reservationId: string
  @ManyToOne(() => ReservationEntity, (p) => p.reservationItemChilds)
  @JoinColumn({ name: 'reservationId', referencedColumnName: 'id' })
  reservation: Promise<ReservationEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  reservationItemId: string
  @ManyToOne(() => ReservationItemEntity, (p) => p.reservationItemChilds)
  @JoinColumn({ name: 'reservationItemId', referencedColumnName: 'id' })
  reservationItem: Promise<ReservationItemEntity>

  /** số lượng cần đặt hàng */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  /** tên hàng */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  shortText: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.reservationItemChilds)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** asset */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetCode: string

  /** asset Desc */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetDesc: string

  /** io */
  @Column({
    length: 'max',
    nullable: true,
  })
  io: string

  /** io Name */
  @Column({
    length: 'max',
    nullable: true,
  })
  ioName: string

  /** iotype */
  @Column({
    length: 'max',
    nullable: true,
  })
  iotype: string

  /** io Name */
  @Column({
    length: 'max',
    nullable: true,
  })
  relstatus: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  reservationItemNewId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  reservationItemOldId: string
}
