/*
https://docs.nestjs.com/modules
*/

import { Module } from '@nestjs/common'
import { ProfitCenterController } from './profitcenter.controller'
import { ProfitCenterService } from './profitcenter.service'
import { TypeOrmExModule } from '../../typeorm'
import { ProfitCenterRepository } from '../../repositories/profiCenter.repository'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([ProfitCenterRepository])],
  controllers: [ProfitCenterController],
  providers: [ProfitCenterService],
})
export class ProfitCenterModule {}
