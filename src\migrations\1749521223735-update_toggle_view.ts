import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateToggleView1749521223735 implements MigrationInterface {
  name = 'UpdateToggleView1749521223735'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "roleViewUpdated" bit NOT NULL CONSTRAINT "DF_f2e67c32cbbb3bcf11b28704b60" DEFAULT 0`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "DF_f2e67c32cbbb3bcf11b28704b60"`)
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "roleViewUpdated"`)
  }
}
