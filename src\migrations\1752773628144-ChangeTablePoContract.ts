import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeTablePoContract1752773628144 implements MigrationInterface {
    name = 'ChangeTablePoContract1752773628144'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po_contract" DROP CONSTRAINT "FK_4bf4da351075735ad528dd7d31a"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "employeeId"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "poRoleCode"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "description"`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "contractId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "poCode" nvarchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "quantityPo" int`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD CONSTRAINT "FK_6a4db1ae62b6adab3addeecb9c9" FOREIGN KEY ("contractId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po_contract" DROP CONSTRAINT "FK_6a4db1ae62b6adab3addeecb9c9"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "quantityPo"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "poCode"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "contractId"`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "description" varchar(250)`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "poRoleCode" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "employeeId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD CONSTRAINT "FK_4bf4da351075735ad528dd7d31a" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
