import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateRole1749030827196 implements MigrationInterface {
    name = 'UpdateRole1749030827196'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ADD "queryParams" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "user" ADD "roleUpdated" bit NOT NULL CONSTRAINT "DF_0f36290d620527f125aad069073" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "DF_0f36290d620527f125aad069073"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "roleUpdated"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "queryParams"`);
    }

}
