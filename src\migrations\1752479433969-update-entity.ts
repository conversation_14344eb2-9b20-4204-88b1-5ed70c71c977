import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateEntity1752479433969 implements MigrationInterface {
  name = 'UpdateEntity1752479433969'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "referenceType" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "shipmentPlanIncotermId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "shipmentPlanPriceId" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "shipmentPlanPriceId"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "shipmentPlanIncotermId"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "referenceType"`)
  }
}
