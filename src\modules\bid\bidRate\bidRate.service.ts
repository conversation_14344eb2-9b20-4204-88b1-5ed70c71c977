import { Injectable, NotFoundException, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'
import { enumData, ERROR_YOU_DO_NOT_HAVE_PERMISSION, ERROR_NOT_FOUND_DATA } from '../../../constants'
import { EmailService } from '../../email/email.service'
import {
  BidRepository,
  BidEmployeeAccessRepository,
  BidSupplierRepository,
  BidDealRepository,
  BidPriceColRepository,
  BidTechRepository,
  BidTradeRepository,
  BidPriceRepository,
  BidEmployeeRateRepository,
  PermissionApproveRepository,
  BidPrItemRepository,
  RateDataRepository,
  BidAuctionRepository,
} from '../../../repositories'
import { PaginationDto, UserDto } from '../../../dto'
import {
  BidSupplierTechValueEntity,
  BidSupplierTradeValueEntity,
  BidSupplierPriceValueEntity,
  BidSupplierCustomPriceValueEntity,
  BidHistoryEntity,
  BidEntity,
  BidTechEntity,
  SupplierEntity,
} from '../../../entities'
import { coreHelper } from '../../../helpers'
import { And, In, IsNull, Like, Not, Or, Raw } from 'typeorm'
import * as moment from 'moment'
import { FlowApproveService } from '../../flowApprove/flowApprove.service'
import { BidEmployeeRateEntity } from '../../../entities/bidEmployeeRate.entity'
import { ifft } from 'mathjs'
import { OrganizationalPositionService } from '../../organizationalPosition/organizationalPosition.service'
import { RateDataEntity } from '../../../entities/rateData.entity'
// import { RateDataRepository } from '../../../repositories/rateData.repository'

@Injectable()
export class BidRateService {
  constructor(
    private readonly repo: BidRepository,
    private readonly bidDealRepo: BidDealRepository,
    private readonly bidAuctionRepository: BidAuctionRepository,
    private readonly bidPrItemRepository: BidPrItemRepository,
    private readonly bidTechRepo: BidTechRepository,
    private readonly bidTradeRepo: BidTradeRepository,
    private readonly bidPriceRepo: BidPriceRepository,
    private readonly bidPriceColRepo: BidPriceColRepository,
    private readonly bidEmployeeRateRepository: BidEmployeeRateRepository,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly emailService: EmailService,
    private readonly rateDataRepository: RateDataRepository,
    private flowService: FlowApproveService,
    private permissionApproveRepository: PermissionApproveRepository,
    private organizationalPositionService: OrganizationalPositionService,
  ) {}

  /* hàm đánh giá từng tiêu chí trong thầu */
  async rateData(user: UserDto, data: { lstData: any; type: any; bidSupplierId: any }) {
    /* Tìm ra xem nó thuộc bid nào */
    let bidRateDetail = null
    if (data.type === enumData.RATE_TYPE.TECH.code) {
      bidRateDetail = await this.bidTechRepo.findOne({ where: { id: data.lstData[0].id } })
    }
    if (data.type === enumData.RATE_TYPE.TRADE.code) {
      bidRateDetail = await this.bidTradeRepo.findOne({ where: { id: data.lstData[0].id } })
    }
    if (data.type === enumData.RATE_TYPE.PRICE.code) {
      bidRateDetail = await this.bidPriceRepo.findOne({ where: { id: data.lstData[0].id } })
    }
    if (!bidRateDetail) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    /* nếu có thì lưu danh sách vào db để load lên lại*/
    /* từ bidSupplierId với bidRateDetail.bidId tìm ra được bidsup  */
    const bidSupplier = await this.bidSupplierRepo.findOne({ where: { id: data.bidSupplierId } })
    /* xóa hết những gì ở trong bảng ratedata*/
    const lstId = data.lstData.map((c) => c.id)
    await this.rateDataRepository.delete({
      bidId: bidRateDetail.bidId,
      employeeId: user.employeeId,
      supplierId: bidSupplier.id,
      type: data.type,
      targetId: In(lstId),
    })
    /* nếu như có bid exGr thì đánh giá theo bid ex, dựa theo số lượng mà chia đều */
    const dicRate: any = {}
    let score = 0
    const dicExGr: any = {}
    for (const item of data.lstData) {
      dicRate[item.id] = item.scoreRow
      if (item.percent) {
        score += (item.scoreRow * item.percent) / 100
        if (item.bidExgroupId) {
          if (!dicExGr[item.bidExgroupId]) dicExGr[item.bidExgroupId] = 0
          dicExGr[item.bidExgroupId] += item.scoreRow * (item.percent / 100)
        }
      }
      const bidEmployeeRate = new RateDataEntity()
      bidEmployeeRate.rateNumber = item.scoreRow
      bidEmployeeRate.employeeId = user.employeeId
      bidEmployeeRate.type = data.type
      bidEmployeeRate.bidId = bidRateDetail.bidId
      bidEmployeeRate.supplierId = bidSupplier.id
      bidEmployeeRate.targetId = item.id
      bidEmployeeRate.entityName = BidTechEntity.name
      await this.rateDataRepository.save(bidEmployeeRate)
    }

    // tính điểm bằng tổng list dicExGr / số lượng trong dicExGr
    const lstDicExGr = Object.values(dicExGr)
    if (lstDicExGr.length > 0) {
      for (const item of lstDicExGr) {
        score = +item / lstDicExGr.length
      }
    }
    if (score > 100) score = 100
    console.log(score)
    /* tính ra điểm trung bình dựa theo xử lý theo type truyền vào */
    let scoreValue = 0
    let type = data.type
    if (data.type === enumData.RATE_TYPE.TECH.code) {
      /* tìm ra điểm trung bình để cập nhật vào nhân viên đánh giá  */
      // const score = await this.repo.calScoreTech(bidRateDetail.bidItemId, dicRate)
      scoreValue = score
      await this.updateTechRate(user, { id: bidRateDetail.bidId, supplierId: bidSupplier.supplierId, scoreManualTech: score })
    }
    if (data.type === enumData.RATE_TYPE.TRADE.code) {
      // const score = await this.repo.calScoreTrade(bidRateDetail.bidItemId, dicRate)
      scoreValue = score
      await this.updateTradeRate(user, { id: bidRateDetail.bidId, supplierId: bidSupplier.supplierId, scoreManualTrade: score })
    }
    if (data.type === enumData.RATE_TYPE.PRICE.code) {
      // const score = await this.repo.calScorePrice(bidRateDetail.bidItemId, dicRate)
      scoreValue = score
      await this.updatePriceRate(user, { id: bidRateDetail.bidId, supplierId: bidSupplier.supplierId, scoreManualPrice: score })
    }
    /* lưu điểm trung bình lại vào trong rate data để kiểm tra xem nó đã đánh giá chưachưa */

    await this.rateDataRepository.delete({
      bidId: bidRateDetail.bidId,
      employeeId: user.employeeId,
      supplierId: bidSupplier.id,
      type: data.type,
      entityName: SupplierEntity.name,
    })
    const bidEmployeeRate = new RateDataEntity()
    bidEmployeeRate.rateNumber = scoreValue
    bidEmployeeRate.employeeId = user.employeeId
    bidEmployeeRate.bidId = bidRateDetail.bidId
    bidEmployeeRate.type = data.type
    bidEmployeeRate.supplierId = bidSupplier.id
    bidEmployeeRate.entityName = SupplierEntity.name
    await this.rateDataRepository.save(bidEmployeeRate)

    return { message: 'Đánh giá thành công' }
  }
  /*User thao tác khoá lại danh sách lưu 
  0.tìm ra theo type trong hội đồng đánh giá của nó có bao nhiêu người đánh giá
  1.Tìm ra RateData dựa theo idNcc, user.emp và type truyền vào
  2.chuyển trạng thái sang đã khoá (trạng thái của rate Data)
  3.tính ra điểm trung bình và những cái đã RateData khoá 
  4.cập nhật điểm trung bình vào bidSupplierRepo dựa vào type truyền vào
  5.1 Nếu như tất cảc RateData đểu đánh giá thì chuyển trạng thái sang  enumData.BidSupplierTradeStatus.DaXacNhan.code, dựa vào type truyền vào
  6. nếu như nó đã duyệt tất cả ncc thì chuyển trạng thái nhân viên thành đã đánh giá
  
  */
  async lockRateData(user: UserDto, data: { bidSupplierId: string; type: string }) {
    const bidSup = await this.bidSupplierRepo.findOne({ where: { id: data.bidSupplierId } })

    /* tìm ra xem trong hội đồng đánh giá có bao nhiêu người 
    _/ nếu như là tech,price thì hội đồng đánh giá là enumData.BidRuleType.EmpTech.code
     \ nếu như là trade thì hội đồng đánh giá là enumData.BidRuleType.EmpOther.code 
     và tất cả phải dc đánh giá bằng cờ isScore = true 
     */
    let type = enumData.BidRuleType.EmpTech.code
    if (enumData.RATE_TYPE.TRADE.code) {
      let type = enumData.BidRuleType.EmpOther.code
    }
    const lsEmployeeRating = await this.bidEmployeeAccessRepo.find({ where: { bidId: bidSup.bidId, type: type, isScore: true } })
    /* chuyển trạng thái của rateData bằng user.emp và ncc và bidId */
    const empBidRate = await this.rateDataRepository.find({
      where: { bidId: bidSup.bidId, employeeId: user.employeeId, supplierId: bidSup.id, type: data.type },
    })
    for (const item of empBidRate) {
      item.lock = true
      await this.rateDataRepository.save(item)
    }
    /* tính điểm trung bình dựa vào danh sách lsEmployeeRating trong bảng rateData bằng điểm với biến là rateNumber và lock=true   */
    /* lọc ra danh sách những nhân viên đánh giá có lock = true , id = bidId , sup = supplierId*/
    const lstRateData = await this.rateDataRepository.find({
      where: { bidId: bidSup.bidId, supplierId: bidSup.id, type: data.type, lock: true, entityName: SupplierEntity.name },
    })
    let scoreValue = 0
    /* tính ra điểm trung bình dựa vào tổng số rateNumber/lstRateData.length  */
    for (const item of lstRateData) {
      scoreValue += item.rateNumber
    }
    scoreValue = scoreValue / lstRateData.length
    /* cập nhật điểm trung bình vào bidSupplierRepo dựa vào type truyền vào */
    if (data.type === enumData.RATE_TYPE.TECH.code) {
      await this.bidSupplierRepo.update(bidSup.id, { scoreManualTech: scoreValue })
    }
    if (data.type === enumData.RATE_TYPE.TRADE.code) {
      await this.bidSupplierRepo.update(bidSup.id, { scoreManualTrade: scoreValue })
    }
    if (data.type === enumData.RATE_TYPE.PRICE.code) {
      await this.bidSupplierRepo.update(bidSup.id, { scoreManualPrice: scoreValue })
    }
    /* nếu như tất cả đều đã đánh giá thì cập nhật trạng thái sang đã xác nhận */
    if (lsEmployeeRating.length === lstRateData.length) {
      if (data.type === enumData.RATE_TYPE.TECH.code) {
        await this.bidSupplierRepo.update(bidSup.id, { statusTech: enumData.BidSupplierTechStatus.DaXacNhan.code })
      }
      if (data.type === enumData.RATE_TYPE.TRADE.code) {
        await this.bidSupplierRepo.update(bidSup.id, { statusTrade: enumData.BidSupplierTradeStatus.DaXacNhan.code })
      }
      if (data.type === enumData.RATE_TYPE.PRICE.code) {
        await this.bidSupplierRepo.update(bidSup.id, { statusPrice: enumData.BidSupplierPriceStatus.DaXacNhan.code })
      }
    }
    return { message: 'Khóa thành công' }
  }

  /* Hàm cập nhật vào điểm trung bình của gói thầu */

  async updateTechRate(user: UserDto, data: { id: string; supplierId: string; scoreManualTech: number }) {
    // kiểm tra quyền

    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    /* tìm ra danh sách nhân viên đánh giá kỹ thuật*
    /* tìm ra danh sách nhân viên đánh giá kỹ thuật hiện tại*/
    const employeeAction = await this.bidEmployeeAccessRepo.findOne({
      where: { bidId: bid.id, type: enumData.BidRuleType.EmpTech.code, employeeId: user.employeeId, isScore: true },
    })

    if (!employeeAction) throw new Error(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const userScore = await this.bidEmployeeRateRepository.findOne({
      where: { employeeAccessId: employeeAction.id, supplierId: data.supplierId },
    })
    if (userScore) {
      await this.bidEmployeeRateRepository.update(userScore.id, { scoreManualTech: data.scoreManualTech })
    } else {
      const newUserScore = new BidEmployeeRateEntity()
      newUserScore.createdAt = new Date()
      newUserScore.employeeAccessId = employeeAction.id
      newUserScore.supplierId = data.supplierId
      newUserScore.scoreManualTech = data.scoreManualTech
      await this.bidEmployeeRateRepository.save(newUserScore)
    }

    return { message: 'Gửi yêu cầu phê duyệt kết quả đánh giá năng lực thành công.' }
  }

  async updateTradeRate(user: UserDto, data: { id: string; supplierId: string; scoreManualTrade: number }) {
    // kiểm tra quyền

    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    /* tìm ra danh sách nhân viên đánh giá kỹ thuật*
    /* tìm ra danh sách nhân viên đánh giá kỹ thuật hiện tại*/
    const employeeAction = await this.bidEmployeeAccessRepo.findOne({
      where: { bidId: bid.id, type: enumData.BidRuleType.EmpOther.code, employeeId: user.employeeId, isScore: true },
    })

    const userScore = await this.bidEmployeeRateRepository.findOne({
      where: { employeeAccessId: employeeAction.id, supplierId: data.supplierId },
    })
    if (userScore) {
      await this.bidEmployeeRateRepository.update(userScore.id, { scoreManualTrade: data.scoreManualTrade })
    } else {
      const newUserScore = new BidEmployeeRateEntity()
      newUserScore.createdAt = new Date()
      newUserScore.employeeAccessId = employeeAction.id
      newUserScore.supplierId = data.supplierId
      newUserScore.scoreManualTrade = data.scoreManualTrade
      await this.bidEmployeeRateRepository.save(newUserScore)
    }

    return { message: 'Gửi yêu cầu phê duyệt kết quả đánh giá năng lực thành công.' }
  }

  async updatePriceRate(user: UserDto, data: { id: string; supplierId: string; scoreManualPrice: number }) {
    // kiểm tra quyền
    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    /* tìm ra danh sách nhân viên đánh giá kỹ thuật*
    /* tìm ra danh sách nhân viên đánh giá kỹ thuật hiện tại*/
    const employeeAction = await this.bidEmployeeAccessRepo.findOne({
      where: { bidId: bid.id, type: enumData.BidRuleType.EmpTech.code, employeeId: user.employeeId, isScore: true },
    })

    const userScore = await this.bidEmployeeRateRepository.findOne({
      where: { employeeAccessId: employeeAction.id, supplierId: data.supplierId },
    })
    if (userScore) {
      await this.bidEmployeeRateRepository.update(userScore.id, { scoreManualPrice: data.scoreManualPrice })
    } else {
      const newUserScore = new BidEmployeeRateEntity()
      newUserScore.createdAt = new Date()
      newUserScore.employeeAccessId = employeeAction.id
      newUserScore.supplierId = data.supplierId
      newUserScore.scoreManualPrice = data.scoreManualPrice
      await this.bidEmployeeRateRepository.save(newUserScore)
    }

    return { message: 'Gửi yêu cầu phê duyệt kết quả đánh giá năng lực thành công.' }
  }

  /** Hàm load gói thầu module đánh giá */
  async pagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    let lstStatus = [
      enumData.BidStatus.DangNhanBaoGia.code,
      enumData.BidStatus.DangNhanBaoGia.code,
      enumData.BidStatus.DangDanhGia.code,
      enumData.BidStatus.DangDuyetDanhGia.code,
      enumData.BidStatus.HoanTatDanhGia.code,
      enumData.BidStatus.DongDauGia.code,
      enumData.BidStatus.DongDamPhanGia.code,
      enumData.BidStatus.DangDauGia.code,
      enumData.BidStatus.ChoXacNhan.code,
      enumData.BidStatus.DangDamPhanGia.code,
      enumData.BidStatus.DongThau.code,
      enumData.BidStatus.DuyetNCCThangThau.code,
      enumData.BidStatus.DangDuyetKetThucThau.code,
    ]
    let whereCon: any = [
      {
        parentId: IsNull(),
        // companyId: user.companyId,
        isDeleted: false,
        isSurvey: false,
        biddingTypeCode: Or(In([enumData.BiddingType.PRODUCT.code]), IsNull()),
        status: In(lstStatus),
      },
    ]

    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.BidRate.code, [
      enumData.FlowCode.CONTRACT_APPENDIX.code,
    ])
    if (dataRs.whereApprove?.length > 0) whereCon[dataRs.propertiesApprove] = In(dataRs.whereApprove)
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }

    //  whereCon.status =
    // Lấy gói thầu mpoLead cần duyệt
    if (data.where.isGetBidNeedApprove) {
      const whereTemp = [
        {
          companyId: user.companyId,
          employeeAccess: { employeeId: user.employeeId, type: enumData.BidRuleType.MPOLeader.code },
          status: In([enumData.BidStatus.DangDanhGia.code, enumData.BidStatus.DangDuyetDanhGia.code]),
          statusRateTech: enumData.BidTechRateStatus.DaTao.code,
        },
        {
          companyId: user.companyId,
          employeeAccess: { employeeId: user.employeeId, type: enumData.BidRuleType.MPOLeader.code },
          status: In([enumData.BidStatus.DangDanhGia.code, enumData.BidStatus.DangDuyetDanhGia.code]),
          statusRateTrade: enumData.BidTradeRateStatus.DaTao.code,
          statusRatePrice: enumData.BidPriceRateStatus.DaTao.code,
        },
        {
          companyId: user.companyId,
          employeeAccess: { employeeId: user.employeeId, type: enumData.BidRuleType.MPOLeader.code },
          status: In([enumData.BidStatus.DongThau.code, enumData.BidStatus.DangDuyetKetThucThau.code]),
        },
      ]

      const lstBidTemp = await this.repo.find({ where: whereTemp, select: { id: true } })
      const lstBidId = lstBidTemp.map((c) => c.id)

      if (lstBidId.length == 0) return [[], 0]
      whereCon.id = In(lstBidId)
    }
    // Lấy gói thầu có quyền xem
    else whereCon.employeeAccess = { employeeId: user.employeeId }

    if (data.where.serviceId) whereCon.childs = { serviceId: data.where.serviceId }

    if (data.where.status?.length > 0) lstStatus = data.where.status
    // whereTemp.status = In(lstStatus)
    whereCon.status = In(lstStatus)

    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    } else if (data.where.dateTo) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)
    }

    // Tìm theo mã số hoặc tên gói thầu
    if (data.where.name) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.where.name}%`) },
        { ...whereCon, name: Like(`%${data.where.name}%`) },
      ]
    }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      // where: { id: 'CEF11480-59D0-4D6A-BEC2-7F9CDC05E88E' },
      skip: data.skip,
      take: data.take,
      order: { code: 'DESC' },
    })
    if (res[0].length == 0) return res

    const lstId = res[0].map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), isDeleted: false },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c))
    }

    for (const item of res[0]) {
      item.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
        lsType: [
          enumData.FlowCode.EVALUATE_RESULT_CAPACITY.code,
          enumData.FlowCode.EVALUATE_RESULT_TRADE.code,
          enumData.FlowCode.SUPPLIER_WIN_BID.code,
          enumData.FlowCode.FINISH_BID.code,
        ],
      })
      // item.canApprove = item.objPermissionApprove[enumData.FlowCode.BIDTRADE.code]

      const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)
      const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techName = objTech?.__employee__?.name || ''
      const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.__employee__?.name || ''
      item.isCreator = item.createdBy === user.employeeId ? true : false

      const lstAccessUser = lstAccess.filter((c) => c.employeeId === user.employeeId)
      item.isMPO = lstAccessUser.some((c) => c.type === enumData.BidRuleType.EmpOther.code || c.type === enumData.BidRuleType.MPO.code)
      if (item.isMemberScore)
        item.isMPO = lstAccessUser.some((c) => c.type === enumData.BidRuleType.EmpOther.code || c.type === enumData.BidRuleType.MPO.code)
      item.isMPOLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPOLeader.code)
      item.isTech = lstAccessUser.some((c) => c.type === enumData.BidRuleType.EmpTech.code || c.type === enumData.BidRuleType.Tech.code)
      if (item.isMemberScore)
        item.isTech = lstAccessUser.some((c) => c.type === enumData.BidRuleType.EmpTech.code || c.type === enumData.BidRuleType.Tech.code)
      item.isTechLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.TechLeader.code)
      item.isMember = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Memmber.code)
      item.statusName = dicStatus[item.status]?.name
      item.statusColor = dicStatus[item.status]?.statusColor
      item.statusBorderColor = dicStatus[item.status]?.statusBorderColor
      item.statusBgColor = dicStatus[item.status]?.statusBgColor
      // mpo có quyền như tech
      item.isTech = item.isTech || item.isMPO
      // mpoLead có quyền như techLead
      item.isTechLeader = item.isTechLeader || item.isMPOLeader
      item.isShowCopy = item.isMPO || item.isMPOLeader || item.isCreator
      item.isShowDelete = (item.isMPO || item.isCreator) && !item.isRequestDelete
      item.isShowApproveDelete = (item.isMPOLeader || item.isCreator) && item.isRequestDelete
      // mở thầu
      item.isShowOpenBid = item.status === enumData.BidStatus.DangNhanBaoGia.code && (item.isMPO || item.isMPOLeader || item.isCreator)
      // biên bản mở thầu
      item.isShowProtocolOpenBid = item.status !== enumData.BidStatus.DangNhanBaoGia.code && (item.isMPO || item.isMPOLeader || item.isCreator)
      // report: các thành viên trong hội đồng thầu, mpo, mpoLead
      item.isShowReport =
        (item.isMPO || item.isMPOLeader || item.isMember || item.isCreator) && item.status !== enumData.BidStatus.DangNhanBaoGia.code
      // phân tích giá
      item.isShowAnalysis = (item.isMPO || item.isMPOLeader) && item.status !== enumData.BidStatus.DangNhanBaoGia.code
      // Kết thúc nộp chào giá hiệu chỉnh
      item.isShowEndResetPrice =
        item.statusResetPrice == enumData.BidResetPriceStatus.DaTao.code && (item.isMPO || item.isMPOLeader || item.isCreator)
      // đánh giá

      /* Show theo dõi đấu giá */
      const haveData = await this.bidAuctionRepository.findOne({
        where: { bidId: item.id, status: enumData.BidAuctionStatus.DangDauGia.code, isDeleted: false },
      })
      item.isShowEndResetPrice = haveData && (item.isMPO || item.isMPOLeader || item.isCreator)

      if (
        item.statusResetPrice !== enumData.BidResetPriceStatus.DaTao.code &&
        (item.status === enumData.BidStatus.DangDanhGia.code || item.status === enumData.BidStatus.DangDuyetDanhGia.code)
      ) {
        // nút đánh giá kỹ thuật
        item.isShowTechRate = item.isTech || item.isCreator || item.objPermissionApprove[enumData.FlowCode.EVALUATE_RESULT_CAPACITY.code]
        if (item.isShowTechRate) {
          if (item.statusRateTech === enumData.BidTechRateStatus.DangTao.code) {
            item.techType = 'dashed'
          }
          if (item.statusRateTech === enumData.BidTechRateStatus.TuChoi.code) {
            item.techType = 'danger'
          }
          if (item.statusRateTech === enumData.BidTechRateStatus.DaTao.code) {
            item.techType = 'warning'
          }
          if (item.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) {
            item.techType = 'success'
          }
        }

        // nút đánh giá thương mại
        item.isShowTradeRate =
          item.isMPO || item.isMPOLeader || item.isCreator || item.objPermissionApprove[enumData.FlowCode.EVALUATE_RESULT_TRADE.code]
        if (item.isShowTradeRate) {
          if (item.statusRateTrade === enumData.BidTradeRateStatus.DangTao.code) {
            item.tradeType = 'dashed'
          }
          if (item.statusRateTrade === enumData.BidTradeRateStatus.TuChoi.code) {
            item.tradeType = 'danger'
          }
          if (item.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code) {
            item.tradeType = 'warning'
          }
          if (item.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code) {
            item.tradeType = 'success'
          }
        }

        // nút đánh giá chào giá
        item.isShowPriceRate =
          item.isMPO || item.isMPOLeader || item.isCreator || item.objPermissionApprove[enumData.FlowCode.EVALUATE_RESULT_TRADE.code]
        if (item.isShowPriceRate) {
          if (item.statusRatePrice === enumData.BidPriceRateStatus.DangTao.code) {
            item.priceType = 'dashed'
          }
          if (item.statusRatePrice === enumData.BidPriceRateStatus.TuChoi.code) {
            item.priceType = 'danger'
          }
          if (item.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code) {
            item.priceType = 'warning'
          }
          if (item.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code) {
            item.priceType = 'success'
          }
        }
      }

      // Cấu hình lại bảng giá
      item.isShowResetPrice =
        (item.status === enumData.BidStatus.HoanTatDanhGia.code ||
          item.status === enumData.BidStatus.DongDamPhanGia.code ||
          item.status === enumData.BidStatus.DongDauGia.code) &&
        (item.isMPO || item.isMPOLeader || item.isCreator)

      if (item.statusResetPrice == enumData.BidResetPriceStatus.ChuaTao.code || item.statusResetPrice == enumData.BidResetPriceStatus.KetThuc.code) {
        // Chọn ncc thắng thầu
        item.isShowEnd =
          item.status === enumData.BidStatus.HoanTatDanhGia.code ||
          item.status === enumData.BidStatus.DongDamPhanGia.code ||
          item.status === enumData.BidStatus.DongDauGia.code ||
          item.objPermissionApprove[enumData.FlowCode.SUPPLIER_WIN_BID.code]
        //    &&
        // item.isMPO

        // Duyệt chọn ncc thắng thầu
        // item.isShowAcceptEnd = item.status === enumData.BidStatus.DongThau.code && item.isMPOLeader
        item.isShowAcceptEnd = item.status === enumData.BidStatus.DongThau.code || item.objPermissionApprove[enumData.FlowCode.SUPPLIER_WIN_BID.code]

        // Đàm phán giá
        item.isShowDeal =
          (item.status === enumData.BidStatus.HoanTatDanhGia.code || item.status === enumData.BidStatus.DongDamPhanGia.code) && item.isMPO

        // Đấu giá
        item.isShowAuction = item.status === enumData.BidStatus.HoanTatDanhGia.code && (item.isMPO || item.isCreator)

        // Đàm phán giá & Đấu giá
        item.isShowDealAuction = item.status === enumData.BidStatus.HoanTatDanhGia.code && (item.isMPO || item.isCreator)
      }
      // In hồ sơ thầu
      item.isShowPrint = item.status === enumData.BidStatus.DuyetNCCThangThau.code && (item.isMPO || item.isMPOLeader || item.isCreator)
      // Gửi yêu cầu phê duyệt kết thúc thầu
      item.isShowSendRequestFinishBid = item.status === enumData.BidStatus.DuyetNCCThangThau.code && (item.isMPO || item.isCreator)

      // Phê duyệt kết thúc thầu
      // item.isShowApproveFinishBid = item.status === enumData.BidStatus.DangDuyetKetThucThau.code && item.isMPOLeader
      item.isShowApproveFinishBid =
        item.status === enumData.BidStatus.DangDuyetKetThucThau.code || item.objPermissionApprove[enumData.FlowCode.FINISH_BID.code]
    }

    return res
  }

  //#region bidRateTech

  /** Check quyền tạo đánh giá kỹ thuật cho gói thầu */
  private async checkPermissionCreateBidTechRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId }, select: { id: true, statusRateTech: true } })
    if (!bid) return { hasPermission: result, message }

    if (bid.statusRateTech === enumData.BidTechRateStatus.DangTao.code || bid.statusRateTech === enumData.BidTechRateStatus.TuChoi.code) {
      result = await this.bidEmployeeAccessRepo.isTech(user, bidId)
      if (!result) message = 'Bạn không có quyền đánh giá kỹ thuật cho gói thầu.'
    } else {
      result = false
      message = 'Gói thầu đã được đánh giá kỹ thuật.'
    }

    return { hasPermission: result, message }
  }

  /** Lấy ds NCC tham gia thầu và tính điểm */
  async loadTechRate(user: UserDto, bidId: string, isMobile?: boolean) {
    const bidSupplierTechValueRepo = this.repo.manager.getRepository(BidSupplierTechValueEntity)

    let bidIdO = null
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: {
        bidId: bidId,
        companyId: user.companyId,
        isDeleted: false,
        type: enumData.BidRuleType.EmpTech.code,
      },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        isScore: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid2(user, bidId)
    res.isShowBtn = true
    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    const dicStatusTech: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierTechStatus)
      lstStatus.forEach((c) => (dicStatusTech[c.code] = c.name))
    }
    const setType = new Set()

    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.List.code)

    bidId = res.id
    // Lấy template hồ sơ kỹ thuật của Item
    res.lstBidTech = await this.bidTechRepo.getTech(user, res.id)
    // Lấy hồ sơ kỹ thuật của các NCC tham gia
    res.lstBidSupplier = await this.bidSupplierRepo.find({
      // where: { bidId: item.id, statusFile: enumData.BidSupplierFileStatus.HopLe.code, },
      where: { bidId: res.id, statusFile: enumData.BidSupplierFileStatus.HopLe.code },
      relations: { supplier: true, bidSupplierTechValue: true },
    })
    if (res.lstBidSupplier.length === 0) return []

    //Lấy tiêu chí tính điểm (cấp 1 & kiểu number hoặc list)
    var lstBidTechCal = res.lstBidTech.filter((c) => c.parentId === null && setType.has(c.type))

    // với từng NCC tham gia Item
    for (const bidSupplier of res.lstBidSupplier) {
      bidSupplier.supplierCode = bidSupplier.__supplier__.code
      bidSupplier.supplierName = bidSupplier.__supplier__.name
      // delete bidSupplier.__supplier__
      bidSupplier.statusFileName = dicStatusFile[bidSupplier.statusFile]
      bidSupplier.statusTechName = dicStatusTech[bidSupplier.statusTech]
      /* lấy trạng thái lock dựa theo nhân viên đó */
      const lockStatus = await this.rateDataRepository.findOne({
        where: {
          entityName: SupplierEntity.name,
          supplierId: bidSupplier.id,
          bidId: bidId,
          type: enumData.RATE_TYPE.TECH.code,
          employeeId: user.employeeId,
        },
      })
      bidSupplier.lock = false
      bidSupplier.rating = false
      bidSupplier.lockName = 'Chưa đánh giá'
      if (lockStatus) {
        bidSupplier.rating = true
        bidSupplier.lock = lockStatus.lock
        bidSupplier.lockName = bidSupplier.lock ? 'Đã khóa' : 'Chưa khóa'
      }

      if (bidSupplier.scoreTech === 0) {
        const lstBidSupplierTechValue = bidSupplier.__bidSupplierTechValue__ || []
        let scoreTech = 0
        let isHighlight = false
        let isNotHaveMinValue = false

        // với từng tiêu chí cần tính điểm
        for (const bidTech of lstBidTechCal) {
          const itemChilds = bidTech.__childs__ || []
          if (itemChilds.length > 0) {
            let scoreTechChild = 0
            for (const itemChild of itemChilds) {
              const objValueChild = lstBidSupplierTechValue.find((c: any) => c.bidTechId === itemChild.id)
              if (objValueChild) {
                const tem = this.calScoreTechItem(itemChild, objValueChild.value)
                objValueChild.score = tem

                // Lưu điểm
                await bidSupplierTechValueRepo.update(objValueChild.id, { score: tem, updatedBy: user.id })
                scoreTechChild += tem
                isHighlight = this.checkHighlightTechItem(itemChild, objValueChild.value)
                isNotHaveMinValue = this.checkMinValueTechItem(itemChild, objValueChild.value)
              }
            }
            const temp = (bidTech.percent * scoreTechChild) / 100
            scoreTech += temp

            // Lưu điểm
            await bidSupplierTechValueRepo.update({ bidTechId: bidTech.id, bidSupplierId: bidSupplier.id }, { score: temp, updatedBy: user.id })
          } else {
            const objValue = lstBidSupplierTechValue.find((c: any) => c.bidTechId === bidTech.id)
            if (objValue) {
              const temp = this.calScoreTechItem(bidTech, objValue.value)
              objValue.score = temp
              scoreTech += temp

              isHighlight = this.checkHighlightTechItem(bidTech, objValue.value)
              isNotHaveMinValue = this.checkMinValueTechItem(bidTech, objValue.value)

              // Lưu điểm
              await bidSupplierTechValueRepo.update({ bidTechId: bidTech.id, bidSupplierId: bidSupplier.id }, { score: temp, updatedBy: user.id })
            }
          }
        }
        if (isNaN(scoreTech) || !isFinite(scoreTech)) bidSupplier.scoreTech = 0
        bidSupplier.scoreTech = scoreTech
        bidSupplier.isHighlight = isHighlight
        bidSupplier.isNotHaveMinValue = isNotHaveMinValue

        // Lưu điểm
        await this.bidSupplierRepo.update(bidSupplier.id, {
          scoreTech: bidSupplier.scoreTech,
          isHighlight: bidSupplier.isHighlight,
          isNotHaveMinValue: bidSupplier.isNotHaveMinValue,
          updatedBy: user.id,
        })
      }

      bidSupplier.rankABCD = coreHelper.rankABCD(bidSupplier.scoreTech)
    }

    // sort
    res.lstBidSupplier.sort((a, b) => b.scoreTech - a.scoreTech)
    res.lstBidSupplier.sort((a, b) => (a.isNotHaveMinValue ? 1 : 0) - (b.isNotHaveMinValue ? 1 : 0))

    res.lstEmployeeAccess = lstEmployeeAccess
    /* Check quyền duyệt của nhân viên  */

    res.canSend = true
    for (const employee of res.lstEmployeeAccess) {
      /* tìm xem nhân viên đó có đánh giá chưa */
      const employeeAction: any = await this.bidEmployeeAccessRepo.findOne({
        where: { bidId: bidId, type: enumData.BidRuleType.EmpTech.code, employeeId: employee.employeeId, isScore: true },
      })
      // Tìm ra số lượng đánh giá của nhân viên đó trong rateValue
      const lstScore = await this.rateDataRepository.find({
        where: { bidId: bidId, employeeId: employee.employeeId, entityName: SupplierEntity.name, lock: true, type: enumData.RATE_TYPE.TECH.code },
      })
      const lstBidSupplier = await this.bidSupplierRepo.find({
        // where: { bidId: item.id, statusFile: enumData.BidSupplierFileStatus.HopLe.code, },
        where: { bidId: bidId, statusFile: enumData.BidSupplierFileStatus.HopLe.code },
      })
      if (lstScore.length > 0) {
        if (lstScore.length >= lstBidSupplier.length) {
          employee.status = 'Đã đánh giá'
        } else {
          employee.status = 'Đang đánh giá'
          res.canSend = false
        }
      } else {
        employee.status = 'Chưa đánh giá '
        res.canSend = false
      }
      /* tìm ra số lượng ncc cần đánh giá */
    }

    const employeeAction: any = await this.bidEmployeeAccessRepo.findOne({
      where: { bidId: bidId, type: enumData.BidRuleType.EmpTech.code, employeeId: user.employeeId, isScore: true },
      relations: { bidEmployeeRate: true },
    })
    res.canSave = false

    if (employeeAction) {
      res.isShowBtn = true
      res.canSave = true
    } else {
      res.isShowBtn = false
    }
    if (employeeAction && employeeAction.scoreManualTech > 0) {
      res.isShowBtn = false
    }

    res.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
      lsType: [enumData.FlowCode.EVALUATE_RESULT_CAPACITY.code],
      entityName: BidEntity.name,
    })

    res.canApprove = res.objPermissionApprove[enumData.FlowCode.EVALUATE_RESULT_CAPACITY.code]

    if (isMobile) {
      const listTech = []
      const listItem = res?.listItem.find((item) => item.isExmatgroup)

      for (let i of listItem.lstBidSupplier) {
        let listTechValue = await this.loadBestTechValue(user, {
          bidId: i.bidId,
          bidSupplierId: i.id,
        })

        listTech.push({
          supplierId: i.id,
          supplierName: i.supplierName,
          statusFileName: i.statusFileName,
          statusTechName: i.statusTechName,
          rankABCD: i.rankABCD,
          scoreTech: i.scoreTech,
          scoreManualTech: i.scoreManualTech,
          listTechValue,
        })
      }

      const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
        targetId: res?.id,
        entityName: BidEntity.name,
        type: enumData.FlowCode.EVALUATE_RESULT_CAPACITY.code,
      })

      return {
        id: res.id,
        name: res.name,
        serviceName: res.serviceName,
        listTech,
        lstEmployeeAccess: res?.lstEmployeeAccess.map((item) => {
          return {
            id: item?.id,
            type: item?.type,
            employeeId: item?.employeeId,
            bidId: item?.bidId,
            isScore: item?.isScore,
            name: item?.__employee__.name,
          }
        }),
        status: res.status ?? '',
        statusName: 'Chờ duyệt',
        approvalProgress,
        canApprove,
      }
    }

    /* Tìm ra danh sách cấp duyệt và comment và người duyệt */
    res.haveProgress = false
    res.approvalProgress = await this.flowService.getListApproveDetail(user, {
      targetId: res.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.EVALUATE_RESULT_CAPACITY.code,
    })
    res.showComment = false
    /* nếu như nhân viên hiện tại chưa duyệt và nằm trong luồng duyệt thì toggle bật showComment = true */
    for (const item of res.approvalProgress) {
      if (item.approveType == user.orgPositionId && !item.approved) {
        res.showComment = true
        break
      }
    }
    if (res.approvalProgress.length > 0) res.haveProgress = true

    return res
  }

  /**
   * Lấy danh sách bidTech và điểm cao nhất tương ứng
   * @param data.bidId - Item trong gói thầu
   * @param data.bidSupplierId - NCC tham gia Item
   */
  async loadBestTechValue(user: UserDto, data: { bidId: string; bidSupplierId: string }) {
    const lstBidSupplier = await this.bidSupplierRepo.find({ where: { bidId: data.bidId }, select: { id: true } })
    const listBidSupplierId = lstBidSupplier.map((p) => p.id)
    if (listBidSupplierId.length == 0) throw new Error('Không có hồ sơ thầu hợp lệ')

    // Danh sách bidTech
    const bidTechs: any = await this.bidTechRepo.find({
      where: { bidId: data.bidId, parentId: IsNull(), isDeleted: false },
      relations: { bid: true },
    })

    const isPrivate = bidTechs[0].__bid__.isPersonalScoreVisible
    let lsEmployeeRating = []
    // if (!isPrivate) {
    if (true) {
      /* nếu như không hiện cá nhân thì lấy ra danh sách
      Load ra danh sách nhân viên đánh đánh giá  
       */
      lsEmployeeRating = await this.bidEmployeeAccessRepo.find({
        // where: { bidId: data.bidId, type: enumData.BidRuleType.EmpTech.code, isScore: true, employeeId: Not(user.employeeId) },
        where: { bidId: data.bidId, type: enumData.BidRuleType.EmpTech.code, isScore: true },

        relations: { employee: true },
      })
      for (const item of lsEmployeeRating) {
        item.employeeName = item?.__employee__?.name
        item.employeeId = item?.__employee__?.id
      }
      /* tìm ra danh sách */
    }
    // Danh sách tất cả giá trị các NCC đã nộp
    const bidTechValue = await this.repo.manager.getRepository(BidSupplierTechValueEntity).find({
      where: { bidSupplierId: In(listBidSupplierId) },
    })

    let result = []
    const length = bidTechs.length

    const setType = new Set()
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.List.code)

    // Lọc qua danh sách bidTech
    for (let i = 0; i < length; i++) {
      let itemResult: any = {}
      const item = bidTechs[i]
      itemResult.id = item.id
      itemResult.name = item.name
      itemResult.percent = item.percent
      itemResult.percentRule = item.percentRule
      itemResult.type = item.type
      itemResult.sort = item.sort

      /* 
      2580ECD6-D4C8-EF11-ACD4-06546786BEE7
      2580ECD6-D4C8-EF11-ACD4-06546786BEE7
       */
      // Giá trị mà NCC tương ứng đã nộp hồ sơ
      const supplierTechValue = bidTechValue.find((p) => p.bidTechId === item.id && p.bidSupplierId === data.bidSupplierId)

      itemResult.value = supplierTechValue?.value

      // Nếu kiểu list thì lọc để lấy name
      if (itemResult.type === enumData.DataType.List.code) {
        const listValue = await item.bidTechListDetails
        const find = listValue.find((p) => p.id === itemResult.value)
        itemResult.value = find?.name
      }
      itemResult.score = supplierTechValue?.score || 0

      // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
      const listBidSupplierTechValue = bidTechValue.filter((p) => p.bidTechId === item.id)

      // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
      if (listBidSupplierTechValue.length > 0 && setType.has(itemResult.type)) {
        // Lấy giá trị có điểm tốt nhất
        const bestSupplier = this.getMaxOfArrayObj(listBidSupplierTechValue)

        itemResult.bestScore = bestSupplier?.score
        itemResult.bestSupplierName = await (await bestSupplier.bidSupplier).supplier.name
        itemResult.bestValue = bestSupplier?.value

        // Nếu kiểu list thì lọc qua để lấy name
        if (itemResult.type === enumData.DataType.List.code) {
          const listValue = await (await bestSupplier.bidTech).bidTechListDetails
          const find = listValue.find((p) => p.id === bestSupplier.value)
          itemResult.bestValue = find?.name
        }
        if (supplierTechValue) {
          itemResult.rank = this.getCurrentRank(listBidSupplierTechValue, supplierTechValue?.bidSupplierId)
        }
      }

      // Lọc qua danh sách con
      itemResult.childs = []
      const itemChilds = await item.childs
      if (itemChilds?.length > 0) {
        for (const itemC of itemChilds) {
          let itemResultC: any = {}
          itemResultC.id = itemC.id
          itemResultC.name = itemC.name
          itemResultC.percent = itemC.percent
          itemResultC.percentRule = itemC.percentRule
          itemResultC.type = itemC.type
          itemResultC.sort = itemC.sort

          // Lấy giá trị mà NCC tương ứng đã nôpk
          const supplierTechValueC = bidTechValue.find((p) => p.bidTechId === itemC.id && p.bidSupplierId === data.bidSupplierId)

          itemResultC.value = supplierTechValueC?.value

          // Nếu kiểu list thì lọc qua danh sách để lấy name
          if (itemResultC.type === enumData.DataType.List.code) {
            const listValueC = await itemC.bidTechListDetails
            const findC = listValueC.find((p) => p.id === itemResultC.value)
            itemResultC.value = findC?.name
          }
          itemResultC.score = supplierTechValueC?.score

          // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
          const listBidSupplierTechValueC = bidTechValue.filter((p) => p.bidTechId === itemC.id)
          // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
          if (listBidSupplierTechValueC.length > 0 && setType.has(itemResultC.type)) {
            // Lấy giá trị có điểm tốt nhất
            const bestSupplierC = this.getMaxOfArrayObj(listBidSupplierTechValueC)

            itemResultC.bestScore = bestSupplierC?.score
            itemResultC.bestSupplierName = await (await bestSupplierC.bidSupplier).supplier.name
            itemResultC.bestValue = bestSupplierC?.value
            // Nếu kiểu list thì lọc qua để lấy name
            if (itemResultC.type === enumData.DataType.List.code) {
              const listValueC = await (await bestSupplierC.bidTech).bidTechListDetails
              const findC = listValueC.find((p) => p.id === bestSupplierC.value)
              itemResultC.bestValue = findC?.name
            }
            if (supplierTechValueC) {
              itemResultC.rank = this.getCurrentRank(listBidSupplierTechValueC, supplierTechValueC?.bidSupplierId)
            }
          }
          itemResult.childs.push(itemResultC)
        }
      }
      itemResult.childs = itemResult.childs.sort((a, b) => a.sort - b.sort)

      result.push(itemResult)
    }

    result = result.sort((a, b) => a.sort - b.sort)
    /* map điểm thành điểm của nhân viên  */
    const lstResultId = result.map((c) => c.id)
    const lstValue = await this.rateDataRepository.find({
      where: { entityName: BidTechEntity.name, employeeId: user.employeeId, targetId: In(lstResultId), supplierId: data.bidSupplierId },
    })

    const dicScore: any = {}
    for (const item of lstValue) {
      dicScore[item.targetId] = item.rateNumber
    }
    for (const item of result) {
      if (!isPrivate) {
        item.lstDataEmp = lsEmployeeRating
        let idx = 0
        for (const data of item.lstDataEmp) {
          const score = await this.rateDataRepository.findOne({ where: { employeeId: data.employeeId, bidId: data.bidId, targetId: item.id } })
          item[`score${idx}`] = score?.rateNumber || 0
          idx++
        }
        // score0 = 70
      }
      item.scoreRow = dicScore[item.id] || 0
    }

    return result
  }

  /** Tính điểm đánh giá kỹ thuật */
  calScoreTechItem(item: any, value: string) {
    let score = 0
    if (item.type === enumData.DataType.Number.code && value && value.trim() != '') {
      let temp = 0
      const x = +value
      // Tính theo chiều thuận
      if (item.isCalUp) {
        if (x >= item.percentRule) {
          temp = item.percent
        } else {
          temp = (x * item.percent) / item.percentRule // giá trị * tỉ trọng / điều kiện b
        }
      }
      // Tính theo chiều nghịch
      else {
        if (x <= item.percentRule) {
          temp = item.percent
        } else if (x >= item.percentDownRule) {
          temp = 0
        } else {
          temp = ((item.percentDownRule - x) * item.percent) / (item.percentDownRule - item.percentRule)
        }
      }

      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    } else if (item.type === enumData.DataType.List.code) {
      const itemChosen = item.__bidTechListDetails__.find((p) => p.id === value)
      const tem = itemChosen ? itemChosen.value : 0
      const temp = (tem * item.percent) / 100
      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    }

    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  /**  Check highlight đánh giá kỹ thuật */
  checkHighlightTechItem(item: any, value: string) {
    let isHighlight = false
    if (!item.hightlightValue) {
      return isHighlight
    }

    if (item.type === enumData.DataType.Number.code && value && value.trim() != '') {
      if (+value >= item.hightlightValue) {
        isHighlight = true
      }
    } else if (item.type === enumData.DataType.List.code) {
      const itemChosen = item.__bidTechListDetails__.find((p) => p.id === value)
      const temp = itemChosen ? itemChosen.value : 0
      if (temp >= item.hightlightValue) {
        isHighlight = true
      }
    }

    return isHighlight
  }

  /** Check isNotHaveMinValue đánh giá kỹ thuật */
  checkMinValueTechItem(item: any, value: string) {
    let isNotHaveMinValue = false
    if (!item.requiredMin) {
      return isNotHaveMinValue
    }

    if (item.type === enumData.DataType.Number.code && value && value.trim() != '') {
      if (+value < item.requiredMin) {
        isNotHaveMinValue = true
      }
    } else if (item.type === enumData.DataType.List.code) {
      const itemChosen = item.__bidTechListDetails__.find((p) => p.id === value)
      const temp = itemChosen ? itemChosen.value : 0
      if (temp < item.requiredMin) {
        isNotHaveMinValue = true
      }
    }

    return isNotHaveMinValue
  }

  /** Tạo đánh giá kỹ thuật cho gói thầu */
  async createTechRate(user: UserDto, data: { id: string; listItem: any[] }) {
    // kiểm tra quyền
    // if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    // const objPermission = await this.checkPermissionCreateBidTechRate(user, data.id)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    /* tìm ra danh sách nhân viên đánh giá kỹ thuật*/
    const lstEmployee = await this.bidEmployeeAccessRepo.find({ where: { bidId: bid.id, type: enumData.BidRuleType.EmpTech.code } })
    /* tìm ra danh sách nhân viên đánh giá kỹ thuật hiện tại*/
    let status = bid.status

    if (
      (bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code || bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code) &&
      (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code || bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code)
    ) {
      status = enumData.BidStatus.DangDuyetDanhGia.code
    }

    await this.repo.update(data.id, {
      statusRateTech: enumData.BidTechRateStatus.DaTao.code,
      status,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DanhGiaKyThuat.code
    bidHistory.save()

    // gửi email trưởng bộ phận kỹ thuật
    this.emailService.GuiTechLeadDuyetDanhGiaKyThuat(data.id)
    // tạo quyền duyệt kết quả đánh giá năng lực

    let flowType: string
    flowType = enumData.FlowCode.EVALUATE_RESULT_CAPACITY.code
    const isHave = await this.permissionApproveRepository.findOne({
      where: { targetId: data.id, entityName: BidEntity.name, type: flowType, isDeleted: false },
    })
    if (!isHave)
      await this.flowService.setRoleRule(user, {
        targetId: data.id,
        target: data,
        entityName: BidEntity.name,
        flowType: flowType,
        companyId: user.orgCompanyId,
        // departmentId: user?.departmentId,
      })

    return { message: 'Gửi yêu cầu phê duyệt kết quả đánh giá năng lực thành công.' }
  }

  /** Check quyền duyệt đánh giá kỹ thuật cho gói thầu */
  async checkPermissionApproveBidTechRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid) {
      if (bid.statusRateTech === enumData.BidTechRateStatus.DaTao.code) {
        const flagPermission = await this.bidEmployeeAccessRepo.isTechLeader(user, bidId)
        if (flagPermission) {
          result = true
        } else {
          message = 'Bạn không có quyền xét duyệt đánh giá năng lực, kỹ thuật cho gói thầu này.'
        }
      } else if (bid.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) {
        message = 'Gói thầu đã được xét duyệt đánh giá năng lực, kỹ thuật.'
      } else {
        message = 'Chưa có yêu cầu xét duyệt đánh giá năng lực, kỹ thuật.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Duyệt đánh giá kỹ thuật */
  async approveTechRate(user: UserDto, data: any) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: data.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.EVALUATE_RESULT_CAPACITY.code,
      comment: data.comment,
    })

    if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
      return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    } else {
      if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
      // const objPermission = await this.checkPermissionApproveBidTechRate(user, data.id)
      // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

      const bid = await this.repo.findOne({ where: { id: data.id } })
      if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

      // for (const item of data.listItem) {
      for (const bidSupplierItem of data.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTech: enumData.BidSupplierTechStatus.DaDuyet.code,
          noteTechLeader: bidSupplierItem.noteTechLeader,
          updatedBy: user.id,
        })
      }
      // }

      let status = bid.status
      if (bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code && bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code) {
        status = enumData.BidStatus.HoanTatDanhGia.code
      }

      await this.repo.update(data.id, {
        statusRateTech: enumData.BidTechRateStatus.DaDuyet.code,
        status,
        updatedBy: user.id,
      })

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = data.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.DuyetDanhGiaKyThuat.code
      bidHistory.save()

      // gửi email
      this.emailService.ThongBaoDuyetDanhGiaKyThuat(data.id)

      return { message: 'Duyệt kết quả đánh giá năng lực thành công.' }
    }
  }

  /** Từ chối đánh giá kỹ thuật */
  async rejectTechRate(user: UserDto, data: { id: string; listItem: any[]; comment?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionApproveBidTechRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    await this.flowService.rejectRule(user, {
      targetId: data.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.EVALUATE_RESULT_CAPACITY.code,
      comment: data.comment,
    })

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTech: enumData.BidSupplierTechStatus.DangDanhGia.code,
          noteTechLeader: bidSupplierItem.noteTechLeader,
          updatedBy: user.id,
        })
      }
    }

    await this.repo.update(data.id, {
      statusRateTech: enumData.BidTechRateStatus.TuChoi.code,
      status: enumData.BidStatus.DangDanhGia.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TuChoiDanhGiaKyThuat.code
    bidHistory.save()

    // gửi email bộ phận kỹ thuật
    this.emailService.GuiTechTuChoiDanhGiaKyThuat(data.id)

    return { message: 'Từ chối đánh giá kỹ thuật thành công.' }
  }
  //#endregion

  //#region bidRateTrade

  /** Check quyền tạo đánh giá điều kiện thương mại cho gói thầu */
  async checkPermissionCreateBidTradeRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid) {
      if (bid.statusRateTrade === enumData.BidTradeRateStatus.DangTao.code || bid.statusRateTrade === enumData.BidTradeRateStatus.TuChoi.code) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bidId)
        if (!result) {
          message = 'Bạn không có quyền đánh giá điều kiện thương mại cho gói thầu.'
        }
      } else {
        result = false
        message = 'Gói thầu đã được đánh giá điều kiện thương mại.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Lấy ds NCC tham gia thầu và tính điểm */
  async loadTradeRate(user: UserDto, bidId: string) {
    const bidSupplierTradeValueRepo = this.repo.manager.getRepository(BidSupplierTradeValueEntity)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: {
        bidId: bidId,
        companyId: user.companyId,
        isDeleted: false,
        type: enumData.BidRuleType.EmpOther.code,
      },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        isScore: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid2(user, bidId)
    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    const dicStatusTrade: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierTradeStatus)
      lstStatus.forEach((c) => (dicStatusTrade[c.code] = c.name))
    }
    const setType = new Set()
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.List.code)
    let bidIdO = null
    // for (const res of res.listItem) {
    bidIdO = res.id
    // Lấy template hồ sơ thương mại của gói thầu
    res.lstBidTrade = await this.bidTradeRepo.getTrade(user, res.id)
    // Lấy hồ sơ thương mại của các NCC tham gia
    res.lstBidSupplier = await this.bidSupplierRepo.find({
      where: { bidId: res.id, statusFile: enumData.BidSupplierFileStatus.HopLe.code },
      relations: { supplier: true, bidSupplierTradeValue: true },
    })
    if (res.lstBidSupplier.length === 0) return []

    //Lấy tiêu chí tính điểm (cấp 1 & kiểu number hoặc list)
    var lstBidTradeCal = res.lstBidTrade.filter((c) => c.parentId === null && setType.has(c.type))

    // Tính cho từng NCC tham gia thầu
    for (const bidSupplier of res.lstBidSupplier) {
      bidSupplier.supplierCode = bidSupplier.__supplier__.code
      bidSupplier.supplierName = bidSupplier.__supplier__.name

      bidSupplier.statusFileName = dicStatusFile[bidSupplier.statusFile]
      bidSupplier.statusTechName = dicStatusTrade[bidSupplier.statusTech]

      const lockStatus = await this.rateDataRepository.findOne({
        where: {
          entityName: SupplierEntity.name,
          supplierId: bidSupplier.id,
          bidId: bidId,
          type: enumData.RATE_TYPE.TRADE.code,
          employeeId: user.employeeId,
        },
      })
      bidSupplier.lock = false
      bidSupplier.rating = false
      bidSupplier.lockName = 'Chưa đánh giá'
      if (lockStatus) {
        bidSupplier.rating = true
        bidSupplier.lock = lockStatus.lock
        bidSupplier.lockName = bidSupplier.lock ? 'Đã khóa' : 'Chưa khóa'
      }

      if (bidSupplier.scoreTrade === 0) {
        const lstBidSupplierTradeValue = bidSupplier.__bidSupplierTradeValue__ || []
        delete bidSupplier.__bidSupplierTradeValue__
        let scoreTrade = 0
        // với từng tiêu chí cần tính điểm
        for (const bidTrade of lstBidTradeCal) {
          const itemChilds = bidTrade.__childs__ || []
          if (itemChilds.length > 0) {
            let scoreTradeChild = 0
            for (const itemChild of itemChilds) {
              const objValueChild = lstBidSupplierTradeValue.find((c) => c.bidTradeId === itemChild.id)
              if (objValueChild) {
                const tem = this.calScoreTradeItem(itemChild, objValueChild.value)
                objValueChild.score = tem
                scoreTradeChild += tem
                // Lưu điểm
                await bidSupplierTradeValueRepo.update(objValueChild.id, { score: tem, updatedBy: user.id })
              }
            }
            const temp = (bidTrade.percent * scoreTradeChild) / 100
            scoreTrade += temp
            // Lưu điểm
            await bidSupplierTradeValueRepo.update({ bidTradeId: bidTrade.id, bidSupplierId: bidSupplier.id }, { score: temp, updatedBy: user.id })
          } else {
            const objValue = lstBidSupplierTradeValue.find((c) => c.bidTradeId === bidTrade.id)
            if (objValue) {
              const temp = this.calScoreTradeItem(bidTrade, objValue.value)
              objValue.score = temp
              scoreTrade += temp
              // Lưu điểm
              await bidSupplierTradeValueRepo.update({ bidTradeId: bidTrade.id, bidSupplierId: bidSupplier.id }, { score: temp, updatedBy: user.id })
            }
          }
        }
        if (isNaN(scoreTrade)) {
          bidSupplier.scoreTrade = 0
        } else if (!isFinite(scoreTrade)) {
          bidSupplier.scoreTrade = 0
        } else bidSupplier.scoreTrade = scoreTrade
        // Lưu điểm
        await this.bidSupplierRepo.update(bidSupplier.id, { scoreTrade: bidSupplier.scoreTrade, updatedBy: user.id })
      }

      bidSupplier.rankABCD = coreHelper.rankABCD(bidSupplier.scoreTrade)
    }

    // sort
    res.lstBidSupplier.sort((a, b) => b.scoreTrade - a.scoreTrade)
    // }
    res.lstEmployeeAccess = lstEmployeeAccess

    for (const employee of res.lstEmployeeAccess) {
      res.canSend = true
      /* tìm xem nhân viên đó có đánh giá chưa */
      const employeeAction: any = await this.bidEmployeeAccessRepo.findOne({
        where: { bidId: bidId, type: enumData.BidRuleType.EmpOther.code, employeeId: employee.employeeId, isScore: true },
      })
      // Tìm ra số lượng đánh giá của nhân viên đó trong rateValue
      const lstScore = await this.rateDataRepository.find({
        where: { bidId: bidId, employeeId: employee.employeeId, entityName: SupplierEntity.name, lock: true, type: enumData.RATE_TYPE.TRADE.code },
      })
      const lstBidSupplier = await this.bidSupplierRepo.find({
        // where: { bidId: item.id, statusFile: enumData.BidSupplierFileStatus.HopLe.code, },
        where: { bidId: bidIdO, statusFile: enumData.BidSupplierFileStatus.HopLe.code },
      })
      if (lstScore.length > 0) {
        if (lstScore.length >= lstBidSupplier.length) {
          employee.status = 'Đã đánh giá'
        } else {
          employee.status = 'Đang đánh giá'
          res.canSend = false
        }
      } else {
        employee.status = 'Chưa đánh giá '
        res.canSend = false
      }
      /* tìm ra số lượng ncc cần đánh giá */
    }

    const employeeAction: any = await this.bidEmployeeAccessRepo.findOne({
      where: { bidId: bidId, type: enumData.BidRuleType.EmpOther.code, employeeId: user.employeeId, isScore: true },
      relations: { bidEmployeeRate: true },
    })
    if (employeeAction) {
      res.isShowBtn = true
    } else {
      res.isShowBtn = false
    }
    if (employeeAction && employeeAction.scoreManualTrade > 0) {
      res.isShowBtn = false
    }

    res.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
      lsType: [enumData.FlowCode.EVALUATE_RESULT_TRADE.code],
      entityName: BidEntity.name,
    })

    res.canApprove = res.objPermissionApprove[enumData.FlowCode.EVALUATE_RESULT_TRADE.code]

    /* Tìm ra danh sách cấp duyệt và comment và người duyệt */
    res.haveProgress = false
    res.approvalProgress = await this.flowService.getListApproveDetail(user, {
      targetId: res.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.EVALUATE_RESULT_TRADE.code,
    })
    res.showComment = false
    /* nếu như nhân viên hiện tại chưa duyệt và nằm trong luồng duyệt thì toggle bật showComment = true */
    for (const item of res.approvalProgress) {
      if (item.approveType == user.orgPositionId && !item.approved) {
        res.showComment = true
        break
      }
    }
    if (res.approvalProgress.length > 0) res.haveProgress = true

    return res
  }

  /**
   * Lấy danh sách bidTrade và điểm cao nhất tương ứng
   * @param data.bidId - Item trong gói thầu
   * @param data.bidSupplierId - NCC tham gia Item
   */
  async loadBestTradeValue(user: UserDto, data: { bidId: string; bidSupplierId: string }) {
    const lstBidSupplier = await this.bidSupplierRepo.find({ where: { bidId: data.bidId }, select: { id: true } })
    if (lstBidSupplier.length == 0) throw new Error('Không có hồ sơ thầu hợp lệ')
    const listBidSupplierId = lstBidSupplier.map((p) => p.id)

    // Danh sách bidTrade
    const bidTrades: any = await this.bidTradeRepo.find({
      where: { bidId: data.bidId, parentId: IsNull(), isDeleted: false },
      relations: { bid: true },
    })

    const isPrivate = bidTrades[0].__bid__.isPersonalScoreVisible
    let lsEmployeeRating = []
    // if (!isPrivate) {
    if (true) {
      /* nếu như không hiện cá nhân thì lấy ra danh sách
      Load ra danh sách nhân viên đánh đánh giá  
       */
      lsEmployeeRating = await this.bidEmployeeAccessRepo.find({
        // where: { bidId: data.bidId, type: enumData.BidRuleType.EmpTech.code, isScore: true, employeeId: Not(user.employeeId) },
        where: { bidId: data.bidId, type: enumData.BidRuleType.EmpTech.code, isScore: true },

        relations: { employee: true },
      })
      for (const item of lsEmployeeRating) {
        item.employeeName = item?.__employee__?.name
        item.employeeId = item?.__employee__?.id
      }
      /* tìm ra danh sách */
    }

    // Danh sách tất cả giá trị các NCC đã nộp
    const bidTradeValue = await this.repo.manager.getRepository(BidSupplierTradeValueEntity).find({
      where: { bidSupplierId: In(listBidSupplierId) },
      select: {
        id: true,
        bidTradeId: true,
        bidSupplierId: true,
        value: true,
        score: true,
      },
    })

    let result = []
    const length = bidTrades.length

    const setType = new Set()
    setType.add(enumData.DataType.Number.code)
    setType.add(enumData.DataType.List.code)

    // Lọc qua danh sách bidTrade
    for (let i = 0; i < length; i++) {
      let itemResult: any = {}
      const item = bidTrades[i]
      itemResult.id = item.id
      itemResult.name = item.name
      itemResult.percent = item.percent
      itemResult.percentRule = item.percentRule
      itemResult.type = item.type
      itemResult.sort = item.sort

      // Giá trị mà NCC tương ứng đã nộp hồ sơ
      const supplierTraveValue = bidTradeValue.find((p) => p.bidTradeId === item.id && p.bidSupplierId === data.bidSupplierId)

      itemResult.value = supplierTraveValue?.value

      // Nếu kiểu list thì lọc để lấy name
      if (itemResult.type === enumData.DataType.List.code) {
        const listValue = await item.bidTradeListDetails
        const find = listValue.find((p) => p.id === itemResult.value)
        itemResult.value = find?.name
      }
      itemResult.score = supplierTraveValue?.score

      // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
      const listBidSupplierTraveValue = bidTradeValue.filter((p) => p.bidTradeId === item.id)

      // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
      if (listBidSupplierTraveValue.length > 0 && setType.has(itemResult.type)) {
        // Lấy giá trị có điểm tốt nhất
        const bestSupplier = this.getMaxOfArrayObj(listBidSupplierTraveValue)

        itemResult.bestScore = bestSupplier?.score
        itemResult.bestSupplierName = (await (await bestSupplier.bidSupplier).supplier).name
        itemResult.bestValue = bestSupplier?.value

        // Nếu kiểu list thì lọc qua để lấy name
        if (itemResult.type === enumData.DataType.List.code) {
          const listValue = await (await bestSupplier.bidTrade).bidTradeListDetails
          const find = listValue.find((p) => p.id === bestSupplier.value)
          itemResult.bestValue = find?.name
        }
        if (supplierTraveValue) {
          itemResult.rank = this.getCurrentRank(listBidSupplierTraveValue, supplierTraveValue?.bidSupplierId)
        }
      }

      // Lọc qua danh sách con
      itemResult.childs = []
      const itemChilds = await item.childs
      if (itemChilds?.length > 0) {
        for (const itemC of itemChilds) {
          let itemResultC: any = {}
          itemResultC.id = itemC.id
          itemResultC.name = itemC.name
          itemResultC.percent = itemC.percent
          itemResultC.percentRule = itemC.percentRule
          itemResultC.type = itemC.type
          itemResultC.sort = itemC.sort

          // Lấy giá trị mà NCC tương ứng đã nôpk
          const supplierTraveValueC = await bidTradeValue.find((p) => p.bidTradeId === itemC.id && p.bidSupplierId === data.bidSupplierId)

          itemResultC.value = supplierTraveValueC?.value

          // Nếu kiểu list thì lọc qua danh sách để lấy name
          if (itemResultC.type === enumData.DataType.List.code) {
            const listValueC = await itemC.bidTradeListDetails
            const findC = listValueC.find((p) => p.id === itemResultC.value)
            itemResultC.value = findC?.name
          }
          itemResultC.score = supplierTraveValueC?.score

          // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
          const listBidSupplierTradeValueC = await bidTradeValue.filter((p) => p.bidTradeId === itemC.id)
          // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
          if (listBidSupplierTradeValueC.length > 0 && setType.has(itemResultC.type)) {
            // Lấy giá trị có điểm tốt nhất
            const bestSupplierC = this.getMaxOfArrayObj(listBidSupplierTradeValueC)

            itemResultC.bestScore = bestSupplierC?.score
            itemResultC.bestSupplierName = (await (await bestSupplierC.bidSupplier).supplier).name
            itemResultC.bestValue = bestSupplierC?.value
            // Nếu kiểu list thì lọc qua để lấy name
            if (itemResultC.type === enumData.DataType.List.code) {
              const listValueC = await (await bestSupplierC.bidTrade).bidTradeListDetails
              const findC = listValueC.find((p) => p.id === bestSupplierC.value)
              itemResultC.bestValue = findC?.name
            }
            if (supplierTraveValueC) {
              itemResultC.rank = this.getCurrentRank(listBidSupplierTradeValueC, supplierTraveValueC?.bidSupplierId)
            }
          }
          itemResult.childs.push(itemResultC)
        }
      }
      itemResult.childs = itemResult.childs.sort((a, b) => a.sort - b.sort)

      result.push(itemResult)
    }
    result = result.sort((a, b) => a.sort - b.sort)

    const lstResultId = result.map((c) => c.id)
    const lstValue = await this.rateDataRepository.find({
      where: { entityName: BidTechEntity.name, employeeId: user.employeeId, targetId: In(lstResultId), supplierId: data.bidSupplierId },
    })

    const dicScore: any = {}
    for (const item of lstValue) {
      dicScore[item.targetId] = item.rateNumber
    }
    for (const item of result) {
      if (!isPrivate) {
        item.lstDataEmp = lsEmployeeRating
        let idx = 0
        for (const data of item.lstDataEmp) {
          const score = await this.rateDataRepository.findOne({ where: { employeeId: data.employeeId, bidId: data.bidId, targetId: item.id } })
          item[`score${idx}`] = score?.rateNumber || 0
          idx++
        }
        // score0 = 70
      }
      item.scoreRow = dicScore[item.id] || 0
    }

    // return result

    return result
  }

  /** Tính điểm đánh giá điều kiện thương mại */
  calScoreTradeItem(item: any, value: string) {
    let score = 0
    if (item.type === enumData.DataType.Number.code && value && value.trim() != '') {
      let temp = 0
      const x = +value
      // Tính theo chiều thuận
      if (item.isCalUp) {
        if (x >= item.percentRule) {
          temp = item.percent
        } else {
          temp = (x * item.percent) / item.percentRule // giá trị * tỉ trọng / điều kiện b
        }
      }
      // Tính theo chiều nghịch
      else {
        if (x <= item.percentRule) {
          temp = item.percent
        } else if (x >= item.percentDownRule) {
          temp = 0
        } else {
          temp = ((item.percentDownRule - x) * item.percent) / (item.percentDownRule - item.percentRule)
        }
      }

      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    } else if (item.type === enumData.DataType.List.code) {
      const itemChosen = item.__bidTradeListDetails__.find((p) => p.id === value)
      const tem = itemChosen ? itemChosen.value : 0
      const temp = (tem * item.percent) / 100
      if (isNaN(temp)) {
        score += 0
      } else if (!isFinite(temp)) {
        score += 0
      } else {
        score += temp
      }
    }

    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  /** Tạo đánh giá điều kiện thương mại cho gói thầu */
  async createTradeRate(user: UserDto, data: { id: string; listItem: any[] }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionCreateBidTradeRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    /* tìm ra danh sách nhân viên đánh giá kỹ thuật*/
    const lstEmployee = await this.bidEmployeeAccessRepo.find({ where: { bidId: bid.id, type: enumData.BidRuleType.EmpOther.code } })
    /* tìm ra danh sách nhân viên đánh giá kỹ thuật hiện tại*/

    let status = bid.status
    if (
      (bid.statusRateTech === enumData.BidTechRateStatus.DaTao.code || bid.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) &&
      (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code || bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code)
    ) {
      status = enumData.BidStatus.DangDuyetDanhGia.code
    }
    await this.repo.update(data.id, {
      statusRateTrade: enumData.BidTradeRateStatus.DaTao.code,
      status,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DanhGiaThuongMai.code
    bidHistory.save()

    if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code || bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code) {
      this.emailService.GuiMpoLeadDuyetDanhGiaGia(data.id)
    }

    // tạo quyền duyệt kết quả đánh giá điều kiện thương mại
    let flowType: string
    flowType = enumData.FlowCode.EVALUATE_RESULT_TRADE.code

    const isHave = await this.permissionApproveRepository.findOne({ where: { targetId: data.id, type: flowType, isDeleted: false } })
    if (!isHave)
      await this.flowService.setRoleRule(user, {
        targetId: data.id,
        target: data,

        entityName: BidEntity.name,
        flowType: flowType,
        companyId: user.orgCompanyId,
        // departmentId: user?.departmentId,
      })

    return { message: 'Gửi yêu cầu phê duyệt kết quả đánh giá điều kiện thương mại thành công.' }
  }
  /** Duyệt đánh giá chào giá và điều kiện thương mại */
  async approveTradeRate(user: UserDto, data: any) {
    // const approveStatus = await this.flowService.approveRule(user, {
    //   targetId: data.id,
    //   entityName: BidEntity.name,
    //   type: enumData.FlowCode.EVALUATE_RESULT_TRADE.code,
    //   comment: data.comment || '',
    // })

    // if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
    //   return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    // } else {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    // const objPermission = await this.checkPermissionApproveBidPriceRate(user, data.id)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    // for (const item of data.listItem) {
    for (const bidSupplierItem of data.lstBidSupplier) {
      await this.bidSupplierRepo.update(bidSupplierItem.id, {
        statusTrade: enumData.BidSupplierTradeStatus.DaDuyet.code,
        statusPrice: enumData.BidSupplierPriceStatus.DaDuyet.code,
        noteMPOLeader: bidSupplierItem.noteMPOLeader,
        updatedBy: user.id,
      })
    }
    // }

    let status = bid.status
    if (bid.statusRateTech === enumData.BidTechRateStatus.DaDuyet.code) {
      status = enumData.BidStatus.HoanTatDanhGia.code
    }
    await this.repo.update(data.id, {
      statusRateTrade: enumData.BidTradeRateStatus.DaDuyet.code,
      statusRatePrice: enumData.BidPriceRateStatus.DaDuyet.code,
      status,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DuyetDanhGiaThuongMai.code
    bidHistory.save()

    this.emailService.ThongBaoDuyetDanhGiaGia(data.id)

    return { message: 'Duyệt đánh giá chào giá và điều kiện thương mại thành công.' }
    // }
  }
  /** Từ chối đánh giá chào giá và điều kiện thương mại */
  async rejectTradeRate(user: UserDto, data: { id: string; listItem: any[]; comment?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionApproveBidPriceRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    for (const item of data.listItem) {
      for (const bidSupplierItem of item.lstBidSupplier) {
        await this.bidSupplierRepo.update(bidSupplierItem.id, {
          statusTrade: enumData.BidSupplierTradeStatus.DangDanhGia.code,
          statusPrice: enumData.BidSupplierPriceStatus.DangDanhGia.code,
          noteMPOLeader: bidSupplierItem.noteMPOLeader,
          updatedBy: user.id,
        })
      }
    }

    await this.flowService.rejectRule(user, {
      targetId: data.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.EVALUATE_RESULT_TRADE.code,
      comment: data.comment || '',
    })

    await this.repo.update(data.id, {
      statusRateTrade: enumData.BidTradeRateStatus.TuChoi.code,
      statusRatePrice: enumData.BidPriceRateStatus.TuChoi.code,
      status: enumData.BidStatus.DangDanhGia.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TuChoiDanhGiaThuongMai.code
    bidHistory.save()

    // gửi email bộ phận thương mại
    this.emailService.GuiMpoTuChoiDanhGiaGia(data.id)

    return { message: 'Từ chối đánh giá chào giá và điều kiện thương mại thành công.' }
  }
  //#endregion

  //#region bidRatePrice

  /** Check quyền tạo đánh giá giá cho gói thầu */
  async checkPermissionCreateBidPriceRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid) {
      if (bid.statusRatePrice === enumData.BidPriceRateStatus.DangTao.code || bid.statusRatePrice === enumData.BidPriceRateStatus.TuChoi.code) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bidId)
        if (!result) {
          message = 'Bạn không có quyền đánh giá giá cho gói thầu.'
        }
      } else {
        result = false
        message = 'Gói thầu đã được đánh giá giá.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Lấy ds NCC tham gia thầu và tính điểm */
  async loadPriceRate(user: UserDto, bidId: string, isMobile?: boolean) {
    const bidSupplierPriceValueRepo = this.repo.manager.getRepository(BidSupplierPriceValueEntity)

    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: {
        bidId: bidId,
        companyId: user.companyId,
        isDeleted: false,
        type: enumData.BidRuleType.EmpOther.code,
      },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        isScore: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res = await this.repo.getBid2(user, bidId)
    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    const dicStatusPrice: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierPriceStatus)
      lstStatus.forEach((c) => (dicStatusPrice[c.code] = c.name))
    }
    let bidIdO = null
    res.listItem = res.__bidEx__
    for (const item of res.listItem) {
      bidIdO = item.bidId
      // Lấy template bảng chào giá của Item
      item.lstBidPrice = await this.bidPriceRepo.getPrice(user, item.id)
      // Lấy hồ sơ giá của các NCC tham gia
      item.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: item.bidId, statusFile: enumData.BidSupplierFileStatus.HopLe.code },
        relations: { supplier: true, bidSupplierPriceValue: true },
      })
      if (item.lstBidSupplier.length === 0) continue

      const lstBidSupplierId = item.lstBidSupplier.map((p) => p.id)

      const scoreDLC = item.scoreDLC || 0

      const lstBidSupplierPriceValue = await bidSupplierPriceValueRepo.find({
        // where: { bidSupplierId: In(lstBidSupplierId), },
        where: { bidSupplierId: In(lstBidSupplierId) },
      })
      // Lọc qua danh sách các hạng mục - tính điểm cho từng hạng mục
      for (const itemBidPrice of item.lstBidPrice) {
        const supplierValue = lstBidSupplierPriceValue.filter((p) => p.bidPriceId === itemBidPrice.id).sort((a, b) => +a.value - +b.value)

        if (supplierValue.length > 0) {
          // Tìm ra giá trị nhỏ nhất theo hạng mục này
          const minValue = this.getMinOfArrayObj(supplierValue)

          const supplierValueLength = supplierValue.length

          const listSupplierScore = supplierValue.map((p) => +p.value)

          // Từ danh sách điểm tạm sẽ tính được độ lệch chuẩn
          const dlc = coreHelper.calDLC(listSupplierScore)

          // Lọc qua danh sach điểm này lại và tính điểm thiệt dựa vào độ lệch chuẩn ở trên
          for (let i = 0; i < supplierValueLength; i++) {
            const itemBidSupplierValue = supplierValue[i]
            let score = scoreDLC
            if (i !== 0 && dlc > 0) {
              score = scoreDLC - (+itemBidSupplierValue.value - +minValue.value) / dlc
            }
            itemBidSupplierValue.score = score
            // Lưu điểm
            await bidSupplierPriceValueRepo.update(itemBidSupplierValue.id, { score, updatedBy: user.id })
          }
        }
      }

      // Lọc qua danh sách NCC tính tổng điểm các hạng mục -> điểm tạm
      for (const bidSupplier of item.lstBidSupplier) {
        bidSupplier.supplierCode = bidSupplier.__supplier__.code
        bidSupplier.supplierName = bidSupplier.__supplier__.name

        bidSupplier.statusFileName = dicStatusFile[bidSupplier.statusFile]
        bidSupplier.statusPriceName = dicStatusPrice[bidSupplier.statusPrice]

        const lockStatus = await this.rateDataRepository.findOne({
          where: {
            entityName: SupplierEntity.name,
            supplierId: bidSupplier.id,
            bidId: bidId,
            type: enumData.RATE_TYPE.PRICE.code,
            employeeId: user.employeeId,
          },
        })
        bidSupplier.lock = false
        bidSupplier.rating = false
        bidSupplier.lockName = 'Chưa đánh giá'
        if (lockStatus) {
          bidSupplier.rating = true
          bidSupplier.lock = lockStatus.lock
          bidSupplier.lockName = bidSupplier.lock ? 'Đã khóa' : 'Chưa khóa'
        }

        const lstPriceValue = lstBidSupplierPriceValue.filter((c) => c.bidSupplierId == bidSupplier.id)
        let scorePrice = 0
        for (const priceValue of lstPriceValue) {
          scorePrice += priceValue.score
        }
        bidSupplier.scorePrice = scorePrice

        bidSupplier.rankABCD = coreHelper.rankABCD(bidSupplier.scorePrice)
      }

      const listBidSupplierScorePrice = item.lstBidSupplier.map((p) => p.scorePrice)

      // Từ danh sách điểm tạm sẽ tính được độ lệch chuẩn
      const dlcBidSupplier = coreHelper.calDLC(listBidSupplierScorePrice)
      const maxScorePrice = Math.max(...listBidSupplierScorePrice)

      item.lstBidSupplier.sort((a, b) => b.scorePrice - a.scorePrice)
      const length = item.lstBidSupplier.length

      // Lọc qua danh sách NCC tính lại điểm cho các NCC
      for (let i = 0; i < length; i++) {
        const itemBidSupplier = item.lstBidSupplier[i]

        let scorePrice = scoreDLC
        if (i !== 0 && dlcBidSupplier > 0) {
          scorePrice = scoreDLC - (maxScorePrice - itemBidSupplier.scorePrice) / dlcBidSupplier
        }

        itemBidSupplier.scorePrice = scorePrice

        // Lưu điểm
        await this.bidSupplierRepo.update(itemBidSupplier.id, {
          scorePrice: itemBidSupplier.scorePrice,
          updatedBy: user.id,
        })
      }
    }
    res.lstEmployeeAccess = lstEmployeeAccess
    res.isShowBtn = true
    res.canSend = true

    for (const employee of res.lstEmployeeAccess) {
      /* tìm xem nhân viên đó có đánh giá chưa */
      const employeeAction: any = await this.bidEmployeeAccessRepo.findOne({
        where: { bidId: bidId, type: enumData.BidRuleType.EmpTech.code, employeeId: employee.employeeId, isScore: true },
      })
      // Tìm ra số lượng đánh giá của nhân viên đó trong rateValue
      const lstScore = await this.rateDataRepository.find({
        where: { bidId: bidId, employeeId: employee.employeeId, entityName: SupplierEntity.name, lock: true, type: enumData.RATE_TYPE.PRICE.code },
      })
      const lstBidSupplier = await this.bidSupplierRepo.find({
        // where: { bidId: item.id, statusFile: enumData.BidSupplierFileStatus.HopLe.code, },
        where: { bidId: bidIdO, statusFile: enumData.BidSupplierFileStatus.HopLe.code },
      })
      if (lstScore.length > 0) {
        if (lstScore.length >= lstBidSupplier.length) {
          employee.status = 'Đã đánh giá'
        } else {
          employee.status = 'Đang đánh giá'
          res.canSend = false
        }
      } else {
        employee.status = 'Chưa đánh giá '
        res.canSend = false
      }
      /* tìm ra số lượng ncc cần đánh giá */
    }

    const employeeAction: any = await this.bidEmployeeAccessRepo.findOne({
      where: { bidId: bidId, type: enumData.BidRuleType.EmpTech.code, employeeId: user.employeeId, isScore: true },
      relations: { bidEmployeeRate: true },
    })
    if (employeeAction) {
      res.isShowBtn = true
    } else {
      res.isShowBtn = false
    }
    if (employeeAction && employeeAction.scoreManualPrice > 0) {
      res.isShowBtn = false
    }

    return res
  }

  /** Lấy danh sách sách bidPrice và điểm cao nhất tương ứng */
  async loadBestPriceValue(user: UserDto, data: { bidId: string; bidSupplierId: string }) {
    const objBidSupplier = await this.bidSupplierRepo.findOne({
      where: { id: data.bidSupplierId, bidId: data.bidId },
      relations: { supplier: true, bidSupplierPriceValue: true, bidSupplierPriceColValue: true },
    })
    if (!objBidSupplier) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bidSupplierPriceColValue = await objBidSupplier.bidSupplierPriceColValue
    const bidSupplier = await this.bidSupplierRepo.find({ where: { bidId: data.bidId }, select: { id: true } })
    const listBidSupplierId = bidSupplier.map((p) => p.id)

    const bidPrices: any = await this.bidPriceRepo.getPrice(user, data.bidId)

    const isPrivate = bidPrices[0]?.__bid__?.isPersonalScoreVisible
    let lsEmployeeRating = []
    // if (!isPrivate) {
    if (true) {
      /* nếu như không hiện cá nhân thì lấy ra danh sách
      Load ra danh sách nhân viên đánh đánh giá  
       */
      lsEmployeeRating = await this.bidEmployeeAccessRepo.find({
        // where: { bidId: data.bidId, type: enumData.BidRuleType.EmpTech.code, isScore: true, employeeId: Not(user.employeeId) },
        where: { bidId: data.bidId, type: enumData.BidRuleType.EmpTech.code, isScore: true },

        relations: { employee: true },
      })
      for (const item of lsEmployeeRating) {
        item.employeeName = item?.__employee__?.name
        item.employeeId = item?.__employee__?.id
      }
      /* tìm ra danh sách */
    }

    const bidPriceCols = await this.bidPriceColRepo.getBidPriceColAll(user, data.bidId)

    // Danh sách tất cả giá trị các NCC đã nộp
    const bidPriceValue = await this.repo.manager.getRepository(BidSupplierPriceValueEntity).find({
      where: { bidSupplierId: In(listBidSupplierId) },
    })

    // function get data dynamic col by row
    const getDataRow = (row: any) => {
      for (const col of bidPriceCols) {
        row[col.id] = ''
        if (col.colType === enumData.ColType.MPO.code) {
          if (row.__bidPriceColValue__?.length > 0) {
            const cell = row.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
            if (cell) row[col.id] = cell.value
          }
        }
        if (col.colType === enumData.ColType.Supplier.code) {
          const cell = bidSupplierPriceColValue.find((c) => c.bidPriceColId === col.id && c.bidPriceId === row.id)
          if (cell) row[col.id] = cell.value
        }
      }
    }

    // Lọc qua danh sách bidPrice
    for (let i = 0, length1 = bidPrices.length; i < length1; i++) {
      const item: any = bidPrices[i]
      getDataRow(item)

      // Giá trị mà NCC tương ứng đã nộp hồ sơ
      const supplierPriceValue = bidPriceValue.find((p) => p.bidPriceId === item.id && p.bidSupplierId === data.bidSupplierId)

      item.value = supplierPriceValue?.value
      item.score = supplierPriceValue?.score

      // Danh sách giá trị tất cả các NCC nộp theo tiêu chí này
      const listBidSupplierPriceValue = bidPriceValue.filter((p) => p.bidPriceId === item.id)

      // Nêu có và là loại list hoặc number thì bắt đầu tính điểm max và rank
      if (listBidSupplierPriceValue.length > 0) {
        // Lấy giá trị có điểm tốt nhất
        const bestSupplier = this.getMinOfArrayObj(listBidSupplierPriceValue)

        item.bestScore = bestSupplier?.score
        item.bestSupplierName = (await (await bestSupplier.bidSupplier).supplier).name
        item.bestValue = bestSupplier?.value

        if (supplierPriceValue) {
          item.rank = this.getCurrentRank(listBidSupplierPriceValue, supplierPriceValue?.bidSupplierId)
        }
      }

      item.__childs__ = item.__childs__ || []
      for (let i2 = 0, length2 = item.__childs__.length; i2 < length2; i2++) {
        const item2: any = item.__childs__[i2]
        getDataRow(item2)

        item2.__childs__ = item2.__childs__ || []
        for (let i3 = 0, length3 = item2.__childs__.length; i3 < length3; i3++) {
          const item3: any = item2.__childs__[i3]
          getDataRow(item3)
        }
      }
    }

    const lstCustomPrice = await this.repo.manager
      .getRepository(BidSupplierCustomPriceValueEntity)
      .find({ where: { bidSupplierId: data.bidSupplierId }, order: { sort: 'ASC', createdAt: 'ASC' } })

    /* map điểm thành điểm của nhân viên  */
    const lstResultId = bidPrices.map((c) => c.id)
    const lstValue = await this.rateDataRepository.find({
      where: { entityName: BidTechEntity.name, employeeId: user.employeeId, targetId: In(lstResultId), supplierId: data.bidSupplierId },
    })

    const dicScore: any = {}
    for (const item of lstValue) {
      dicScore[item.targetId] = item.rateNumber
    }
    for (const item of bidPrices) {
      if (!isPrivate) {
        item.lstDataEmp = lsEmployeeRating
        let idx = 0
        for (const data of item.lstDataEmp) {
          const score = await this.rateDataRepository.findOne({ where: { employeeId: data.employeeId, bidId: data.bidId, targetId: item.id } })
          item[`score${idx}`] = score?.rateNumber || 0
          idx++
        }
        // score0 = 70
      }
      item.scoreRow = dicScore[item.id] || 0
    }

    return [bidPrices, bidPriceCols, bidSupplierPriceColValue, objBidSupplier, lstCustomPrice]
  }

  /** Tạo đánh giá giá cho gói thầu */
  async createPriceRate(user: UserDto, data: { id: string; listItem: any[] }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionCreateBidPriceRate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: data.id } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    let status = bid.status

    await this.repo.update(data.id, {
      statusRatePrice: enumData.BidPriceRateStatus.DaTao.code,
      status,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.DanhGiaGia.code
    bidHistory.save()

    if (bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code || bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code) {
      this.emailService.GuiMpoLeadDuyetDanhGiaGia(data.id)
    }

    return { message: 'Tạo đánh giá bảng chào giá thành công.' }
  }

  /** Check quyền duyệt đánh giá điều kiện thương mại & giá cho gói thầu */
  async checkPermissionApproveBidPriceRate(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid) {
      if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaDuyet.code || bid.statusRateTrade === enumData.BidTradeRateStatus.DaDuyet.code) {
        message = 'Gói thầu đã được xét duyệt đánh giá bảng chào giá, cơ cấu giá và điều kiện thương mại.'
      } else if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code && bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code) {
        const flagPermission = await this.bidEmployeeAccessRepo.isMPOLeader(user, bidId)
        if (flagPermission) {
          result = true
        } else {
          message = 'Bạn không có quyền xét duyệt đánh giá bảng chào giá, cơ cấu giá và điều kiện thương mại.'
        }
      } else if (bid.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code) {
        message = 'Chưa có yêu cầu xét duyệt đánh giá điều kiện thương mại.'
      } else if (bid.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code) {
        message = 'Chưa có yêu cầu xét duyệt đánh giá bảng chào giá, cơ cấu giá.'
      }
    }

    return { hasPermission: result, message }
  }

  //#endregion

  getMaxOfArrayObj(data: any[]) {
    const maxL = data.reduce((max, game) => {
      const first = max.score
      const second = game.score
      if (first === second) {
        const firstValue = +max.value
        const secondValue = +game.value
        return firstValue > secondValue ? max : game
      } else return first > second ? max : game
    })

    return maxL
  }

  getMinOfArrayObj(data: any[]) {
    const minL = data.reduce((min, game) => {
      const firstValue = +min.value
      const secondValue = +game.value
      return firstValue < secondValue ? min : game
    })

    return minL
  }

  getCurrentRank(data: any[], bidSupplierId: string) {
    let rank = 0
    if (data.length > 0) {
      const listFilter = data.sort((a, b) => b.score - a.score)
      const index = listFilter.findIndex((p) => p.bidSupplierId === bidSupplierId)
      if (index) rank = index
    }

    return `${rank + 1} /${data.length}`
  }
  //#region Truy vấn thông tin gói thầu

  /** Danh sách gói thầu đã hoàn tất */
  async resultPagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    let whereCon: any = {
      parentId: IsNull(),
      status: enumData.BidStatus.HoanTat.code,
      employeeAccess: { employeeId: user.employeeId },
      companyId: user.companyId,
      isDeleted: false,
    }
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.BidInfo.code)
    if (dataRs.type === enumData.RoleData.All.code) {
      /* do nothing */
    }
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where.status?.length > 0) whereCon.status = In(data.where.status)
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}") AND DATE("${moment(data.where.dateTo).format(
            'YYYY-MM-DD',
          )}")`,
      )
    } else if (data.where.dateFrom) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) >= DATE("${moment(data.where.dateFrom).format('YYYY-MM-DD')}")`)
    } else if (data.where.dateTo) {
      whereCon.publicDate = Raw((alias) => `DATE(${alias}) <= DATE("${moment(data.where.dateTo).format('YYYY-MM-DD')}")`)
    }

    if (data.where.name) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.where.name}%`) },
        { ...whereCon, name: Like(`%${data.where.name}%`) },
      ]
    }
    if (data.where.serviceId) whereCon.childs = { serviceId: data.where.serviceId }

    const res: any[] = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      select: {
        id: true,
        code: true,
        name: true,
        status: true,
        createdAt: true,
      },
    })
    if (res[1] == 0) return res
    const lstId = res[0].map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), isDeleted: false },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    for (const item of res[0]) {
      const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)

      const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techName = objTech?.__employee__?.name || ''
      const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.__employee__?.name || ''

      const lstAccessUser = lstAccess.filter((c) => c.employeeId === user.employeeId)
      item.isMPO = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPO.code)
      if (item.isMemberScore) item.isMPO = lstAccessUser.some((c) => c.type === enumData.BidRuleType.EmpOther.code)
      item.isMPOLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPOLeader.code)
      item.isTech = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Tech.code)
      if (item.isMemberScore) item.isTech = lstAccessUser.some((c) => c.type === enumData.BidRuleType.EmpTech.code)
      item.isTechLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.TechLeader.code)
      item.isMember = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Memmber.code)

      item.statusName = dicStatus[item.status]
    }

    return res
  }

  //#endregion
}
