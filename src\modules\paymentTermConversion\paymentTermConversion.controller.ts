import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../common/guards'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

import { FilterOneDto } from '../../dto/filterOne.dto'
import { PaymentTermConversionCreateDto } from './dto'
import { PaymentTermConversionService } from './paymentTermConversion.service'

@ApiBearerAuth()
@ApiTags('PaymentTermConversion')
@UseGuards(JwtAuthGuard)
@Controller('payment_term_conversion')
export class PaymentTermConversionController {
  constructor(private readonly service: PaymentTermConversionService) {}

  @ApiOperation({ summary: 'Lấy danh sách incoterm' })
  @Post('find')
  public async find() {
    return await this.service.find()
  }

  @ApiOperation({ summary: 'Danh sách incoterm phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới một incoterm với dữ liệu được cung cấp.' })
  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: PaymentTermConversionCreateDto[]) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái kích hoạt của incoterm.' })
  @Post('update_active')
  async updateActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Load data select' })
  @Post('load_data_select')
  async loadDataSelect(@CurrentUser() user: UserDto) {
    return await this.service.loadDataSelect(user)
  }
}
