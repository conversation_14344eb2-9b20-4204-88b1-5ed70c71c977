import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ContractAppendixEntity } from './contractAppendix.entity'
import { PaymentMethodEntity } from './paymentMethod.entity'

@Entity({ name: 'contract_appendix_payment_progress' })
export class ContractAppendixPaymentProgressEntity extends BaseEntity {
  /** Tên tiến độ */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** % tiến độ */
  @Column({
    default: 0,
  })
  percent: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  /** Thời gian thanh toán */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  time: Date

  /**thời gian thanh toán mới */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  newTime: Date

  /** contract appendix*/
  @Column({
    type: 'varchar',
    nullable: false,
  })
  contractAppendixId: string
  @ManyToOne(() => ContractAppendixEntity, (p) => p.contractAppendixPaymentProgressItems)
  @JoinColumn({ name: 'contractAppendixId', referencedColumnName: 'id' })
  contractAppendix: Promise<ContractAppendixEntity>

  /** Phương thức thanh toán */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentMethodId: string
  @ManyToOne(() => PaymentMethodEntity, (p) => p.contractAppendixPaymentProgressItems)
  @JoinColumn({ name: 'paymentMethodId', referencedColumnName: 'id' })
  paymentMethod: Promise<PaymentMethodEntity>

  /** Ghi chú */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  description: string

  /** Số tiền cần thanh toán của tiến độ */
  @Column({
    nullable: true,
    type: 'float',
    default: 0,
  })
  money: number
}
