import { Controller, Post, Body, UseGuards, Req } from '@nestjs/common'
import { AuthService } from './auth.service'
import { Request as IRequest } from 'express'
import { UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { JwtAuthGuard } from '../common/guards'
import { UpdatePasswordDto, UpdateUsernameDto, ForgotPasswordDto, UserLoginDto } from './dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { RequestFCMTokenDto } from './dto/requestFcmToken.dto'

@ApiBearerAuth()
@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly service: AuthService) {}

  @ApiOperation({ summary: 'Đăng nhập cho nhân viên' })
  @Post('login')
  public async login(@Body() data: UserLoginDto) {
    return await this.service.login(data)
  }

  @ApiOperation({ summary: 'Đăng nhập cho Doanh nghiệp' })
  @Post('login-client')
  public async loginClient(@Req() req: IRequest) {
    return await this.service.loginClient(req.body as any)
  }

  @ApiOperation({ summary: 'Lấy thông tin authorization Doanh nghiệp' })
  @UseGuards(JwtAuthGuard)
  @Post('authorization-client')
  public async authorizationClient(@Req() req: IRequest) {
    return await this.service.authorizationClient(req.user as UserDto)
  }

  @ApiOperation({ summary: 'Đổi mật khẩu' })
  @UseGuards(JwtAuthGuard)
  @Post('update-password')
  public async updatePassword(@Body() data: UpdatePasswordDto, @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.updatePassword(data, user, req)
  }

  @ApiOperation({ summary: 'Đổi tên tài khoản cho user supplier' })
  @UseGuards(JwtAuthGuard)
  @Post('update-username')
  public async updateUsername(@Body() data: UpdateUsernameDto, @CurrentUser() user: UserDto, @Req() req: IRequest) {
    return await this.service.updateUsername(data, user, req)
  }

  @ApiOperation({ summary: 'Gửi mã xác nhận khi quên mật khẩu' })
  @Post('send-confirm-code')
  public async sendConfirmCode(@Req() req: Request, @Body() data: { email: string }) {
    return await this.service.sendConfirmCode(req, data)
  }

  @ApiOperation({ summary: 'Nhập mã xác nhận khi supplier quên mật khẩu' })
  @Post('forgot-password')
  public async forgotPassword(@Req() req: Request, @Body() data: ForgotPasswordDto) {
    return await this.service.forgotPassword(req, data)
  }

  @Post('request_fcmtoken')
  public async requestFcmToken(@Body() data: RequestFCMTokenDto) {
    return await this.service.requestFcmToken(data)
  }

  @ApiOperation({ summary: 'Lưu lại công ty nhân viên đang đăng nhập' })
  @UseGuards(JwtAuthGuard)
  @Post('update_current_company')
  public async convertCompanyLogin(@Body() data: { companyId: string }, @CurrentUser() user: UserDto) {
    return await this.service.convertCompanyLogin(user, data.companyId)
  }
  @Post('refresh')
  async refreshToken(@Body() body: { refreshToken: string }) {
    return this.service.refresh(body.refreshToken)
  }
}
