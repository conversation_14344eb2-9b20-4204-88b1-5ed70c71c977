import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeTypeColum1750128140640 implements MigrationInterface {
    name = 'ChangeTypeColum1750128140640'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_item" ALTER COLUMN "quantityAlternative" decimal(20,4)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ALTER COLUMN "actualQuantity" decimal(20,4)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ALTER COLUMN "norm" decimal(20,4)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ALTER COLUMN "budget" decimal(20,4)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_item" ALTER COLUMN "budget" decimal(20,2)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ALTER COLUMN "norm" decimal(20,2)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ALTER COLUMN "actualQuantity" decimal(20,2)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ALTER COLUMN "quantityAlternative" decimal(20,2)`);
    }

}
