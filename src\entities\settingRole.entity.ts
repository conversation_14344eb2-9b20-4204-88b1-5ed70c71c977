import { BaseEntity } from './base.entity'
import { Entity, Column, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { EmployeeEntity } from './employee.entity'

/** <PERSON>ân quyền duyệt dữ liệu */
@Entity('setting_role')
export class SettingRoleEntity extends BaseEntity {
  /** enum 3 role */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  role: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgCompanyId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgBlockId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgDepartmentId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgPartId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgPositionId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgTreeId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.settingRoles)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  /** Giá trị chữ */
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  jsonSetting: string

  /**Nhóm công ty cho từng nhóm quyền  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgGroupCompanyId: string
}
