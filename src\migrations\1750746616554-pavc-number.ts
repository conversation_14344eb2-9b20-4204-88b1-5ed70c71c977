import { MigrationInterface, QueryRunner } from 'typeorm'

export class PavcNumber1750746616554 implements MigrationInterface {
  name = 'PavcNumber1750746616554'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "title" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "title"`)
  }
}
