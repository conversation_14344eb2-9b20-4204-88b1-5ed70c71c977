import { <PERSON>ti<PERSON>, Column, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { MaterialGroupEntity } from './materialGroup.entity'
import { BusinessPlanTemplateCostListEntity } from './businessTemplatePlanCostList.entity'
import { CostSettingEntity } from './costSetting.entity'

/** Cấu hình chi phí */
@Entity('cost')
export class CostEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string

  @ManyToOne(() => MaterialGroupEntity, (p) => p.costs)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>

  @Column({
    type: 'float',
    nullable: true,
  })
  cost: number

  @OneToMany(() => BusinessPlanTemplateCostListEntity, (p) => p.cost)
  businessPlanTemplateCostLists: Promise<BusinessPlanTemplateCostListEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  costSettingId: string

  @ManyToOne(() => CostSettingEntity, (p) => p.costs)
  @JoinColumn({ name: 'costSettingId', referencedColumnName: 'id' })
  costSetting: Promise<CostSettingEntity>
}
