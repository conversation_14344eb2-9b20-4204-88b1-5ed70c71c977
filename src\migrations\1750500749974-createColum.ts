import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateColum1750500749974 implements MigrationInterface {
    name = 'CreateColum1750500749974'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "reservationUomNormDetailId" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "reservationUomNormDetailId"`);
    }

}
