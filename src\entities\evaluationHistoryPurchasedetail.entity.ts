import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { EvaluationHistoryPurchaseEntity } from './evaluationHistoryPurchase.entity'
import { EvaluationHistoryPurchaseListDetailEntity } from './evaluationHistoryPurchaseListDetail.entity'

/** template đánh giá lịch sử mua hàng  */
@Entity('evaluation_history_purchase_detail')
export class EvaluationHistoryPurchaseDetailEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** <PERSON><PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  percent: number

  /** Điểm đ<PERSON>h giá */
  @Column({
    nullable: true,
    default: 0,
  })
  pointEvaluation: number

  /** Cấp độ */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** Điểm đánh giá */
  @Column({
    nullable: true,
  })
  scoreEvaluation: number

  /** Đường dẫn file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileUrl: string

  /** Tên file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileName: string

  /** Mô tả */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /** Ghi chú */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  note: string

  /** Phiếu đánh giá lịch sử mua hàng*/
  @Column({
    type: 'varchar',
    nullable: false,
  })
  evaluationHistoryPurchaseId: string
  @ManyToOne(() => EvaluationHistoryPurchaseEntity, (p) => p.evaluationHistoryPurchaseDetails)
  @JoinColumn({ name: 'evaluationHistoryPurchaseId', referencedColumnName: 'id' })
  evaluationHistoryPurchase: Promise<EvaluationHistoryPurchaseEntity>

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => EvaluationHistoryPurchaseDetailEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: EvaluationHistoryPurchaseDetailEntity

  @OneToMany(() => EvaluationHistoryPurchaseDetailEntity, (p) => p.parent)
  childs: Promise<EvaluationHistoryPurchaseDetailEntity[]>

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => EvaluationHistoryPurchaseListDetailEntity, (p) => p.evaluationHistoryPurchaseDetail)
  evaluationHistoryPurchaseListDetails: Promise<EvaluationHistoryPurchaseListDetailEntity[]>
}
