import { Injectable, NotAcceptableException } from '@nestjs/common'
import { Between, In, Like } from 'typeorm'
import { CREATE_SUCCESS, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'

import { LeadtimeRepository } from '../../repositories'
import { LeadtimeCreateDto, LeadtimeUpdateDto } from './dto'
import { v4 as uuidv4 } from 'uuid'
import { customAlphabet } from 'nanoid'
import { LeadTimeDetailEntity, LeadTimeEntity, MaterialEntity } from '../../entities'
import * as moment from 'moment'
import { arrayHelper } from '../../helpers'

/** Service xử lý các yêu cầu liên quan đến ngôn ngữ. */
@Injectable()
export class LeadtimeService {
  constructor(private repo: LeadtimeRepository) {}

  async createData(user: UserDto, data: LeadtimeCreateDto) {
    const nanoid = customAlphabet('QWERTYUIOPASDFGHJKLZXCVBNM', 6)
    const checkExsit = await this.repo.exists({ where: { materialGroupId: data.materialGroupId, year: data.year, isDeleted: false } })
    if (checkExsit) throw new Error(` Đã tồn tại đăng kí Leatime cho nhóm mua hàng rồi. Vui lòng kiểm tra lại`)

    if (data.lstDetail.length === 0) throw new Error(` Vui lòng thêm ít nhất một nhóm hàng. Vui lòng kiểm tra lại`)

    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const repo = trans.getRepository(LeadTimeEntity)
      const detailRepo = trans.getRepository(LeadTimeDetailEntity)
      const materialRepo = trans.getRepository(MaterialEntity)
      const entity = new LeadTimeEntity()

      entity.code = nanoid()
      entity.name = `Đăng kí Leadtime mua hàng ${moment(data.year).format('YYYY')}`
      entity.materialGroupId = data.materialGroupId
      entity.id = uuidv4()
      entity.createdBy = user.id
      entity.createdAt = new Date()
      entity.plantId = data.plantId
      entity.description = data.description
      entity.year = data.year
      await repo.insert(entity)
      const lstCode = data.lstDetail.map((x) => x.materialCode)
      const lstMaterial = await materialRepo.find({ where: { code: In(lstCode), materialGroupId: data.materialGroupId } })
      for (let x of lstMaterial) {
        for (let item of data.lstDetail) {
          if (x.code === item.materialCode) {
            const detail = new LeadTimeDetailEntity()
            detail.leadtimeId = entity.id
            detail.materialGroupId = item.materialGroupId
            detail.materialId = item.materialId
            detail.expireDate = item.expireDate
            detail.startDate = item.startDate
            detail.dayOfRecommendedPurchase = item.dayOfRecommendedPurchase
            detail.dayOfContract = item.dayOfContract
            detail.dayOfSupplierPrepare = item.dayOfSupplierPrepare
            detail.dayOfSupplierProduction = item.dayOfSupplierProduction
            detail.dayOfSupplierProductionToPort = item.dayOfSupplierProductionToPort
            detail.dayOfTransportSupplierToVietNam = item.dayOfTransportSupplierToVietNam
            detail.dayOfTransportVietNamToWarehouse = item.dayOfTransportVietNamToWarehouse
            detail.dayOfQualityCheckAndReceiving = item.dayOfQualityCheckAndReceiving
            detail.dayOfLeadtimePurchase = item.dayOfLeadtimePurchase
            detail.dayOfLeadtimeDelivery = item.dayOfLeadtimeDelivery
            detail.quarter = item.quarter
            detail.moqBuy = item.moqBuy
            detail.moqToPull = item.moqToPull
            detail.rouding = item.rouding
            detail.packagingSpecifications = item.packagingSpecifications
            detail.materialCode = item.materialCode
            await detailRepo.insert(detail)
          }
        }
      }
    })

    return { message: CREATE_SUCCESS }
  }

  async updateData(user: UserDto, data: LeadtimeUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (data.lstDetail.length === 0) throw new Error(` Vui lòng thêm ít nhất một nhóm hàng. Vui lòng kiểm tra lại`)
    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const repo = trans.getRepository(LeadTimeEntity)
      const detailRepo = trans.getRepository(LeadTimeDetailEntity)
      const materialRepo = trans.getRepository(MaterialEntity)
      entity.materialGroupId = data.materialGroupId
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      entity.plantId = data.plantId
      entity.description = data.description
      entity.year = data.year
      await repo.save(entity)

      await detailRepo.delete({ leadtimeId: entity.id })

      const lstCode = data.lstDetail.map((x) => x.materialCode)
      const lstMaterial = await materialRepo.find({ where: { code: In(lstCode), materialGroupId: data.materialGroupId } })
      for (let x of lstMaterial) {
        for (let item of data.lstDetail) {
          if (x.code === item.materialCode) {
            const detail = new LeadTimeDetailEntity()
            detail.leadtimeId = entity.id
            detail.materialGroupId = item.materialGroupId
            detail.materialId = item.materialId
            detail.expireDate = item.expireDate
            detail.startDate = item.startDate
            detail.dayOfRecommendedPurchase = item.dayOfRecommendedPurchase
            detail.dayOfContract = item.dayOfContract
            detail.dayOfSupplierPrepare = item.dayOfSupplierPrepare
            detail.dayOfSupplierProduction = item.dayOfSupplierProduction
            detail.dayOfSupplierProductionToPort = item.dayOfSupplierProductionToPort
            detail.dayOfTransportSupplierToVietNam = item.dayOfTransportSupplierToVietNam
            detail.dayOfTransportVietNamToWarehouse = item.dayOfTransportVietNamToWarehouse
            detail.dayOfQualityCheckAndReceiving = item.dayOfQualityCheckAndReceiving
            detail.dayOfLeadtimePurchase = item.dayOfLeadtimePurchase
            detail.dayOfLeadtimeDelivery = item.dayOfLeadtimeDelivery
            detail.quarter = item.quarter
            detail.moqBuy = item.moqBuy
            detail.moqToPull = item.moqToPull
            detail.rouding = item.rouding
            detail.packagingSpecifications = item.packagingSpecifications
            detail.materialCode = item.materialCode
            await detailRepo.insert(detail)
          }
        }
      }
    })
    return { message: UPDATE_SUCCESS }
  }

  public async pagination(data: PaginationDto) {
    const whereCon: any = {}
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.materialGroupId) whereCon.materialGroupId = data.where.materialGroupId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    if (data.where.materialGroupCode) {
      whereCon.materialGroup = {}
      whereCon.materialGroup.code = Like(`%${data.where.materialGroupCode}%`)
    }

    if (data.where.materialGroupName) {
      whereCon.materialGroup = {}
      whereCon.materialGroup.name = Like(`%${data.where.materialGroupName}%`)
    }

    if (data.where.year) {
      const year = new Date(data.where.year).getFullYear()
      const startDate = new Date(`${year}-01-01T00:00:00.000Z`)
      const endDate = new Date(`${year + 1}-01-01T00:00:00.000Z`)
      whereCon.year = Between(startDate, endDate)
    }

    const res: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: { materialGroup: true },
    })
    if (res[0].length === 0) return [[], 0]
    for (let item of res[0]) {
      item.materialGroupCode = item.__materialGroup__?.code
      item.materialGroupName = item.__materialGroup__?.name
      delete item.__materialGroup__
    }

    return res
  }

  public async findDetail(user: UserDto, data: { id: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: {
        materialGroup: true,
        leadtimeDetails: { material: true },
      },
    })
    res.materialGroupCode = res.__materialGroup__?.code
    res.materialGroupName = res.__materialGroup__?.name

    // Bước 1: Chuẩn hóa dữ liệu và gán material info
    const allDetails = (res.__leadtimeDetails__ || [])
      .map((item: any) => {
        item.materialCode = item.__material__?.code
        item.unitCode = item.__material__?.unitCode
        item.materialName = item.__material__?.name
        delete item.__material__
        return item
      })
      .filter((item) => !!item.materialCode)

    const maxPerMaterial = 4
    const countMap: Record<string, number> = {}
    const limitedDetails = allDetails.filter((item) => {
      const code = item.materialCode
      countMap[code] = (countMap[code] || 0) + 1
      return countMap[code] <= maxPerMaterial
    })

    const lstGroup = arrayHelper.groupByArray(limitedDetails, 'materialCode')
    let resultMat: any = []
    let index = 0
    for (let item of lstGroup) {
      resultMat.push({
        code: item.list[0].materialCode,
        unitCode: item.list[0].unitCode,
        materialId: item.heading,
        packagingSpecifications: item.list[0].packagingSpecifications,
        moqBuy: item.list[0].moqBuy,
        rouding: item.list[0].rouding,
        moqToPull: item.list[0].moqToPull,
        quarters: item.list,
        name: item.list[0].materialName,
        index: index++,
      })
    }

    res.lstLeadtimeDetails = resultMat
    delete res.__leadtimeDetails__
    delete res.__materialGroup__
    return res
  }

  async updateActiveStatus(user: UserDto, data: FilterOneDto) {
    const entity = await this.repo.findOne({
      where: { id: data.id },
    })
    if (!entity) throw new Error('Không tìm thấy dữ liệu')
    entity.isDeleted = !entity.isDeleted
    entity.updatedAt = new Date()
    entity.updatedBy = user.id
    await this.repo.update(entity.id, entity)
    return { message: UPDATE_ACTIVE_SUCCESS }
  }
}
