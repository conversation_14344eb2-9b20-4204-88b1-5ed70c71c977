import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, Index } from 'typeorm'
import { SupplierEntity } from './supplier.entity'

/** Thông tin loại hình doanh nghiệp*/
@Entity('business_template_plan_setup')
export class BusinessTemplatePlanSetupEntity extends BaseEntity {
  // Loại phương án
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  businessTemplatePlanTypeId: string

  // type
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  type: string
  // materialId
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  materialId: string
  // materialGr
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  materialGrId: string

  // supllierid
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  supplierId: string
  // RFQ
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  rfqId: string

  // collective number
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  collectiveNumber: string

  // thời gina tạo RFQ
  @Column({
    type: 'date',
    nullable: true,
  })
  createdRfqAt: Date
  // ngày kết thúc tạo RFQ
  @Column({
    type: 'date',
    nullable: true,
  })
  endCreatedRfqAt: Date
  // Loại PAKD tham chiếu
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  referenceBusinessTemplatePlanTypeId: string
}
