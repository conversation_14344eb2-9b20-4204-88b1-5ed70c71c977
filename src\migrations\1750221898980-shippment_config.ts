import { MigrationInterface, QueryRunner } from "typeorm";

export class ShippmentConfig1750221898980 implements MigrationInterface {
    name = 'ShippmentConfig1750221898980'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shipment_condition_type" DROP COLUMN "shipmentFeeConditions"`);
        await queryRunner.query(`ALTER TABLE "shipment_condition_type" ADD "shipmentFeeConditionsMonth" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "shipment_condition_type" ADD "shipmentFeeConditionsYear" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "type" nvarchar(50) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "type"`);
        await queryRunner.query(`ALTER TABLE "shipment_condition_type" DROP COLUMN "shipmentFeeConditionsYear"`);
        await queryRunner.query(`ALTER TABLE "shipment_condition_type" DROP COLUMN "shipmentFeeConditionsMonth"`);
        await queryRunner.query(`ALTER TABLE "shipment_condition_type" ADD "shipmentFeeConditions" varchar(MAX)`);
    }

}
