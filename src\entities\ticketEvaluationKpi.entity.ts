import { Column, <PERSON>ti<PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { KpiEntity } from './kpi.entity'
import { TicketEvaluationKpiDetailEntity } from './ticketEvaluationKpiDetail.entity'
import { TicketEvaluationKpiEmployeeEntity } from './ticketEvaluationKpiEmployee.entity'

/**  kpi mua hàng */
@Entity('ticket_evaluation_kpi')
export class TicketEvaluationKpiEntity extends BaseEntity {
  /** Mã  kpi */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** trạng thái  enumData: KpiStatus*/
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Thời gian ấp dụng từ */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  dateFrom: Date

  /** loại  thời gian tổng hợp */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  typePermissionKpi: string

  /** loại  thời gian tổng hợp */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  quarterType: string

  /** Tháng năm quý */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  sumTimeData: Date

  /** Ngày tổng hợp dữ liệu */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  sumDayData: Date

  /** Đánh giá */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  evaluate: string

  /** Nhận xét */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  comment: string

  /** Khóa kết quả */
  @Column({
    nullable: true,
    default: false,
  })
  isResultLock: boolean

  /** Check hiện nút khóa */
  @Column({
    nullable: true,
    default: false,
  })
  isCheckLock: boolean

  /** Tổng điểm */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  totalScore: number

  /** Xếp loại */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  ratingType: string

  /** id kpi mua hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  kpiId: string
  @ManyToOne(() => KpiEntity, (p) => p.ticketEvaluationKpis)
  @JoinColumn({ name: 'kpiId', referencedColumnName: 'id' })
  kpi: Promise<KpiEntity>

  /** ds phiếu đánh giá template kpi */
  @OneToMany(() => TicketEvaluationKpiDetailEntity, (p) => p.ticketEvaluationKpi)
  ticketEvaluationKpiDetails: Promise<TicketEvaluationKpiDetailEntity[]>

  /** ds phiếu đánh giá template kpi */
  @OneToMany(() => TicketEvaluationKpiEmployeeEntity, (p) => p.ticketEvaluationKpi)
  ticketEvaluationKpiEmployees: Promise<TicketEvaluationKpiEmployeeEntity[]>
}
