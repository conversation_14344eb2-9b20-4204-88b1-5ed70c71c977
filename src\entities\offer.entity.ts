import { <PERSON><PERSON><PERSON>, Column, OneToMany, ManyTo<PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { OfferServiceEntity } from './offerService.entity'
import { OfferPriceEntity } from './offerPrice.entity'
import { OfferSupplierEntity } from './offerSupplier.entity'
import { EmployeeEntity } from './employee.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { PrEntity } from './pr.entity'
import { OfferTechEntity } from './offerTech.entity'
import { OfferTradeEntity } from './offerTrade.entity'
import { OfferPriceColEntity } from './offerPriceCol.entity'
import { OfferCustomPriceEntity } from './offerCustomPrice.entity'
import { enumData } from '../constants'
import { POEntity } from './po.entity'
import { OfferDealEntity } from './offerDeal.entity'
import { OfferShipmentPriceEntity } from './offerShipmentPrice.entity'
import { ShipmentStageEntity } from './shipmentStage.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { ContractEntity } from './contract.entity'
import { BusinessPlanEntity } from './businessPlan.entity'
import { RecommendedPurchaseShipmenStageEntity } from './recommendedPurchaseShipmentStage.entity'

/** Bảng chào giá nhanh */
@Entity('offer')
export class OfferEntity extends BaseEntity {
  /** Mã */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  /** Tên */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  offerId: string

  /** Tên */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  title: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.offer)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  /** Ngày nộp đấu giá */
  @Column({ nullable: true, type: 'datetime' })
  effectiveDate?: Date

  @Column({ nullable: true, type: 'datetime' })
  endDate?: Date

  @Column({ type: 'varchar', length: 250, nullable: true })
  currency: string

  @Column({
    nullable: true,
    default: 0,
  })
  timePeriod: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  condition: string

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  address: string

  @Column({
    nullable: true,
    default: false,
  })
  isHaveVat: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isCompleteAll: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isNotConfigTrade: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isShowClient: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isGetFromPr: boolean

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({ type: 'varchar', nullable: true })
  externalMaterialGroupId?: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.offer)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  exMatGroup?: Promise<ExternalMaterialGroupEntity>

  @Column({ type: 'varchar', nullable: true })
  prId?: string
  @ManyToOne(() => PrEntity, (p) => p.offer)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr?: Promise<PrEntity>

  @Column({ type: 'varchar', nullable: true })
  businessPlantId?: string
  @ManyToOne(() => BusinessPlanEntity, (p) => p.offer)
  @JoinColumn({ name: 'businessPlantId', referencedColumnName: 'id' })
  businessPlan?: Promise<BusinessPlanEntity>

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  refType: string

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({
    nullable: true,
    default: false,
  })
  isLoadFromItem: boolean

  /** Danh sách nhà cung cấp đấu thầu */
  @OneToMany(() => OfferSupplierEntity, (p) => p.offer)
  offerSuppliers: Promise<OfferSupplierEntity[]>

  @OneToMany(() => OfferShipmentPriceEntity, (p) => p.offer)
  shipments: Promise<OfferShipmentPriceEntity[]>

  /** Thời gian đăng tải */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  publicDate: Date

  /** Trạng thái đánh giá kỹ thuật */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusRateTech: string

  /** Trạng thái đánh giá thương mại */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusRateTrade: string

  /** Trạng thái đánh giá giá */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusRatePrice: string

  /** Trạng thái cấu hình lại bảng giá enum BidResetPriceStatus */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    default: 'ChuaTao',
  })
  statusResetPrice: string

  /** Yêu cầu hủy gói thầu */
  @Column({
    nullable: true,
    default: false,
  })
  isRequestDelete: boolean

  /** Lý do yêu cầu hủy gói thầu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteRequestDelete: string

  /** File scan kết quả gói thầu do MPO upload khi gửi yêu cầu phê duyệt kết thúc thầu */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileScan: string

  /** MPO ghi chú khi gửi yêu cầu phê duyệt kết thúc thầu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteFinishBidMPO: string

  /** Trạng thái  thiết lập thương mại */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusTrade: string

  @Column({
    nullable: true,
    type: 'datetime',
  })
  resetPriceEndDate: Date

  /** Trạng thái thiết lập giá */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusPrice: string

  /** Có bắt buộc File chi tiết kỹ thuật */
  @Column({
    nullable: true,
    default: false,
  })
  isRequireFileTechDetail: boolean

  /** Có bắt buộc File chi tiết giá */
  @Column({
    nullable: true,
    default: false,
  })
  isRequireFilePriceDetail: boolean

  /** Cách tính điểm giá */
  @Column({ type: 'varchar', length: 50, nullable: true, default: enumData.PriceScoreCalculateWay.SumScore.code })
  wayCalScorePrice: string

  /** Công thức tính cột đơn giá */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fomular: string

  /** Trạng thái chọn Doanh nghiệp */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    default: 'ChuaChon',
  })
  statusChooseSupplier: string

  /** Thời điểm bắt đầu */
  @Column({ nullable: true, type: 'datetime' })
  dateStart: Date

  /** Thời điểm kết thúc */
  @Column({ nullable: true, type: 'datetime' })
  dateEnd: Date

  @OneToMany(() => OfferServiceEntity, (p) => p.offer)
  offerService: Promise<OfferServiceEntity[]>

  @OneToMany(() => OfferSupplierEntity, (p) => p.offer)
  offerSupplier: Promise<OfferSupplierEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình kỹ thuật */
  @OneToMany(() => OfferTechEntity, (p) => p.offer)
  techs: Promise<OfferTechEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình thương mại */
  @OneToMany(() => OfferTradeEntity, (p) => p.offer)
  trades: Promise<OfferTradeEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình giá */
  @OneToMany(() => OfferPriceEntity, (p) => p.offer)
  prices: Promise<OfferPriceEntity[]>

  /** Danh sách các cột bổ sung thêm để Doanh nghiệp nhập dữ liệu */
  @OneToMany(() => OfferPriceColEntity, (p) => p.offer)
  offerPriceCols: Promise<OfferPriceColEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình cơ cấu giá */
  @OneToMany(() => OfferCustomPriceEntity, (p) => p.offer)
  customPrices: Promise<OfferCustomPriceEntity[]>

  /** PO*/
  @OneToMany(() => POEntity, (p) => p.offer)
  pos: Promise<POEntity[]>

  /** Ghi chú của người tạo khi tạo ĐKTM */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteTrade: string

  /** Chứng từ khác */
  @Column({
    type: 'varchar',
    length: 250,

    nullable: true,
  })
  fileAttach: string

  /** Ghi chú của người tạo khi tạo giá */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  notePrice: string

  /** Ngày duyệt chọn Doanh nghiệp thắng thầu */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  approveChooseSupplierWinDate: Date

  /** Ngày đóng thầu (ngày mpoLead duyệt kết thúc thầu) */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  bidCloseDate: Date

  /** Ghi chú của người duyệt khi duyệt thông tin kỹ thuật */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteTechLeader: string

  /** Ghi chú của người duyệt khi duyệt thông tin ĐKTM, giá, Doanh nghiệp */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteMPOLeader: string

  @OneToMany(() => OfferDealEntity, (p) => p.offer)
  offerDeals: Promise<OfferDealEntity[]>

  /** Shipment */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentId: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  offerTypeCode: string

  @OneToMany(() => ShipmentStageEntity, (p) => p.offer)
  stages: Promise<ShipmentStageEntity[]>

  @OneToMany(() => RecommendedPurchaseEntity, (p) => p.offer)
  recommendedPurchases: Promise<RecommendedPurchaseEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.offer)
  contracts: Promise<ContractEntity[]>

  @OneToMany(() => RecommendedPurchaseShipmenStageEntity, (p) => p.offer)
  recommendedPurchaseShipmentStage: Promise<RecommendedPurchaseShipmenStageEntity[]>
}
