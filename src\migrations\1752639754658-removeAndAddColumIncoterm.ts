import { MigrationInterface, QueryRunner } from 'typeorm'

export class RemoveAndAddColumIncoterm1752639754658 implements MigrationInterface {
  name = 'RemoveAndAddColumIncoterm1752639754658'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`EXEC sp_rename "dbo.incoterm_conversion.lstShipmentFeeConditionCode", "lstShipmentConditionTypeCode"`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`EXEC sp_rename "dbo.incoterm_conversion.lstShipmentConditionTypeCode", "lstShipmentFeeConditionCode"`)
  }
}
