import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm'
import { ShipmentEntity } from './shipment.entity'
import { InboundEntity } from './inbound.entity'

@Entity('shipment_inbound')
export class ShipmentInboundEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  inboundId: string
  @ManyToOne(() => InboundEntity, (p) => p.shipmentInbounds)
  @JoinColumn({ name: 'inboundId', referencedColumnName: 'id' })
  inbound: Promise<InboundEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  shipmentId: string
  @ManyToOne(() => ShipmentEntity, (p) => p.shipmentInbounds)
  @JoinColumn({ name: 'shipmentId', referencedColumnName: 'id' })
  shipment: Promise<ShipmentEntity>
}
