import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeColumnAppendix1752829558158 implements MigrationInterface {
  name = 'ChangeColumnAppendix1752829558158'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "FK_eb779d50474b4b376465d77b3ea"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ALTER COLUMN "name" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ALTER COLUMN "title" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ALTER COLUMN "contractId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ALTER COLUMN "isEffContract" bit`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ALTER COLUMN "isPaymentProgress" bit`)
    await queryRunner.query(
      `ALTER TABLE "contract_appendix" ADD CONSTRAINT "FK_eb779d50474b4b376465d77b3ea" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "FK_eb779d50474b4b376465d77b3ea"`)

    await queryRunner.query(`ALTER TABLE "contract_appendix" ALTER COLUMN "isPaymentProgress" bit NOT NULL`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ALTER COLUMN "isEffContract" bit NOT NULL`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ALTER COLUMN "contractId" uniqueidentifier NOT NULL`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ALTER COLUMN "title" varchar(250) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "contract_appendix" ALTER COLUMN "name" nvarchar(250) NOT NULL`)
    await queryRunner.query(
      `ALTER TABLE "contract_appendix" ADD CONSTRAINT "FK_eb779d50474b4b376465d77b3ea" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }
}
