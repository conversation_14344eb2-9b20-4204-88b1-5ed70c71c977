import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, JoinColumn, ManyToOne } from 'typeorm'
import { POEntity } from './po.entity'
import { ShipmentEntity } from './shipment.entity'

@Entity('shipment_po')
export class ShipmentPoEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.shipmentPos)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  shipmentId: string
  @ManyToOne(() => ShipmentEntity, (p) => p.shipmentPos)
  @JoinColumn({ name: 'shipmentId', referencedColumnName: 'id' })
  shipment: Promise<ShipmentEntity>
}
