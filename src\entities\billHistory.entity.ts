import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BillEntity } from './bill.entity'

@Entity('bill_history')
export class BillHistoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  createdByName: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  billId: string
  @ManyToOne(() => BillEntity, (p) => p.histories)
  @JoinColumn({ name: 'billId', referencedColumnName: 'id' })
  bill: Promise<BillEntity>

  /** Trạng thái hiện tại */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  description: string
}
