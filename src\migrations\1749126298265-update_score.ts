import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateScore1749126298265 implements MigrationInterface {
  name = 'UpdateScore1749126298265'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "comment" ADD "supplierId" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "comment" DROP COLUMN "supplierId"`)
  }
}
