import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeColumnPlanEmp1751127307517 implements MigrationInterface {
    name = 'ChangeColumnPlanEmp1751127307517'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "plan_site_assessment_employee" ALTER COLUMN "isLeader" bit`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "plan_site_assessment_employee" ALTER COLUMN "isLeader" bit NOT NULL`);
    }

}
