import { <PERSON>ti<PERSON>, Column, Jo<PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PlantEntity } from './plant.entity'
import { EmployeeEntity } from '.'
import { KpiPositionEntity } from './kpiPosition.entity'
import { KpiPermissionPositionEntity } from './kpiPermissionPosition.entity'
import { TicketEvaluationKpiEmployeeEntity } from './ticketEvaluationKpiEmployee.entity'
import { PlanSiteAssessmentEmployeeEntity } from './planSiteAssessmentEmployee.entity'

/** C<PERSON>u hình vị trí */
@Entity('position')
export class PositionEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** <PERSON><PERSON> tả khối */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @ManyToOne(() => PlantEntity, (p) => p.positions)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @OneToMany(() => EmployeeEntity, (p) => p.ePosition)
  employee: Promise<EmployeeEntity[]>

  @OneToMany(() => KpiPositionEntity, (p) => p.position)
  kpiPoitions: Promise<KpiPositionEntity[]>

  @OneToMany(() => KpiPermissionPositionEntity, (p) => p.position)
  kpiPermissionPoitions: Promise<KpiPermissionPositionEntity[]>

  /** ds phiếu đánh giá KPI và NV */
  @OneToMany(() => TicketEvaluationKpiEmployeeEntity, (p) => p.position)
  ticketEvaluationKpiEmployees: Promise<TicketEvaluationKpiEmployeeEntity[]>
}
