import { MigrationInterface, QueryRunner } from "typeorm";

export class Create1752048056230 implements MigrationInterface {
    name = 'Create1752048056230'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" ADD "shipmentFeeConditionTypeCompactId" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" ADD "shipmentFeeConditionTypeCompactCode" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" ADD "shipmentFeeConditionTypeCompactValue" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "request_quote" ADD "shipmentFeeConditionTypeCompactId" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "request_quote" ADD "shipmentFeeConditionTypeCompactCode" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "request_quote" ADD "shipmentFeeConditionTypeCompactValue" varchar(max)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "request_quote" DROP COLUMN "shipmentFeeConditionTypeCompactValue"`);
        await queryRunner.query(`ALTER TABLE "request_quote" DROP COLUMN "shipmentFeeConditionTypeCompactCode"`);
        await queryRunner.query(`ALTER TABLE "request_quote" DROP COLUMN "shipmentFeeConditionTypeCompactId"`);
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" DROP COLUMN "shipmentFeeConditionTypeCompactValue"`);
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" DROP COLUMN "shipmentFeeConditionTypeCompactCode"`);
        await queryRunner.query(`ALTER TABLE "request_quote_supplier" DROP COLUMN "shipmentFeeConditionTypeCompactId"`);
    }

}
