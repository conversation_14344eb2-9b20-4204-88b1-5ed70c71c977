import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTableSiteAssessment1749661704234 implements MigrationInterface {
  name = 'AddTableSiteAssessment1749661704234'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "template_site_assessment_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_73bf8cb0f557f3ecb0922a78124" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3bf662257c49158e261755900aa" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "name" nvarchar(250) NOT NULL, "value" int NOT NULL, "templateSiteAssessmentItemChildId" uniqueidentifier NOT NULL, CONSTRAINT "PK_73bf8cb0f557f3ecb0922a78124" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "template_site_assessment_item_child" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_11964bc469be165bb7e8b8ea4d4" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b63b719a82e115a94fa4e19807a" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "name" nvarchar(max), "isToStore" bit CONSTRAINT "DF_3fd670750a542dbe5141434d509" DEFAULT 0, "maxScore" int CONSTRAINT "DF_7a2db84ef3b40a965449ac33250" DEFAULT 0, "isSenSupplier" bit CONSTRAINT "DF_a011dd6aad1aec3bee4342f8958" DEFAULT 0, "dataType" nvarchar(250), "document" nvarchar(max), "templateSiteAssessmentItemId" uniqueidentifier NOT NULL, CONSTRAINT "PK_11964bc469be165bb7e8b8ea4d4" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "template_site_assessment_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9ce626f8cc94b9eb4f68c80db23" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ed93d2880a8b20847f05e786a25" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_c9ef0ae8900c3ebfc51a2276f1d" DEFAULT 0, "content" nvarchar(max) NOT NULL, "templateSiteAssessmentItemId" uniqueidentifier NOT NULL, CONSTRAINT "PK_9ce626f8cc94b9eb4f68c80db23" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "template_site_assessment_list_detail" ADD CONSTRAINT "FK_a4eb71f6078a174e5ec197c1ac8" FOREIGN KEY ("templateSiteAssessmentItemChildId") REFERENCES "template_site_assessment_item_child"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "template_site_assessment_item_child" ADD CONSTRAINT "FK_aedb9cf163cc498dbe401c209ad" FOREIGN KEY ("templateSiteAssessmentItemId") REFERENCES "template_site_assessment_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "template_site_assessment_item" ADD CONSTRAINT "FK_c76018c94d8472c57974801bae0" FOREIGN KEY ("templateSiteAssessmentItemId") REFERENCES "template_site_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "template_site_assessment_item" DROP CONSTRAINT "FK_c76018c94d8472c57974801bae0"`)
    await queryRunner.query(`ALTER TABLE "template_site_assessment_item_child" DROP CONSTRAINT "FK_aedb9cf163cc498dbe401c209ad"`)
    await queryRunner.query(`ALTER TABLE "template_site_assessment_list_detail" DROP CONSTRAINT "FK_a4eb71f6078a174e5ec197c1ac8"`)
    await queryRunner.query(`DROP TABLE "template_site_assessment_item"`)
    await queryRunner.query(`DROP TABLE "template_site_assessment_item_child"`)
    await queryRunner.query(`DROP TABLE "template_site_assessment_list_detail"`)
  }
}
