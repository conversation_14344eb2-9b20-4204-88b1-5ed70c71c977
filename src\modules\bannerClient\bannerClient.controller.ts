import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { BannerClientService } from './bannerClient.service'
import { JwtAuthGuard } from '../common/guards'
import { PaginationDto, UserDto } from '../../dto'
import { BannerClientCreateDto, BannerClientUpdateDto } from './dto'
import { CurrentUser } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

@ApiBearerAuth()
@ApiTags('Client')
@UseGuards(JwtAuthGuard)
@Controller('bannerClients')
export class BannerClientController {
  constructor(private readonly service: BannerClientService) {}

  @ApiOperation({ summary: 'Danh sách banner phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo banner' })
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BannerClientCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật banner' })
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BannerClientUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái hoạt động banner' })
  @Post('update_active')
  public async updateActive(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateIsDelete(user, data)
  }
}
