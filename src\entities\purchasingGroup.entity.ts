import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, JoinColumn, ManyToOne } from 'typeorm'
import { PrItemEntity } from './prItem.entity'
import { EmployeePurchasingGroupEntity } from './employeePurchasingGroup.entity'
import { SupplierNumberRequestApproveEntity } from './supplierNumberRequestApprove.entity'
import { MaterialEntity } from './material.entity'
import { CompanyEntity } from './company.entity'
import { PrEntity } from './pr.entity'
import { ComplaintEntity } from './complaint.entity'
import { EmployeeEntity } from './employee.entity'
import { PurchasingOrgEntity } from './purchasingOrg.entity'

import { RoleSupplierEntity } from './roleSupplier.entity'
import { RequestQuoteEntity } from './requestQuote.entity'
import { ContractEntity } from './contract.entity'
import { RfqEntity } from './rfq.entity'

@Entity('purchasing_group')
export class PurchasingGroupEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string

  @ManyToOne(() => CompanyEntity, (p) => p.purchasingGroups)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string
  @ManyToOne(() => PurchasingOrgEntity, (p) => p.purchasingGroups)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  @OneToMany(() => PrItemEntity, (p) => p.purchasingGroup)
  prItems: Promise<PrItemEntity[]>

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.purchasingGroup)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>

  @OneToMany(() => EmployeePurchasingGroupEntity, (p) => p.employee)
  employeePurchasingGroup: Promise<EmployeePurchasingGroupEntity[]>

  @OneToMany(() => MaterialEntity, (p) => p.purchasingGroup)
  materials: Promise<MaterialEntity[]>

  @OneToMany(() => PrEntity, (p) => p.purchasingGroup)
  prs: Promise<PrEntity[]>

  @OneToMany(() => ComplaintEntity, (p) => p.purchasingGroup)
  complaints: Promise<ComplaintEntity[]>

  @OneToMany(() => EmployeeEntity, (p) => p.purchasingGroup)
  employee: Promise<EmployeeEntity[]>

  @OneToMany(() => RoleSupplierEntity, (p) => p.purchasingGroup)
  roleSuppliers: Promise<RoleSupplierEntity[]>

  @OneToMany(() => RequestQuoteEntity, (p) => p.purchasingGroup)
  requestQuotes: Promise<RequestQuoteEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.purchasingGroup)
  contracts: Promise<ContractEntity[]>

  @OneToMany(() => RfqEntity, (p) => p.purchasingGroup)
  rfqs: Promise<RfqEntity[]>
}
