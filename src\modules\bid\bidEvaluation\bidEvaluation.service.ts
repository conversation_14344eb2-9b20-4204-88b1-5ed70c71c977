import { Injectable, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'
import { EmailService } from '../../email/email.service'
import {
  BidRepository,
  BidEmployeeAccessRepository,
  BidSupplierRepository,
  BidPrItemRepository,
  BidSupplierItemRepository,
} from '../../../repositories'
import { enumData, ERROR_NOT_FOUND_DATA, ERROR_YOU_DO_NOT_HAVE_PERMISSION } from '../../../constants'
import { UserDto } from '../../../dto'
import { In, Not } from 'typeorm'
import { BidEntity, BidHistoryEntity } from '../../../entities'
import { coreHelper } from '../../../helpers'
import { FlowApproveService } from '../../flowApprove/flowApprove.service'
import { BidSupplierItemEntity } from '../../../entities/bidSupplierItem.entity'

@Injectable()
export class BidEvaluationService {
  constructor(
    private readonly repo: BidRepository,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,
    private readonly bidPrItemRepository: BidPrItemRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly bidSupplierItemRepository: BidSupplierItemRepository,
    private readonly emailService: EmailService,
    private readonly flowService: FlowApproveService,
  ) {}

  /** Tự động chọn NCC thắng thầu và kết thúc thầu */
  async autoBid(user: UserDto, data: { bidId: string }) {
    const bid: any = await this.repo.findOne({
      where: { id: data.bidId, companyId: user.companyId },
      relations: { childs: true },
      select: { id: true, isAutoBid: true, childs: true },
    })
    if (!bid) throw new Error('Gói thầu không còn tồn tại')
    if (bid.isDeleted) throw new Error('Gói thầu đã ngưng hoạt động')
    if (!bid.isAutoBid) throw new Error('Gói thầu không cấu hình tự động chọn NCC thắng thầu và kết thúc thầu')

    /* Tìm ra danh sách nhà cung cấp thắng thầu của gói thầu */
    const dicSupplierItemWin: any = {}
    const listSupplierItemWin = await this.bidSupplierItemRepository.find({ where: { bidId: data.bidId } })
    for (const supplierItemWin of listSupplierItemWin) {
      dicSupplierItemWin[supplierItemWin.supplierId + supplierItemWin.materialId] = true
    }
    for (const item of bid.__childs__) {
      item.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: item.id, companyId: user.companyId, isDeleted: false },
        order: { isSuccessBid: 'DESC' },
      })
      if (item.lstBidSupplier.length == 0) continue

      const lstValue = item.lstBidSupplier.map((c) => c.scorePrice)
      const maxValue = Math.max(...lstValue)
      const dlc = coreHelper.calDLC(lstValue)

      const lstValueManual = item.lstBidSupplier.map((c) => c.scoreManualPrice)
      const maxValueManual = Math.max(...lstValueManual)
      const dlcManual = coreHelper.calDLC(lstValueManual)

      for (const itemBidSupplier of item.lstBidSupplier) {
        const isWin = dicSupplierItemWin[itemBidSupplier.supplierId + item.materialId]
        itemBidSupplier.supplierName = itemBidSupplier.__supplier__.name
        delete itemBidSupplier.__supplier__
        itemBidSupplier.isChoose = isWin ? true : false
        let isHasTotal = false
        itemBidSupplier.scoreTotal = 0
        itemBidSupplier.scoreManualTotal = 0
        if (
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code ||
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTech * item.percentTech) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTech * item.percentTech) / 100
        } else {
          itemBidSupplier.scoreTech = -1
          itemBidSupplier.scoreManualTech = -1
        }

        if (
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code ||
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTrade * item.percentTrade) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTrade * item.percentTrade) / 100
        } else {
          itemBidSupplier.scoreTrade = -1
          itemBidSupplier.scoreManualTrade = -1
        }

        if (
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code ||
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code
        ) {
          isHasTotal = true
          let priceScore = 0
          if (dlc > 0) {
            priceScore = item.percentPrice - (maxValue - itemBidSupplier.scorePrice) / dlc
          } else {
            priceScore = item.percentPrice
          }
          itemBidSupplier.scoreTotal += priceScore

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = item.percentPrice - (maxValueManual - itemBidSupplier.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = item.percentPrice
          }
          itemBidSupplier.scoreManualTotal += priceManualScore
        } else {
          itemBidSupplier.scorePrice = -1
          itemBidSupplier.scoreManualPrice = -1
        }

        if (!isHasTotal) {
          itemBidSupplier.scoreTotal = -1
          itemBidSupplier.scoreManualTotal = -1
        }
      }

      item.lstBidSupplier.sort((a, b) => b.scoreTotal - a.scoreTotal)
      let rank = 1
      const total = item.lstBidSupplier.length
      item.lstBidSupplier.forEach((itemBidSupplier) => {
        itemBidSupplier.rank = rank + '/' + total
        rank++
      })
    }
  }

  /** Load ds Doanh nghiệp để chọn trúng thầu */
  async loadSupplierData(user: UserDto, data: { bidId: string; isMobile?: boolean }) {
    // const bidItem = await this.bidPrItemRepository.find({where: { id: data.bidId}})
    const res: any = await this.repo.getBid3(user, data.bidId)
    const dicStatusFile: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      lstStatus.forEach((c) => (dicStatusFile[c.code] = c.name))
    }
    const dicSupplierItemWin: any = {}
    const listSupplierItemWin = await this.bidSupplierItemRepository.find({ where: { bidId: data.bidId } })
    for (const supplierItemWin of listSupplierItemWin) {
      dicSupplierItemWin[supplierItemWin.supplierId + supplierItemWin.materialId] = true
    }
    // res.listItem = res.listItem
    for (const item of res.listItem) {
      item.lstBidSupplier = await this.bidSupplierRepo.find({
        // where: { bidId: item.id, companyId: user.companyId, isDeleted: false },
        where: { bidId: item.bidId, isDeleted: false },
        relations: { supplier: true },
        order: { isSuccessBid: 'DESC' },
      })
      if (item.lstBidSupplier.length == 0) continue

      const lstAll = await this.bidSupplierRepo.find({
        where: { bidId: item.id },
        select: { id: true, scorePrice: true, scoreManualPrice: true },
      })
      const lstValue = lstAll.map((c) => c.scorePrice)
      const maxValue = Math.max(...lstValue)
      const dlc = coreHelper.calDLC(lstValue)

      const lstValueManual = lstAll.map((c) => c.scoreManualPrice)
      const maxValueManual = Math.max(...lstValueManual)
      const dlcManual = coreHelper.calDLC(lstValueManual)

      for (const itemBidSupplier of item.lstBidSupplier) {
        const isWin = dicSupplierItemWin[itemBidSupplier.supplierId + item.materialId]
        itemBidSupplier.supplierName = itemBidSupplier.__supplier__.name
        delete itemBidSupplier.__supplier__
        itemBidSupplier.statusFileName = dicStatusFile[itemBidSupplier.statusFile]
        itemBidSupplier.isChoose = isWin ? true : false
        let isHasTotal = false
        itemBidSupplier.scoreTotal = 0
        itemBidSupplier.scoreManualTotal = 0
        if (
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code ||
          itemBidSupplier.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTech * item.percentTech) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTech * item.percentTech) / 100
        } else {
          itemBidSupplier.scoreTech = -1
          itemBidSupplier.scoreManualTech = -1
        }

        if (
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code ||
          itemBidSupplier.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code
        ) {
          isHasTotal = true
          itemBidSupplier.scoreTotal += (itemBidSupplier.scoreTrade * item.percentTrade) / 100
          itemBidSupplier.scoreManualTotal += (itemBidSupplier.scoreManualTrade * item.percentTrade) / 100
        } else {
          itemBidSupplier.scoreTrade = -1
          itemBidSupplier.scoreManualTrade = -1
        }

        if (
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code ||
          itemBidSupplier.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code
        ) {
          isHasTotal = true
          let priceScore = 0
          if (dlc > 0) {
            priceScore = item.percentPrice - (maxValue - itemBidSupplier.scorePrice) / dlc
          } else {
            priceScore = item.percentPrice
          }
          itemBidSupplier.scoreTotal += priceScore

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = item.percentPrice - (maxValueManual - itemBidSupplier.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = item.percentPrice
          }
          itemBidSupplier.scoreManualTotal += priceManualScore
        } else {
          itemBidSupplier.scorePrice = -1
          itemBidSupplier.scoreManualPrice = -1
        }

        if (!isHasTotal) {
          itemBidSupplier.scoreTotal = -1
          itemBidSupplier.scoreManualTotal = -1
        }
      }

      item.lstBidSupplier.sort((a, b) => b.scoreTotal - a.scoreTotal)
      let rank = 1
      const total = item.lstBidSupplier.length
      item.lstBidSupplier.forEach((itemBidSupplier) => {
        itemBidSupplier.rank = rank + '/' + total
        rank++
      })
    }

    if (data.isMobile) {
      const listContractor = []
      const lstStatus = coreHelper.convertObjToArray(enumData.BidSupplierFileStatus)
      for (const item of res.listItem) {
        item.lstBidSupplier = await this.bidSupplierRepo.find({
          // where: { bidId: item.id, companyId: user.companyId, isDeleted: false },
          where: { bidId: item.bidId, isDeleted: false, isSuccessBid: true },
          relations: { supplier: true },
          order: { isSuccessBid: 'DESC' },
        })
        if (item.lstBidSupplier.length == 0) continue
        let rank = 1
        for (const itemBidSupplier of item.lstBidSupplier) {
          const lstAll = await this.bidSupplierRepo.find({
            where: { bidId: item.id },
            select: { id: true, scorePrice: true, scoreManualPrice: true },
          })
          const lstValue = lstAll.map((c) => c.scorePrice)
          const maxValue = Math.max(...lstValue)
          const dlc = coreHelper.calDLC(lstValue)

          const lstValueManual = lstAll.map((c) => c.scoreManualPrice)
          const maxValueManual = Math.max(...lstValueManual)
          const dlcManual = coreHelper.calDLC(lstValueManual)
          const supDetail: any = {}
          supDetail.supplierId = itemBidSupplier.__supplier__.id
          supDetail.supplierName = itemBidSupplier.__supplier__.name

          //   điểm kỹ thuật
          supDetail.scoreTech = itemBidSupplier.scoreTech
          //   điểm HĐXT kỹ thuật
          supDetail.scoreTechTotal = itemBidSupplier.scoreManualTotal
          //   điểm báo giá
          let priceScore = 0
          if (dlc > 0) {
            priceScore = item.percentPrice - (maxValue - itemBidSupplier.scorePrice) / dlc
          } else {
            priceScore = item.percentPrice
          }
          supDetail.scoreTotal += priceScore
          //   điểm HĐXT BG

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = item.percentPrice - (maxValueManual - itemBidSupplier.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = item.percentPrice
          }
          supDetail.priceManualScore += priceManualScore
          //   điểm ĐKTM
          supDetail.scoreTrade = itemBidSupplier.scoreTrade
          //   Điểm HĐXT ĐKTM
          supDetail.scoreManualTrade = itemBidSupplier.scoreManualTrade
          //   Trạng thái hồ sơ
          supDetail.statusFileName = dicStatusFile[itemBidSupplier.statusFile]
          //   Tổng điểm hồ sơ
          supDetail.scoreTotal = 0
          supDetail.scoreManualTotal = 0
          //   Tổng điểm hệ thống
          supDetail.scoreTotal += (itemBidSupplier.scoreTech * item.percentTech) / 100
          supDetail.scoreTotal += (itemBidSupplier.scoreTrade * item.percentTrade) / 100
          supDetail.scoreTotal += priceScore
          //   Tổng điểm đánh giá
          supDetail.scoreManualTotal += (itemBidSupplier.scoreManualTech * item.percentTech) / 100
          supDetail.scoreManualTotal += (itemBidSupplier.scoreManualTrade * item.percentTrade) / 100
          supDetail.scoreManualTotal += priceManualScore
          //   Thứ hạng
          supDetail.rank = rank
          rank++
          listContractor.push(supDetail)
        }
      }

      const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
        targetId: data?.bidId,
        entityName: BidEntity.name,
        type: enumData.FlowCode.SUPPLIER_WIN_BID.code,
      })

      return {
        bidId: data?.bidId,
        listContractor,
        status: res.status ?? '',
        approvalProgress,
        canApprove,
      }
    }

    /* Tìm ra danh sách cấp duyệt và comment và người duyệt */
    res.haveProgress = false
    res.approvalProgress = await this.flowService.getListApproveDetail(user, {
      targetId: res.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.SUPPLIER_WIN_BID.code,
    })
    res.showComment = false
    /* nếu như nhân viên hiện tại chưa duyệt và nằm trong luồng duyệt thì toggle bật showComment = true */
    for (const item of res.approvalProgress) {
      if (item.approveType == user.orgPositionId && !item.approved) {
        res.showComment = true
        break
      }
    }
    if (res.approvalProgress.length > 0) res.haveProgress = true

    return res
  }

  /** Hàm kiểm tra quyền xác nhận Doanh nghiệp trúng thầu */
  async checkPermissionEvaluation(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId }, select: { id: true, status: true } })
    if (!bid) return { hasPermission: result, message }
    if (
      bid.status === enumData.BidStatus.HoanTatDanhGia.code ||
      bid.status === enumData.BidStatus.DongDamPhanGia.code ||
      bid.status === enumData.BidStatus.DongDauGia.code
    ) {
      result = await this.bidEmployeeAccessRepo.isMPO(user, bidId)
      if (!result) message = 'Bạn không có quyền xác nhận Doanh nghiệp trúng thầu cho gói thầu.'
    } else message = 'Gói thầu đã được xác nhận Doanh nghiệp trúng thầu.'

    return { hasPermission: result, message }
  }

  /** Chọn Doanh nghiệp trúng thầu, trượt thầu theo từng Item */
  async evaluationBidSupplier(user: UserDto, data: { bidId: string; listItem: any[]; comment: string }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionEvaluation(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const lstPromise = []
    for (const item of data.listItem) {
      const lstSupplierChoose = item.lstBidSupplier.filter((c) => c.isChoose)
      for (const supplierChoose of lstSupplierChoose) {
        const newSupplierWin = new BidSupplierItemEntity()
        newSupplierWin.bidId = data.bidId
        newSupplierWin.bidSupplierId = supplierChoose.id
        newSupplierWin.supplierId = supplierChoose.supplierId
        newSupplierWin.materialId = item.materialId
        newSupplierWin.bidPrId = item.prItemId
        //  newSupplierWin. = data.bidId
        lstPromise.push(this.bidSupplierItemRepository.insert(newSupplierWin))

        lstPromise.push(
          this.bidSupplierRepo.update(supplierChoose.id, { isSuccessBid: true, noteSuccessBid: supplierChoose.noteSuccessBid, updatedBy: user.id }),
        )
      }
      const lstId = lstSupplierChoose.map((c) => c.id)
      lstPromise.push(this.bidSupplierRepo.update({ bidId: item.id, id: Not(In(lstId)) }, { isSuccessBid: false, updatedBy: user.id }))
    }
    await Promise.all(lstPromise)
    await this.repo.update(data.bidId, {
      status: enumData.BidStatus.DongThau.code,
      noteCloseBidMPO: data.comment,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.XacNhanNCCTrungThau.code
    bidHistory.save()

    // gửi email mpo leader duyệt
    this.emailService.GuiMpoDuyetNCCThangThau(data.bidId)

    // tạo quyền duyệt nhà cung cấp thắng thầu
    let flowType: string
    flowType = enumData.FlowCode.SUPPLIER_WIN_BID.code
    await this.flowService.setRoleRule(user, {
      targetId: data.bidId,
      target: data,
      entityName: BidEntity.name,
      flowType: flowType,
      companyId: user.orgCompanyId,
      // departmentId: user?.departmentId,
    })

    return { message: 'Chọn doanh nghiệp thắng thầu thành công.' }
  }

  /** Phê duyệt Doanh nghiệp thắng thầu */
  async approveSupplierWinBid(user: UserDto, data: { bidId: string; comment: string }) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: data.bidId,
      entityName: BidEntity.name,
      type: enumData.FlowCode.SUPPLIER_WIN_BID.code,
      comment: data.comment,
    })

    if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
      return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    } else {
      // kiểm tra quyền
      // if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
      const objPermission = await this.checkPermissionApproveSupplierWinBid(user, data.bidId)
      if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

      const todate = new Date()
      await this.repo.update(data.bidId, {
        status: enumData.BidStatus.DuyetNCCThangThau.code,
        noteCloseBidMPOLeader: data.comment,
        approveChooseSupplierWinDate: todate,
        updatedBy: user.id,
      })

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = data.bidId
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.PheDuyetNCCThangThau.code
      bidHistory.save()

      // Gửi email thông báo nội bộ: tạm khóa
      // await this.emailService.ThongBaoNCCThangThauDuocDuyet(bidId)

      return { message: 'Phê duyệt Doanh nghiệp thắng thầu thành công.' }
    }
  }

  /** Yêu cầu đánh giá và chọn lại Doanh nghiệp thắng thầu */
  async rejectSupplierWinBid(user: UserDto, data: { bidId: string; comment: string; recheck: any[] }) {
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    // const objPermission = await this.checkPermissionApproveSupplierWinBid(user, data.bidId)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.flowService.rejectRule(user, {
      targetId: data.bidId,
      entityName: BidEntity.name,
      type: enumData.FlowCode.SUPPLIER_WIN_BID.code,
      comment: data.comment,
    })

    const isCheck1 = data.recheck[0].checked
    const isCheck2 = data.recheck[1].checked
    const dataUpdate: any = {
      status: enumData.BidStatus.DangDanhGia.code,
      // statusRateTech: enumData.BidTechRateStatus.DangTao.code,
      // statusRateTrade: enumData.BidTradeRateStatus.DangTao.code,
      // statusRatePrice: enumData.BidPriceRateStatus.DangTao.code,
      noteCloseBidMPOLeader: data.comment,
      updatedBy: user.id,
    }
    //nếu như có 1 thì reset Đánh giá NL, KT
    if (isCheck1) {
      dataUpdate.statusRateTech = enumData.BidTechRateStatus.DangTao.code
    }
    //nếu như có 2 Đánh giá bảng CG, CCG & Đánh giá DKTM
    if (isCheck2) {
      dataUpdate.statusRateTrade = enumData.BidTradeRateStatus.DangTao.code
      dataUpdate.statusRatePrice = enumData.BidTradeRateStatus.DangTao.code
    }
    // throw new Error('')
    await this.repo.update(data.bidId, dataUpdate)

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.YeuCauKiemTraLai.code
    bidHistory.save()

    // Gửi email
    this.emailService.ThongBaoNCCThangThauBiTuChoi(data.bidId)

    return { message: 'Yêu cầu chọn lại Doanh nghiệp thắng thầu thành công.' }
  }

  /** Kiểm tra quyền Phê duyệt/Từ chối Doanh nghiệp thắng thầu */
  async checkPermissionApproveSupplierWinBid(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId }, select: { id: true, status: true } })
    if (!bid) return { hasPermission: result, message }

    if (bid.status === enumData.BidStatus.DongThau.code) {
      // result = await this.bidEmployeeAccessRepo.isMPOLeader(user, bidId)
      result = true
      if (!result) message = 'Bạn không có quyền phê duyệt Doanh nghiệp thắng thầu.'
    } else message = 'Gói thầu đã được phê duyệt Doanh nghiệp thắng thầu.'

    return { hasPermission: result, message }
  }
}
