import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeColumNorm1749112571195 implements MigrationInterface {
    name = 'ChangeColumNorm1749112571195'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "DF_a75a480d9be5ba2e06424de4365"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "norm"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "norm" float CONSTRAINT "DF_a75a480d9be5ba2e06424de4365" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "DF_a75a480d9be5ba2e06424de4365"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "norm"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "norm" decimal(20,2)`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD CONSTRAINT "DF_a75a480d9be5ba2e06424de4365" DEFAULT 0 FOR "norm"`);
    }

}
