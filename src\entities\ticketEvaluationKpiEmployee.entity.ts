import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { TicketEvaluationKpiEntity } from './ticketEvaluationKpi.entity'
import { EmployeeEntity } from './employee.entity'
import { CompanyEntity } from './company.entity'
import { BlockEntity } from './block.entity'
import { DepartmentEntity } from './department.entity'
import { PartEntity } from './part.entity'
import { PositionEntity } from './position.entity'

/**  danh sách nhân viên áp dụng phiếu đánh giá */
@Entity('ticket_evaluation_kpi_employee')
export class TicketEvaluationKpiEmployeeEntity extends BaseEntity {
  /** id phiếu đánh giá KPI */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  ticketEvaluationKpiId: string
  @ManyToOne(() => TicketEvaluationKpiEntity, (p) => p.ticketEvaluationKpiEmployees)
  @JoinColumn({ name: 'ticketEvaluationKpiId', referencedColumnName: 'id' })
  ticketEvaluationKpi: Promise<TicketEvaluationKpiEntity>

  /** id Nhân viên */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.ticketEvaluationKpiEmployees)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  /** id Công ty */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string
  @ManyToOne(() => CompanyEntity, (p) => p.ticketEvaluationKpiEmployees)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  /** id khối */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  blockId: string
  @ManyToOne(() => BlockEntity, (p) => p.ticketEvaluationKpiEmployees)
  @JoinColumn({ name: 'blockId', referencedColumnName: 'id' })
  block: Promise<BlockEntity>

  /** id phòng ban */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  departmentId: string
  @ManyToOne(() => DepartmentEntity, (p) => p.ticketEvaluationKpiEmployees)
  @JoinColumn({ name: 'departmentId', referencedColumnName: 'id' })
  department: Promise<DepartmentEntity>

  /** id bộ phận */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  partId: string
  @ManyToOne(() => PartEntity, (p) => p.ticketEvaluationKpiEmployees)
  @JoinColumn({ name: 'partId', referencedColumnName: 'id' })
  part: Promise<PartEntity>

  /** id vị trí */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  positionId: string
  @ManyToOne(() => PositionEntity, (p) => p.ticketEvaluationKpiEmployees)
  @JoinColumn({ name: 'positionId', referencedColumnName: 'id' })
  position: Promise<PositionEntity>
}
