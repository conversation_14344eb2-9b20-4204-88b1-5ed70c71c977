import { BaseEntity } from './base.entity'
import { En<PERSON><PERSON>, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { EvaluationHistoryPurchaseDetailEntity } from './evaluationHistoryPurchasedetail.entity'

@Entity('evaluation_history_purchase_list_detail')
export class EvaluationHistoryPurchaseListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  evaluationHistoryPurchaseDetailId: string
  @ManyToOne(() => EvaluationHistoryPurchaseDetailEntity, (p) => p.evaluationHistoryPurchaseListDetails)
  @JoinColumn({ name: 'evaluationHistoryPurchaseId', referencedColumnName: 'id' })
  evaluationHistoryPurchaseDetail: Promise<EvaluationHistoryPurchaseDetailEntity>
}
