import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateEntity1752463376354 implements MigrationInterface {
  name = 'UpdateEntity1752463376354'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_group_plan" ADD "code" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "businessTemplateGroupPlanId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "business_template_plan" ADD CONSTRAINT "FK_8a6d5246ba13d2608233780d775" FOREIGN KEY ("businessTemplateGroupPlanId") REFERENCES "business_template_group_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP CONSTRAINT "FK_8a6d5246ba13d2608233780d775"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "businessTemplateGroupPlanId"`)
    await queryRunner.query(`ALTER TABLE "business_template_group_plan" DROP COLUMN "code"`)
  }
}
