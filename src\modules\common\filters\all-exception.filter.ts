import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { HttpService } from '@nestjs/axios'

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(private configService: ConfigService, private httpService: HttpService) {}

  catch(exception: any, host: ArgumentsHost) {
    console.log(exception?.stack)
    const ctx = host.switchToHttp()
    const response = ctx.getResponse()
    const request = ctx.getRequest()
    const detailMessages = exception?.response?.message || []

    if (exception instanceof HttpException) {
      const status = exception.getStatus()

      let message: any = exception.message
      const name = exception.name

      if (message === 'INTERNAL_SERVER_ERROR' && exception.message) {
        message = exception.message
      } else if (message.message) {
        message = message.message
      }

      if (status == HttpStatus.UNAUTHORIZED && message == 'Unauthorized') {
        if (response?.req?.authInfo?.name == 'TokenExpiredError') {
          message = 'Hết phiên đăng nhập, vui lòng đăng nhập lại để tiếp tục.'
        }
      }

      if (status == HttpStatus.BAD_REQUEST && name == 'BadRequestException' && message == 'Bad Request Exception') {
        const detailMessage = detailMessages.join('<br>+ ') || ''
        message = `Dữ liệu không hợp lệ, chi tiết:<br>+ ${detailMessage}`
      }
      response.status(status).json({
        statusCode: status,
        timestamp: new Date().toISOString(),
        path: request.url,
        message: message,
        name: name,
      })
    } else {
      const err: any = exception
      const status = err?.status || HttpStatus.INTERNAL_SERVER_ERROR
      const name = err?.name || err?.statusText || 'INTERNAL_SERVER_ERROR'
      let message = err?.message || err?.data?.message || 'Server đang update hoặc mất kết nối, vui lòng thử lại sau.'
      if (message.message) {
        message = message.message
      }

      const jsonRequest = {
        body: request.body,
        header: request.headers,
        ip: request.ip,
        user: request.user,
      }

      try {
        const obj = {
          project: this.configService.get<string>('PROJECT_NAME'),
          source: this.configService.get<string>('SOURCE_CODE'),
          environments: this.configService.get<string>('ENVIRONMENT'),
          error: exception,
          request: jsonRequest,
          message: message,
          statusCode: status,
          timestamp: new Date().toISOString(),
          path: request.url,
          name: name,
        }
        const url = this.configService.get<string>('LOG_URL') // 'https://ape-bot-api.apetechs.co/bug_log/create_data'
        if (this.configService.get<string>('ENVIRONMENT') !== 'LOCAL') {
          this.httpService
            .post(url, obj)
            .toPromise()
            .then((res: any) => {
              // console.log('success')
            })
            .catch((err: any) => {
              console.log(err)
            })
        }
      } catch (error) {
        console.log(error)
      }

      response.status(status).json({
        statusCode: status,
        timestamp: new Date().toISOString(),
        path: request.url,
        message: message,
        name: name,
      })
    }
  }
}
