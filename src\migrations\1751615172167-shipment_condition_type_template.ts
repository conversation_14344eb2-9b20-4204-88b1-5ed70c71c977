import { MigrationInterface, QueryRunner } from "typeorm";

export class ShipmentConditionTypeTemplate1751615172167 implements MigrationInterface {
    name = 'ShipmentConditionTypeTemplate1751615172167'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "jsonPrice" varchar(max)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "jsonPrice"`);
    }

}
