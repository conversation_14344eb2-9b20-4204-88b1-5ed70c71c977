import { SupplierNumberRequestApproveEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { RoleFiSupplierEntity } from './roleFiSupplier.entity'

@Entity('planning_group')
export class PlanningGroupEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.paymentMethod)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>

  @OneToMany(() => RoleFiSupplierEntity, (p) => p.planningGroup)
  roleFiSuppliers: Promise<RoleFiSupplierEntity[]>
}
