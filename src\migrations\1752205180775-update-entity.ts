import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateEntity1752205180775 implements MigrationInterface {
  name = 'UpdateEntity1752205180775'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "business_template_plan_type" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f591ad3ad1bb4ee2dada2951805" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2e543c1b72cee8993729a6df2e1" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "code" varchar(150) NOT NULL, "name" varchar(150), "status" varchar(50), "description" varchar(max), CONSTRAINT "PK_f591ad3ad1bb4ee2dada2951805" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "business_template_plan_setup" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_507d9a737c4c59836a407941273" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f12f14fdd97fa07586bc0e7b54f" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "businessTemplatePlanTypeId" varchar(150) NOT NULL, "type" varchar(150), "materialId" varchar(150), "materialGrId" varchar(150), "supplierId" varchar(150), "rfqId" varchar(150), "collectiveNumber" varchar(150), "createdRfqAt" date, "endCreatedRfqAt" date, "referenceBusinessTemplatePlanTypeId" varchar(150), CONSTRAINT "PK_507d9a737c4c59836a407941273" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "business_template_plan" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_62f7caee68334b64b981b6424d1" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_d84bee9b0e77c198bc1e06b4391" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "code" varchar(150) NOT NULL, "name" varchar(150), "status" varchar(50), "description" varchar(max), CONSTRAINT "PK_62f7caee68334b64b981b6424d1" PRIMARY KEY ("id"))`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "business_template_plan"`)
    await queryRunner.query(`DROP TABLE "business_template_plan_setup"`)
    await queryRunner.query(`DROP TABLE "business_template_plan_type"`)
  }
}
