import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'

/**<PERSON><PERSON> quyền cá nhân cho từ nhân viên */
@Entity('permission_individual')
export class PermissionIndividualEntity extends BaseEntity {
  /**JSON phân quyền */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  roleStringify: string


  /** Employee */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  employeeId: string

  @OneToOne(() => EmployeeEntity, (employee) => employee.permissionIndividual)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

}
