import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddCloumnCont1750761278536 implements MigrationInterface {
  name = 'AddCloumnCont1750761278536'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "templateExcelName" nvarchar(255)`)
    await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "dataCalculate" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "dataCalculate"`)
    await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "templateExcelName"`)
  }
}
