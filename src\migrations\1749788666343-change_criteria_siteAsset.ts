import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeCriteriaSiteAsset1749788666343 implements MigrationInterface {
    name = 'ChangeCriteriaSiteAsset1749788666343'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_child" DROP COLUMN "evaluationNumber"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_child" DROP COLUMN "evaluationList"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_child" ADD "evaluationList" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_child" ADD "evaluationNumber" int`);
    }

}
