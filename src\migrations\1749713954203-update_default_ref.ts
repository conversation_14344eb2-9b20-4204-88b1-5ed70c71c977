import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateDefaultRef1749713954203 implements MigrationInterface {
    name = 'UpdateDefaultRef1749713954203'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service_capacity_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_capacity_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_year_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_year_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "item_tech_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "item_tech_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "item_tech" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "item_tech" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_member" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_member" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_tech_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_tech_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_tech" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_tech" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_trade_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_trade_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_trade" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_trade" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_custom_price_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_custom_price_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "cost" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "cost" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_group" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_group" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_scene_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_scene_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_scene" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_scene" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "title" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "title" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "stakeholder_category" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "stakeholder_category" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "industry_standard" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "industry_standard" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "gl_account" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "gl_account" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_purchasing_group" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_purchasing_group" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_group" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_group" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_department" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_department" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_handling_plan" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_handling_plan" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "complaint" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "master_condition_type" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "master_condition_type" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "procedure" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "procedure" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_price_list" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_price_list" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill_lookup" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill_lookup" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bill" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_bill" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_bill" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_po" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_po" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_contract" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_contract" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "currency_exchange" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "currency_exchange" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "currency" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "currency" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_schema" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_schema" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_org_schema" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_org_schema" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_org" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_org" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "incoterm" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "incoterm" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "role_supplier" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "role_supplier" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_term" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_term" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "planning_group" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "planning_group" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_method" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_method" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template_col" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template_col" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_col" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_col" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_template" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_template" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rfq_details" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rfq_details" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_string" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_string" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template_col" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template_col" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_type" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_type" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_col" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_col" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_trade_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_trade_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_trade" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_trade" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_custom_price_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_custom_price_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_tech_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_tech_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_tech" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_tech" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "external_material_group" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "external_material_group" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_uom" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_uom" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "warehouse" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "warehouse" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "uom" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "uom" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "auction" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "banner_client" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "banner_client" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_type" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_type" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_notify" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_notify" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_warning" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_warning" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "faq_category" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "faq_category" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "faq" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "faq" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "link_client" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "link_client" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "email_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "email_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "email_template" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "email_template" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_custom_price" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_custom_price" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_tech_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_tech_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_tech" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_tech" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_trade_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_trade_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_trade" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_trade" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_string_client" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_string_client" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_law_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_law_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_year_value" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_year_value" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_notify" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_notify" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_role" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "setting_role" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_number" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_number" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_po" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_po" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_document_handover" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_document_handover" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_container" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_container" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_type" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_type" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_route" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_route" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_type" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_type" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_container" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "inbound_container" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user_confirm_code" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user_confirm_code" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_access" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_access" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "data_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "data_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_member" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_member" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "language" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "language" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "language_key" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "language_key" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "condition_type" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "condition_type" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bank" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bank" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "country" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "country" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "region" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "region" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_partner_group" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_partner_group" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_area" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "purchasing_area" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "factory_supplier" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "factory_supplier" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plan_site_assessment" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plan_site_assessment" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "site_assessment" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "site_assessment" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_company" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_company" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_position" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_position" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "position" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "position" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_scale" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_scale" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "kpi" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "part" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "part" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "block" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "block" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service_history" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service_history" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_acceptance" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_acceptance" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_employee" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_employee" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_approve" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_approve" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "organizational_tree" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "organizational_tree" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_individual" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_individual" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_type" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "business_type" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_potential_upgrade" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_potential_upgrade" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "action_log" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "action_log" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_lead_time" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_lead_time" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "header_lead_time" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "header_lead_time" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "organizational_position" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "organizational_position" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rate_data" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rate_data" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rating_configuration" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "rating_configuration" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve_base" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve_base" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "product_hierarchy" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "product_hierarchy" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_view" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_view" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_to_group" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "permission_to_group" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_criterial_child" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_criterial_child" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_criterial" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_criterial" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_evaluation_potential" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_evaluation_potential" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_revenue" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_revenue" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_product_service" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_product_service" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_product_line" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_product_line" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_certificate" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_certificate" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service_factory" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service_factory" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_list_detail" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_list_detail" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_item_child" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_item_child" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_product" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po_product" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "po" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_progress" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "payment_progress" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_pr" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_pr" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "media_file" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "media_file" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plant" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "plant" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_company" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_company" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "company" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "company" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "flow_approve" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "department" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "department" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_role" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee_role" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "employee" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_capacity" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service_capacity" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "service" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user_external_material_group" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "user_external_material_group" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_score" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "supplier_score" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_view_permission" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_view_permission" ADD "poTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "comment" ADD "prTypeCodeRef" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "comment" ADD "poTypeCodeRef" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "comment" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "comment" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "group_view_permission" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "group_view_permission" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_score" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_score" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "user_external_material_group" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "user_external_material_group" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_auction" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_auction" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_capacity" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_capacity" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "employee_role" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "employee_role" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "department" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "department" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "flow_approve" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "flow_approve" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_company" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_company" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "plant" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "plant" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "media_file" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "media_file" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_pr" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_pr" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_progress" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_progress" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_product" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_product" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_item_child" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_item_child" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_site_assessment_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_service_factory" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_service_factory" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_certificate" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_certificate" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_product_line" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_product_line" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_product_service" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_product_service" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_revenue" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_revenue" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_evaluation_potential" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_evaluation_potential" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_criterial" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_criterial" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_criterial_child" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_criterial_child" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission_to_group" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission_to_group" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission_view" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission_view" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "product_hierarchy" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "product_hierarchy" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_base" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "rating_configuration" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "rating_configuration" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "rate_data" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "rate_data" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "organizational_position" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "organizational_position" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "header_lead_time" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "header_lead_time" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_lead_time" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "template_lead_time" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "action_log" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "action_log" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_potential_upgrade" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_potential_upgrade" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_type" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_type" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission_individual" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission_individual" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "organizational_tree" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "organizational_tree" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission_approve" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission_approve" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission_employee" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission_employee" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_acceptance" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_acceptance" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_service_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_service_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "block" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "block" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "part" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "part" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_scale" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_scale" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "position" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "position" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_position" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_position" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_company" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "kpi_company" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "site_assessment" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "site_assessment" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "plan_site_assessment" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "plan_site_assessment" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "factory_supplier" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "factory_supplier" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "purchasing_area" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "purchasing_area" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_partner_group" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_partner_group" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "region" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "region" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bank_branch" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bank_branch" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bank" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bank" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "condition_type" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "condition_type" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "language_key" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "language_key" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "language" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "language" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_member" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_member" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "data_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "data_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_access" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_access" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "user_confirm_code" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "user_confirm_code" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "inbound_container" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "inbound_container" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "inbound_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "inbound_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_type" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_type" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_route" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_route" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_type" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_type" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_container" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_container" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "inbound_document_handover" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "inbound_document_handover" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_po" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_po" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_number" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_number" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "setting_role" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "setting_role" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_notify" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_notify" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_year_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_year_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_law_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_law_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "setting_string_client" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "setting_string_client" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_trade" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_trade" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_trade_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_trade_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_tech" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_tech" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_tech_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_tech_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_custom_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_custom_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "email_template" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "email_template" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "link_client" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "link_client" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "faq" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "faq" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "faq_category" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "faq_category" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "employee_warning" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "employee_warning" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "employee_notify" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "employee_notify" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_type" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_type" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "banner_client" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "banner_client" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "auction" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "auction" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "uom" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "uom" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "warehouse" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "warehouse" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material_uom" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material_uom" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "external_material_group" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "external_material_group" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_tech" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_tech" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_tech_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_tech_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_custom_price_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_custom_price_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_trade" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_trade" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_trade_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_trade_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_price_col" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_price_col" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_price_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_price_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_deal" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_deal" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_price_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_price_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material_type" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material_type" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template_col" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template_col" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "setting_string" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "setting_string" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_col" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_col" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template_col" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template_col" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_method" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_method" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "planning_group" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "planning_group" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_term" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_term" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "role_supplier" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "role_supplier" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "incoterm" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "incoterm" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "purchasing_org" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "purchasing_org" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "purchasing_org_schema" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "purchasing_org_schema" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_schema" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_schema" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "currency" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "currency" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "currency_exchange" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "currency_exchange" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_contract" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_contract" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_po" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_po" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_bill" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "payment_bill" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bill" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bill" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bill_lookup" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bill_lookup" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bill_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bill_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_price_list" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_price_list" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "procedure" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "procedure" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "master_condition_type" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "master_condition_type" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_handling_plan" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_handling_plan" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_department" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "complaint_department" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "purchasing_group" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "purchasing_group" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "employee_purchasing_group" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "employee_purchasing_group" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "gl_account" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "gl_account" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "industry_standard" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "industry_standard" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "stakeholder_category" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "stakeholder_category" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "title" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "title" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_scene" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_scene" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_scene_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_scene_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material_group" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "material_group" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "cost" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "cost" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_deal" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_deal" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_price_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_price_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_custom_price_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_custom_price_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_trade" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_trade" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_trade_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_trade_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_tech" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_tech" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_tech_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_tech_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "auction_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "auction_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_member" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_member" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "po_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr_history" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "pr_history" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "item_tech" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "item_tech" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "item_tech_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "item_tech_list_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_year_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_year_detail" DROP COLUMN "prTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_capacity_list_detail" DROP COLUMN "poTypeCodeRef"`);
        await queryRunner.query(`ALTER TABLE "service_capacity_list_detail" DROP COLUMN "prTypeCodeRef"`);
    }

}
