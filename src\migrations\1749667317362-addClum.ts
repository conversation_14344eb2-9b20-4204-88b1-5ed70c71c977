import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddClum1749667317362 implements MigrationInterface {
  name = 'AddClum1749667317362'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pr" ADD "legalEntity" varchar(50)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pr" DROP COLUMN "legalEntity"`)
  }
}
