import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm'
import { BidEmployeeAccessEntity } from './bidEmployeeAccess.entity'
import { SupplierEntity } from './supplier.entity'

@Entity('bid_employee_rate')
export class BidEmployeeRateEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  employeeAccessId: string
  @ManyToOne(() => BidEmployeeAccessEntity, (p) => p.bidEmployeeRate)
  @JoinColumn({ name: 'employeeAccessId', referencedColumnName: 'id' })
  employeeAccess: Promise<BidEmployeeAccessEntity>

  @Column({ type: 'varchar', nullable: false })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.bidEmployeeRate)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Điểm HĐXT Kỹ thuật */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  scoreManualTech: number

  /** Điểm HĐXT giá */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  scoreManualPrice: number

  /** Điểm HĐXT DKTM */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  scoreManualTrade: number

  @Column({
    nullable: true,
    default: false,
  })
  scoreTechLock: boolean

  @Column({
    nullable: true,
    default: false,
  })
  scoreTradeBlock: boolean

  @Column({
    nullable: true,
    default: false,
  })
  scorePriceBlock: boolean
}
