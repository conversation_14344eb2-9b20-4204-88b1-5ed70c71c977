import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveColum1749492197823 implements MigrationInterface {
    name = 'RemoveColum1749492197823'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_13b64d9a10631da673245cb8254"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "materialPriceId"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material" ADD "materialPriceId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_13b64d9a10631da673245cb8254" FOREIGN KEY ("materialPriceId") REFERENCES "material_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
