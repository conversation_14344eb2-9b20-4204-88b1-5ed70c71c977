import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { OfferTradeEntity } from './offerTrade.entity'

@Entity('offer_trade_list_detail')
export class OfferTradeListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  offerTradeId: string
  @ManyToOne(() => OfferTradeEntity, (p) => p.offerTradeListDetails)
  @JoinColumn({ name: 'offerTradeId', referencedColumnName: 'id' })
  offerTrade: Promise<OfferTradeEntity>
}
