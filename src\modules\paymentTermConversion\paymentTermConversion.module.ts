import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { PaymentTermConversionRepository, PaymentTermRepository } from '../../repositories'
import { PaymentTermConversionController } from './paymentTermConversion.controller'
import { PaymentTermConversionService } from './paymentTermConversion.service'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([PaymentTermConversionRepository, PaymentTermRepository])],
  controllers: [PaymentTermConversionController],
  providers: [PaymentTermConversionService],
  exports: [PaymentTermConversionService],
})
export class PaymentTermConversionModule {}
