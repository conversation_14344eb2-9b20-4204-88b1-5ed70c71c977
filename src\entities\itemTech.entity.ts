import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { PrItemEntity } from './prItem.entity'
import { ItemTechListDetailEntity } from './itemTechListDetail.entity'

/** Yêu cầu kỹ thuật của vật tư trong PR */
@Entity('item_tech')
export class ItemTechEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** <PERSON><PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** Cách tính điểm theo loại càng cao càng tốt */
  @Column({
    nullable: false,
    default: true,
  })
  isCalUp: boolean

  /** Kiểu dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  percent: number

  /** % điều kiện đạt tỉ trọng */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentRule: number

  /** % điều kiện liệt tỉ trọng khi tính theo chiều giảm dần */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentDownRule: number

  /** Cấp độ */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** Mô tả */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => ItemTechEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: ItemTechEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => ItemTechEntity, (p) => p.parent)
  childs: Promise<ItemTechEntity[]>

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Giá trị nhỏ nhất */
  @Column({
    nullable: true,
  })
  requiredMin: number

  /** Thuộc tính của tiêu chí thể hệ Doanh nghiệp sẽ được highlight màu xanh nếu đạt giá trị X khi xếp hạng năng lực */
  @Column({
    nullable: false,
    default: false,
  })
  isHighlight: boolean

  @Column({
    nullable: true,
  })
  hightlightValue: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  prItemId: string
  @ManyToOne(() => PrItemEntity, (p) => p.itemTechs)
  @JoinColumn({ name: 'prItemId', referencedColumnName: 'id' })
  prItem: Promise<PrItemEntity>

  /** Con - 1 công thức sẽ có thể có nhiều detail list */
  @OneToMany(() => ItemTechListDetailEntity, (p) => p.itemTech)
  itemTechListDetails: Promise<ItemTechListDetailEntity[]>
}
