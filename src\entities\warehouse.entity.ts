import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { InboundEntity } from '.'
import { ReservationEntity } from './reservation.entity'

@Entity('warehouse')
export class WarehouseEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @OneToMany(() => InboundEntity, (p) => p.expectWarehouse)
  inbounds: Promise<InboundEntity[]>

  @OneToMany(() => ReservationEntity, (p) => p.warehouseReceiving)
  receivingReservations: Promise<ReservationEntity[]>

  @OneToMany(() => ReservationEntity, (p) => p.warehouseIssue)
  issueReservations: Promise<ReservationEntity[]>
}
