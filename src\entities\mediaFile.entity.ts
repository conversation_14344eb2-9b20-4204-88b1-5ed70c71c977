import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { MaterialEntity } from './material.entity'
import { PrItemEntity } from './prItem.entity'
import { ShipmentEntity } from './shipment.entity'
import { BusinessPlanEntity } from './businessPlan.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { ContractDocumentHandoverEntity } from './contractDocumentHandover.entity'
import { SupplierUpgradeEntity } from './supplierUpgrade.entity'
import { PrEntity } from './pr.entity'
import { CriteriaSiteAssessmentChildEntity } from './criteriaSiteAssessmentChild.entity'
import { RequestQuoteEntity } from './requestQuote.entity'

/** Media */
@Entity('media_file')
export class MediaFileEntity extends BaseEntity {
  /** Đường dẫn file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  fileUrl: string

  /** Tên file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileName: string

  /** Vật tư */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  /** Lịch sử vật tư */
  @ManyToOne(() => MaterialEntity, (p) => p.files)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** PR Item */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemId: string
  @ManyToOne(() => PrItemEntity, (p) => p.mediaFiles)
  @JoinColumn({ name: 'prItemId', referencedColumnName: 'id' })
  prItem: Promise<PrItemEntity>

  /** Shipment */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentId: string
  @ManyToOne(() => ShipmentEntity, (p) => p.mediaFiles)
  @JoinColumn({ name: 'shipmentId', referencedColumnName: 'id' })
  shipment: Promise<ShipmentEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanId: string
  @ManyToOne(() => BusinessPlanEntity, (p) => p.mediaFiles)
  @JoinColumn({ name: 'businessPlanId', referencedColumnName: 'id' })
  businessPlan: Promise<BusinessPlanEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseId: string
  @ManyToOne(() => RecommendedPurchaseEntity, (p) => p.mediaFiles)
  @JoinColumn({ name: 'recommendedPurchaseId', referencedColumnName: 'id' })
  recommendedPurchase: Promise<RecommendedPurchaseEntity>

  /** Bill */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  contracyDocumentHandoverId: string
  @ManyToOne(() => ContractDocumentHandoverEntity, (p) => p.mediaFiles)
  @JoinColumn({ name: 'contracyDocumentHandoverId', referencedColumnName: 'id' })
  contracyDocumentHandover: Promise<ContractDocumentHandoverEntity>

  /** Chứng từ nâng cấp nhà cung cấp */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierUpgradeId: string
  @ManyToOne(() => SupplierUpgradeEntity, (p) => p.mediaFiles)
  @JoinColumn({ name: 'supplierUpgradeId', referencedColumnName: 'id' })
  supplierUpgrade: Promise<SupplierUpgradeEntity>

  /** PR Item */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.mediaFiles)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  /** Criteria Site Assessment Child - Câu hỏi con phiếu đánh giá */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  criteriaSiteAssessmentChildId: string
  @ManyToOne(() => CriteriaSiteAssessmentChildEntity, (p) => p.mediaFiles)
  @JoinColumn({ name: 'criteriaSiteAssessmentChildId', referencedColumnName: 'id' })
  criteriaSiteAssessmentChild: Promise<CriteriaSiteAssessmentChildEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  requestQuoteId: string
  @ManyToOne(() => RequestQuoteEntity, (p) => p.mediaFiles)
  @JoinColumn({ name: 'requestQuoteId', referencedColumnName: 'id' })
  requestQuote: Promise<RequestQuoteEntity>
}
