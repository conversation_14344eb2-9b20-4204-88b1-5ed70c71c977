import { Column, <PERSON>ti<PERSON>, Jo<PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ContractEntity } from './contract.entity'
import { POEntity } from './po.entity'

/** ListItem trong PO và quyền*/
@Entity({ name: 'po_contract' })
export class POContractEntity extends BaseEntity {
  @Column({
    nullable: true,
  })
  poId: string

  @ManyToOne(() => POEntity, (po) => po.poContract)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  @Column({
    nullable: true,
  })
  contractId: string

  @ManyToOne(() => ContractEntity, (po) => po.poContract)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  // Số lượng item khi lên PO
  @Column({ type: 'int', nullable: true })
  quantityPo: number

  // tổng giá trị PO
  @Column({
    nullable: true,
  })
  totalPO: number

  // ngân sách đã sử dụng
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  usedBudget: string
}
