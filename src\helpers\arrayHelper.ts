class ArrayHelper {
  public groupByArray(data: any, key: any) {
    const groupedObj = data.reduce((prev: any, cur: any) => {
      if (!prev[cur[key]]) {
        prev[cur[key]] = [cur]
      } else {
        prev[cur[key]].push(cur)
      }
      return prev
    }, {})
    return Object.keys(groupedObj).map((Heading) => ({
      heading: Heading,
      list: groupedObj[Heading],
    }))
  }

  public groupByArrayByManyKey(arr: any, lstKey: any[]) {
    const arrR: any[] = []
    const dic: any = {}
    for (const item of arr) {
      let key: any = ''
      for (let i = 0; i < lstKey.length; i++) {
        if (i < lstKey.length - 1) key += `${item[lstKey[i]]}-`
        else key += `${item[lstKey[i]]}`
      }
      let temp = dic[key]
      if (temp) {
        temp.list.push(item)
      } else {
        temp = {
          employeeId: item.employeeId,
          serviceId: item.serviceId,
          list: [item],
        }
        dic[key] = temp
        arrR.push(temp)
      }
    }
    return Object.values(dic)
  }
}

export const arrayHelper = new ArrayHelper()
