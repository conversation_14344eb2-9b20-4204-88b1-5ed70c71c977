import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdatePermisson1748417887921 implements MigrationInterface {
    name = 'UpdatePermisson1748417887921'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "purchasingOrgId" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "purchasingGrId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "purchasingGrId" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "companyOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "companyOrgId" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "blockOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "blockOrgId" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "departmentOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "departmentOrgId" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "partOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "partOrgId" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "positionOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "positionOrgId" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "employeeOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "employeeOrgId" varchar(max)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "employeeId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "employeeId" varchar(max)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "employeeId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "employeeId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "employeeOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "employeeOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "positionOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "positionOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "partOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "partOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "departmentOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "departmentOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "blockOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "blockOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "companyOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "companyOrgId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "purchasingGrId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "purchasingGrId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "purchasingOrgId"`);
        await queryRunner.query(`ALTER TABLE "group_permission" ADD "purchasingOrgId" varchar(255)`);
    }

}
