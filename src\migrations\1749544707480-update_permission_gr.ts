import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdatePermissionGr1749544707480 implements MigrationInterface {
  name = 'UpdatePermissionGr1749544707480'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "group_permission" ADD "poTypeCode" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "group_permission" ADD "prTypeCode" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "group_permission" ADD "fundProgram" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "group_permission" ADD "fundCenter" varchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "fundCenter"`)
    await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "fundProgram"`)
    await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "prTypeCode"`)
    await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "poTypeCode"`)
  }
}
