import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { PlantEntity } from './plant.entity'
import { EmployeeEntity } from './employee.entity'
import { DepartmentEntity } from './department.entity'
import { WarehouseEntity } from './warehouse.entity'
import { BaseEntity } from './base.entity'
import { ReservationItemEntity } from './reservationItem.entity'
import { ReservationItemChildEntity } from './reservationItemChild.entity'
import { GLAccountEntity } from './glAccount.entity'

@Entity('reservation')
export class ReservationEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  sapCode: string

  /** Nguồn tạo enum từ VPP / CNTT */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  sourceType: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Id Của plant */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @ManyToOne(() => PlantEntity, (p) => p.reservations)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /** Id Của plant */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  requisitionerId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.reservations)
  @JoinColumn({ name: 'requisitionerId', referencedColumnName: 'id' })
  requisitioner: Promise<EmployeeEntity>

  /** Bộ phận yêu cầu */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  departmentId: string
  @ManyToOne(() => DepartmentEntity, (p) => p.reservations)
  @JoinColumn({ name: 'departmentId', referencedColumnName: 'id' })
  department: Promise<DepartmentEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  warehouseReceivingId: string
  @ManyToOne(() => WarehouseEntity, (p) => p.receivingReservations)
  @JoinColumn({ name: 'warehouseReceivingId', referencedColumnName: 'id' })
  warehouseReceiving: Promise<WarehouseEntity>

  /** kho nhận */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  warehouseReceivingSloc: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  warehouseIssueId: string
  @ManyToOne(() => WarehouseEntity, (p) => p.issueReservations)
  @JoinColumn({ name: 'warehouseIssueId', referencedColumnName: 'id' })
  warehouseIssue: Promise<WarehouseEntity>

  /** kho xuất  */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  warehouseIssueSloc: string

  /** mục đích sử dụng */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  uses: string

  /** Ghi chú */
  @Column({
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => ReservationItemEntity, (p) => p.reservation)
  reservationItems: Promise<ReservationItemEntity[]>

  /** số phiếu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  reservationNo: string

  /**  Có check ngân sách ? */
  @Column({
    nullable: true,
    default: false,
  })
  isCheckBudget: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isApprovedDone: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  reservationParentId: string

  /** Là reservation  tổng hợp */
  @Column({
    nullable: false,
    default: false,
  })
  isParent: boolean

  @Column({
    length: 'max',
    nullable: true,
  })
  lstReservationChild: string

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstDepartmentId: string

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstRequisitionerId: string

  /** Ngân sách */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  budget: number

  /** số lượng */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstPlantId: string

  @OneToMany(() => ReservationItemChildEntity, (p) => p.reservation)
  reservationItemChilds: Promise<ReservationItemChildEntity[]>

  /** số phiếu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  lstReservationNo: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  glAccountId: string
  @ManyToOne(() => GLAccountEntity, (p) => p.reservations)
  @JoinColumn({ name: 'glAccountId', referencedColumnName: 'id' })
  glAccount: Promise<GLAccountEntity>

  /** io */
  @Column({
    length: 'max',
    nullable: true,
  })
  order: string

  /** Movement Type */
  @Column({
    length: 'max',
    nullable: true,
  })
  movementType: string

  /** Base Date */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  baseDate: Date

}
