import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnContractItem1752205165574 implements MigrationInterface {
    name = 'AddColumnContractItem1752205165574'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "toleranceRange" nvarchar(100)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "toleranceRange"`);
    }

}
