import { BaseEntity } from './base.entity'
import { En<PERSON><PERSON>, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { CriteriaSiteAssessmentChildEntity } from './criteriaSiteAssessmentChild.entity'

@Entity('criteria_site_assessment_list_detail')
export class CriteriaSiteAssessmentListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  criteriaSiteAssessmentChildId: string
  @ManyToOne(() => CriteriaSiteAssessmentChildEntity, (p) => p.criteriaSiteAssessmentListDetails)
  @JoinColumn({ name: 'criteriaSiteAssessmentChildId', referencedColumnName: 'id' })
  criteriaSiteAssessmentChild: Promise<CriteriaSiteAssessmentChildEntity>
}
