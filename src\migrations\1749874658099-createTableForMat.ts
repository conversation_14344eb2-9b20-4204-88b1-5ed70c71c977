import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableForMat1749874658099 implements MigrationInterface {
    name = 'CreateTableForMat1749874658099'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "material_val_type" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5af9f0bf663e7023769f8685d79" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_490f5fe079f1c5da1b1e3fce1f9" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "materialId" uniqueidentifier, "plantId" uniqueidentifier, "materialCode" varchar(250), "plantCode" varchar(250), "valuationType" varchar(10), "lastChange" datetime, "abcIndicator" varchar(250), "valuationClass" varchar(250), "priceControl" nvarchar(50), "price" float, "currencyCode" varchar(50), "currencyId" uniqueidentifier, "priceUnit" float, CONSTRAINT "PK_5af9f0bf663e7023769f8685d79" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "material_storage_location" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f1fc806e854bbc2e34a96023273" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_1148968aa4d3b11263d3d1e47eb" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "materialId" uniqueidentifier, "plantId" uniqueidentifier, "materialCode" varchar(250), "plantCode" varchar(250), "storageLocation" varchar(250), "currentPeriod" float, "dfStorLocLevel" varchar(250), "yearCurrentPeriod" float, "maintenanceStatus" varchar(250), "unrestricted" float, CONSTRAINT "PK_f1fc806e854bbc2e34a96023273" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "material_val_type" ADD CONSTRAINT "FK_bdf5f2c81ab02a474c23434312e" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_val_type" ADD CONSTRAINT "FK_500b57f42defcd4eb859bcc080a" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_val_type" ADD CONSTRAINT "FK_f2bf48cd7e795f0f87f385c67e6" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_storage_location" ADD CONSTRAINT "FK_038b477b6b5836477cd89c3972a" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_storage_location" ADD CONSTRAINT "FK_e01ec339d713a67715e08ee6159" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_storage_location" DROP CONSTRAINT "FK_e01ec339d713a67715e08ee6159"`);
        await queryRunner.query(`ALTER TABLE "material_storage_location" DROP CONSTRAINT "FK_038b477b6b5836477cd89c3972a"`);
        await queryRunner.query(`ALTER TABLE "material_val_type" DROP CONSTRAINT "FK_f2bf48cd7e795f0f87f385c67e6"`);
        await queryRunner.query(`ALTER TABLE "material_val_type" DROP CONSTRAINT "FK_500b57f42defcd4eb859bcc080a"`);
        await queryRunner.query(`ALTER TABLE "material_val_type" DROP CONSTRAINT "FK_bdf5f2c81ab02a474c23434312e"`);
        await queryRunner.query(`DROP TABLE "material_storage_location"`);
        await queryRunner.query(`DROP TABLE "material_val_type"`);
    }

}
