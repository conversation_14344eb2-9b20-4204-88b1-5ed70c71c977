import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTable1750142085237 implements MigrationInterface {
    name = 'CreateTable1750142085237'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD "baseUnitId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD "baseUnitId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD "baseUnitId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD CONSTRAINT "FK_c9cf40690080b8fad00006dff4f" FOREIGN KEY ("baseUnitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD CONSTRAINT "FK_3d43386c2aa0fbb63710217b995" FOREIGN KEY ("baseUnitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_6b5a45398077e11028337176a57" FOREIGN KEY ("baseUnitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_6b5a45398077e11028337176a57"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP CONSTRAINT "FK_3d43386c2aa0fbb63710217b995"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "FK_c9cf40690080b8fad00006dff4f"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP COLUMN "baseUnitId"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP COLUMN "baseUnitId"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP COLUMN "baseUnitId"`);
    }

}
