import { BaseEntity } from './base.entity'
import { Entity, Column, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { InboundEntity } from '.'

/** <PERSON><PERSON> sách bàn giao chứng từ của inbound*/
@Entity('inbound_document_handover')
export class InboundDocumentHandoverEntity extends BaseEntity {
  /** <PERSON><PERSON> chứng từ */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  /** Tiêu đề chứng từ */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  name: string

  /** <PERSON><PERSON><PERSON> chứng từ */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  time: Date

  /** Chứng từ khác */
  @Column({
    type: 'varchar',
    length: 250,

    nullable: true,
  })
  fileAttach: string

  /** ghi chú */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  note: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  inboundId: string
  @ManyToOne(() => InboundEntity, (p) => p.documentHandovers)
  @JoinColumn({ name: 'inboundId', referencedColumnName: 'id' })
  inbound: Promise<InboundEntity>
}
