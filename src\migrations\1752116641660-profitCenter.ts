import { MigrationInterface, QueryRunner } from "typeorm";

export class ProfitC<PERSON>1752116641660 implements MigrationInterface {
    name = 'ProfitCenter1752116641660'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "profit_center" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_64a70157b95c732928b20ae14f9" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_85993a349dc02f18a8b80e4e7ff" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "profitCenterCode" nvarchar(7) NOT NULL, "profitCenterDescription" nvarchar(max) NOT NULL, CONSTRAINT "PK_64a70157b95c732928b20ae14f9" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "profit_center"`);
    }

}
