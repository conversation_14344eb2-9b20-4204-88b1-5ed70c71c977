import { Controller, UseGuards, Post, Body, Get, Param } from '@nestjs/common'
import { JwtAuthGuard } from '../common/guards'
import { BidDealService } from './bidDeal.service'
import { CurrentUser } from '../common/decorators'
import { UserDto } from '../../dto'
import { BidDealCreateDto } from './dto'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'

/** Đàm phán giá */
@ApiBearerAuth()
@ApiTags('Bid')
@UseGuards(JwtAuthGuard)
@Controller('bid_deal')
export class BidDealController {
  constructor(private readonly service: BidDealService) {}

  @ApiOperation({ summary: 'Lấy ds NCC để mời tham gia đàm phán giá' })
  @Post('load_supplier_bid')
  public async loadSupplierBid(@CurrentUser() user: UserDto, @Body() data: { bidId: string; supplierId: string }) {
    return await this.service.loadSupplierBid(user, data)
  }

  @ApiOperation({ summary: 'Lấy ds NCC để mời tham gia đàm phán giá' })
  @Post('load_supplier_deal')
  public async loadSupplierDeal(@CurrentUser() user: UserDto, @Body() data: { bidId: string; supplierId: string }) {
    return await this.service.loadSupplierDeal(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách hạng mục chào giá' })
  @Get('get_price/:bidId')
  public async getPrice(@CurrentUser() user: UserDto, @Param('bidId') bidId: string) {
    return await this.service.getPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy danh sách hạng mục chào giá' })
  @Get('get_report_price/:bidId')
  public async getReportPrice(@CurrentUser() user: UserDto, @Param('bidId') bidId: string) {
    return await this.service.getReportPrice(user, bidId)
  }

  @ApiOperation({ summary: 'Lấy ds NCC để mời tham gia đàm phán giá' })
  @Post('load_supplier_data')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: { bidId: string; statusFile: string[]; name?: string }) {
    return await this.service.loadSupplierData(user, data)
  }

  @ApiOperation({ summary: 'Tạo đàm phán giá Item' })
  @Post('save_bid_deal')
  public async saveBidDeal(@CurrentUser() user: UserDto, @Body() data: BidDealCreateDto) {
    return await this.service.saveBidDeal(user, data)
  }

  @ApiOperation({ summary: 'Tạo đàm phán giá Item' })
  @Post('bid_service')
  public async bidService(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.bidService(user, data)
  }
}
