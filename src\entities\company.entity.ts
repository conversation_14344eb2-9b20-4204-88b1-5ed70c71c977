import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PlantEntity } from './plant.entity'
import { FlowApproveEntity } from './flowApprove.entity'
import { EmployeeEntity } from './employee.entity'
import { DepartmentEntity } from './department.entity'
import { BlockEntity } from './block.entity'
import { SupplierNumberRequestApproveEntity } from './supplierNumberRequestApprove.entity'
import { PurchasingOrgEntity } from './purchasingOrg.entity'
import { PurchasingGroupEntity } from './purchasingGroup.entity'
import { RoundUpContEntity } from './roundUpCont.entity'
import { BusinessPlanEntity } from './businessPlan.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { KpiCompanyEntity } from './kpiCompany.entity'
import { KpiEntity } from './kpi.entity'
import { POEntity } from './po.entity'
import { TicketEvaluationKpiEmployeeEntity } from './ticketEvaluationKpiEmployee.entity'
import { RoleSupplierEntity } from './roleSupplier.entity'
import { SupplierEntity } from './supplier.entity'
import { SupplierCompanyEntity } from './supplierCompany.entity'
import { RequestQuoteEntity } from './requestQuote.entity'
import { ContractEntity } from './contract.entity'
import { RfqEntity } from './rfq.entity'

/**Công ty */
@Entity('company')
export class CompanyEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  shortName: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  address: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  district: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  postCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  country: string

  /**  Công ty là công ty Holding ? */
  @Column({
    nullable: false,
    default: false,
  })
  isHolding: boolean

  /**  Cấp tổ chức công ty ? */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  level: string

  /**  Số điện thoại ? */
  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  phone: string

  /**  Số fax ? */
  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  fax: string

  /**  Số fax ? */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  email: string

  /**  Số fax ? */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  website: string

  /**  thực thuộc công ty ? */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string
  @ManyToOne(() => PurchasingOrgEntity, (p) => p.company)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  @OneToMany(() => PlantEntity, (p) => p.companys)
  plants: Promise<PlantEntity[]>

  @OneToMany(() => FlowApproveEntity, (p) => p.companys)
  flowApprovies: Promise<FlowApproveEntity[]>

  @OneToMany(() => EmployeeEntity, (p) => p.company)
  employee: Promise<EmployeeEntity[]>

  @OneToMany(() => DepartmentEntity, (p) => p.company)
  departments: Promise<DepartmentEntity[]>

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.company)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>

  @OneToMany(() => BlockEntity, (p) => p.company)
  blocks: Promise<BlockEntity[]>

  @OneToMany(() => PurchasingGroupEntity, (p) => p.company)
  purchasingGroups: Promise<PurchasingGroupEntity[]>

  @OneToMany(() => RoundUpContEntity, (p) => p.company)
  roundUpCont: Promise<RoundUpContEntity[]>

  @OneToMany(() => BusinessPlanEntity, (p) => p.company)
  businessPlan: Promise<BusinessPlanEntity[]>

  @OneToMany(() => RecommendedPurchaseEntity, (p) => p.company)
  recommendedPurchase: Promise<RecommendedPurchaseEntity[]>

  /** ds kpi công ty */
  @OneToMany(() => KpiCompanyEntity, (p) => p.company)
  kpiCompanys: Promise<KpiCompanyEntity[]>

  /** ds kpi */
  @OneToMany(() => KpiEntity, (p) => p.company)
  kpis: Promise<KpiEntity[]>

  /** ds PO */
  @OneToMany(() => POEntity, (p) => p.company)
  pos: Promise<POEntity[]>

  /** ds phiếu đánh giá KPI và NV */
  @OneToMany(() => TicketEvaluationKpiEmployeeEntity, (p) => p.company)
  ticketEvaluationKpiEmployees: Promise<TicketEvaluationKpiEmployeeEntity[]>

  @OneToMany(() => RoleSupplierEntity, (p) => p.company)
  roleSuppliers: Promise<RoleSupplierEntity[]>

  @OneToMany(() => SupplierEntity, (p) => p.company)
  suppliers: Promise<SupplierEntity[]>

  @OneToMany(() => SupplierCompanyEntity, (p) => p.company)
  supplierCompanies: Promise<SupplierCompanyEntity[]>

  @OneToMany(() => RequestQuoteEntity, (p) => p.company)
  requestQuotes: Promise<RequestQuoteEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.company)
  contracts: Promise<ContractEntity[]>

  @OneToMany(() => RfqEntity, (p) => p.company)
  rfqs: Promise<RfqEntity[]>
}
