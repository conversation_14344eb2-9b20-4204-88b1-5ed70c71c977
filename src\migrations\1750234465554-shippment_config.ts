import { MigrationInterface, QueryRunner } from 'typeorm'

export class ShippmentConfig1750234465554 implements MigrationInterface {
  name = 'ShippmentConfig1750234465554'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" DROP COLUMN "dateFrom"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" DROP COLUMN "dateTo"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" DROP COLUMN "price"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" DROP COLUMN "conditionCompact"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" ADD "shipmentFeeConditionId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" ADD "shipmentFeeConditionListId" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" DROP COLUMN "shipmentFeeConditionListId"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" DROP COLUMN "shipmentFeeConditionId"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" ADD "conditionCompact" nvarchar(MAX)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" ADD "price" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" ADD "dateTo" datetime`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template_value" ADD "dateFrom" datetime`)
  }
}
