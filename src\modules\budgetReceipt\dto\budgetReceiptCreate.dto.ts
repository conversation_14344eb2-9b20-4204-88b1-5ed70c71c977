import { IsOptional } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class BudgetReceiptCreateDto {
  @ApiProperty({ description: 'Id Pr' })
  prId: string

  @ApiProperty({ description: 'Id Po' })
  poId: string

  @ApiProperty({ description: 'Mã' })
  code: string

  @ApiProperty({ description: '<PERSON><PERSON>y phiếu' })
  @IsOptional()
  date: Date

  @ApiProperty({ description: 'Kỳ ngân sách' })
  @IsOptional()
  dateBudget: Date

  @ApiProperty({ description: 'Công ty' })
  @IsOptional()
  companyId: string

  @ApiProperty({ description: 'Ngân sách liên công ty' })
  @IsOptional()
  isMultiCompany: boolean

  @ApiProperty({ description: 'Loại đề xuất' })
  @IsOptional()
  proposeType: string

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> chứng từ' })
  @IsOptional()
  receiptType: string

  @ApiProperty({ description: 'Id N<PERSON>ân viên đề xuất' })
  @IsOptional()
  employeeId: string

  @ApiProperty({ description: 'Phòng QLNS' })
  @IsOptional()
  departmentQLNS: string

  @ApiProperty({ description: 'Đơn vị tiền tệ' })
  @IsOptional()
  currencyId: string

  @ApiProperty({ description: 'Đơn vị tiền tệ' })
  @IsOptional()
  currency: string

  @ApiProperty({ description: 'Diễn giải' })
  @IsOptional()
  interpretation: string

  @ApiProperty({ description: 'Tệp đính kèm' })
  @IsOptional()
  fileAttachmentUrl: string

  @IsOptional()
  items: BudgetItemCreateDto[]
}

export class BudgetItemCreateDto {
  @ApiProperty({ description: 'Điều chỉnh tăng' })
  @IsOptional()
  isIncrease: boolean

  @ApiProperty({ description: 'funCenter' })
  @IsOptional()
  funCenter: string

  @ApiProperty({ description: 'Mã CI' })
  @IsOptional()
  CICode: string

  @ApiProperty({ description: 'Tên CI' })
  @IsOptional()
  CIName: string

  @ApiProperty({ description: 'Số tiền khả dụng' })
  @IsOptional()
  moneyAvailable: number

  @ApiProperty({ description: 'Số tiền đề xuất' })
  @IsOptional()
  moneyPropose: number

  @ApiProperty({ description: 'Cơ sở điều chỉnh' })
  @IsOptional()
  baseAdjust: string

  @ApiProperty({ description: 'Lý do điều chỉnh' })
  @IsOptional()
  reasonAdjust: string

  @ApiProperty({ description: 'funCenter' })
  @IsOptional()
  fundCenter: string

  @ApiProperty({ description: 'funCenter' })
  @IsOptional()
  ci: string

  @ApiProperty({ description: 'funCenter' })
  @IsOptional()
  ciName: string

  @ApiProperty({ description: 'funCenter' })
  @IsOptional()
  fp: string

  @ApiProperty({ description: 'funCenter' })
  @IsOptional()
  fc: string

  @ApiProperty({ description: 'funCenter' })
  @IsOptional()
  budgetperiod: string
}
