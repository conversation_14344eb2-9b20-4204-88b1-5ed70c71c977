import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BusinessPlanRepository, BusinessPlanTemplateColRepository, BusinessPlanTemplateRepository, EmployeeRepository } from '../../repositories'
import { BusinessPlanTemplateController } from './businessPlanTemplate.controller'
import { BusinessPlanTemplateService } from './businessPlanTemplate.service'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BusinessPlanTemplateRepository,
      BusinessPlanTemplateColRepository,
      EmployeeRepository,
      BusinessPlanRepository,
    ]),
    OrganizationalPositionModule,
  ],
  controllers: [BusinessPlanTemplateController],
  providers: [BusinessPlanTemplateService],
  exports: [BusinessPlanTemplateService],
})
export class BusinessPlanTemplateModule {}
