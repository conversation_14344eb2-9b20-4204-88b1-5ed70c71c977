import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { RfqEntity } from './rfq.entity'

@Entity('recommended_purchase_rfq')
export class RecommendedPurchaseRfqEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseId: string
  @ManyToOne(() => RecommendedPurchaseEntity, (p) => p.recommendedPurchaseRfq)
  @JoinColumn({ name: 'recommendedPurchaseId', referencedColumnName: 'id' })
  recommendedPurchase: Promise<RecommendedPurchaseEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  rfqId: string
  @ManyToOne(() => RfqEntity, (p) => p.recommendedPurchaseRfq)
  @JoinColumn({ name: 'rfqId', referencedColumnName: 'id' })
  rfq: Promise<RfqEntity>
}
