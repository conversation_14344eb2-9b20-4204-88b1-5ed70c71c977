import { Column, <PERSON>tity, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { SupplierServiceEntity } from './supplierService.entity'

/** <PERSON><PERSON>u cầu chỉnh sửa nhà cung cấp của supplier portal */
@Entity('request_update_supplier')
export class RequestUpdateSupplierEntity extends BaseEntity {
  /** Mã */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Tên */
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  name: string

  /**  Loại điều chỉnh */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  updateType: string

  /**  Lý do điều chỉnh */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  reasonUpdate: string

  /** chỉnh sửa pháp lý */
  @Column({
    nullable: true,
    default: false,
  })
  isPL: boolean

  /** chỉnh sửa năng lực */
  @Column({
    nullable: true,
    default: false,
  })
  isNL: boolean

  /** Cập nhật khóa /  mở khóa nhà cung cấp   */
  @Column({
    nullable: true,
    default: false,
  })
  isLockSupplier: boolean

  /** Cập nhật khóa /  mở khóa lĩnh vực mua hàng của nhà cung cấp   */
  @Column({
    nullable: true,
    default: false,
  })
  isLockSupplierService: boolean

  /* Loại Khóa/mở khóa: NCC và LVKD */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  lockType: string

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  status: string

  /** JSON thông tin cập nhật pháp lý */
  @Column({
    length: 'max',
    nullable: true,
  })
  jsonLaw: string

  /** JSON thông tin cập nhật năng lực */
  @Column({
    length: 'max',
    nullable: true,
  })
  jsonCapacity: string

  /** yêu cầu đã được duyệt hay chưa */
  @Column({
    nullable: false,
    default: false,
  })
  isApproved: boolean

  /** JSON chứa dữ liệu cũ trước khi yêu cầu chỉnh sửa*/
  @Column({
    length: 'max',
    nullable: true,
  })
  oldJson: string

  /** JSON chứa dữ liệu mới sau khi yêu cầu chỉnh sửa được duyệt xong */
  @Column({
    length: 'max',
    nullable: true,
  })
  newJson: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.requestUpdateSuppliers)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierServiceId: string
  @ManyToOne(() => SupplierServiceEntity, (p) => p.requestUpdateSuppliers)
  @JoinColumn({ name: 'supplierServiceId', referencedColumnName: 'id' })
  supplierService: Promise<SupplierServiceEntity>
}
