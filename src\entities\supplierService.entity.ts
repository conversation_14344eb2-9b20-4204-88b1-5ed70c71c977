import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { ServiceEntity } from './service.entity'
import { SupplierEntity } from './supplier.entity'
import { SupplierCapacityEntity } from './supplierCapacity.entity'
import { SupplierExpertiseEntity } from './supplierExpertise.entity'
import { RequestUpdateSupplierEntity } from './requestUpdateSupplier.entity'
import { SupplierServiceHistoryEntity } from './supplierServiceHistory.entity'
import { PaymentMethodEntity } from './paymentMethod.entity'
import { PaymentTermEntity } from './paymentTerm.entity'
import { SupplierProductServiceEntity } from './supplierProductService.entity'
import { SupplierServiceFactoryEntity } from './supplierServiceFactory.entity'
import { SupplierProductLineEntity } from './supplierProductLine.entity'
import { SupplierCertificateEntity } from './supplierCertificate.entity'
import { SupplierCompanyEntity } from './supplierCompany.entity'

@Entity('supplier_service')
export class SupplierServiceEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  score: number

  @Column({
    nullable: true,
    type: 'datetime',
  })
  approveDate: Date

  /** Trạng thái LVMH của NCC */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: 'ChuaThamDinh',
  })
  statusExpertise: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  comment: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  approverComment: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.supplierServices)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.supplierServices)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Tổng doanh thu (cộng dồn khi trúng thầu hoặc khởi tạo qua hàm gen lại cho data cũ) */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  totalPrice: number

  // Phân loại Doanh nghiệp
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  supplierType: string

  // Điểm năng lực cũ
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  oldScore: number

  @OneToMany(() => SupplierCapacityEntity, (p) => p.supplierService)
  capacities: Promise<SupplierCapacityEntity[]>

  @OneToMany(() => SupplierExpertiseEntity, (p) => p.supplier)
  supplierExpertise: Promise<SupplierExpertiseEntity[]>

  /** Đã Gởi duyệt hoạt động/ngưng hoạt động lĩnh vực mua hàng của nhà cung cấp chưa? */
  @Column({
    nullable: true,
    default: false,
  })
  isApproveActive: boolean

  /**yêu cầu chỉnh sửa lĩnh vực kinh doanh */
  @OneToMany(() => RequestUpdateSupplierEntity, (p) => p.supplierService)
  requestUpdateSuppliers: Promise<RequestUpdateSupplierEntity[]>

  /** Trạng thái duyệt chỉnh sửa NCC enum RequestUpdateStatusSupplierService */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  requestUpdateStatus: string

  @OneToMany(() => SupplierServiceHistoryEntity, (p) => p.supplierService)
  supplierServiceHistories: Promise<SupplierServiceHistoryEntity[]>

  /**Lý do từ chối năng lực */
  @Column({
    type: 'nvarchar',
    length: 400,
    nullable: true,
  })
  reasonRejectCapacity: string

  /** Phương thức thanh toán */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentMethodId: string
  @ManyToOne(() => PaymentMethodEntity, (p) => p.supplierServices)
  @JoinColumn({ name: 'paymentMethodId', referencedColumnName: 'id' })
  paymentMethod: Promise<PaymentMethodEntity>

  /** Thời hạn thanh toán */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentTermId: string
  @ManyToOne(() => PaymentTermEntity, (p) => p.supplierServices)
  @JoinColumn({ name: 'paymentTermId', referencedColumnName: 'id' })
  paymentTerm: Promise<PaymentTermEntity>

  // Người quyết định
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  decider: string

  /**Chức vụ Người quyết định */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  deciderPosition: string

  /**Số điện thoại Người quyết định */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  deciderPhone: string

  /**Số Fax Người quyết định  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  deciderFax: string

  /** Email Người quyết định  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  deciderEmail: string

  /** Lưu ý về Người quyết định */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  deciderNote: string

  // Người giao dịch
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  trader: string

  /**Chức vụ Người giao dịch */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  traderPosition: string

  /**Số điện thoại Người giao dịch */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  traderPhone: string

  /**Số Fax Người giao dịch  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  traderFax: string

  /** Email Người giao dịch  */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  traderEmail: string

  /** Lưu ý về Người giao dịch */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  traderNote: string

  /** Có chuẩn Iso không */
  @Column({
    nullable: true,
    default: false,
  })
  isISO: boolean

  @OneToMany(() => SupplierProductServiceEntity, (p) => p.supplierService)
  productServices: Promise<SupplierProductServiceEntity[]>

  @OneToMany(() => SupplierServiceFactoryEntity, (p) => p.supplierService)
  serviceFacilities: Promise<SupplierServiceFactoryEntity[]>

  @OneToMany(() => SupplierProductLineEntity, (p) => p.supplierService)
  supplierProductLines: Promise<SupplierProductLineEntity[]>

  @OneToMany(() => SupplierCertificateEntity, (p) => p.supplierService)
  supplierCertificates: Promise<SupplierCertificateEntity[]>

  /** LVKD này được đăng kí lần đầu với công ty và supplier mới: bỏ qua bước xác nhận*/
  @Column({
    nullable: true,
    default: false,
  })
  isFirstRegister: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierCompanyId: string
  @ManyToOne(() => SupplierCompanyEntity, (p) => p.supplierServices)
  @JoinColumn({ name: 'supplierCompanyId', referencedColumnName: 'id' })
  supplierCompany: Promise<SupplierCompanyEntity>

  /** Mã nhà cung cấp */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  supplierCode: string
}
