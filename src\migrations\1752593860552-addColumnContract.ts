import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnContract1752593860552 implements MigrationInterface {
    name = 'AddColumnContract1752593860552'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "reason" nvarchar(450)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "reason" nvarchar(450)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "reason"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "reason"`);
    }

}
