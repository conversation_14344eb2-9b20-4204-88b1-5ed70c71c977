import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { CurrentUser } from '../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { JwtAuthGuard } from '../common/guards'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { BudgetReceiptService } from './budgetReceipt.service'
import { BudgetReceiptCreateDto, BudgetReceiptUpdateDto } from './dto'

@ApiBearerAuth()
@ApiTags('BudgetReceipt')
@UseGuards(JwtAuthGuard)
@Controller('budget_receipt')
export class BudgetReceiptController {
  constructor(private readonly service: BudgetReceiptService) {}

  @ApiOperation({ summary: 'L<PERSON>y danh sách phiếu điều chỉnh ngân sách' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.find(user, data)
  }

  @ApiOperation({ summary: 'Danh sách phiếu điều chỉnh ngân sách phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo phiếu điều chỉnh ngân sách' })
  @Post('create_data')
  public async createData(@CurrentUser() user: UserDto, @Body() data: BudgetReceiptCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Tạo phiếu điều chỉnh ngân sách cho po' })
  @Post('create_data_budget_po')
  public async createDataBudGetPo(@CurrentUser() user: UserDto, @Body() data: BudgetReceiptCreateDto) {
    return await this.service.createDataBudGetPo(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật phiếu điều chỉnh ngân sách' })
  @Post('update_data')
  public async updateData(@CurrentUser() user: UserDto, @Body() data: BudgetReceiptUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Chi tiết phiếu điều chỉnh ngân sách' })
  @Post('find_detail')
  public async findDetail(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.findDetail(user, data)
  }

  @ApiOperation({ summary: 'Sinh mã ngân sách' })
  @Post('gen_code')
  async genCodeBudget() {
    return this.service.genCodeBudget()
  }

  @ApiOperation({ summary: 'API XÓA' })
  @Post('update_delete')
  public async updateDelete(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateDelete(user, data)
  }

  @ApiOperation({ summary: 'API gửi duyệt' })
  @Post('update_send')
  public async updateSend(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateSend(user, data)
  }

  @ApiOperation({ summary: 'API gửi duyệt' })
  @Post('update_approved')
  public async updateApproved(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateApproved(user, data)
  }

  @ApiOperation({ summary: 'API gửi duyệt' })
  @Post('update_reject')
  public async updateReject(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateReject(user, data)
  }
}
