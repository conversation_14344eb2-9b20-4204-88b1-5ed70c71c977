import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class BillLookupCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  nameEN: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  link: string

  @ApiPropertyOptional()
  abbreviation: string

  abbreviationEN: string

  @ApiPropertyOptional()
  description: string

  descriptionEN: string
}
