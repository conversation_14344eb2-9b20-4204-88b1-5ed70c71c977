import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTotalEmployee1749549117540 implements MigrationInterface {
    name = 'AddTotalEmployee1749549117540'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material" ADD "totalEmployee" int CONSTRAINT "DF_6358e9bf8aea81f257020141be6" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_6358e9bf8aea81f257020141be6"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "totalEmployee"`);
    }

}
