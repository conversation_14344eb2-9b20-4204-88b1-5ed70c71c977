import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { BidEntity } from './bid.entity'
import { BidPrItemEntity } from './bidPrItem.entity'
import { BidExMatGroupEntity } from './bidExgroup.entity'

@Entity('bid_custom_price')
export class BidCustomPriceEntity extends BaseEntity {
  /** Số lượng */
  @Column({
    nullable: false,
    default: 0,
  })
  number: number

  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  /** Tên  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** <PERSON><PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** Kiểu dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: false,
    default: 'Number',
  })
  type: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unit: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currency: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidId: string
  /** 1 công thức chỉ có 1 dịch vụ cha */
  @ManyToOne(() => BidEntity, (p) => p.customPrices)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidExgroupId: string
  /** 1 công thức chỉ có 1 dịch vụ cha */
  @ManyToOne(() => BidExMatGroupEntity, (p) => p.customPrices)
  @JoinColumn({ name: 'bidExgroupId', referencedColumnName: 'id' })
  bidExgroup: Promise<BidExMatGroupEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidItemId: string
  @ManyToOne(() => BidPrItemEntity, (p) => p.trades)
  @JoinColumn({ name: 'bidItemId', referencedColumnName: 'id' })
  bidItem: Promise<BidPrItemEntity>
}
