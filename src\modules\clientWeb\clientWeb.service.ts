import { Injectable, Req } from '@nestjs/common'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { SupplierCreateCustomPriceItemDto, SupplierCreatePriceItemDto, SupplierCreateTechItemDto, SupplierCreateTradeItemDto } from '../bid/dto'
import { BidDetailService } from '../bidDetail/bidDetail.service'
import { SupplierService } from '../supplier/supplier.service'
import { BidDealService } from '../bidDeal/bidDeal.service'
import { BidAuctionService } from '../bidAuction/bidAuction.service'
import { BidAuctionSupplierSaveDto } from '../bidAuction/dto'
import { FaqService } from '../faq/faq.service'
import { FaqCategoryService } from '../faqCategory/faqCategory.service'
import { BidDealSupplierSaveDto } from '../bidDeal/dto'
import { POService } from '../po/po.service'
import { POUpdateDeliveryDateDto, POUpdateStatusDto } from '../po/dto'
import { BidClientService } from '../bid/bidClient.service'
import { OfferClientService } from '../offer/offerClient.service'
import { ContractClientService } from '../contract/contractClient.service'
import { PrService } from '../pr/pr.service'
import { ContractAppendixClientService } from '../contractAppendix/contractAppendixClient.service'
import { InboundClientService } from '../inbound/services/inboundClient.service'
import { InboundClientCreateDto } from '../inbound/dto/inboundClientCreate.dto'
import { OfferDealService } from '../offerDeal/offerDeal.service'
import { AuctionService } from '../auction/auction.service'
import { RecommendedPurchaseClientService } from '../recommendedPurchase/recommendedPurchaseClient.service'
import { CompanyService } from '../company/company.service'
import { PaymentMethodService } from '../paymentMethod/paymentMethod.service'
import { PaymentTermService } from '../paymentTerm/paymentTerm.service'
import { CurrencyService } from '../curency/currency.service'

@Injectable()
export class ClientWebService {
  constructor(
    private supplierService: SupplierService,
    private bidDetailService: BidDetailService,
    private bidDealService: BidDealService,
    private offerDealService: OfferDealService,
    private bidAuctionService: BidAuctionService,
    private faqService: FaqService,
    private faqCategoryService: FaqCategoryService,
    private pOService: POService,
    private prService: PrService,
    private bidClientService: BidClientService,
    private offerClientService: OfferClientService,
    private contractService: ContractClientService,
    private appendixClientService: ContractAppendixClientService,
    private inboundService: InboundClientService,
    private auctionervice: AuctionService,
    private supplierSevice: SupplierService,
    private recommendedPurService: RecommendedPurchaseClientService,
    private companyService: CompanyService,
    private paymentMethodService: PaymentMethodService,
    private paymentTermService: PaymentTermService,
    private currencyService: CurrencyService,
  ) {}

  //#region Supplier

  /** Kiểm tra quyền xem kết quả thẩm định */
  async checkIsYourExpertise(user: UserDto, data: { supplierExpertiseId: string }) {
    return await this.supplierService.checkIsYourExpertise(user, data)
  }

  /** Lấy dữ liệu mà MPO đề xuất cho nhà cung cấp */
  async getDataSuggest(user: UserDto, data: { supplierExpertiseId: string }) {
    return await this.supplierService.getDataSuggest(user, data)
  }

  async supplierAcceptChangeData(user: UserDto, data: { supplierExpertiseId: string }) {
    return await this.supplierService.supplierAcceptChangeData(user, data)
  }
  //#endregion

  //#region Bid
  async paginationHomePage(@Req() req: Request, data: PaginationDto, lan?: string) {
    return await this.bidClientService.paginationHomePage(req, data, lan)
  }

  async paginationHomePageHadToken(user: UserDto, data: PaginationDto, lan?: string) {
    return await this.bidClientService.paginationHomePageHadToken(user, data, lan)
  }

  async loadDataBidding(data: { bidId: string }, user: UserDto) {
    return await this.bidClientService.loadDataBidding(data, user)
  }

  async bidDetailHadToken(data: { bidId: string }, user: UserDto) {
    return await this.bidClientService.bidDetailHadToken(data, user)
  }

  async isDisplayBtnAcceptBid(user: UserDto, data: { bidId: string }) {
    return await this.bidClientService.isDisplayBtnAcceptBid(data.bidId, user)
  }

  async isDisplayBtnBid(user: UserDto, data: { bidId: string }) {
    return await this.bidClientService.isDisplayBtnBid(data.bidId, user)
  }

  async acceptBid(user: UserDto, data: { bidId: string }) {
    return await this.bidClientService.acceptBid(data.bidId, user)
  }

  async rejectBid(user: UserDto, data: { bidId: string }) {
    return await this.bidClientService.rejectBid(data.bidId, user)
  }

  async createBidSupplier(
    user: UserDto,
    data: {
      bidId: string
      techInfo: SupplierCreateTechItemDto[]
      tradeInfo: SupplierCreateTradeItemDto[]
      priceShipmentInfo: any[]
      priceInfo: any[]
      customPriceInfo: any[]
    },
  ) {
    return await this.bidClientService.createBidSupplier(user, data)
  }

  async checkIsShipment(
    user: UserDto,
    data: {
      bidId: string
    },
  ) {
    return await this.bidClientService.checkIsShipment(user, data)
  }

  async createOfferSupplier(
    user: UserDto,
    data: {
      fileAttach: string
      fileTech: string
      filePrice: string
      linkDrive: string
      note: string
      bidId: string
      fromAdmin: boolean
      tradeInfo: SupplierCreateTradeItemDto[]
      priceInfo: SupplierCreatePriceItemDto[]
      customPriceInfo: SupplierCreateCustomPriceItemDto[]
      priceShipmentInfo: any[]
    },
  ) {
    return await this.offerClientService.createOfferSupplier(user, data)
  }

  /** Check quyền truy cập gói thầu của Doanh nghiệp */
  async checkPermissionLoadDataBid(user: UserDto, data: { bidId: string }) {
    return await this.bidClientService.checkPermissionLoadDataBid(data?.bidId, user)
  }

  async loadDataBidTech(user: UserDto, data: { bidId: string }) {
    return await this.bidClientService.loadDataBidTech(user, data)
  }

  async loadDataTradeShipment(user: UserDto, data: { bidId: string }) {
    return await this.bidClientService.loadDataTradeShipment(user, data)
  }

  async loadDataBidTrade(user: UserDto, data: { bidId: string }) {
    return await this.bidClientService.loadDataBidTrade(user, data)
  }

  async loadDataOfferTrade(user: UserDto, data: { bidId: string }) {
    return await this.offerClientService.loadDataOfferTrade(user, data)
  }

  async loadDataOfferTradeView(user: UserDto, data: { bidId: string; supplierId?: string }) {
    return await this.offerClientService.loadDataOfferTradeView(user, data)
  }

  async loadDataBidPrice(user: UserDto, data: { bidId: string }) {
    return await this.bidClientService.loadDataBidPrice(user, data)
  }

  async loadDataOfferPrice(user: UserDto, data: { bidId: string }) {
    return await this.offerClientService.loadDataOfferPrice(user, data)
  }

  async loadDataOfferPriceView(user: UserDto, data: { bidId: string }) {
    return await this.offerClientService.loadDataOfferPriceView(user, data)
  }

  async loadDataBidCustomPrice(user: UserDto, data: { bidId: string }) {
    return await this.bidClientService.loadDataBidCustomPrice(user, data)
  }

  async loadDataOfferCustomPrice(user: UserDto, data: { bidId: string }) {
    return await this.offerClientService.loadDataOfferCustomPrice(user, data)
  }

  async loadDataOfferCustomPriceView(user: UserDto, data: { bidId: string }) {
    return await this.offerClientService.loadDataOfferCustomPriceView(user, data)
  }

  /** Lịch sử đấu thầu Doanh nghiệp */
  async paginationBidHistory(user: UserDto, data: PaginationDto) {
    return await this.bidClientService.paginationBidHistory(user, data)
  }

  /** Check quyền nộp giá bổ sung cho gói thầu của Doanh nghiệp */
  async checkPermissionJoinResetPrice(user: UserDto, data: { bidId: string }) {
    return await this.bidClientService.checkPermissionJoinResetPrice(data?.bidId, user)
  }

  async supplierSaveResetPrice(
    user: UserDto,
    data: {
      bidId: string
      dataInfo: { filePriceDetail?: string; fileTechDetail?: string }
      priceInfo: SupplierCreatePriceItemDto[]
    },
  ) {
    return await this.bidClientService.supplierSaveResetPrice(user, data)
  }
  //#endregion

  //#region BidDetail
  /** Danh sách kế hoạch mua hàng có phân trang */
  async getBidHistoryPrice(user: UserDto, bidId: string) {
    return await this.bidDetailService.getBidHistoryPrice(user, bidId)
  }
  //#endregion

  //#region BidDeal

  /** Lấy thông tin đàm phán giá NCC */
  async getBidDealSupplier(user: UserDto, bidDealId: string) {
    return await this.bidDealService.getBidDealSupplier(user, bidDealId)
  }

  async getOfferDealSupplier(user: UserDto, bidDealId: string) {
    return await this.offerDealService.getBidDealSupplier(user, bidDealId)
  }

  /** Đề nghị đàm phán giá/ Chấp nhận giá đề nghị/ Lưu */
  async acceptBidDealSupplier(user: UserDto, data: BidDealSupplierSaveDto) {
    return await this.bidDealService.acceptBidDealSupplier(user, data)
  }

  async acceptOfferDealSupplier(user: UserDto, data: BidDealSupplierSaveDto) {
    return await this.offerDealService.acceptBidDealSupplier(user, data)
  }

  /** Từ chối giá đề nghị */
  async rejectBidDealSupplier(user: UserDto, bidDealId: string) {
    return await this.bidDealService.rejectBidDealSupplier(user, bidDealId)
  }
  async rejectOfferDealSupplier(user: UserDto, bidDealId: string) {
    return await this.offerDealService.rejectBidDealSupplier(user, bidDealId)
  }

  /** Lấy kết quả đàm phán */
  async checkResultMessage(user: UserDto, bidDealId: string) {
    return await this.bidDealService.checkResultMessage(user, bidDealId)
  }

  async checkResultMessageOffer(user: UserDto, bidDealId: string) {
    return await this.offerDealService.checkResultMessage(user, bidDealId)
  }

  //#endregion

  //#region BidAuction

  /** Lấy thông tin đấu giá NCC */
  async getBidAuctionSupplier(user: UserDto, bidAuctionId: string) {
    return await this.bidAuctionService.getBidAuctionSupplier(user, bidAuctionId)
  }

  /** NCC nộp đấu giá của mình */
  async supplierSaveAuction(user: UserDto, data: BidAuctionSupplierSaveDto) {
    await this.bidAuctionService.supplierSaveAuction(user, data)
  }

  // async getCurrentRank(user: UserDto, bidId: string) {
  //   return await this.bidAuctionService.getCurrentRank(user, bidId)
  // }

  /** Danh sách PO */
  async paginationSupplier(user: UserDto, data: PaginationDto) {
    return await this.pOService.paginationSupplier(user, data)
  }

  /** Nhà cung cấp cập nhật xác nhận giao hàng */
  async updateStatusDelivery(user: UserDto, data: { id: string }) {
    return await this.pOService.updateStatusDelivery(user, data)
  }

  /** Nhà cung cấp cập nhật ngày giao hàng */
  async updateDeliveryDate(user: UserDto, data: POUpdateDeliveryDateDto) {
    return await this.pOService.updateDeliveryDate(user, data)
  }
  /** Nhà cung cấp cập nhật ngày giao hàng */
  async updateStatus(user: UserDto, data: POUpdateStatusDto) {
    return await this.pOService.updateStatus(user, data)
  }

  /** Nhà cung cấp cập nhật xác nhận PO */
  async updateStatusConfirm(user: UserDto, data: { id: string }) {
    return await this.pOService.updateStatusConfirm(user, data)
  }

  /** Nhà cung cấp cập nhật từ chối giao hàng xác nhận PO */
  async updateStatusSupplierRefuse(user: UserDto, data: { id: string; reason: string }) {
    return await this.pOService.updateStatusSupplierRefuse(user, data)
  }

  /** Chi tiết của PO theo nhà cung cấp */
  async findDetailPO(user: UserDto, data: { id: string }) {
    return await this.pOService.findDetailPo(user, data)
  }
  /** Load danh sách item */
  async loadListProductClient(user: UserDto, data: FilterOneDto) {
    return await this.pOService.loadListProductClient(user, data)
  }

  /** Cập nhật trạng thái thực hiện PO */
  async updateStatusOrderClient(user: UserDto, data: { id: string; orderStatus: string; reason: string }) {
    return await this.pOService.updateStatusOrderClient(user, data)
  }
  /** Lấy danh sách PO */
  async findPoClient(user: UserDto, data: { contractId?: string; status?: any }) {
    return await this.pOService.findPoClient(user, data)
  }

  //#endregion

  //#region FAQ
  public async getFAQHomePage(user: UserDto, id: string) {
    return await this.faqService.getFAQHomePage(user, id)
  }

  public async getFAQCategoryHomePage(user: UserDto, lan?: string) {
    return await this.faqCategoryService.getFAQCategoryHomePage(user, lan)
  }

  public async findFaq(user: UserDto, data: { categoryId?: string }) {
    return await this.faqService.find(user, data)
  }
  //#endregion

  /**Contract */

  /** Danh sách Contract theo nhà cung cấp */
  async paginationContractSupplier(data: PaginationDto, user: UserDto) {
    return await this.contractService.pagination(data, user)
  }

  /** Chi tiết của Contract theo nhà cung cấp */
  async findDetailContract(user: UserDto, data: { id: string }) {
    return await this.contractService.findDetail(user, data)
  }

  /** Chi tiết của Contract theo nhà cung cấp */
  async find(user: UserDto, data: { status?: string }) {
    return await this.contractService.find(user, data)
  }

  /** DS của Pr */
  async findClient(user: UserDto, data: FilterOneDto) {
    return await this.prService.findClient(user, data)
  }

  /**Contract Appendix */

  /** Danh sách  phụ lục hợp đồng */
  async paginationContractAppendix(data: PaginationDto, user: UserDto) {
    return await this.appendixClientService.pagination(data, user)
  }
  /** chi tiết inbound */
  async loadDetail(user: UserDto, data: FilterOneDto) {
    return await this.inboundService.loadDetail(user, data)
  }

  /** danh sách item của hợp đồng */
  async loadListItem(user: UserDto, data: { contractId: string }) {
    return await this.contractService.loadListItem(user, data)
  }

  /** Thêm mới inbound */
  async createDataPoInbound(user: UserDto, data: InboundClientCreateDto) {
    return await this.inboundService.createDataPoInbound(user, data)
  }

  /**External Mat category */

  /** Danh sách đấu giá */
  async findAuction(user: UserDto, data: { status?: string }) {
    return await this.auctionervice.findClient(user, data)
  }

  /** Danh sách  nhà cung cấp select box */
  async findSelectSuplier(user: UserDto, data: {}) {
    return await this.supplierSevice.loadData(user, data)
  }

  async RePurloadListPr(user: UserDto, data: { recommendedPurchaseId: string }) {
    return await this.recommendedPurService.loadListPr(user, data)
  }

  async RePurloadListExmatGroup(user: UserDto, data: { recommendedPurchaseId: string }) {
    return await this.recommendedPurService.loadListExmatGroup(user, data)
  }

  async findDataCompany(user: UserDto, data: any) {
    return await this.companyService.dataSelectBox(user, data)
  }
  /* lòa payment method */
  async findDataPaymentMethod(user: UserDto, data: any) {
    return await this.paymentMethodService.loadDataSelect(user)
  }
  async findDataPaymentTerm(user: UserDto, data: any) {
    return await this.paymentTermService.loadDataSelect(user)
  }
  async findCurrency(user: UserDto, data: any) {
    return await this.currencyService.dataSelectBox(user)
  }
}
