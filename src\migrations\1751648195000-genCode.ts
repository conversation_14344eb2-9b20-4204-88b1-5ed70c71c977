import { MigrationInterface, QueryRunner } from "typeorm";

export class GenCode1751648195000 implements MigrationInterface {
    name = 'GenCode1751648195000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "leadtime_detail" ADD "materialCode" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "leadtime_detail" DROP COLUMN "materialCode"`);
    }

}
