import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, OneToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, BeforeInsert, BeforeUpdate, AfterLoad } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { EmployeeEntity } from './employee.entity'
import { UserConfirmCodeEntity } from './userConfirmCode.entity'
import { PWD_SALT_ROUNDS } from '../constants'
import { compare, hash } from 'bcrypt'
import { UserExternalMaterialGroupEntity } from './userExternalMaterialGroup.entity'

/** Danh sách tài khoản */
@Entity({ name: 'user' })
export class UserEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  username: string

  @Column({
    name: 'password',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  password: string

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password) {
      const hashedPassword = await hash(this.password, PWD_SALT_ROUNDS)
      this.password = hashedPassword
    }
  }

  comparePassword(candidate: string) {
    return compare(candidate, this.password)
  }

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fcmToken: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  deviceToken: string

  /** Loại user: enum UserType */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  /** Nhà cung cấp */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @OneToOne(() => SupplierEntity, (p) => p.user)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: SupplierEntity

  /* Base userId */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  baseUserId: string

  /** Nhân viên */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @OneToOne(() => EmployeeEntity, (p) => p.user)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: EmployeeEntity

  /** JSON DS quyền của user */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  roles: string

  /** JSON DS quyền của user */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  queryParams: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  viewPermission: string

  @Column({
    nullable: false,
    default: false,
  })
  roleUpdated: boolean

  @Column({
    nullable: false,
    default: false,
  })
  roleViewUpdated: boolean

  /* công ty hiện tại nhân viên đang login */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currentCompanyId: string

  /** DS mã xác nhận của user */
  @OneToMany(() => UserConfirmCodeEntity, (p) => p.user)
  userConfirm: Promise<UserConfirmCodeEntity[]>

  @OneToMany(() => UserExternalMaterialGroupEntity, (p) => p.user)
  userExGr: Promise<UserExternalMaterialGroupEntity[]>

  lstUserExGr: UserExternalMaterialGroupEntity[]

  @AfterLoad()
  async getRelationData() {
    const resultData: any = this
    if (resultData.__userExGr__) {
      resultData.userExGrDetail = resultData.__userExGr__
      resultData.serviceName = ''
      for (const item of resultData.__userExGr__) {
        if (item?.__externalMaterialGroup__) {
          item.externalMaterialGroupDetail = item?.__externalMaterialGroup__
          resultData.serviceName += item?.__externalMaterialGroup__?.name
          delete item.__externalMaterialGroup__
        }
      }
      delete resultData.__userExGr__
    }
  }
}
