import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BankBranchEntity } from './bankBranch.entity'
import { SupplierEntity } from './supplier.entity'
import { SupplierBankEntity } from './supplierBank.entity'
import { RegionEntity } from './region.entity'

@Entity('country')
export class CountryEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierEntity, (p) => p.country)
  suppliers: Promise<SupplierEntity[]>

  @OneToMany(() => BankBranchEntity, (p) => p.country)
  bankBranchs: Promise<BankBranchEntity[]>

  @OneToMany(() => SupplierBankEntity, (p) => p.country)
  supplierBanks: Promise<SupplierBankEntity[]>

  @OneToMany(() => RegionEntity, (p) => p.country)
  regions: Promise<RegionEntity[]>
}
