import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnContractAppendix1752763183576 implements MigrationInterface {
  name = 'AddColumnContractAppendix1752763183576'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contract_appendix_payment_progress" ADD "money" float CONSTRAINT "DF_cb05592624a89740b1e006bf6f0" DEFAULT 0`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP CONSTRAINT "DF_cb05592624a89740b1e006bf6f0"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP COLUMN "money"`)
  }
}
