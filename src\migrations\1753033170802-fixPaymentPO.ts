import { MigrationInterface, QueryRunner } from "typeorm";

export class FixPaymentPO1753033170802 implements MigrationInterface {
    name = 'FixPaymentPO1753033170802'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_6a9bd6d1b0c829ba79e9df5269b"`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "contractPaymentPlanId"`);
        await queryRunner.query(`ALTER TABLE "po" ADD "paymentPlanId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po" ADD "paymentMethodId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "poId"`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "poId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD CONSTRAINT "FK_66e273ae29d05ea3faca09ec8d2" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_03f1994d341432e17194fb96ad0" FOREIGN KEY ("paymentPlanId") REFERENCES "payment_progress"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_0d2d2d384477fef59f0ba8af244" FOREIGN KEY ("paymentMethodId") REFERENCES "payment_method"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_0d2d2d384477fef59f0ba8af244"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_03f1994d341432e17194fb96ad0"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP CONSTRAINT "FK_66e273ae29d05ea3faca09ec8d2"`);
        await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "poId"`);
        await queryRunner.query(`ALTER TABLE "po_contract" ADD "poId" nvarchar(255)`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "paymentMethodId"`);
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "paymentPlanId"`);
        await queryRunner.query(`ALTER TABLE "po" ADD "contractPaymentPlanId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_6a9bd6d1b0c829ba79e9df5269b" FOREIGN KEY ("contractPaymentPlanId") REFERENCES "payment_progress"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
