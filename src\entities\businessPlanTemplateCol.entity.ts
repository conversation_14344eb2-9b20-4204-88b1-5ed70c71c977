import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, Jo<PERSON><PERSON><PERSON>um<PERSON>, OneToMany } from 'typeorm'
import { BusinessPlanTemplateEntity } from './businessPlanTemplate.entity'
import { BusinessPlanColValueEntity } from './businessPlanColValue.entity'
import { BusinessPlanSettingValueEntity } from './businessPlanSettingValue.entity'

@Entity('business_plan_template_col')
export class BusinessPlanTemplateColEntity extends BaseEntity {
  /** Mã */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  /** Công thức */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fomular: string

  /** Có bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: true,
  })
  isRequired: boolean

  /** <PERSON><PERSON><PERSON><PERSON> chỉnh sửa */
  @Column({
    nullable: false,
    default: true,
  })
  isEdited: boolean

  @Column({
    nullable: true,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** Kiểu dữ liệu */
  @Column({
    length: 50,
    nullable: true,
  })
  colType: string

  /** Kiểu dữ liệu */
  @Column({
    length: 50,
    nullable: true,
  })
  type: string

  /** Lấy dữ liệu từ hệ thống */
  @Column({
    nullable: true,
    default: false,
  })
  isSystemData: boolean

  /** Data Mapping */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  dataMapping: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanTemplateId: string
  @ManyToOne(() => BusinessPlanTemplateEntity, (p) => p.businessPlanTemplateCols)
  @JoinColumn({ name: 'businessPlanTemplateId', referencedColumnName: 'id' })
  businessPlanTemplate: Promise<BusinessPlanTemplateEntity>

  /** businessPlanTemplate Code */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  businessPlanTemplateCode: string

  @OneToMany(() => BusinessPlanColValueEntity, (p) => p.businessPlanTemplateCol)
  businessPlanColValues: Promise<BusinessPlanColValueEntity[]>

  @OneToMany(() => BusinessPlanSettingValueEntity, (p) => p.businessPlanTemplateCol)
  businessPlanSettingValue: Promise<BusinessPlanSettingValueEntity[]>
}
