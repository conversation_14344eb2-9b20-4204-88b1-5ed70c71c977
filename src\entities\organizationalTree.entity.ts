import { BaseEntity } from './base.entity'
import { Column, Entity } from 'typeorm'

@Entity('organizational_tree')
export class OrganizationalTreeEntity extends BaseEntity {
  /** Loại: enumdata OrganizationalType*/
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  type: string

  /* tên của giá trị bên trong */
  @Column({
    type: 'nvarchar',
    length: '50',
    nullable: true,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  lstLevelPrevious: string

  @Column({
    length: 50,
    nullable: false,
  })
  entityName: string

  /* nếu là cấp 0 thì targetId = companyId*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  targetId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string

  @Column({ nullable: true })
  level: number

  @Column()
  lft: number

  @Column()
  rgt: number

  targetDetail: any // Thông tin chi tiết của targetId, có thể là một đối tượng hoặc null nếu không có thông tin chi tiết nào được liên kết
}
