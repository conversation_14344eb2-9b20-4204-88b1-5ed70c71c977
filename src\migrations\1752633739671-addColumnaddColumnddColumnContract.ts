import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnaddColumnddColumnContract1752633739671 implements MigrationInterface {
  name = 'AddColumnaddColumnddColumnContract1752633739671'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "factorySupplierId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "factorySupplierId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "material" ADD "technicalSpec" nvarchar(250)`)
    await queryRunner.query(
      `ALTER TABLE "contract_item" ADD CONSTRAINT "FK_fb4f9b2864b044c2fe0663a4689" FOREIGN KEY ("factorySupplierId") REFERENCES "factory_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "contract_appendix_item" ADD CONSTRAINT "FK_7050ec8b278e8a49e29866ef7e1" FOREIGN KEY ("factorySupplierId") REFERENCES "factory_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP CONSTRAINT "FK_7050ec8b278e8a49e29866ef7e1"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP CONSTRAINT "FK_fb4f9b2864b044c2fe0663a4689"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "technicalSpec"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "factorySupplierId"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "factorySupplierId"`)
  }
}
