import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../common/guards'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { CurrentUser } from '../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { BussinessPlanService } from './businessPlan.service'
import { BussinessPlanCreateDto, BussinessPlanUpdateDto } from './dto'
@ApiBearerAuth()
@ApiTags('BussinessPlan')
@UseGuards(JwtAuthGuard)
@Controller('bussiness_plan')
export class BussinessPlanController {
  constructor(private readonly service: BussinessPlanService) {}

  @ApiOperation({ summary: 'Tạo mới một dữ liệu.' })
  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: BussinessPlanCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật một dữ liệu.' })
  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: BussinessPlanUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Danh sách' })
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.pagination(data, user)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái kích hoạt của vùng.' })
  @Post('update_active')
  async updateActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Chi tiết' })
  @Post('find_detail')
  public async findDetail(@Body() data: FilterOneDto) {
    return await this.service.findDetail(data)
  }

  @ApiOperation({ summary: 'Tính phương án kinh doanh' })
  @Post('calculator')
  public async calculator(@Body() data: { lstColum: any; lstMaterial: any; lstBusinessPlanTemplateSettingCol: any }) {
    return await this.service.calculator(data)
  }

  @ApiOperation({ summary: 'Hủy.' })
  @Post('update_unactive')
  async updateUnActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateUnActive(user, data)
  }

  @ApiOperation({ summary: 'Gửi duyệt.' })
  @Post('update_wait')
  async updateWait(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateWait(user, data)
  }

  @ApiOperation({ summary: 'Kiểm tra lại.' })
  @Post('update_check_again')
  async updateCheckAgain(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateCheckAgain(user, data)
  }

  @ApiOperation({ summary: 'Duyệt phương án kinh doanh.' })
  @Post('update_approved')
  async updateApproved(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateApproved(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách ' })
  @Post('find')
  public async find(@Body() data: FilterOneDto) {
    return await this.service.find(data)
  }

  @ApiOperation({ summary: 'Lấy danh sách ' })
  @Post('find_list_pr')
  public async findListPr(@Body() data: FilterOneDto) {
    return await this.service.findListPr(data)
  }

  @ApiOperation({ summary: 'Lấy danh sách ' })
  @Post('find_list_col_setting')
  public async findColSetting(@Body() data: FilterOneDto) {
    return await this.service.findColSetting(data)
  }

  @ApiOperation({ summary: 'Lấy danh sách ' })
  @Post('find_list_material_item')
  public async findListMaterialItem(@Body() data: FilterOneDto) {
    return await this.service.findListMaterialItem(data)
  }

  @ApiOperation({ summary: 'Lấy danh sách PR đã duyệt theo phân quyền và có thể tạo thầu' })
  @Post('load_Auction')
  public async loadAuction(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.loadItemByBusinessPlan(user, data)
  }
}
