import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddLeatime1751534736462 implements MigrationInterface {
  name = 'AddLeatime1751534736462'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "ç" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_2e63fa844cbb1d89762df132393" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2061a742ff8facc2afd2b43b6a2" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "materialGroupId" uniqueidentifier, "leadtimeId" uniqueidentifier, "materialId" uniqueidentifier, "startDate" datetime, "expireDate" datetime, "dayOfRecommendedPurchase" float, "dayOfContract" float, "dayOfSupplierPrepare" float, "dayOfSupplierProduction" float, "dayOfSupplierProductionToPort" float, "dayOfTransportSupplierToVietNam" float, "dayOfTransportVietNamToWarehouse" float, "dayOfQualityCheckAndReceiving" float, "dayOfLeadtimePurchase" float, "dayOfLeadtimeDelivery" float, "quarter" varchar(255), CONSTRAINT "PK_2e63fa844cbb1d89762df132393" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "leadtime" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_301475c551d829182cc433b3da0" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_605d9e13ef12471a39822a21d53" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "name" varchar(250), "code" varchar(50), "materialGroupId" varchar(255), "year" datetime, "lstMaterialGroupId" nvarchar(max), "materialId" uniqueidentifier, CONSTRAINT "PK_301475c551d829182cc433b3da0" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "leadtime_detail" ADD CONSTRAINT "FK_faa7754eb431ac88b0e9f0b1fe2" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "leadtime_detail" ADD CONSTRAINT "FK_c4624f2a76bd22eec8e22234e76" FOREIGN KEY ("leadtimeId") REFERENCES "leadtime"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "leadtime_detail" ADD CONSTRAINT "FK_428e8aa57f047afa56003d362a8" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "leadtime" ADD CONSTRAINT "FK_4060c31a644ded6f510b8525c10" FOREIGN KEY ("materialId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "leadtime" DROP CONSTRAINT "FK_4060c31a644ded6f510b8525c10"`)
    await queryRunner.query(`ALTER TABLE "leadtime_detail" DROP CONSTRAINT "FK_428e8aa57f047afa56003d362a8"`)
    await queryRunner.query(`ALTER TABLE "leadtime_detail" DROP CONSTRAINT "FK_c4624f2a76bd22eec8e22234e76"`)
    await queryRunner.query(`ALTER TABLE "leadtime_detail" DROP CONSTRAINT "FK_faa7754eb431ac88b0e9f0b1fe2"`)

    await queryRunner.query(`DROP TABLE "leadtime"`)
    await queryRunner.query(`DROP TABLE "leadtime_detail"`)
  }
}
