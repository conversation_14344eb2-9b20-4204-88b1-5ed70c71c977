import { MigrationInterface, QueryRunner } from 'typeorm'

export class ShippmentConfig1750158958269 implements MigrationInterface {
  name = 'ShippmentConfig1750158958269'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_fee_conditions_list" DROP CONSTRAINT "FK_30329fcbf4cb9c965f5b4de5d67"`)
    await queryRunner.query(
      `CREATE TABLE "shipment_fee_conditions_to_list" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0db859d4b37eea7c51bd9191532" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_aa3389f03805b816331495820da" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "shipmentFeeConditionsListId" uniqueidentifier NOT NULL, "shipmentFeeConditionsId" uniqueidentifier NOT NULL, CONSTRAINT "PK_0db859d4b37eea7c51bd9191532" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(`ALTER TABLE "shipment_fee_conditions_list" DROP COLUMN "shipmentConditionTypeId"`)
    await queryRunner.query(
      `ALTER TABLE "shipment_fee_conditions_to_list" ADD CONSTRAINT "FK_64ab2ea3c133015447d13f66805" FOREIGN KEY ("shipmentFeeConditionsListId") REFERENCES "shipment_fee_conditions_list"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "shipment_fee_conditions_to_list" ADD CONSTRAINT "FK_9b5d24f3cbcfcf2ab3c4495bbb9" FOREIGN KEY ("shipmentFeeConditionsId") REFERENCES "shipment_fee_conditions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_fee_conditions_to_list" DROP CONSTRAINT "FK_9b5d24f3cbcfcf2ab3c4495bbb9"`)
    await queryRunner.query(`ALTER TABLE "shipment_fee_conditions_to_list" DROP CONSTRAINT "FK_64ab2ea3c133015447d13f66805"`)
    await queryRunner.query(`ALTER TABLE "shipment_fee_conditions_list" ADD "shipmentConditionTypeId" uniqueidentifier NOT NULL`)
    await queryRunner.query(`DROP TABLE "shipment_fee_conditions_to_list"`)
    await queryRunner.query(
      `ALTER TABLE "shipment_fee_conditions_list" ADD CONSTRAINT "FK_30329fcbf4cb9c965f5b4de5d67" FOREIGN KEY ("shipmentConditionTypeId") REFERENCES "shipment_fee_conditions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }
}
