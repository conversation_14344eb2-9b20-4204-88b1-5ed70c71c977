import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BusinessPlanEntity, ContractEntity, MaterialEntity, POEntity, RfqEntity, SupplierNumberRequestApproveEntity } from '.'
import { SupplierListPricePOEntity } from './supplierListPricePo.entity'
import { PoPriceListEntity } from './poPriceList.entity'
import { PaymentEntity } from './payment.entity'
import { RoleSupplierEntity } from './roleSupplier.entity'
import { MaterialPriceEntity } from './materialPrice.entity'
import { CurrencyExchangeEntity } from './currencyExchange.entity'
import { MaterialValtypeEntity } from './materialValtype.entity'
import { ShipmentPlanNumberEntity } from './shipmentPlanNumber.entity'

/**Tiền tệ */
@Entity('currency')
export class CurrencyEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.purchasingGroup)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.currency)
  contracts: Promise<ContractEntity[]>

  @OneToMany(() => ShipmentPlanNumberEntity, (p) => p.currency)
  shipmentPlanNumbers: Promise<ShipmentPlanNumberEntity[]>

  /** PO */
  @OneToMany(() => POEntity, (p) => p.currency)
  pos: Promise<POEntity[]>

  @OneToMany(() => BusinessPlanEntity, (p) => p.currencyFrom)
  businessPlanCurrencyFrom: Promise<BusinessPlanEntity[]>

  @OneToMany(() => BusinessPlanEntity, (p) => p.currencyTo)
  businessPlanCurrencyTo: Promise<BusinessPlanEntity[]>

  @OneToMany(() => SupplierListPricePOEntity, (p) => p.currency)
  listPricePos: Promise<SupplierListPricePOEntity[]>

  @OneToMany(() => PoPriceListEntity, (p) => p.currency)
  poPriceList: Promise<PoPriceListEntity[]>

  @OneToMany(() => PaymentEntity, (p) => p.currency)
  payments: Promise<PaymentEntity[]>

  @OneToMany(() => RoleSupplierEntity, (p) => p.currency)
  roleSuppliers: Promise<RoleSupplierEntity[]>

  @OneToMany(() => MaterialPriceEntity, (p) => p.currency)
  materialPrices: Promise<MaterialPriceEntity[]>

  @OneToMany(() => CurrencyExchangeEntity, (p) => p.currency)
  currencyExchange: Promise<CurrencyExchangeEntity[]>

  @OneToMany(() => MaterialEntity, (p) => p.currency)
  materials: Promise<MaterialEntity[]>

  @OneToMany(() => MaterialValtypeEntity, (p) => p.currency)
  materialValtypes: Promise<MaterialValtypeEntity[]>

  @OneToMany(() => RfqEntity, (p) => p.currency)
  rfqs: Promise<RfqEntity[]>
}
