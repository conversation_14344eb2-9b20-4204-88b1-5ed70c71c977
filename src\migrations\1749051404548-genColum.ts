import { MigrationInterface, QueryRunner } from "typeorm";

export class GenColum1749051404548 implements MigrationInterface {
    name = 'GenColum1749051404548'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_3e3aed95cdbdeb91c00401949a6"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "safetyStock"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "deliveryTime"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_ca6bd4b5370a07ef15dc4457f49"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "maximumStockLevel"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_0e5f9261a6752beff0e6257ea16"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "roundingValue"`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD "roundingValue" decimal(20,3) CONSTRAINT "DF_d07b4543ef432036999cea76212" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD "maximumLotSize" decimal(20,3) CONSTRAINT "DF_7e41a1872674f12e8a4bc0cba17" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD "safetyStock" decimal(20,3) CONSTRAINT "DF_2cceb30f01c11a160de42aa3368" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD "deliveryTime" float`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD "maximumStockLevel" decimal(20,3) CONSTRAINT "DF_b4323061af81c191da6a8aab14f" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material_plant" DROP CONSTRAINT "DF_b4323061af81c191da6a8aab14f"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP COLUMN "maximumStockLevel"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP COLUMN "deliveryTime"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP CONSTRAINT "DF_2cceb30f01c11a160de42aa3368"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP COLUMN "safetyStock"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP CONSTRAINT "DF_7e41a1872674f12e8a4bc0cba17"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP COLUMN "maximumLotSize"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP CONSTRAINT "DF_d07b4543ef432036999cea76212"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP COLUMN "roundingValue"`);
        await queryRunner.query(`ALTER TABLE "material" ADD "roundingValue" decimal(20,3)`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "DF_0e5f9261a6752beff0e6257ea16" DEFAULT 0 FOR "roundingValue"`);
        await queryRunner.query(`ALTER TABLE "material" ADD "maximumStockLevel" decimal(20,3)`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "DF_ca6bd4b5370a07ef15dc4457f49" DEFAULT 0 FOR "maximumStockLevel"`);
        await queryRunner.query(`ALTER TABLE "material" ADD "deliveryTime" float`);
        await queryRunner.query(`ALTER TABLE "material" ADD "safetyStock" decimal(20,3)`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "DF_3e3aed95cdbdeb91c00401949a6" DEFAULT 0 FOR "safetyStock"`);
    }

}
