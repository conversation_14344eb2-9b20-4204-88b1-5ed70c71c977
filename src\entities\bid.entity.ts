import { BaseEntity } from './base.entity'
import { En<PERSON><PERSON>, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { ServiceEntity } from './service.entity'
import { BidSupplierEntity } from './bidSupplier.entity'
import { BidEmployeeAccessEntity } from './bidEmployeeAccess.entity'
import { BidTechEntity } from './bidTech.entity'
import { BidTradeEntity } from './bidTrade.entity'
import { BidPriceEntity } from './bidPrice.entity'
import { BidTypeEntity } from './bidType.entity'
import { SettingStringEntity } from './settingString.entity'
import { BidHistoryEntity } from './bidHistory.entity'
import { BidDealEntity } from './bidDeal.entity'
import { BidAuctionEntity } from './bidAuction.entity'
import { BidCustomPriceEntity } from './bidCustomPrice.entity'
import { BidPriceColEntity } from './bidPriceCol.entity'
import { ContractEntity } from './contract.entity'
import { POEntity } from './po.entity'
import { PrItemEntity } from './prItem.entity'
import { PrEntity } from './pr.entity'
import { AuctionEntity } from './auction.entity'
import { enumData } from '../constants'
import { BidPrItemEntity } from './bidPrItem.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { BidShipmentPriceEntity } from './bidShipmentPrice.entity'
import { ShipmentStageEntity } from './shipmentStage.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { BusinessPlanEntity } from './businessPlan.entity'
import { ShipmentCostEntity } from './shipmentCost.entity'
import { RecommendedPurchaseShipmenStageEntity } from './recommendedPurchaseShipmentStage.entity'
import { BidExMatGroupEntity } from './bidExgroup.entity'
import { BidPrEntity } from './bidPr.entity'

@Entity('bid')
export class BidEntity extends BaseEntity {
  /** Có tự động đấu thầu không? */
  @Column({
    nullable: true,
    default: false,
  })
  isAutoBid: boolean

  /** Có hiển thị ở trang chủ không */
  @Column({
    nullable: true,
    default: false,
  })
  isShowHomePage: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isCompleteAll: boolean

  /** Có gửi thông báo mời thầu Doanh nghiệp qua email không */
  @Column({
    nullable: true,
    default: true,
  })
  isSendEmailInviteBid: boolean

  /** Đã gửi email mời thầu Doanh nghiệp chưa, nếu chưa thì cho phép sửa isSendEmailInviteBid và gửi mail nếu bật isSendEmailInviteBid = true */
  @Column({
    nullable: true,
    default: false,
  })
  hasSendEmailInviteBid: boolean

  /** Tên gói thầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** Tên gói thầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  pmOrder: string

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  refType: string

  @Column({ type: 'varchar', nullable: true })
  businessPlantId?: string
  @ManyToOne(() => BusinessPlanEntity, (p) => p.bids)
  @JoinColumn({ name: 'businessPlantId', referencedColumnName: 'id' })
  businessPlan?: Promise<BusinessPlanEntity>

  @Column({ type: 'varchar', nullable: true })
  plantId?: string
  /** Mã hệ thống tự sinh */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  /** Mô tả nội dung mời thầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  serviceInvite: string

  /** Ngày hết hạn xác nhận tham gia đấu thầu */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  acceptEndDate: Date

  /** Ngày hết hạn nộp hồ sơ thầu */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  submitEndDate: Date

  /** Địa chỉ nộp hồ sơ thầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  addressSubmit: string

  /** Công ty mời thầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  companyInvite: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  companyInviteId: string

  /** Các địa điểm thực hiện gói thầu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  listAddress: string

  /** Thời gian đăng tải */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  publicDate: Date

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  type: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  biddingTypeCode: string

  /** Hình thức đấu thầu */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidTypeId: string
  /** Hình thức đấu thầu */
  @ManyToOne(() => BidTypeEntity, (p) => p.bids)
  @JoinColumn({ name: 'bidTypeId', referencedColumnName: 'id' })
  bidType: Promise<BidTypeEntity>

  /** Hiệu lực hợp đồng (tháng) */
  @Column({ type: 'float', nullable: true, default: 0 })
  timeserving: number

  /** Thời điểm mở thầu */
  @Column({ nullable: true, type: 'datetime' })
  startBidDate: Date

  /** Ngày xác nhận mở thầu */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  bidOpenDate: Date

  /** Số tiền bảo lãnh dự thầu (VNĐ) */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  moneyGuarantee: number

  /** Thời hạn bảo lãnh dự thầu (tháng) */
  @Column({ type: 'float', nullable: true })
  timeGuarantee: number

  /** Hình thức bảo lãnh dự thầu */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  masterBidGuaranteeId: string
  /** Hình thức bảo lãnh dự thầu */
  @ManyToOne(() => SettingStringEntity, (p) => p.masterBidGuarantee)
  @JoinColumn({ name: 'masterBidGuaranteeId', referencedColumnName: 'id' })
  masterBidGuarantee: Promise<SettingStringEntity>

  /** Thời hạn thiết lập yêu cầu kỹ thuật, năng lực */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  timeTechDate: Date

  /** Thời hạn thiết lập các hạng mục báo giá, cơ cấu giá và điều kiện thương mại */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  timePriceDate: Date

  /** Thời hạn đánh giá yêu cầu kỹ thuật, năng lực */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  timeCheckTechDate: Date

  /** Thời hạn đánh giá các hạng mục báo giá, cơ cấu giá và điều kiện thương mại */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  timeCheckPriceDate: Date

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Trạng thái thiết lập kỹ thuật */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusTech: string

  /** Trạng thái  thiết lập thương mại */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusTrade: string

  /** Trạng thái thiết lập giá */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusPrice: string

  /** Trạng thái chọn Doanh nghiệp */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    default: 'ChuaChon',
  })
  statusChooseSupplier: string

  /** Trạng thái đánh giá kỹ thuật */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusRateTech: string

  /** Trạng thái đánh giá thương mại */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusRateTrade: string

  /** Trạng thái đánh giá giá */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  statusRatePrice: string

  /** Trạng thái cấu hình lại bảng giá enum BidResetPriceStatus */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    default: 'ChuaTao',
  })
  statusResetPrice: string

  /** Ngày kết thúc nộp chào giá hiệu chỉnh */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  resetPriceEndDate: Date

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Ghi chú của người tạo khi tạo kỹ thuật */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteTech: string

  /** Ghi chú của người tạo khi tạo ĐKTM */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteTrade: string

  /** Ghi chú của người tạo khi tạo giá */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  notePrice: string

  /** Ghi chú của người duyệt khi duyệt thông tin kỹ thuật */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteTechLeader: string

  /** Ghi chú của người duyệt khi duyệt thông tin ĐKTM, giá, Doanh nghiệp */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteMPOLeader: string

  /** Người phụ trách đánh giá khi chọn Doanh nghiệp trúng thầu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteCloseBidMPO: string

  /** Người duyệt đánh giá khi chọn Doanh nghiệp trúng thầu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteCloseBidMPOLeader: string

  /** Lĩnh vực mời thầu */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId?: string
  /** Lĩnh vực mời thầu */
  @ManyToOne(() => ServiceEntity, (p) => p.bids)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  /** Id của pr ở gói thầu tổng/gói thầu chi tiết khi tạo gói thầu từ việc chọn pr */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId?: string
  /** Pr */
  @ManyToOne(() => PrEntity, (p) => p.bids)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  @Column({ type: 'varchar', nullable: true })
  exMatGroupId?: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.bids)
  @JoinColumn({ name: 'exMatGroupId', referencedColumnName: 'id' })
  exMatGroup?: Promise<ExternalMaterialGroupEntity>

  /** Id của prItem ở gói thầu chi tiết khi tạo gói thầu từ việc chọn pr */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemId?: string
  /** Pr */
  @ManyToOne(() => PrItemEntity, (p) => p.bids)
  @JoinColumn({ name: 'prItemId', referencedColumnName: 'id' })
  prItem: Promise<PrItemEntity>

  /** Số lượng của Item trong gói thầu chi tiết */
  @Column({
    nullable: true,
    default: 0,
  })
  quantityItem: number

  /** Yêu cầu hủy gói thầu */
  @Column({
    nullable: true,
    default: false,
  })
  isRequestDelete: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isLoadFromBusinessPlan: boolean

  /** Lý do yêu cầu hủy gói thầu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteRequestDelete: string

  /** Công thức tính cột đơn giá */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fomular: string

  /** Cách tính điểm giá */
  @Column({ type: 'varchar', length: 50, nullable: true, default: enumData.PriceScoreCalculateWay.SumScore.code })
  wayCalScorePrice: string

  /** Tỉ trọng % kỹ thuật */
  @Column({
    nullable: true,
    default: 0,
  })
  percentTech: number

  /** Tỉ trọng % DKTM */
  @Column({
    nullable: true,
    default: 0,
  })
  percentTrade: number

  /** Tỉ trọng % giá */
  @Column({
    nullable: true,
    default: 0,
  })
  percentPrice: number

  /** Bản vẽ kỹ thuật hoặc hình ảnh minh hoạ */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileDrawing: string

  /** Phạm vi công việc */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileJD: string

  /** Tiêu chuẩn đánh giá KPI */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileKPI: string

  /** Các quy định về nội quy gói thầu: an toàn, an ninh, VSTP,... */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileRule: string

  /** Tài liệu mẫu (mẫu báo giá, mẫu hợp đồng,...) */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileDocument: string

  /** Khác */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileAnother: string

  /** Có bắt buộc File chi tiết giá */
  @Column({
    nullable: true,
    default: false,
  })
  isRequireFilePriceDetail: boolean

  /** Không cần duyệt gói thầu */
  @Column({
    nullable: true,
    default: false,
  })
  skipApprove: boolean

  /** Có bắt buộc File chi tiết kỹ thuật */
  @Column({
    nullable: true,
    default: false,
  })
  isRequireFileTechDetail: boolean

  /** Ngày duyệt chọn Doanh nghiệp thắng thầu */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  approveChooseSupplierWinDate: Date

  /** MPO ghi chú khi gửi yêu cầu phê duyệt kết thúc thầu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteFinishBidMPO: string

  @Column({
    nullable: true,
    default: false,
  })
  isGetFromPr: boolean

  /** Có tự động đấu thầu không? */
  @Column({
    nullable: true,
    default: false,
  })
  isSurvey: boolean

  /** Có tự động đấu thầu không? */
  @Column({
    nullable: true,
    default: false,
  })
  isLoadFromItem: boolean

  /** File scan kết quả gói thầu do MPO upload khi gửi yêu cầu phê duyệt kết thúc thầu */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileScan: string

  /** Ngày đóng thầu (ngày mpoLead duyệt kết thúc thầu) */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  bidCloseDate: Date

  @OneToMany(() => ContractEntity, (p) => p.bid)
  contracts: Promise<ContractEntity[]>

  @OneToMany(() => BidShipmentPriceEntity, (p) => p.bid)
  shipments: Promise<BidShipmentPriceEntity[]>

  @OneToMany(() => POEntity, (p) => p.bid)
  pos: Promise<POEntity[]>

  /** Id của gói thầu cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId?: string
  /** Cha */
  @ManyToOne(() => BidEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: Promise<BidEntity>

  /** Shipment */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentId: string
  @ManyToOne(() => ShipmentCostEntity, (p) => p.bids)
  @JoinColumn({ name: 'shipmentId', referencedColumnName: 'id' })
  shipment: Promise<ShipmentCostEntity>

  /** Con - 1 gói thầu sẽ có thể có nhiều gói thầu con */
  @OneToMany(() => BidEntity, (p) => p.parent)
  childs: Promise<BidEntity[]>

  /** Danh sách nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierEntity, (p) => p.bid)
  bidSuppliers: Promise<BidSupplierEntity[]>

  /** Danh sách nhà cung cấp đấu thầu */
  @OneToMany(() => BidExMatGroupEntity, (p) => p.bid)
  bidEx: Promise<BidExMatGroupEntity[]>

  /** Danh sách nhà cung cấp đấu thầu */
  @OneToMany(() => BidPrItemEntity, (p) => p.bid)
  bidPrItem: Promise<BidPrItemEntity[]>

  /** Danh sách nhân viên có quyền */
  @OneToMany(() => BidEmployeeAccessEntity, (p) => p.bid)
  employeeAccess: Promise<BidEmployeeAccessEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình kỹ thuật */
  @OneToMany(() => BidTechEntity, (p) => p.bid)
  techs: Promise<BidTechEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình thương mại */
  @OneToMany(() => BidTradeEntity, (p) => p.bid)
  trades: Promise<BidTradeEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình giá */
  @OneToMany(() => BidPriceEntity, (p) => p.bid)
  prices: Promise<BidPriceEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình cơ cấu giá */
  @OneToMany(() => BidCustomPriceEntity, (p) => p.bid)
  customPrices: Promise<BidCustomPriceEntity[]>

  /** Lịch sử bid */
  @OneToMany(() => BidHistoryEntity, (p) => p.bid)
  bidHistorys: Promise<BidHistoryEntity[]>

  @OneToMany(() => BidDealEntity, (p) => p.bid)
  bidDeals: Promise<BidDealEntity[]>

  @OneToMany(() => BidAuctionEntity, (p) => p.bid)
  bidAuctions: Promise<BidAuctionEntity[]>

  /** Danh sách các cột bổ sung thêm để Doanh nghiệp nhập dữ liệu */
  @OneToMany(() => BidPriceColEntity, (p) => p.bid)
  bidPriceCols: Promise<BidPriceColEntity[]>

  /** Các lần đấu thầu nhanh */
  @OneToMany(() => AuctionEntity, (p) => p.bid)
  auctions: Promise<AuctionEntity[]>

  /** Là thành viên trong hội đồng thầu nhập điểm?*/
  @Column({
    nullable: false,
    default: false,
  })
  isMemberScore: boolean

  /** Là cùng hội đồng Kỹ thuật, Giá và điều kiện thương mại?*/
  @Column({
    nullable: false,
    default: false,
  })
  isOtherCouncil: boolean

  /** Hội đồng chỉ được xem điểm đánh giá của cá nhân?*/
  @Column({
    nullable: false,
    default: false,
  })
  isPersonalScoreVisible: boolean

  /** shipment stage */
  @OneToMany(() => ShipmentStageEntity, (p) => p.bid)
  stages: Promise<ShipmentStageEntity[]>

  @OneToMany(() => RecommendedPurchaseEntity, (p) => p.bid)
  recommendedPurchase: Promise<RecommendedPurchaseEntity[]>

  @OneToMany(() => RecommendedPurchaseShipmenStageEntity, (p) => p.bid)
  recommendedPurchaseShipmentStage: Promise<RecommendedPurchaseShipmenStageEntity[]>

  @OneToMany(() => BidPrEntity, (p) => p.bid)
  bidPr: Promise<BidPrEntity[]>
}
