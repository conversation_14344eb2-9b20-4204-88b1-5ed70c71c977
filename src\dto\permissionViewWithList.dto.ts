import { ApiProperty } from '@nestjs/swagger'
import { <PERSON><PERSON>rra<PERSON>, IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class PermissionViewListItemDto {
  @ApiProperty({ description: 'ID', example: '281C61D5-2945-F011-ACD4-06546786BEE7' })
  @IsString()
  @IsOptional()
  id?: string

  @ApiProperty({ description: 'Ngày tạo', example: '2025-05-29T10:51:53.000Z', required: false })
  @IsString()
  @IsOptional()
  createdAt?: string

  @ApiProperty({ description: 'Danh sách nhóm quyền', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  permissionGroupIds?: string[]

  @ApiProperty({ description: 'Người tạo', example: '7896C2E1-9710-F011-ACD4-06546786BEE7', required: false })
  @IsString()
  @IsOptional()
  createdBy?: string

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> cập nhật', example: '2025-06-08T02:20:32.000Z', required: false })
  @IsString()
  @IsOptional()
  updatedAt?: string

  @ApiProperty({ description: 'Người cập nhật', example: '4503E885-11DB-4DCA-8A55-292F5F8166EB', required: false })
  @IsString()
  @IsOptional()
  updatedBy?: string

  @ApiProperty({ description: 'Trạng thái xóa', example: false })
  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean

  @ApiProperty({ description: 'Mã công ty', required: false })
  @IsString()
  @IsOptional()
  companyId?: string

  @ApiProperty({ description: 'Mã tổ chức mua hàng', required: false })
  @IsString()
  @IsOptional()
  purchasingOrgId?: string

  @ApiProperty({ description: 'Mã nhóm mua hàng', required: false })
  @IsString()
  @IsOptional()
  purchasingGroupId?: string

  @ApiProperty({ description: 'Tên quyền', example: 'BC NCC' })
  @IsString()
  @IsOptional()
  name?: string

  @ApiProperty({ description: 'Mã quyền', example: 'ReportSupplier' })
  @IsString()
  @IsOptional()
  code?: string

  @ApiProperty({ description: 'Đường dẫn', example: '/bid/report-supplier' })
  @IsString()
  @IsOptional()
  path?: string

  @ApiProperty({ description: 'Có quyền xem', example: false })
  @IsBoolean()
  @IsOptional()
  view?: boolean

  @ApiProperty({ description: 'Có quyền xóa', example: false })
  @IsBoolean()
  @IsOptional()
  delete?: boolean

  @ApiProperty({ description: 'Có quyền sửa', example: false })
  @IsBoolean()
  @IsOptional()
  edit?: boolean

  @ApiProperty({ description: 'Có quyền thêm', example: false })
  @IsBoolean()
  @IsOptional()
  add?: boolean

  @ApiProperty({ description: 'Có quyền xem dữ liệu khác', example: false })
  @IsBoolean()
  @IsOptional()
  watchAnother?: boolean

  @ApiProperty({ description: 'Có quyền sửa dữ liệu khác', example: false })
  @IsBoolean()
  @IsOptional()
  editAnother?: boolean
}

export class PermissionViewWithListDto {
  @ApiProperty({ description: 'Mã quyền', example: 'ReportSupplier' })
  @IsString()
  @IsNotEmpty()
  code: string

  @ApiProperty({ description: 'Tên quyền', example: 'BC NCC' })
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty({ description: 'Đường dẫn', example: '/bid/report-supplier' })
  @IsString()
  @IsNotEmpty()
  path: string

  @ApiProperty({ description: 'Danh sách nhóm quyền', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  permissionGroupIds?: string[]

  @ApiProperty({ description: 'Có quyền xem', example: false })
  @IsBoolean()
  view: boolean

  @ApiProperty({ description: 'Có quyền sửa', example: false })
  @IsBoolean()
  edit: boolean

  @ApiProperty({ description: 'Có quyền xóa', example: false })
  @IsBoolean()
  delete: boolean

  @ApiProperty({ description: 'Có quyền thêm', example: false })
  @IsBoolean()
  add: boolean

  @ApiProperty({ description: 'Có quyền xem dữ liệu khác', example: false })
  @IsBoolean()
  watchAnother: boolean

  @ApiProperty({ description: 'Có quyền sửa dữ liệu khác', example: false })
  @IsBoolean()
  editAnother: boolean

  @IsString()
  idGroup: string

  @ApiProperty({ description: 'Danh sách quyền đã tạo', type: [PermissionViewListItemDto] })
  @IsArray()
  @IsOptional()
  listOfData?: PermissionViewListItemDto[]
}
