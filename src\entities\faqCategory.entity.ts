import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { FAQEntity } from './faq.entity'

@Entity({ name: 'faq_category' })
export class FAQCategoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => FAQEntity, (p) => p.category)
  faqs: Promise<FAQEntity[]>
}
