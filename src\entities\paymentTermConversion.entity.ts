import { Entity, Column, <PERSON>ToOne, Join<PERSON><PERSON>umn } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PaymentTermEntity } from './paymentTerm.entity'

@Entity('payment_term_conversion')
export class PaymentTermConversionEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentTermId: string
  @ManyToOne(() => PaymentTermEntity, (p) => p.paymentTermConversions)
  @JoinColumn({ name: 'paymentTermId', referencedColumnName: 'id' })
  paymentTerm: Promise<PaymentTermEntity>

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  // % bank
  @Column({
    type: 'float',
    nullable: true,
  })
  percentBank: number

  /** đề nghị mua hàng */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfRecommendedPurchase: number

  /** % đề nghị mua hàng */
  @Column({
    type: 'float',
    nullable: true,
  })
  percentOfRecommendedPurchase: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  recommendedPurchaseCodeLeadTime: string

  /** Hợp đồng */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfContract: number

  /** % hợp đồng */
  @Column({
    type: 'float',
    nullable: true,
  })
  percentOfContract: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  contractCodeLeadTime: string

  /** TG NCC chuẩn bị nguyên liệu */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfSupplierPrepare: number

  /** TG NCC chuẩn bị nguyên liệu */
  @Column({
    type: 'float',
    nullable: true,
  })
  percentSupplierPrepare: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  supplierPrepareCodeLeadTime: string

  /** NCC sản xuất */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfSupplierProduction: number

  /** NCC sản xuất */
  @Column({
    type: 'float',
    nullable: true,
  })
  percentSupplierProduction: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  supplierProductionCodeLeadTime: string

  /** TG từ NCC sx xong đến cảng */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfSupplierProductionToPort: number

  /** TG từ NCC sx xong đến cảng */
  @Column({
    type: 'float',
    nullable: true,
  })
  percentSupplierProductionToPort: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  supplierProductionToPortCodeLeadTime: string

  /** TG vận chuyển từ cảng NCC về VN */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfTransportSupplierToVietNam: number

  /** TG vận chuyển từ cảng NCC về VN */
  @Column({
    type: 'float',
    nullable: true,
  })
  percentTransportSupplierToVietNam: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  transportSupplierToVietNamCodeLeadTime: string

  /** TG vận chuyển từ cảng VN về kho KT */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfTransportVietNamToWarehouse: number

  /** TG vận chuyển từ cảng VN về kho KT */
  @Column({
    type: 'float',
    nullable: true,
  })
  percentTransportVietNamToWarehouse: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  transportVietNamToWarehouseCodeLeadTime: string

  /** Kiểm tra chất lượng + Nhập kho */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfQualityCheckAndReceiving: number

  /** Kiểm tra chất lượng + Nhập kho */
  @Column({
    type: 'float',
    nullable: true,
  })
  percentQualityCheckAndReceiving: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  qualityCheckAndReceivingCodeLeadTime: string

  /** Tổng leadtime mua hàng */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfLeadtimePurchase: number

  /** Tổng leadtime mua hàng */
  @Column({
    type: 'float',
    nullable: true,
  })
  percentLeadtimePurchase: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  leadtimePurchaseCodeLeadTime: string

  /** Tổng leadtime kéo hàng */
  @Column({
    type: 'float',
    nullable: true,
  })
  dayOfLeadtimeDelivery: number

  /** Tổng leadtime kéo hàng */
  @Column({
    type: 'float',
    nullable: true,
  })
  percentLeadtimeDelivery: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  leadtimeDeliveryCodeLeadTime: string
}
