import path from 'path'

export const enumLanguage = {
  /** <PERSON><PERSON><PERSON> ngôn ngữ */
  Website: {
    CLIENT: { code: 'CLIENT', name: 'Client' },
    ADMIN: { code: 'ADMIN', name: 'Admin' },
  },
  /** <PERSON><PERSON><PERSON> ngôn ngữ */
  LanguageType: {
    VI: { code: 'VI', name: 'Việt Nam' },
    EN: { code: 'EN', name: 'Tiếng Anh' },
    CN: { code: 'CN', name: 'Tiếng Trung' },
  },
  /** Key ngôn ngữ */
  LanguageKey: {
    //#region "COMMON"
    Button_Add: {
      key: 'Button_Add',
      value: 'Thêm mới',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút thêm mới',
    },
    Button_QuickAdd: {
      key: 'Button_QuickAdd',
      value: 'Thêm',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thêm',
    },
    Button_Delete: {
      key: 'Button_Delete',
      value: 'Xóa',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút xóa',
    },
    Button_Confirm: {
      key: 'Button_Confirm',
      value: 'Xác nhận',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xác nhận',
    },
    Button_DownloadTemplateExcel: {
      key: 'Button_DownloadTemplateExcel',
      value: 'Tải Template Excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút tải Template Excel',
    },
    Button_ImportExcel: {
      key: 'Button_ImportExcel',
      value: 'Nhập Excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút nhập Excel',
    },
    Button_DownloadExcel: {
      key: 'Button_DownloadExcel',
      value: 'Tải Excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút tải Excel',
    },
    Button_DownloadExcelDetail: {
      key: 'Button_DownloadExcelDetail',
      value: 'Tải excel chi tiết',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút Tải excel chi tiết',
    },
    Button_ClearSearch: {
      key: 'Button_ClearSearch',
      value: 'Xóa bộ lọc',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút xóa bộ lọc',
    },
    Button_Search: {
      key: 'Button_Search',
      value: 'Tìm kiếm',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút tìm kiếm',
    },
    Button_Save: {
      key: 'Button_Save',
      value: 'Lưu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút Lưu',
    },
    Button_STT: {
      key: 'Button_STT',
      value: 'STT',
      languageType: 'VI',
      path: 'all_view',
      description: 'STT',
    },
    Button_Action: {
      key: 'Button_Action',
      value: 'Tác vụ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tác vụ',
    },
    Button_Exit: {
      key: 'Button_Exit',
      value: 'Thoát',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút Thoát',
    },
    Button_ClickUpload: {
      key: 'Button_ClickUpload',
      value: 'Click to Upload',
      languageType: 'VI',
      path: 'all_view',
      description: 'Click to Upload',
    },
    Button_UploadFile: {
      key: 'Button_UploadFile',
      value: 'Upload File',
      languageType: 'VI',
      path: 'all_view',
      description: 'Upload File',
    },
    Button_ViewFile: {
      key: 'Button_ViewFile',
      value: 'Xem file',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xem file',
    },
    Button_CancelEdit: {
      key: 'Button_CancelEdit',
      value: 'Hủy chỉnh sửa',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút Hủy chỉnh sửa',
    },
    Button_Update: {
      key: 'Button_Update',
      value: 'Cập nhật',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút Cập nhật',
    },
    Button_Close: {
      key: 'Button_Close',
      value: 'Đóng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút Đóng',
    },
    Button_Reload: {
      key: 'Button_Reload',
      value: 'Làm mới',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút Làm mới',
    },
    Button_SaveChange: {
      key: 'Button_SaveChange',
      value: 'Lưu thay đổi',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút Lưu thay đổi',
    },
    Column_Action: {
      key: 'Column_Action',
      value: 'Tác vụ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Cột Tác vụ',
    },
    Column_SequenceNumber: {
      key: 'Column_SequenceNumber',
      value: 'STT',
      languageType: 'VI',
      path: 'all_view',
      description: 'Cột STT',
    },
    Choose_FromDate: {
      key: 'Choose_FromDate',
      value: 'Từ ngày',
      languageType: 'VI',
      path: 'all_view',
      description: 'Từ ngày',
    },
    Choose_ToDate: {
      key: 'Choose_ToDate',
      value: 'Đến ngày',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đến ngày',
    },
    Table_Footer_Of: {
      key: 'Table_Footer_Of',
      value: 'của',
      languageType: 'VI',
      path: 'all_view',
      description: 'của',
    },
    Table_Footer_Row: {
      key: 'Table_Footer_Row',
      value: 'dòng',
      languageType: 'VI',
      path: 'all_view',
      description: 'dòng',
    },
    Status: {
      key: 'Status',
      value: 'Trạng thái hoạt động',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái hoạt động',
    },
    Status_Active: {
      key: 'Status_Active',
      value: 'Đang hoạt động',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đang hoạt động',
    },
    Status_InActive: {
      key: 'Status_InActive',
      value: 'Ngưng hoạt động',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngưng hoạt động',
    },
    Status_Active_Question: {
      key: 'Status_Active_Question',
      value: 'Bạn có chắc muốn ngưng hoạt động ?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn có chắc muốn ngưng hoạt động ?',
    },

    Button_Status: {
      key: 'Button_Status',
      value: 'Trạng thái',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái',
    },
    Button_ChooseStatus: {
      key: 'Button_ChooseStatus',
      value: 'Chọn trạng thái',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chọn trạng thái',
    },
    Button_Option: {
      key: 'Button_Option',
      value: 'Tuỳ chọn',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tuỳ chọn',
    },

    /**Search */
    Search_Collapse: {
      key: 'Search_Collapse',
      value: 'Tìm kiếm',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tìm kiếm ở thanh collapse',
    },

    Search_Status: {
      key: 'Search_Status',
      value: 'Trạng thái',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái',
    },
    Search_ChooseStatus: {
      key: 'Search_ChooseStatus',
      value: 'Chọn trạng thái',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chọn trạng thái',
    },

    Search_Supplier: {
      key: 'Search_Supplier',
      value: 'Nhà cung cấp',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhà cung cấp',
    },

    Search_Choose_Supplier: {
      key: 'Search_Choose_Supplier',
      value: 'Chọn nhà cung cấp',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chọn nhà cung cấp',
    },

    Error_Upload_File_Size: {
      key: 'Error_Upload_File_Size',
      value: 'Kích thước tối đa để upload là',
      languageType: 'VI',
      path: 'all_view',
      description: 'Kích thước tối đa để upload là',
    },

    Error_Upload_File: {
      key: 'Error_Upload_File',
      value: 'vui lòng chọn file khác',
      languageType: 'VI',
      path: 'all_view',
      description: 'vui lòng chọn file khác',
    },

    /**Pagination */
    CreatedAt_Date: {
      key: 'CreatedAt_Date',
      value: 'Ngày tạo',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày tạo',
    },

    Creator: {
      key: 'Creator',
      value: 'Người tạo',
      languageType: 'VI',
      path: 'all_view',
      description: 'Người tạo',
    },

    Action_Yes: {
      key: 'Action_Yes',
      value: 'Có',
      languageType: 'VI',
      path: 'all_view',
      description: 'Có',
    },
    Action_No: {
      key: 'Action_No',
      value: 'Không',
      languageType: 'VI',
      path: 'all_view',
      description: 'Không',
    },
    Status_InActive_Question: {
      key: 'Status_InActive_Question',
      value: 'Bạn có chắc muốn hoạt động lại ?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn có chắc muốn hoạt động lại ?',
    },
    Status_Active_Again: {
      key: 'Status_Active_Again',
      value: 'Hoạt động lại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hoạt động lại',
    },
    Cancel_Text: {
      key: 'Cancel_Text',
      value: 'Hủy',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hủy',
    },
    Confirm_Text: {
      key: 'Confirm_Text',
      value: 'Ok',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ok',
    },
    On_Text: {
      key: 'On_Text',
      value: 'Bật',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bật',
    },
    Off_Text: {
      key: 'Off_Text',
      value: 'Tắt',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tắt',
    },

    Remove_Question: {
      key: 'Remove_Question',
      value: 'Bạn có chắc muốn xóa?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn có chắc muốn xóa?',
    },

    Warning_NoInfor: {
      key: 'Warning_NoInfor',
      value: 'Chưa có thông tin',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chưa có thông tin',
    },

    Button_ViewDetail: {
      key: 'Button_ViewDetail',
      value: 'Xem chi tiết',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút Xem chi tiết',
    },

    Button_Edit: {
      key: 'Button_Edit',
      value: 'Chỉnh sửa',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút chỉnh sửa',
    },
    Button_Agree: {
      key: 'Button_Agree',
      value: 'Đồng ý',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nút đồng ý',
    },
    Button_Refuse: {
      key: 'Button_Refuse',
      value: 'Từ chối',
      languageType: 'VI',
      path: 'all_view',
      description: 'Từ chối',
    },
    Button_Upload: {
      key: 'Button_Upload',
      value: 'Upload',
      languageType: 'VI',
      path: 'all_view',
      description: 'Upload',
    },
    Button_MaximumSizeToUpload: {
      key: 'Button_MaximumSizeToUpload',
      value: 'Kích thước tối đa để upload là',
      languageType: 'VI',
      path: 'all_view',
      description: 'Kích thước tối đa để upload là',
    },
    Button_ChooseAnotherFile: {
      key: 'Button_ChooseAnotherFile',
      value: 'vui lòng chọn file khác',
      languageType: 'VI',
      path: 'all_view',
      description: 'vui lòng chọn file khác',
    },
    Button_FromDateEarlierToDate: {
      key: 'Button_FromDateEarlierToDate',
      value: 'Từ ngày phải sớm hơn đến ngày',
      languageType: 'VI',
      path: 'all_view',
      description: 'Từ ngày phải sớm hơn đến ngày',
    },
    Button_FileSizeTooLarge: {
      key: 'Button_FileSizeTooLarge',
      value: 'Tệp có kích thước quá lớn, vui lòng chọn tệp < 10MB.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tệp có kích thước quá lớn, vui lòng chọn tệp < 10MB.',
    },
    //#region Warning & Placeholder

    Warning_Choose: {
      key: 'Warning_Choose',
      value: 'Vui lòng chọn',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng chọn',
    },
    Placeholder_Choose: {
      key: 'Placeholder_Choose',
      value: 'Chọn',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chọn',
    },
    Warning_Input: {
      key: 'Warning_Input',
      value: 'Vui lòng nhập',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập',
    },
    Placeholder_Input: {
      key: 'Placeholder_Input',
      value: 'Nhập',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhập',
    },
    Warning_InputData: {
      key: 'Warning_InputData',
      value: 'Vui lòng nhập dữ liệu!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Warning_Code: {
      key: 'Warning_Code',
      value: 'Vui lòng nhập ký tự không dấu, số, chiều dài từ 2-50 ký tự, không chứa ký tự đặc biệt ngoại trừ "-", "_", "/"',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_Code: {
      key: 'Placeholder_Code',
      value: 'Nhập ký tự không dấu, số, chiều dài từ 2-50 ký tự, không chứa ký tự đặc biệt ngoại trừ "-", "_", "/"',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },
    Warning_LatLng: {
      key: 'Warning_LatLng',
      value: 'Vui lòng nhập ký tự số, tối đa 3 ký tự trước dấu "." và tối đa 20 ký tự sau dấu "."',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_LatLng: {
      key: 'Placeholder_LatLng',
      value: 'Nhập ký tự số, tối đa 3 ký tự trước dấu "." và tối đa 20 ký tự sau dấu "."',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },

    Warning_Email: {
      key: 'Warning_Email',
      value: 'Vui lòng nhập đúng định dạng email! Ví dụ: <EMAIL>',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_Email: {
      key: 'Placeholder_Email',
      value: 'Nhập email',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },
    Warning_TextPassword: {
      key: 'Warning_TextPassword',
      value: 'Vui lòng nhập mật khẩu có chiều dài từ 6-50 ký tự!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_TextPassword: {
      key: 'Placeholder_TextPassword',
      value: 'Nhập mật khẩu có chiều dài từ 6-50 ký tự!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },
    Warning_TextConfirmPassword: {
      key: 'Warning_TextConfirmPassword',
      value: 'Vui lòng nhập xác nhận mật khẩu từ 6-50 ký tự!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_TextConfirmPassword: {
      key: 'Placeholder_TextConfirmPassword',
      value: 'Nhập lại mật khẩu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },

    Warning_Text0_20: {
      key: 'Warning_Text0_20',
      value: 'Vui lòng nhập nội dung có chiều dài từ 0-20 ký tự!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_Text0_20: {
      key: 'Placeholder_Text0_20',
      value: 'Nhập nội dung có chiều dài từ 0-20 ký tự',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },

    Warning_Number0_50: {
      key: 'Warning_Number0_50',
      value: 'Vui lòng nhập nội dung có chiều dài từ 0-50 ký tự chữ số!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_Number0_50: {
      key: 'Placeholder_Number0_50',
      value: 'Nhập nội dung có chiều dài từ 0-50 ký tự chữ số',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },

    Warning_Text1_50: {
      key: 'Warning_Text1_50',
      value: 'Vui lòng nhập nội dung có chiều dài từ 1-50 ký tự',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_Text1_50: {
      key: 'Placeholder_Text1_50',
      value: 'Nhập nội dung có chiều dài từ 1-50 ký tự',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },
    Warning_Text0_50: {
      key: 'Warning_Text0_50',
      value: 'Vui lòng nhập nội dung có chiều dài từ 0-50 ký tự!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },

    Placeholder_Text0_50: {
      key: 'Placeholder_Text0_50',
      value: 'Nhập nội dung có chiều dài từ 0-50 ký tự',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },
    Warning_Text0_100: {
      key: 'Warning_Text0_100',
      value: 'Vui lòng nhập nội dung có chiều dài từ 0-100 ký tự!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_Text0_100: {
      key: 'Placeholder_Text0_100',
      value: 'Nhập nội dung có chiều dài từ 0-100 ký tự',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },
    Warning_Text0_250: {
      key: 'Warning_Text0_250',
      value: 'Vui lòng nhập nội dung có chiều dài từ 0-250 ký tự!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_Text0_250: {
      key: 'Placeholder_Text0_250',
      value: 'Nhập nội dung có chiều dài từ 0-250 ký tự',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },

    Warning_Text1_250: {
      key: 'Warning_Text1_250',
      value: 'Vui lòng nhập nội dung có chiều dài từ 1-250 ký tự!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_Text1_250: {
      key: 'Placeholder_Text1_250',
      value: 'Nhập nội dung có chiều dài từ 1-250 ký tự',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },

    Warning_Text0_500: {
      key: 'Warning_Text0_500',
      value: 'Vui lòng nhập nội dung có chiều dài từ 0-500 ký tự!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_Text0_500: {
      key: 'Placeholder_Text0_500',
      value: 'Nhập nội dung có chiều dài từ 0-500 ký tự',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },
    Warning_Text1_500: {
      key: 'Warning_Text1_500',
      value: 'Vui lòng nhập nội dung có chiều dài từ 1-500 ký tự!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_Text1_500: {
      key: 'Placeholder_Text1_500',
      value: 'Nhập nội dung có chiều dài từ 1-500 ký tự',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },
    Warning_Text1_1000: {
      key: 'Warning_Text1_1000',
      value: 'Vui lòng nhập nội dung có chiều dài từ 1-1000 ký tự!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lỗi show ra khi nhập sai',
    },
    Placeholder_Text1_1000: {
      key: 'Placeholder_Text1_1000',
      value: 'Nhập nội dung có chiều dài từ 1-1000 ký tự',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gợi ý nhập',
    },
    Text_EmtyData: {
      key: 'Text_EmtyData',
      value: 'Không có dữ liệu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Text khi Không có dữ liệu',
    },

    Supplier: {
      key: 'Supplier',
      value: 'Nhà cung cấp',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhà cung cấp',
    },

    //#endregion

    //#region "Client"

    //#region "common-layout"

    Text_Logo: {
      key: 'Text_Logo',
      value: 'KIM TIN',
      languageType: 'VI',
      path: 'all_view',
      description: 'KIM TIN',
    },

    Text_Company: {
      key: 'Text_Company',
      value: 'CỔNG THÔNG TIN ĐẤU THẦU KIM TÍN',
      languageType: 'VI',
      path: 'all_view',
      description: 'CỔNG THÔNG TIN ĐẤU THẦU KIM TÍN',
    },

    //#endregion "COMMON"

    //#region "page-header"

    PageHeader_HomePage: {
      key: 'PageHeader_HomePage',
      value: 'Trang chủ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trang chủ',
    },

    PageHeader_BidInvite: {
      key: 'PageHeader_BidInvite',
      value: 'Thông báo mời thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông báo mời thầu',
    },

    PageHeader_PriceQuote: {
      key: 'PageHeader_PriceQuote',
      value: 'Yêu cầu báo giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Yêu cầu báo giá',
    },

    PageHeader_BiddingHistory: {
      key: 'PageHeader_BiddingHistory',
      value: 'Lịch sử đấu thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lịch sử đấu thầu',
    },

    PageHeader_SiteAssessment: {
      key: 'PageHeader_SiteAssessment',
      value: 'Đánh giá hiện trường',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đánh giá hiện trường',
    },

    PageHeader_Contract: {
      key: 'PageHeader_Contract',
      value: 'Hợp đồng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hợp đồng',
    },

    PageHeader_PurchaseOrder: {
      key: 'PageHeader_PurchaseOrder',
      value: 'PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'PO',
    },

    PageHeader_Inbound: {
      key: 'PageHeader_Inbound',
      value: 'Inbound',
      languageType: 'VI',
      path: 'all_view',
      description: 'Inbound',
    },

    PageHeader_Bill: {
      key: 'PageHeader_Bill',
      value: 'Hóa đơn',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hóa đơn',
    },

    PageHeader_Payment: {
      key: 'PageHeader_Payment',
      value: 'Thanh toán',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thanh toán',
    },

    PageHeader_Complain: {
      key: 'PageHeader_Complain',
      value: 'Khiếu nại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Khiếu nại',
    },

    PageHeader_Login: {
      key: 'PageHeader_Login',
      value: 'Đăng nhập',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đăng nhập',
    },

    PageHeader_FAQ: {
      key: 'PageHeader_FAQ',
      value: 'FAQ',
      languageType: 'VI',
      path: 'all_view',
      description: 'FAQ',
    },

    PageHeader_UserManual: {
      key: 'PageHeader_UserManual',
      value: 'Hướng dẫn sử dụng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hướng dẫn sử dụng',
    },

    PageHeader_ChatNotify: {
      key: 'PageHeader_ChatNotify',
      value: 'Thông báo chat',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông báo chat',
    },
    PageHeader_Notice: {
      key: 'PageHeader_Notice',
      value: 'Thông báo',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông báo',
    },

    PageHeader_PleaseEnterYourUsername: {
      key: 'PageHeader_PleaseEnterYourUsername',
      value: 'Vui lòng nhập tài khoản đăng nhập!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập tài khoản đăng nhập!',
    },

    PageHeader_Username: {
      key: 'PageHeader_Username',
      value: 'Tên đăng nhập',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên đăng nhập',
    },

    PageHeader_Password: {
      key: 'PageHeader_Password',
      value: 'Mật khẩu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mật khẩu',
    },

    PageHeader_ForgotPassword: {
      key: 'PageHeader_ForgotPassword',
      value: 'Quên mật khẩu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Quên mật khẩu',
    },

    PageHeader_RegisterNow: {
      key: 'PageHeader_RegisterNow',
      value: 'Đăng ký ngay!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đăng ký ngay!',
    },

    PageHeader_AccountInformation: {
      key: 'PageHeader_AccountInformation',
      value: 'Thông tin tài khoản',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin tài khoản',
    },
    PageHeader_RequestUpdateSupplier: {
      key: 'PageHeader_RequestUpdateSupplier',
      value: 'Yêu cầu chỉnh sửa nhà cung cấp',
      languageType: 'VI',
      path: 'all_view',
      description: 'Yêu cầu chỉnh sửa nhà cung cấp',
    },

    PageHeader_ChangeLoginName: {
      key: 'PageHeader_ChangeLoginName',
      value: 'Thay đổi tên đăng nhập',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thay đổi tên đăng nhập',
    },

    PageHeader_ChangePassword: {
      key: 'PageHeader_ChangePassword',
      value: 'Đổi mật khẩu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đổi mật khẩu',
    },

    PageHeader_LogOut: {
      key: 'PageHeader_LogOut',
      value: 'Đăng xuất',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đăng xuất',
    },

    PageHeader_SeeMore: {
      key: 'PageHeader_SeeMore',
      value: 'Xem thêm',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xem thêm',
    },

    PageHeader_NonNotify: {
      key: 'PageHeader_NonNotify',
      value: 'Chưa có thông báo',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chưa có thông báo',
    },

    PageHeader_CommentNotRead: {
      key: 'PageHeader_CommentNotRead',
      value: 'Bạn vừa có 1 comment chưa đọc!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn vừa có 1 comment chưa đọc!',
    },

    //#endregion "page-header"

    //#region "page-footer"

    PageFooter_Register: {
      key: 'PageFooter_Register',
      value: 'Đăng kí nhận thông tin:',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đăng kí nhận thông tin:',
    },

    PageFooter_EmailAddress: {
      key: 'PageFooter_EmailAddress',
      value: 'Địa chỉ email',
      languageType: 'VI',
      path: 'all_view',
      description: 'Địa chỉ email',
    },

    PageFooter_Send: {
      key: 'PageFooter_Send',
      value: 'Gửi',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gửi',
    },

    //#endregion "page-footer"

    //#region "page-not-found"

    PageNotFound_SorryPageNotFound: {
      key: 'PageNotFound_SorryPageNotFound',
      value: 'Xin lỗi, trang bạn đang tìm kiếm không tồn tại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xin lỗi, trang bạn đang tìm kiếm không tồn tại',
    },

    PageNotFound_GoToHomePage: {
      key: 'PageNotFound_GoToHomePage',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đi tới trang chủ',
    },

    //#endregion "page-not-found"

    //#region "page-sider-left"

    PageSiderLeft_Service: {
      key: 'PageSiderLeft_Service',
      value: 'Nhập lên lĩnh vực',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhập lên lĩnh vực',
    },

    //#endregion "page-sider-left"

    //#region "page-sider-left"

    PageUnauthorized_SorryPleaseLogin: {
      key: 'PageUnauthorized_SorryPleaseLogin',
      value: 'Xin lỗi, bạn cần phải đăng nhập để truy cập vào trang này',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xin lỗi, bạn cần phải đăng nhập để truy cập vào trang này',
    },

    PageUnauthorized_GoToHomePage: {
      key: 'PageUnauthorized_SorryPleaseLogin',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đi tới trang chủ',
    },

    //#endregion "page-sider-left"

    //#region "Purchasing order"

    //#endregion "Purchasing order"

    //#region "Complaint"

    // #region "Purchasing order"
    PurchaseOrder_POList: {
      key: 'PurchaseOrder_POList',
      value: 'DANH SÁCH PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'DANH SÁCH PO',
    },
    PurchaseOrder_ChooseStatus: {
      key: 'PurchaseOrder_ChooseStatus',
      value: 'Chọn trạng thái',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Chọn trạng thái',
    },
    PurchaseOrder_POCode: {
      key: 'PurchaseOrder_POCode',
      value: 'Mã PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Mã PO',
    },
    PurchaseOrder_ImportPOCode: {
      key: 'PurchaseOrder_ImportPOCode',
      value: 'Nhập mã PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Nhập mã PO',
    },
    PurchaseOrder_POSupplier: {
      key: 'PurchaseOrder_POSupplier',
      value: 'Nhà cung cấp',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Nhà cung cấp',
    },
    PurchaseOrder_POSelectSupplier: {
      key: 'PurchaseOrder_POSelectSupplier',
      value: 'Chọn Nhà cung cấp',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Chọn Nhà cung cấp',
    },
    PurchaseOrder_POReferenceSource: {
      key: 'PurchaseOrder_POReferenceSource',
      value: 'Nguồn tham chiếu',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Nguồn tham chiếu',
    },
    PurchaseOrder_POSelectReferenceSource: {
      key: 'PurchaseOrder_POSelectReferenceSource',
      value: 'Chọn Nguồn tham chiếu',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Chọn Nguồn tham chiếu',
    },
    PurchaseOrder_PODateFrom: {
      key: 'PurchaseOrder_PODateFrom',
      value: 'Ngày tạo PO - Từ ngày',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Ngày tạo PO - Từ ngày',
    },
    PurchaseOrder_POSelectDateFrom: {
      key: 'PurchaseOrder_POSelectDateFrom',
      value: 'Nhập Từ ngày',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Nhập Từ ngày',
    },
    PurchaseOrder_PODateTo: {
      key: 'PurchaseOrder_PODateTo',
      value: 'Ngày tạo PO - Đến ngày',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Ngày tạo PO - Đến ngày',
    },
    PurchaseOrder_POSelectDateTo: {
      key: 'PurchaseOrder_POSelectDateTo',
      value: 'Nhập Đến ngày',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Nhập Đến ngày',
    },
    PurchaseOrder_POReferenceDocuments: {
      key: 'PurchaseOrder_POReferenceDocuments',
      value: 'Nhập Đến ngày',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Nhập Đến ngày',
    },
    PurchaseOrder_POName: {
      key: 'PurchaseOrder_POName',
      value: 'Tên PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Tên PO',
    },
    PurchaseOrder_CreatedDate: {
      key: 'PurchaseOrder_CreatedDate',
      value: 'Ngày tạo',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Ngày tạo',
    },
    PurchaseOrder_POValue: {
      key: 'PurchaseOrder_POValue',
      value: 'Giá trị PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Giá trị PO',
    },
    PurchaseOrder_POStatus: {
      key: 'PurchaseOrder_POStatus',
      value: 'Trạng thái PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Trạng thái PO',
    },
    PurchaseOrder_POSelectStatus: {
      key: 'PurchaseOrder_POSelectStatus',
      value: 'Chọn trạng thái PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Chọn trạng thái PO',
    },
    PurchaseOrder_POExecutionStatus: {
      key: 'PurchaseOrder_POExecutionStatus',
      value: 'Trạng thái thực hiện PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Trạng thái thực hiện PO',
    },
    PurchaseOrder_Action: {
      key: 'PurchaseOrder_Action',
      value: 'Tác vụ',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Tác vụ',
    },
    PurchaseOrder_ViewDetail: {
      key: 'PurchaseOrder_ViewDetail',
      value: 'Xem chi tiết',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Xem chi tiết',
    },
    PurchaseOrder_UpdateExpectedDeliveryDate: {
      key: 'PurchaseOrder_UpdateExpectedDeliveryDate',
      value: 'Cập nhật ngày dự kiến giao hàng',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Cập nhật ngày dự kiến giao hàng',
    },
    PurchaseOrder_ExpectedDeliveryDate: {
      key: 'PurchaseOrder_ExpectedDeliveryDate',
      value: 'Ngày dự kiến giao hàng',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Ngày dự kiến giao hàng',
    },
    PurchaseOrder_SingleStatus: {
      key: 'PurchaseOrder_SingleStatus',
      value: 'Trạng thái đơn',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Trạng thái đơn',
    },
    PurchaseOrder_UpdateSingleStatus: {
      key: 'PurchaseOrder_UpdateSingleStatus',
      value: 'Cập nhật trạng thái đơn',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Cập nhật trạng thái đơn',
    },
    PurchaseOrder_UpdatePriceList: {
      key: 'PurchaseOrder_UpdatePriceList',
      value: 'Cập nhật bảng giá',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Cập nhật bảng giá',
    },
    //#endregion "Purchasing order"

    //#region PurchaseOrder_PurchaseOrderDetail
    PurchaseOrder_PurchaseOrderDetail_GeneralInformation: {
      key: 'PurchaseOrder_PurchaseOrderDetail_GeneralInformation',
      value: 'Thông tin chung',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin chung',
    },
    PurchaseOrder_PurchaseOrderDetail_CodePO: {
      key: 'PurchaseOrder_PurchaseOrderDetail_CodePO',
      value: 'Mã PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mã PO',
    },
    PurchaseOrder_PurchaseOrderDetail_TitlePO: {
      key: 'PurchaseOrder_PurchaseOrderDetail_TitlePO',
      value: 'Tiêu đề PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tiêu đề PO',
    },

    PurchaseOrder_PurchaseOrderDetail_ReferenceDocuments: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ReferenceDocuments',
      value: 'Chứng từ tham chiếu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chứng từ tham chiếu',
    },
    PurchaseOrder_PurchaseOrderDetail_ReferenceDocumentsNumber: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ReferenceDocumentsNumber',
      value: 'Số chứng từ tham chiếu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số chứng từ tham chiếu',
    },
    PurchaseOrder_PurchaseOrderDetail_POValue: {
      key: 'PurchaseOrder_PurchaseOrderDetail_POValue',
      value: 'Trị giá PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trị giá PO',
    },
    PurchaseOrder_PurchaseOrderDetail_ListItem: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ListItem',
      value: 'Danh sách item',
      languageType: 'VI',
      path: 'all_view',
      description: 'Danh sách item',
    },
    PurchaseOrder_PurchaseOrderDetail_ContractCode: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ContractCode',
      value: 'Mã Hợp đồng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mã Hợp đồng',
    },
    // Add additional language keys following the same pattern as above
    PurchaseOrder_PurchaseOrderDetail_HistoryChanges: {
      key: 'PurchaseOrder_PurchaseOrderDetail_HistoryChanges',
      value: 'Lịch sử thay đổi',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lịch sử thay đổi',
    },
    PurchaseOrder_PurchaseOrderDetail_ContractName: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ContractName',
      value: 'Tên Hợp đồng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên Hợp đồng',
    },
    PurchaseOrder_PurchaseOrderDetail_BiddingPackageCode: {
      key: 'PurchaseOrder_PurchaseOrderDetail_BiddingPackageCode',
      value: 'Mã gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mã gói thầu',
    },
    PurchaseOrder_PurchaseOrderDetail_BiddingPackageName: {
      key: 'PurchaseOrder_PurchaseOrderDetail_BiddingPackageName',
      value: 'Tên gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên gói thầu',
    },
    PurchaseOrder_PurchaseOrderDetail_DateCreated: {
      key: 'PurchaseOrder_PurchaseOrderDetail_DateCreated',
      value: 'Ngày tạo',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày tạo',
    },
    PurchaseOrder_PurchaseOrderDetail_DeliveryDate: {
      key: 'PurchaseOrder_PurchaseOrderDetail_DeliveryDate',
      value: 'Ngày giao hàng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày giao hàng',
    },
    PurchaseOrder_PurchaseOrderDetail_ValuePO: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ValuePO',
      value: 'Giá trị PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'Giá trị PO',
    },
    PurchaseOrder_PurchaseOrderDetail_Material: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Material',
      value: 'Material',
      languageType: 'VI',
      path: 'all_view',
      description: 'Material',
    },
    PurchaseOrder_PurchaseOrderDetail_MaterialGroup: {
      key: 'PurchaseOrder_PurchaseOrderDetail_MaterialGroup',
      value: 'Material group',
      languageType: 'VI',
      path: 'all_view',
      description: 'Material group',
    },
    PurchaseOrder_PurchaseOrderDetail_ShortText: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ShortText',
      value: 'Short text',
      languageType: 'VI',
      path: 'all_view',
      description: 'Short text',
    },
    PurchaseOrder_PurchaseOrderDetail_QuantityPr: {
      key: 'PurchaseOrder_PurchaseOrderDetail_QuantityPr',
      value: 'Số lượng PR',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số lượng PR',
    },
    PurchaseOrder_PurchaseOrderDetail_ListPr: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ListPr',
      value: 'Danh sách PR',
      languageType: 'VI',
      path: 'all_view',
      description: 'Danh sách PR',
    },
    PurchaseOrder_PurchaseOrderDetail_ProposedDeliveryDate: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ProposedDeliveryDate',
      value: 'Ngày đề nghị giao hàng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày đề nghị giao hàng',
    },
    PurchaseOrder_PurchaseOrderDetail_DispatchBox: {
      key: 'PurchaseOrder_PurchaseOrderDetail_DispatchBox',
      value: 'Nơi nhận hàng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nơi nhận hàng',
    },
    PurchaseOrder_PurchaseOrderDetail_Standard: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Standard',
      value: 'Tiêu chuẩn',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tiêu chuẩn',
    },
    PurchaseOrder_PurchaseOrderDetail_Description: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Description',
      value: 'Mô tả',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mô tả',
    },
    PurchaseOrder_PurchaseOrderDetail_UpdateStatus: {
      key: 'PurchaseOrder_PurchaseOrderDetail_UpdateStatus',
      value: 'Cập nhật trạng thái',
      languageType: 'VI',
      path: 'all_view',
      description: 'Cập nhật trạng thái',
    },
    PurchaseOrder_PurchaseOrderDetail_UpdateHistory: {
      key: 'PurchaseOrder_PurchaseOrderDetail_UpdateHistory',
      value: 'Lịch sử cập nhật',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lịch sử cập nhật',
    },
    PurchaseOrder_PurchaseOrderDetail_UpdatedDateFromDate: {
      key: 'PurchaseOrder_PurchaseOrderDetail_UpdatedDateFromDate',
      value: 'Ngày cập nhật - Từ ngày',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày cập nhật - Từ ngày',
    },
    PurchaseOrder_PurchaseOrderDetail_UpdatedDateToDate: {
      key: 'PurchaseOrder_PurchaseOrderDetail_UpdatedDateToDate',
      value: 'Ngày cập nhật - Đến ngày',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày cập nhật - Đến ngày',
    },
    PurchaseOrder_PurchaseOrderDetail_UpdatedDate: {
      key: 'PurchaseOrder_PurchaseOrderDetail_UpdatedDate',
      value: 'Ngày cập nhật',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày cập nhật',
    },
    PurchaseOrder_PurchaseOrderDetail_Updater: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Updater',
      value: 'Người cập nhật',
      languageType: 'VI',
      path: 'all_view',
      description: 'Người cập nhật',
    },
    PurchaseOrder_PurchaseOrderDetail_FromStatus: {
      key: 'PurchaseOrder_PurchaseOrderDetail_FromStatus',
      value: 'Từ trạng thái',
      languageType: 'VI',
      path: 'all_view',
      description: 'Từ trạng thái',
    },
    PurchaseOrder_PurchaseOrderDetail_Totatus: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Totatus',
      value: 'Sang trạng thái',
      languageType: 'VI',
      path: 'all_view',
      description: 'Sang trạng thái',
    },
    PurchaseOrder_PurchaseOrderDetail_RestQuantity: {
      key: 'PurchaseOrder_PurchaseOrderDetail_RestQuantity',
      value: 'Số lượng còn lại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số lượng còn lại',
    },
    PurchaseOrder_PurchaseOrderDetail_QuantityPo: {
      key: 'PurchaseOrder_PurchaseOrderDetail_QuantityPo',
      value: 'Số lượng lên PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số lượng lên PO',
    },
    PurchaseOrder_PurchaseOrderDetail_Price: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Price',
      value: 'Đơn giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn giá',
    },
    PurchaseOrder_PurchaseOrderDetail_Status: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Status',
      value: 'Trạng thái',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái',
    },
    PurchaseOrder_PurchaseOrderDetail_FormPO: {
      key: 'PurchaseOrder_PurchaseOrderDetail_FormPO',
      value: 'Hình thức PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hình thức PO',
    },
    PurchaseOrder_PurchaseOrderDetail_CurrencyUnit: {
      key: 'PurchaseOrder_PurchaseOrderDetail_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tiền tệ',
    },
    PurchaseOrder_PurchaseOrderDetail_Email: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Email',
      value: 'Email',
      languageType: 'VI',
      path: 'all_view',
      description: 'Email',
    },
    PurchaseOrder_PurchaseOrderDetail_Buyer: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Buyer',
      value: 'Bên mua hàng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bên mua hàng',
    },
    PurchaseOrder_PurchaseOrderDetail_PhoneNumber: {
      key: 'PurchaseOrder_PurchaseOrderDetail_PhoneNumber',
      value: 'Số điện thoại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số điện thoại',
    },
    PurchaseOrder_PurchaseOrderDetail_PhoneNumberNcc: {
      key: 'PurchaseOrder_PurchaseOrderDetail_PhoneNumberNcc',
      value: 'Số điện thoại NCC',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số điện thoại NCC',
    },
    PurchaseOrder_PurchaseOrderDetail_Time: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Time',
      value: 'Thời gian',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thời gian',
    },
    PurchaseOrder_PurchaseOrderDetail_Implementer: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Implementer',
      value: 'Người thực hiện',
      languageType: 'VI',
      path: 'all_view',
      description: 'Người thực hiện',
    },
    PurchaseOrder_PurchaseOrderDetail_OldState: {
      key: 'PurchaseOrder_PurchaseOrderDetail_OldState',
      value: 'Trạng thái cũ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái cũ',
    },
    PurchaseOrder_PurchaseOrderDetail_CurrentState: {
      key: 'PurchaseOrder_PurchaseOrderDetail_CurrentState',
      value: 'Trạng thái hiện tại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái hiện tại',
    },
    PurchaseOrder_PurchaseOrderDetail_NewStatus: {
      key: 'PurchaseOrder_PurchaseOrderDetail_NewStatus',
      value: 'Trạng thái mới',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái mới',
    },
    PurchaseOrder_PurchaseOrderDetail_ContentChanges: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ContentChanges',
      value: 'Nội dung thay đổi',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nội dung thay đổi',
    },
    PurchaseOrder_PurchaseOrderDetail_ListProducts: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ListProducts',
      value: 'Danh sách sản phẩm',
      languageType: 'VI',
      path: 'all_view',
      description: 'Danh sách sản phẩm',
    },
    PurchaseOrder_PurchaseOrderDetail_Supplies: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Supplies',
      value: 'Vật tư',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vật tư',
    },
    PurchaseOrder_PurchaseOrderDetail_GoodsName: {
      key: 'PurchaseOrder_PurchaseOrderDetail_GoodsName',
      value: 'Tên hàng hóa',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên hàng hóa',
    },
    PurchaseOrder_PurchaseOrderDetail_Unit: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Unit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tính',
    },
    PurchaseOrder_PurchaseOrderDetail_Quantity: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số lượng',
    },

    PurchaseOrder_PurchaseOrderDetail_IntoMoney: {
      key: 'PurchaseOrder_PurchaseOrderDetail_IntoMoney',
      value: 'Thành tiền',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thành tiền',
    },
    PurchaseOrder_PurchaseOrderDetail_DescriptionGoods: {
      key: 'PurchaseOrder_PurchaseOrderDetail_DescriptionGoods',
      value: 'Mô tả hàng hóa',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mô tả hàng hóa',
    },
    PurchaseOrder_PurchaseOrderDetail_Note: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Note',
      value: 'Ghi chú',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ghi chú',
    },
    PurchaseOrder_PurchaseOrderDetail_PaymentProgress: {
      key: 'PurchaseOrder_PurchaseOrderDetail_PaymentProgress',
      value: 'Tiến độ thanh toán',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tiến độ thanh toán',
    },
    PurchaseOrder_PurchaseOrderDetail_ProgressName: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ProgressName',
      value: 'Tên tiến độ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên tiến độ',
    },
    PurchaseOrder_PurchaseOrderDetail_ProgressPercent: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ProgressPercent',
      value: 'Phần trăm tiến độ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Phần trăm tiến độ',
    },
    PurchaseOrder_PurchaseOrderDetail_PaymentStatus: {
      key: 'PurchaseOrder_PurchaseOrderDetail_PaymentStatus',
      value: 'Trạng thái thanh toán',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái thanh toán',
    },
    PurchaseOrder_PurchaseOrderDetail_InboundNumberPms: {
      key: 'PurchaseOrder_PurchaseOrderDetail_InboundNumberPms',
      value: 'Số IB PMS',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số IB PMS',
    },
    PurchaseOrder_PurchaseOrderDetail_ListImportNotifications: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ListImportNotifications',
      value: 'Số IB SAP',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số IB SAP',
    },
    PurchaseOrder_PurchaseOrderDetail_ContractNumber: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ContractNumber',
      value: 'Số HĐ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số HĐ',
    },
    PurchaseOrder_PurchaseOrderDetail_PoNumber: {
      key: 'PurchaseOrder_PurchaseOrderDetail_PoNumber',
      value: 'Số PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số PO',
    },
    PurchaseOrder_PurchaseOrderDetail_ShipmentNumber: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ShipmentNumber',
      value: 'Số Shipment',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số Shipment',
    },
    PurchaseOrder_PurchaseOrderDetail_Supplier: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Supplier',
      value: 'NCC',
      languageType: 'VI',
      path: 'all_view',
      description: 'NCC',
    },
    PurchaseOrder_PurchaseOrderDetail_PR: {
      key: 'PurchaseOrder_PurchaseOrderDetail_PR',
      value: 'PR',
      languageType: 'VI',
      path: 'all_view',
      description: 'PR',
    },
    PurchaseOrder_PurchaseOrderDetail_ArrivalDate: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ArrivalDate',
      value: 'Ngày về cảng dự kiến',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày về cảng dự kiến',
    },
    PurchaseOrder_PurchaseOrderDetail_ReceivingWarehouse: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ReceivingWarehouse',
      value: 'Kho nhận dự kiến',
      languageType: 'VI',
      path: 'all_view',
      description: 'Kho nhận dự kiến',
    },
    PurchaseOrder_PurchaseOrderDetail_CreatedDate: {
      key: 'PurchaseOrder_PurchaseOrderDetail_CreatedDate',
      value: 'Ngày tạo',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày tạo',
    },
    PurchaseOrder_PurchaseOrderDetail_PersonCharge: {
      key: 'PurchaseOrder_PurchaseOrderDetail_PersonCharge',
      value: 'Người phụ trách',
      languageType: 'VI',
      path: 'all_view',
      description: 'Người phụ trách',
    },
    PurchaseOrder_PurchaseOrderDetail_ListNotifications: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ListNotifications',
      value: 'Danh sách thông báo nhập hàng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Danh sách thông báo nhập hàng',
    },
    PurchaseOrder_PurchaseOrderDetail_ReferenceDocumentName: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ReferenceDocumentName',
      value: 'Tên chứng từ tham chiếu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên chứng từ tham chiếu',
    },
    PurchaseOrder_PurchaseOrderDetail_NewPrice: {
      key: 'PurchaseOrder_PurchaseOrderDetail_NewPrice',
      value: 'Đơn giá mới',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn giá mới',
    },

    //#endregion

    // #region "Complaint"
    Complaint_ComplaintList: {
      key: 'Complaint_ComplaintList',
      value: 'DANH SÁCH KHIẾU NẠI',
      languageType: 'VI',
      path: 'complaint',
      description: 'DANH SÁCH KHIẾU NẠI',
    },
    Complaint_ComplaintCode: {
      key: 'Complaint_ComplaintCode',
      value: 'Mã phiếu khiếu nại',
      languageType: 'VI',
      path: 'complaint',
      description: 'Mã phiếu khiếu nại',
    },
    Complaint_ComplaintTitle: {
      key: 'Complaint_ComplaintTitle',
      value: 'Tiêu đề khiếu nại',
      languageType: 'VI',
      path: 'complaint',
      description: 'Tiêu đề khiếu nại',
    },
    Complaint_ComplaintCreator: {
      key: 'Complaint_ComplaintCreator',
      value: 'Người tạo',
      languageType: 'VI',
      path: 'complaint',
      description: 'Người tạo',
    },
    Complaint_ComplaintCreationDate: {
      key: 'Complaint_ComplaintCreationDate',
      value: 'Ngày tạo',
      languageType: 'VI',
      path: 'complaint',
      description: 'Ngày tạo',
    },
    Complaint_ComplaintSupplier: {
      key: 'Complaint_ComplaintSupplier',
      value: 'Nhà cung cấp',
      languageType: 'VI',
      path: 'complaint',
      description: 'Nhà cung cấp',
    },
    Complaint_ComplaintReferenceDocuments: {
      key: 'Complaint_ComplaintReferenceDocuments',
      value: 'Chứng từ tham chiếu',
      languageType: 'VI',
      path: 'complaint',
      description: 'Chứng từ tham chiếu',
    },
    Complaint_ComplaintDetail: {
      key: 'Complaint_ComplaintDetail',
      value: 'Xem chi tiết',
      languageType: 'VI',
      path: 'complaint',
      description: 'Xem chi tiết',
    },
    //#endregion "Complaint"

    //#region "FAQ-Cate"

    PageUnauthorized_FAQCate: {
      key: 'PageUnauthorized_FAQCate',
      value: 'CÁC CÂU HỎI THƯỜNG GẶP',
      languageType: 'VI',
      path: 'faq-category?',
      description: 'CÁC CÂU HỎI THƯỜNG GẶP',
    },

    //#endregion "FAQ-Cate"

    //#region complaintDetail
    ComplaintDetail_Code: {
      key: 'ComplaintDetail_Code',
      value: 'Số phiếu Khiếu nại',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Số phiếu Khiếu nại',
    },
    ComplaintDetail_ComplaintTitle: {
      key: 'ComplaintDetail_ComplaintTitle',
      value: 'Tiêu đề Khiếu nại',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Tiêu đề Khiếu nại',
    },
    ComplaintDetail_Type: {
      key: 'ComplaintDetail_Type',
      value: 'Loại khiếu nại',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Loại khiếu nại',
    },
    ComplaintDetail_TypeName: {
      key: 'ComplaintDetail_TypeName',
      value: 'Tên loại khiếu nại',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Tên loại khiếu nại',
    },
    ComplaintDetail_ReferenceSource: {
      key: 'ComplaintDetail_ReferenceSource',
      value: 'Nguồn tham chiếu',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Nguồn tham chiếu',
    },
    ComplaintDetail_ReferenceDocuments: {
      key: 'ComplaintDetail_ReferenceDocuments',
      value: 'Chứng từ tham chiếu',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Chứng từ tham chiếu',
    },
    ComplaintDetail_Warehouse: {
      key: 'ComplaintDetail_Warehouse',
      value: 'Chứng từ nhập kho',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Chứng từ nhập kho',
    },
    ComplaintDetail_Plant: {
      key: 'ComplaintDetail_Plant',
      value: 'Plant',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Plant',
    },
    ComplaintDetail_WarehouseTime: {
      key: 'ComplaintDetail_WarehouseTime',
      value: 'Thời gian nhập kho',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Thời gian nhập kho',
    },
    ComplaintDetail_ProvenQuality: {
      key: 'ComplaintDetail_ProvenQuality',
      value: 'Chứng từ kiểm tra chất lượng',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Chứng từ kiểm tra chất lượng',
    },
    ComplaintDetail_PurchasingOrg: {
      key: 'ComplaintDetail_PurchasingOrg',
      value: 'Purchasing Org',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Purchasing Org',
    },
    ComplaintDetail_PurchGroup: {
      key: 'ComplaintDetail_PurchGroup',
      value: 'Purch group',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Purch group',
    },
    ComplaintDetail_ListDepartment: {
      key: 'ComplaintDetail_ListDepartment',
      value: 'Danh sách phòng ban liên quan',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Danh sách phòng ban liên quan',
    },
    ComplaintDetail_ListRecipients: {
      key: 'ComplaintDetail_ListRecipients',
      value: 'Danh sách người tiếp nhận',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Danh sách người tiếp nhận',
    },
    ComplaintDetail_SupplierEmail: {
      key: 'ComplaintDetail_SupplierEmail',
      value: 'Email nhà cung cấp',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Email nhà cung cấp',
    },
    ComplaintDetail_Phone: {
      key: 'ComplaintDetail_Phone',
      value: 'Số điện thoại',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Số điện thoại',
    },
    ComplaintDetail_Fax: {
      key: 'ComplaintDetail_Fax',
      value: 'Fax',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Fax',
    },
    ComplaintDetail_Address: {
      key: 'ComplaintDetail_Address',
      value: 'Địa chỉ',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Địa chỉ',
    },
    ComplaintDetail_LineItem: {
      key: 'ComplaintDetail_LineItem',
      value: 'Danh sách line item',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Danh sách line item',
    },
    ComplaintDetail_ListItem: {
      key: 'ComplaintDetail_ListItem',
      value: 'Danh sách item',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Danh sách item',
    },
    ComplaintDetail_Description: {
      key: 'ComplaintDetail_Description',
      value: 'Mô tả',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Mô tả',
    },
    ComplaintDetail_InputDescription: {
      key: 'ComplaintDetail_InputDescription',
      value: 'Nhập mô tả',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Nhập mô tả',
    },
    ComplaintDetail_Item: {
      key: 'ComplaintDetail_Item',
      value: 'Item',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Item',
    },
    ComplaintDetail_ShortText: {
      key: 'ComplaintDetail_ShortText',
      value: 'Short text',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Short text',
    },
    ComplaintDetail_Coding: {
      key: 'ComplaintDetail_Coding',
      value: 'Coding',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Coding',
    },
    ComplaintDetail_ViewDescription: {
      key: 'ComplaintDetail_ViewDescription',
      value: 'Chi tiết mô tả',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Chi tiết mô tả',
    },
    ComplaintDetail_AttachedFiles: {
      key: 'ComplaintDetail_AttachedFiles',
      value: 'File đính kèm',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'File đính kèm',
    },
    ComplaintDetail_UploadFiles: {
      key: 'ComplaintDetail_UploadFiles',
      value: 'Upload File',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Upload File',
    },
    ComplaintDetail_UploadAttachedFiles: {
      key: 'ComplaintDetail_UploadAttachedFiles',
      value: 'Vui lòng upload file đính kèm',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Vui lòng upload file đính kèm',
    },
    ComplaintDetail_ViewFiles: {
      key: 'ComplaintDetail_ViewFiles',
      value: 'Xem file',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Xem file',
    },
    ComplaintDetail_CargoHandling: {
      key: 'ComplaintDetail_CargoHandling',
      value: 'Xử lý hàng hóa',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Xử lý hàng hóa',
    },
    ComplaintDetail_LineItemCTNK: {
      key: 'ComplaintDetail_LineItemCTNK',
      value: 'Line item CTNK',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Line item CTNK',
    },
    ComplaintDetail_Unit: {
      key: 'ComplaintDetail_Unit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Đơn vị tính',
    },
    ComplaintDetail_Quantity: {
      key: 'ComplaintDetail_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Số lượng',
    },
    ComplaintDetail_NumberErrorsInt: {
      key: 'ComplaintDetail_NumberErrorsInt',
      value: 'Số lượng lỗi(Int)',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Số lượng lỗi(Int)',
    },
    ComplaintDetail_NumberErrorsExt: {
      key: 'ComplaintDetail_NumberErrorsExt',
      value: 'Số lượng lỗi(Ext)',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Số lượng lỗi(Ext)',
    },
    ComplaintDetail_DegreeChange: {
      key: 'ComplaintDetail_DegreeChange',
      value: 'Mức độ thay đổi',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Mức độ thay đổi',
    },
    ComplaintDetail_NumberReturns: {
      key: 'ComplaintDetail_NumberReturns',
      value: 'Number of returns',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Number of returns',
    },
    ComplaintDetail_PriorityLevel: {
      key: 'ComplaintDetail_PriorityLevel',
      value: 'Number of returns',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Number of returns',
    },
    ComplaintDetail_ProductNcc: {
      key: 'ComplaintDetail_ProductNcc',
      value: 'Mã hàng NCC',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Mã hàng NCC',
    },
    ComplaintDetail_LotNumber: {
      key: 'ComplaintDetail_LotNumber',
      value: 'Số lô',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Số lô',
    },
    ComplaintDetail_LotNumberNcc: {
      key: 'ComplaintDetail_LotNumberNcc',
      value: 'Số lô NCC',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Số lô NCC',
    },
    ComplaintDetail_SerialNumber: {
      key: 'ComplaintDetail_SerialNumber',
      value: 'Số seri',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Số seri',
    },
    ComplaintDetail_DepartmentResponse: {
      key: 'ComplaintDetail_DepartmentResponse',
      value: 'Phòng ban phản hồi',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Phòng ban phản hồi',
    },
    ComplaintDetail_Recipient: {
      key: 'ComplaintDetail_Recipient',
      value: 'Người tiếp nhận',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Người tiếp nhận',
    },
    ComplaintDetail_Title: {
      key: 'ComplaintDetail_Title',
      value: 'Tiêu đề',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Tiêu đề',
    },
    ComplaintDetail_PleaseTitle: {
      key: 'ComplaintDetail_PleaseTitle',
      value: 'Vui lòng nhập tiêu đề',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Vui lòng nhập tiêu đề',
    },
    ComplaintDetail_InputTitle: {
      key: 'ComplaintDetail_InputTitle',
      value: 'Nhập tiêu đề',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Nhập tiêu đề',
    },
    ComplaintDetail_Input: {
      key: 'ComplaintDetail_Input',
      value: 'Nhập',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Nhập',
    },
    ComplaintDetail_PostComment: {
      key: 'ComplaintDetail_PostComment',
      value: 'Post Comment',
      languageType: 'VI',
      path: 'complaint/detail',
      description: 'Post Comment',
    },

    //#region Contract
    Contract_List: {
      key: 'Contract_List',
      value: 'Danh sách hợp đồng',
      languageType: 'VI',
      path: 'contract',
      description: 'Contract List',
    },

    Contract_Code: {
      key: 'Contract_Code',
      value: 'Mã hợp đồng',
      languageType: 'VI',
      path: 'contract',
      description: 'Contract Code',
    },

    Contract_Code_Search: {
      key: 'Contract_Code_Search',
      value: 'Nhập mã hợp đồng',
      languageType: 'VI',
      path: 'contract',
      description: 'Contract Code Search',
    },

    /**Nhập ngày tạo */
    Contract_CreatedAt_From: {
      key: 'Contract_CreatedAt_From',
      value: 'Ngày tạo HĐ - Từ ngày',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngày tạo HĐ - Từ ngày',
    },

    Contract_CreatedAt_To: {
      key: 'Contract_CreatedAt_To',
      value: 'Ngày tạo HĐ - Đến ngày',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngày tạo HĐ - Đến ngày',
    },

    /**Nhập ngày hiệu lực */
    Contract_Effective_From: {
      key: 'Contract_Effective_From',
      value: 'Ngày hiệu lực - Từ ngày',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngày hiệu lực - Từ ngày',
    },

    Contract_Effective_To: {
      key: 'Contract_Effective_To',
      value: 'Ngày hiệu lực - Đến ngày',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngày hiệu lực - Đến ngày',
    },

    /**Nhập ngày hết hạn */

    /**Nhập ngày hiệu lực */
    Contract_Expired_From: {
      key: 'Contract_Expired_From',
      value: 'Ngày hết hạn - Từ ngày',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngày hết hạn - Từ ngày',
    },

    Contract_Expired_To: {
      key: 'Contract_Expired_To',
      value: 'Ngày hết hạn - Đến ngày',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngày hết hạn - Đến ngày',
    },

    Contract_Number: {
      key: 'Contract_Number',
      value: 'Số hợp đồng',
      languageType: 'VI',
      path: 'contract',
      description: 'Số hợp đồng',
    },

    Contract_Name: {
      key: 'Contract_Name',
      value: 'Tên hợp đồng',
      languageType: 'VI',
      path: 'contract',
      description: 'Tên hợp đồng',
    },

    Contract_PO_Quantity: {
      key: 'Contract_PO_Quantity',
      value: 'Số lượng PO',
      languageType: 'VI',
      path: 'contract',
      description: 'Số lượng PO hiện trong danh sách hợp đồng',
    },

    Contract_Appendix_Quantity: {
      key: 'Contract_Appendix_Quantity',
      value: 'Số lượng phụ lục',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngày hiệu lực - Đến ngày',
    },

    Contract_Expired_Date: {
      key: 'Contract_Expired_Date',
      value: 'Ngày hết hạn',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngày hết hạn hợp đồng',
    },

    Contract_Effective_Date: {
      key: 'Contract_Effective_Date',
      value: 'Ngày hiệu lực',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngày hiệu lực hợp đồng',
    },

    /**Xem chi tiết hợp đồng */
    Contract_Detail: {
      key: 'Contract_Detail',
      value: 'Xem chi tiết hợp đồng',
      languageType: 'VI',
      path: 'contract',
      description: 'Text xem chi tiết hợp đồng',
    },

    Contract_Value: {
      key: 'Contract_Value',
      value: 'Trị giá hợp đồng',
      languageType: 'VI',
      path: 'contract',
      description: 'Text Trị giá hợp đồng',
    },

    Delivery_Date: {
      key: 'Delivery_Date',
      value: 'Thời gian giao hàng',
      languageType: 'VI',
      path: 'contract',
      description: 'Text Thời gian giao hàng',
    },

    Contract_Date: {
      key: 'Contract_Date',
      value: 'Ngày kí hợp đồng',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngày kí hợp đồng',
    },

    External_MatGroup: {
      key: 'External_MatGroup',
      value: 'External MatGroup',
      languageType: 'VI',
      path: 'all_view',
      description: 'text External MatGroup',
    },

    Incoterm_Name: {
      key: 'Incoterm_Name',
      value: 'Điều kiện thương mại!',
      languageType: 'VI',
      path: 'contract',
      description: 'Điều kiện thương mại',
    },

    Contract_File: {
      key: 'Contract_File',
      value: 'File hợp đồng',
      languageType: 'VI',
      path: 'contract',
      description: 'File hợp đồng',
    },

    File_Other: {
      key: 'File_Other',
      value: 'Chứng từ khác',
      languageType: 'VI',
      path: 'contract',
      description: 'Chứng từ khác',
    },

    Email: {
      key: 'Email',
      value: 'Email',
      languageType: 'VI',
      path: 'contract',
      description: 'Text Email',
    },

    Phone: {
      key: 'Phone',
      value: 'SĐT',
      languageType: 'VI',
      path: 'contract',
      description: 'Số điện thoại',
    },

    Fax: {
      key: 'Fax',
      value: 'Fax',
      languageType: 'VI',
      path: 'contract',
      description: 'Text Fax',
    },

    Account_Number: {
      key: 'Account_Number',
      value: 'STK',
      languageType: 'VI',
      path: 'contract',
      description: 'Text số tài khoản',
    },

    Bank_User: {
      key: 'Bank_User',
      value: 'Chủ tài khoản',
      languageType: 'VI',
      path: 'contract',
      description: 'Chủ tài khoản',
    },

    Bank: {
      key: 'Bank',
      value: 'Ngân hàng',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngân hàng',
    },

    Bank_Branch: {
      key: 'Bank_Branch',
      value: 'Chi nhánh ngân hàng',
      languageType: 'VI',
      path: 'contract',
      description: 'Chi nhánh ngân hàng',
    },

    Swift_Code: {
      key: 'Swift_Code',
      value: 'Swift Code',
      languageType: 'VI',
      path: 'contract',
      description: 'Swift Code',
    },

    Iban: {
      key: 'Iban',
      value: 'IBAN',
      languageType: 'VI',
      path: 'contract',
      description: 'IBAN',
    },

    EContract_Code: {
      key: 'EContract_Code',
      value: 'Mã EContract',
      languageType: 'VI',
      path: 'contract',
      description: 'Mã EContract',
    },

    Item_Code: {
      key: 'Item_Code',
      value: 'Item Code',
      languageType: 'VI',
      path: 'contract',
      description: 'Item Code',
    },

    Quantity_PR: {
      key: 'Quantity_PR',
      value: 'Số lượng PR',
      languageType: 'VI',
      path: 'contract',
      description: 'Số lượng PR',
    },

    Rest_Quantity: {
      key: 'Rest_Quantity',
      value: 'Số lượng còn lại',
      languageType: 'VI',
      path: 'contract',
      description: 'Số lượng còn lại',
    },

    Quantity_Contract_Use: {
      key: 'Quantity_Contract_Use',
      value: 'Số lượng lên hợp đồng',
      languageType: 'VI',
      path: 'contract',
      description: 'Số lượng lên hợp đồng',
    },
    Payment_Plan: {
      key: 'Payment_Plan',
      value: 'Danh sách tiến độ thanh toán',
      languageType: 'VI',
      path: 'contract',
      description: 'Danh sách tiến độ thanh toán',
    },

    Progress: {
      key: 'Progress',
      value: 'Tên tiến độ',
      languageType: 'VI',
      path: 'contract',
      description: 'Tên tiến độ',
    },

    Implementation_Progress: {
      key: 'Implementation_Progress',
      value: 'Tiến độ thực hiện (%)',
      languageType: 'VI',
      path: 'contract',
      description: 'Tiến độ thực hiện (%)',
    },

    Money: {
      key: 'Money',
      value: 'Số tiền',
      languageType: 'VI',
      path: 'contract',
      description: 'Số tiền',
    },

    Time: {
      key: 'Time',
      value: 'Thời gian',
      languageType: 'VI',
      path: 'contract',
      description: 'Thời gian',
    },

    Contract_Po_List: {
      key: 'Contract_Po_List',
      value: 'Danh sách PO',
      languageType: 'VI',
      path: 'contract',
      description: 'Danh sách PO',
    },

    Contract_Appendix_List: {
      key: 'Contract_Appendix_List',
      value: 'Danh sách phụ lục',
      languageType: 'VI',
      path: 'contract',
      description: 'Danh sách phụ lục',
    },

    Contract_Document_Handover_List: {
      key: 'Contract_Document_Handover_List',
      value: 'Bàn giao chứng từ',
      languageType: 'VI',
      path: 'contract',
      description: 'Bàn giao chứng từ',
    },

    //#endregion

    //#region DeliveryDate
    PurchaseOrder_DeliveryDate_InboundCode: {
      key: 'PurchaseOrder_DeliveryDate_InboundCode',
      value: 'Mã inbould',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Mã inbould',
    },
    PurchaseOrder_DeliveryDate_Po: {
      key: 'PurchaseOrder_DeliveryDate_Po',
      value: 'PO',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'PO',
    },
    PurchaseOrder_DeliveryDate_SelectPo: {
      key: 'PurchaseOrder_DeliveryDate_SelectPo',
      value: 'Vui lòng chọn PO!',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Vui lòng chọn PO!',
    },
    PurchaseOrder_DeliveryDate_EstimatedPort: {
      key: 'PurchaseOrder_DeliveryDate_EstimatedPort',
      value: 'Thời gian dự kiến về cảng',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Thời gian dự kiến về cảng',
    },
    PurchaseOrder_DeliveryDate_Contract: {
      key: 'PurchaseOrder_DeliveryDate_Contract',
      value: 'Hợp đồng',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Hợp đồng',
    },
    PurchaseOrder_DeliveryDate_PleaseSelectContract: {
      key: 'PurchaseOrder_DeliveryDate_PleaseSelectContract',
      value: 'Vui lòng chọn Hợp đồng!',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Vui lòng chọn Hợp đồng!',
    },
    PurchaseOrder_DeliveryDate_SelectContract: {
      key: 'PurchaseOrder_DeliveryDate_SelectContract',
      value: 'Chọn Hợp đồng',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Chọn Hợp đồng',
    },
    PurchaseOrder_DeliveryDate_ListItems: {
      key: 'PurchaseOrder_DeliveryDate_ListItems',
      value: 'Danh sách Items',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Danh sách Items',
    },
    PurchaseOrder_DeliveryDate_EstimatedDeliveryQuantity: {
      key: 'PurchaseOrder_DeliveryDate_EstimatedDeliveryQuantity',
      value: 'Số lượng giao dự kiến',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Số lượng giao dự kiến',
    },
    PurchaseOrder_DeliveryDate_BatchNumber: {
      key: 'PurchaseOrder_DeliveryDate_BatchNumber',
      value: 'Số lô hàng',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Số lô hàng',
    },
    PurchaseOrder_DeliveryDate_ContNumber: {
      key: 'PurchaseOrder_DeliveryDate_ContNumber',
      value: 'Số Cont',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Số Cont',
    },
    PurchaseOrder_DeliveryDate_ShippingSealNumber: {
      key: 'PurchaseOrder_DeliveryDate_ShippingSealNumber',
      value: 'Số seal hãng tàu',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Số seal hãng tàu',
    },
    PurchaseOrder_DeliveryDate_SealNumber: {
      key: 'PurchaseOrder_DeliveryDate_SealNumber',
      value: 'Số seal',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Số seal',
    },
    PurchaseOrder_DeliveryDate_ContType: {
      key: 'PurchaseOrder_DeliveryDate_ContType',
      value: 'Loại cont',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Loại cont',
    },
    PurchaseOrder_DeliveryDate_PackagesContNumber: {
      key: 'PurchaseOrder_DeliveryDate_PackagesContNumber',
      value: 'Số kiện trên cont',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Số kiện trên cont',
    },
    PurchaseOrder_DeliveryDate_SaveInformation: {
      key: 'PurchaseOrder_DeliveryDate_SaveInformation',
      value: 'Lưu thông tin',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Lưu thông tin',
    },
    PurchaseOrder_DeliveryDate_ListContainer: {
      key: 'PurchaseOrder_DeliveryDate_ListContainer',
      value: 'Danh sách Container',
      languageType: 'VI',
      path: 'delivery-date',
      description: 'Danh sách Container',
    },

    //#endregion

    //#region SupplierRegistration
    SupplierRegistration_ChooseCompany: {
      key: 'SupplierRegistration_ChooseCompany',
      value: 'Chọn công ty muốn làm việc',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn công ty muốn làm việc',
    },
    SupplierRegistration_PleaseChooseCompany: {
      key: 'SupplierRegistration_PleaseChooseCompany',
      value: 'Vui lòng chọn công ty muốn làm việc',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng chọn công ty muốn làm việc',
    },
    SupplierRegistration_PleaseLeaveWithEmptyCompany: {
      key: 'SupplierRegistration_PleaseLeaveWithEmptyCompany',
      value: 'Nếu bạn chưa xác định được công ty muốn làm việc thì vui lòng bỏ trống',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nếu bạn chưa xác định được công ty muốn làm việc thì vui lòng bỏ trống',
    },
    SupplierRegistration_ProductionAndBusinessType: {
      key: 'SupplierRegistration_ProductionAndBusinessType',
      value: 'Loại hình sản xuất/kinh doanh',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Loại hình sản xuất kinh doanh',
    },
    SupplierRegistration_PleaseEnterProductionAndBusinessType: {
      key: 'SupplierRegistration_PleaseEnterProductionAndBusinessType',
      value: 'Vui lòng chọn loại hình sản xuất/kinh doanh',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng chọn loại hình sản xuất/kinh doanh',
    },
    SupplierRegistration_EnterProductionAndBusinessType: {
      key: 'SupplierRegistration_EnterProductionAndBusinessType',
      value: 'Chọn loại hình sản xuất/kinh doanh',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn loại hình sản xuất/kinh doanh',
    },
    SupplierRegistration_Certification: {
      key: 'SupplierRegistration_Certification',
      value: 'Nhà cung cấp có đạt chứng nhận ISO 9000 hoặc chứng nhận tương đương - Vui lòng nêu rõ và đính kèm giấy chứng nhận',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhà cung cấp có đạt chứng nhận ISO 9000 hoặc chứng nhận tương đương - Vui lòng nêu rõ và đính kèm giấy chứng nhận',
    },

    //region "Strategic Vision"
    SupplierRegistration_StrategicVisionHeader: {
      key: 'SupplierRegistration_StrategicVisionHeader',
      value: 'II. MỘT SỐ THÔNG TIN CƠ BẢN VỀ TẦM NHÌN CHIẾN LƯỢC',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'II. MỘT SỐ THÔNG TIN CƠ BẢN VỀ TẦM NHÌN CHIẾN LƯỢC',
    },

    SupplierRegistration_VisionAndMission: {
      key: 'SupplierRegistration_VisionAndMission',
      value: 'Tầm nhìn và sứ mệnh nhà cung cấp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tầm nhìn và sứ mệnh nhà cung cấp',
    },

    SupplierRegistration_PleaseEnterVisionAndMission: {
      key: 'SupplierRegistration_PleaseEnterVisionAndMission',
      value: 'Vui lòng nhập Tầm nhìn và sứ mệnh nhà cung cấp từ 1-2000 kí tự!!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Tầm nhìn và sứ mệnh nhà cung cấp từ 1-2000 kí tự!!',
    },

    SupplierRegistration_MediumAndLongTermGoals: {
      key: 'SupplierRegistration_MediumAndLongTermGoals',
      value: 'Mục tiêu trung và dài hạn',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Mục tiêu trung và dài hạn',
    },

    SupplierRegistration_PleaseEnterMediumAndLongTermGoals: {
      key: 'SupplierRegistration_PleaseEnterMediumAndLongTermGoals',
      value: 'Vui lòng nhập mục tiêu trung và dài hạn từ 1-2000 kí tự!!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập mục tiêu trung và dài hạn từ 1-2000 kí tự!!',
    },

    SupplierRegistration_HumanResource: {
      key: 'SupplierRegistration_HumanResource',
      value: 'III. NHÂN SỰ',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'III. NHÂN SỰ',
    },
    //#endreigon "Strategic Vision"

    SupplierRegistration_BusinessRegistrationCertificate: {
      key: 'SupplierRegistration_BusinessRegistrationCertificate',
      value: 'Giấy phép đăng ký kinh doanh',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Giấy phép đăng ký kinh doanh',
    },

    SupplierRegistration_RegisterSupplierInformation: {
      key: 'SupplierRegistration_RegisterSupplierInformation',
      value: 'ĐĂNG KÍ THÔNG TIN NHÀ CUNG CẤP',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'ĐĂNG KÍ THÔNG TIN NHÀ CUNG CẤP',
    },
    SupplierRegistration_AbbreviatedName: {
      key: 'SupplierRegistration_AbbreviatedName',
      value: 'Tên viết tắt',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tên viết tắt',
    },
    SupplierRegistration_EnterAbbreviatedName: {
      key: 'SupplierRegistration_EnterAbbreviatedName',
      value: 'Vui lòng nhập tên viết tắt (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tên viết tắt (1-250 kí tự)!',
    },
    SupplierRegistration_VietnameseEnterprises: {
      key: 'SupplierRegistration_VietnameseEnterprises',
      value: 'Doanh nghiệp Việt Nam',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Doanh nghiệp Việt Nam',
    },
    SupplierRegistration_TypeBusiness: {
      key: 'SupplierRegistration_TypeBusiness',
      value: 'Loại hình doanh nghiệp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Loại hình doanh nghiệp',
    },
    SupplierRegistration_PleaseSelectTypeBusiness: {
      key: 'SupplierRegistration_PleaseSelectTypeBusiness',
      value: 'Vui lòng chọn Loại hình doanh nghiệp!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng chọn Loại hình doanh nghiệp!',
    },
    SupplierRegistration_SelectTypeBusiness: {
      key: 'SupplierRegistration_SelectTypeBusiness',
      value: 'Chọn Loại hình doanh nghiệp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn Loại hình doanh nghiệp',
    },
    SupplierRegistration_Country: {
      key: 'SupplierRegistration_Country',
      value: 'Quốc gia',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Quốc gia',
    },
    SupplierRegistration_PleaseSelectCountry: {
      key: 'SupplierRegistration_PleaseSelectCountry',
      value: 'Vui lòng chọn Quốc gia!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng chọn Quốc gia!',
    },
    SupplierRegistration_SelectCountry: {
      key: 'SupplierRegistration_SelectCountry',
      value: 'Chọn Quốc gia',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn Quốc gia',
    },
    SupplierRegistration_Fax: {
      key: 'SupplierRegistration_Fax',
      value: 'Số Fax',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số Fax',
    },
    SupplierRegistration_PleaseEnterFax: {
      key: 'SupplierRegistration_PleaseEnterFax',
      value: 'Vui lòng nhập Số Fax (4-20 số)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Số Fax (4-20 số)!',
    },
    SupplierRegistration_EnterFax: {
      key: 'SupplierRegistration_PleaseEnterFax',
      value: 'Nhập Số Fax',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Số Fax',
    },
    SupplierRegistration_Website: {
      key: 'SupplierRegistration_Website',
      value: 'Website',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Website',
    },
    SupplierRegistration_PleaseEnterWebsite: {
      key: 'SupplierRegistration_PleaseEnterWebsite',
      value: 'Vui lòng nhập Website (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Website (1-50 kí tự)!',
    },
    SupplierRegistration_EnterWebsite: {
      key: 'SupplierRegistration_EnterWebsite',
      value: 'Nhập Đường link website',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Đường link website',
    },
    SupplierRegistration_StartDateKimTin: {
      key: 'SupplierRegistration_StartDateKimTin',
      value: 'Ngày bắt đầu giao dịch với Kim Tín',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Ngày bắt đầu giao dịch với Kim Tín',
    },
    SupplierRegistration_PleaseEnterStartDateKimTin: {
      key: 'SupplierRegistration_PleaseEnterStartDateKimTin',
      value: 'Vui lòng nhập Ngày bắt đầu giao dịch với Kim Tín!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Ngày bắt đầu giao dịch với Kim Tín!',
    },
    SupplierRegistration_EnterStartDate: {
      key: 'SupplierRegistration_EnterStartDate',
      value: 'Nhập Ngày hiệu lực',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Ngày hiệu lực',
    },
    SupplierRegistration_PositionRepresentative: {
      key: 'SupplierRegistration_PositionRepresentative',
      value: 'Chức vụ người ĐDPL',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chức vụ người ĐDPL',
    },
    SupplierRegistration_PleaseEnterPositionRepresentative: {
      key: 'SupplierRegistration_PleaseEnterPositionRepresentative',
      value: 'Vui lòng nhập Chức vụ người ĐDPL (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Chức vụ người ĐDPL (1-50 kí tự)!',
    },
    SupplierRegistration_EnterPositionRepresentative: {
      key: 'SupplierRegistration_EnterPositionRepresentative',
      value: 'Nhập Chức vụ người ĐDPL',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Chức vụ người ĐDPL',
    },
    SupplierRegistration_PhoneNumberRepresentative: {
      key: 'SupplierRegistration_PhoneNumberRepresentative',
      value: 'Số điện thoại người ĐDPL',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số điện thoại người ĐDPL',
    },
    SupplierRegistration_PleaseEnterPhoneNumberRepresentative: {
      key: 'SupplierRegistration_PleaseEnterPhoneNumberRepresentative',
      value: 'Vui lòng nhập số điện thoại  (10-11 số)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập số điện thoại  10-11 số',
    },
    SupplierRegistration_EnterPhoneNumberRepresentative: {
      key: 'SupplierRegistration_EnterPhoneNumberRepresentative',
      value: 'Nhập số điện thoại người ĐDPL',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập số điện thoại người ĐDPL',
    },
    SupplierRegistration_FaxRepresentative: {
      key: 'SupplierRegistration_FaxRepresentative',
      value: 'Số Fax người ĐDPL',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số Fax người ĐDPL',
    },
    SupplierRegistration_PleaseEnterFaxRepresentative: {
      key: 'SupplierRegistration_PleaseEnterFaxRepresentative',
      value: 'Vui lòng nhập Số Fax người ĐDPL (4-20 số)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Số Fax người ĐDPL (4-20 số)!',
    },
    SupplierRegistration_EnterFaxRepresentative: {
      key: 'SupplierRegistration_EnterFaxRepresentative',
      value: 'Nhập Số Fax người ĐDPL',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Số Fax người ĐDPL',
    },
    SupplierRegistration_EmailRepresentative: {
      key: 'SupplierRegistration_EmailRepresentative',
      value: 'Email người ĐDPL',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Email người ĐDPL',
    },
    SupplierRegistration_PleaseEnterEmailRepresentative: {
      key: 'SupplierRegistration_PleaseEnterEmailRepresentative',
      value: 'Vui lòng nhập email người ĐDPL hợp lệ!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập email người ĐDPL hợp lệ!',
    },
    SupplierRegistration_EnterEmailRepresentative: {
      key: 'SupplierRegistration_EnterEmailRepresentative',
      value: 'Nhập email người ĐDPL hợp lệ',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập email người ĐDPL hợp lệ',
    },
    SupplierRegistration_NoteAboutRepresentative: {
      key: 'SupplierRegistration_NoteAboutRepresentative',
      value: 'Lưu ý về người ĐDPL',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Lưu ý về người ĐDPL',
    },
    SupplierRegistration_PleaseEnterNoteAboutRepresentative: {
      key: 'SupplierRegistration_PleaseEnterNoteAboutRepresentative',
      value: 'Vui lòng nhập Lưu ý về người ĐDPL (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Lưu ý về người ĐDPL (1-50 kí tự)!',
    },
    SupplierRegistration_EnterNoteAboutRepresentative: {
      key: 'SupplierRegistration_EnterNoteAboutRepresentative',
      value: 'Nhập Lưu ý về người ĐDPL',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Lưu ý về người ĐDPL',
    },
    SupplierRegistration_Decider: {
      key: 'SupplierRegistration_Decider',
      value: 'Người quyết định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Người quyết định',
    },
    SupplierRegistration_PleaseEnterDecider: {
      key: 'SupplierRegistration_PleaseEnterDecider',
      value: 'Vui lòng nhập Người quyết định (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Người quyết định (1-50 kí tự)!',
    },
    SupplierRegistration_EnterDecider: {
      key: 'SupplierRegistration_EnterDecider',
      value: 'Nhập Người quyết định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Người quyết định',
    },
    SupplierRegistration_PositionDecider: {
      key: 'SupplierRegistration_PositionDecider',
      value: 'Chức vụ người quyết định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chức vụ người quyết định',
    },
    SupplierRegistration_PleaseEnterPositionDecider: {
      key: 'SupplierRegistration_PleaseEnterPositionDecider',
      value: 'Vui lòng nhập Chức vụ người quyết định (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Chức vụ người quyết định (1-50 kí tự)!',
    },
    SupplierRegistration_EnterPositionDecider: {
      key: 'SupplierRegistration_EnterPositionDecider',
      value: 'Nhập Chức vụ người quyết định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Chức vụ người quyết định',
    },
    SupplierRegistration_PhoneNumberDecider: {
      key: 'SupplierRegistration_PhoneNumberDecider',
      value: 'Số điện thoại người quyết định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số điện thoại người quyết định',
    },
    SupplierRegistration_PleaseEnterPhoneNumberDecider: {
      key: 'SupplierRegistration_PleaseEnterPhoneNumberDecider',
      value: 'Vui lòng nhập số điện thoại người quyết định (10-11 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập số điện thoại người quyết định 10-11 kí tự',
    },
    SupplierRegistration_EnterPhoneNumberDecider: {
      key: 'SupplierRegistration_EnterPhoneNumberDecider',
      value: 'Nhập Số điện thoại người quyết định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Số điện thoại người quyết định',
    },
    SupplierRegistration_FaxDecider: {
      key: 'SupplierRegistration_FaxDecider',
      value: 'Số Fax người quyết định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số Fax người quyết định',
    },
    SupplierRegistration_PleaseEnterFaxDecider: {
      key: 'SupplierRegistration_PleaseEnterFaxDecider',
      value: 'Vui lòng nhập Số Fax người quyết định (4-20 số)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Số Fax người quyết định (4-20 số)!',
    },
    SupplierRegistration_EnterFaxDecider: {
      key: 'SupplierRegistration_EnterFaxDecider',
      value: 'Nhập Số Fax người quyết định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Số Fax người quyết định',
    },
    SupplierRegistration_EmailDecider: {
      key: 'SupplierRegistration_EmailDecider',
      value: 'Email người quyết định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Email người quyết định',
    },
    SupplierRegistration_PleaseEnterEmailDecider: {
      key: 'SupplierRegistration_PleaseEnterEmailDecider',
      value: 'Vui lòng nhập email người quyết định hợp lệ!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập email người quyết định hợp lệ!',
    },
    SupplierRegistration_EnterEmailDecider: {
      key: 'SupplierRegistration_EnterEmailDecider',
      value: 'Nhập email người quyết định hợp lệ',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập email người quyết định hợp lệ',
    },
    SupplierRegistration_NoteAboutDecider: {
      key: 'SupplierRegistration_NoteAboutDecider',
      value: 'Lưu ý về người quyết định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Lưu ý về người quyết định',
    },
    SupplierRegistration_PleaseEnterNoteAboutDecider: {
      key: 'SupplierRegistration_PleaseEnterNoteAboutDecider',
      value: 'Vui lòng nhập Lưu ý về người quyết định (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Lưu ý về người quyết định (1-250 kí tự)!',
    },
    SupplierRegistration_EnterNoteAboutDecider: {
      key: 'SupplierRegistration_EnterNoteAboutDecider',
      value: 'Nhập Lưu ý về người quyết định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Lưu ý về người quyết định',
    },
    SupplierRegistration_Trader: {
      key: 'SupplierRegistration_Trader',
      value: 'Người giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Người giao dịch',
    },
    SupplierRegistration_PleaseEnterTrader: {
      key: 'SupplierRegistration_PleaseEnterTrader',
      value: 'Vui lòng nhập Người giao dịch (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Người giao dịch (1-50 kí tự)!',
    },
    SupplierRegistration_EnterTrader: {
      key: 'SupplierRegistration_EnterTrader',
      value: 'Nhập Người giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Người giao dịch',
    },
    SupplierRegistration_PositionTrader: {
      key: 'SupplierRegistration_PositionTrader',
      value: 'Chức vụ người giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chức vụ người giao dịch',
    },
    SupplierRegistration_PleaseEnterPositionTrader: {
      key: 'SupplierRegistration_PleaseEnterPositionTrader',
      value: 'Vui lòng nhập Chức vụ người giao dịch (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Chức vụ người giao dịch (1-50 kí tự)!',
    },
    SupplierRegistration_EnterPositionTrader: {
      key: 'SupplierRegistration_EnterPositionTrader',
      value: 'Nhập Chức vụ người giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Chức vụ người giao dịch',
    },
    SupplierRegistration_PhoneNumberTrader: {
      key: 'SupplierRegistration_PhoneNumberTrader',
      value: 'Số điện thoại người giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số điện thoại người giao dịch',
    },
    SupplierRegistration_PleaseEnterPhoneNumberTrader: {
      key: 'SupplierRegistration_PleaseEnterPhoneNumberTrader',
      value: 'Vui lòng nhập số điện thoại người giao dịch (10-11 kí tự)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập số điện thoại người giao dịch 10-11 kí tự',
    },
    SupplierRegistration_EnterPhoneNumberTrader: {
      key: 'SupplierRegistration_EnterPhoneNumberTrader',
      value: 'Nhập số điện thoại người giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập số điện thoại người giao dịch',
    },
    SupplierRegistration_FaxTrader: {
      key: 'SupplierRegistration_FaxTrader',
      value: 'Số Fax người giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số Fax người giao dịch',
    },
    SupplierRegistration_PleaseEnterFaxTrader: {
      key: 'SupplierRegistration_PleaseEnterFaxTrader',
      value: 'Vui lòng nhập Số Fax người giao dịch (4-20 số)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Số Fax người giao dịch (4-20 số)!',
    },
    SupplierRegistration_EnterFaxTrader: {
      key: 'SupplierRegistration_EnterFaxTrader',
      value: 'Nhập Số Fax người giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Số Fax người giao dịch',
    },
    SupplierRegistration_EmailTrader: {
      key: 'SupplierRegistration_EmailTrader',
      value: 'Email người giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Email người giao dịch',
    },
    SupplierRegistration_PleaseEnterEmailTrader: {
      key: 'SupplierRegistration_PleaseEnterEmailTrader',
      value: 'Vui lòng nhập email người giao dịch hợp lệ!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập email người giao dịch hợp lệ!',
    },
    SupplierRegistration_EnterEmailTrader: {
      key: 'SupplierRegistration_EnterEmailTrader',
      value: 'Nhập email người giao dịch hợp lệ',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập email người giao dịch hợp lệ',
    },
    SupplierRegistration_NoteAboutTrader: {
      key: 'SupplierRegistration_NoteAboutTrader',
      value: 'Lưu ý về người giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Lưu ý về người giao dịch',
    },
    SupplierRegistration_PleaseEnterNoteAboutTrader: {
      key: 'SupplierRegistration_PleaseEnterNoteAboutTrader',
      value: 'Vui lòng nhập Lưu ý về người giao dịch (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Lưu ý về người giao dịch (1-50 kí tự)!',
    },
    SupplierRegistration_EnterNoteAboutTrader: {
      key: 'SupplierRegistration_EnterNoteAboutTrader',
      value: 'Nhập Lưu ý về người giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Lưu ý về người giao dịch',
    },
    SupplierRegistration_DevelopmentProcess: {
      key: 'SupplierRegistration_DevelopmentProcess',
      value: 'Sơ lược về quá trình hình thành & phát triển của nhà cung cấp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Sơ lược về quá trình hình thành & phát triển của nhà cung cấp',
    },
    SupplierRegistration_PleaseEnterDevelopmentProcess: {
      key: 'SupplierRegistration_PleaseEnterDevelopmentProcess',
      value: 'Vui lòng nhập Sơ lược về quá trình hình thành & phát triển của nhà cung cấp!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Sơ lược về quá trình hình thành & phát triển của nhà cung cấp!',
    },
    SupplierRegistration_ConditionalBusinessLicense: {
      key: 'SupplierRegistration_ConditionalBusinessLicense',
      value: 'Giấy phép kinh doanh có điều kiện',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Giấy phép kinh doanh có điều kiện',
    },
    SupplierRegistration_PleaseConditionalBusinessLicense: {
      key: 'SupplierRegistration_PleaseConditionalBusinessLicense',
      value: 'Vui lòng upload Giấy phép kinh doanh có điều kiện',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng upload Giấy phép kinh doanh có điều kiện',
    },
    SupplierRegistration_BankCode: {
      key: 'SupplierRegistration_BankCode',
      value: 'Mã ngân hàng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Mã ngân hàng',
    },
    SupplierRegistration_AccountOpeningNotificationFile: {
      key: 'SupplierRegistration_AccountOpeningNotificationFile',
      value: 'File thông báo mở TK',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'File thông báo mở TK',
    },
    SupplierRegistration_MoreFactories: {
      key: 'SupplierRegistration_MoreFactories',
      value: 'Thêm nhà máy',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Thêm nhà máy',
    },
    SupplierRegistration_FactoryName: {
      key: 'SupplierRegistration_FactoryName',
      value: 'Tên nhà máy sản xuất',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tên nhà máy sản xuất',
    },
    SupplierRegistration_FactoryAddress: {
      key: 'SupplierRegistration_FactoryAddress',
      value: 'Địa chỉ nhà máy sản xuất',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Địa chỉ nhà máy sản xuất',
    },
    SupplierRegistration_ProvidingCapacityInformation: {
      key: 'SupplierRegistration_ProvidingCapacityInformation',
      value: 'Cung cấp thông tin năng lực:',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Cung cấp thông tin năng lực:',
    },
    SupplierRegistration_Criteria: {
      key: 'SupplierRegistration_Criteria',
      value: 'Tiêu chí',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tiêu chí',
    },
    SupplierRegistration_DataType: {
      key: 'SupplierRegistration_DataType',
      value: 'Kiểu dữ liệu',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Kiểu dữ liệu',
    },
    SupplierRegistration_Value: {
      key: 'SupplierRegistration_Value',
      value: 'Giá trị',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Giá trị',
    },
    SupplierRegistration_Year: {
      key: 'SupplierRegistration_Year',
      value: 'Năm',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Năm',
    },
    SupplierRegistration_ChooseFile: {
      key: 'SupplierRegistration_ChooseFile',
      value: 'Chọn file',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn file',
    },
    SupplierRegistration_AddValue: {
      key: 'SupplierRegistration_AddValue',
      value: 'Thêm giá trị',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Thêm giá trị',
    },
    SupplierRegistration_GeneralInformation: {
      key: 'SupplierRegistration_GeneralInformation',
      value: 'Thông tin chung',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Thông tin chung',
    },
    SupplierRegistration_CompanyName: {
      key: 'SupplierRegistration_CompanyName',
      value: 'Tên doanh nghiệp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tên doanh nghiệp',
    },
    SupplierRegistration_TradingName: {
      key: 'SupplierRegistration_TradingName',
      value: 'Tên giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tên giao dịch',
    },
    SupplierRegistration_CompanyCreateYear: {
      key: 'SupplierRegistration_CompanyCreateYear',
      value: 'Năm thành lập công ty',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Năm thành lập công ty',
    },
    SupplierRegistration_BusinessCode: {
      key: 'SupplierRegistration_BusinessCode',
      value: 'Mã số doanh nghiệp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Mã số doanh nghiệp',
    },
    SupplierRegistration_PleaseEnterBusinessCode: {
      key: 'SupplierRegistration_PleaseEnterBusinessCode',
      value: 'Vui lòng nhập mã số doanh nghiệp gồm 10 hoặc 13 chữ số!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập mã số doanh nghiệp gồm 10 hoặc 13 chữ số!',
    },
    SupplierRegistration_EnterBusinessCode: {
      key: 'SupplierRegistration_EnterBusinessCode',
      value: 'Nhập mã số doanh nghiệp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập mã số doanh nghiệp',
    },
    SupplierRegistration_HeadOffice: {
      key: 'SupplierRegistration_HeadOffice',
      value: 'Trụ sở chính (Địa chỉ)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Trụ sở chính (Địa chỉ)',
    },
    SupplierRegistration_PleaseSelectHeadOffice: {
      key: 'SupplierRegistration_PleaseSelectHeadOffice',
      value: 'Vui lòng nhập Trụ sở chính (Địa chỉ) (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Trụ sở chính (Địa chỉ) (1-250 kí tự)!',
    },
    SupplierRegistration_SelectHeadOffice: {
      key: 'SupplierRegistration_SelectHeadOffice',
      value: 'Nhập Trụ sở chính (Địa chỉ)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Trụ sở chính (Địa chỉ)',
    },
    SupplierRegistration_PhoneNumberReceive: {
      key: 'SupplierRegistration_PhoneNumberReceive',
      value: 'Số điện thoại nhận thông báo hệ thống',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số điện thoại nhận thông báo hệ thống',
    },
    SupplierRegistration_PleaseEnterPhoneNumberReceive: {
      key: 'SupplierRegistration_PleaseEnterPhoneNumberReceive',
      value: 'Vui lòng nhập số điện thoại nhận thông báo hệ thống 10-11 kí tự',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập số điện thoại nhận thông báo hệ thống 10-11 kí tự',
    },
    SupplierRegistration_EnterPhoneNumberReceive: {
      key: 'SupplierRegistration_EnterPhoneNumberReceive',
      value: 'Nhập số điện thoại nhận thông báo hệ thống',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập số điện thoại nhận thông báo hệ thống',
    },
    SupplierRegistration_OfficeAddress: {
      key: 'SupplierRegistration_OfficeAddress',
      value: 'Địa chỉ trụ sở',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Địa chỉ trụ sở',
    },
    SupplierRegistration_PleaesEnterCompanyName: {
      key: 'SupplierRegistration_PleaseEnterCompanyName',
      value: 'Vui lòng nhập tên doanh nghiệp (4-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Lời nhắc nhập tên doanh nghiệp',
    },
    SupplierRegistration_EnterCompanyName: {
      key: 'SupplierRegistration_EnterCompanyName',
      value: 'Nhập tên doanh nghiệp (4-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập tên doanh nghiệp (4-250 kí tự)!',
    },
    SupplierRegistration_EnterTradingName: {
      key: 'SupplierRegistration_EnterTradingName',
      value: 'Vui lòng nhập tên giao dịch (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Lời nhắc nhập tên giao dịch',
    },
    SupplierRegistration_SelectBusinessField: {
      key: 'SupplierRegistration_SelectBusinessField',
      value: 'Chọn lĩnh vực kinh doanh',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn lĩnh vực kinh doanh',
    },
    SupplierRegistration_AddBusinessArea: {
      key: 'SupplierRegistration_AddBusinessArea',
      value: 'Thêm lĩnh vực kinh doanh',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Thêm lĩnh vực kinh doanh',
    },
    SupplierRegistration_DownloadTemplate: {
      key: 'SupplierRegistration_DownloadTemplate',
      value: 'Tải template',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tải template',
    },
    SupplierRegistration_UploadExcel: {
      key: 'SupplierRegistration_UploadExcel',
      value: 'Upload Excel',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Upload Excel',
    },

    SupplierRegistration_TradingAddress: {
      key: 'SupplierRegistration_TradingAddress',
      value: 'Địa chỉ giao dịch (1)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Địa chỉ giao dịch (1)',
    },

    SupplierRegistration_TradingAddress2: {
      key: 'SupplierRegistration_TradingAddress2',
      value: 'Địa chỉ giao dịch (2)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Địa chỉ giao dịch (2)',
    },

    SupplierRegistration_TradingAddress3: {
      key: 'SupplierRegistration_TradingAddress3',
      value: 'Địa chỉ giao dịch (3)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Địa chỉ giao dịch (3)',
    },

    SupplierRegistration_TradingAddress4: {
      key: 'SupplierRegistration_TradingAddress4',
      value: 'Địa chỉ giao dịch (4)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Địa chỉ giao dịch (4)',
    },

    SupplierRegistration_TradingAddress5: {
      key: 'SupplierRegistration_TradingAddress5',
      value: 'Địa chỉ giao dịch (5)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Địa chỉ giao dịch (5)',
    },
    SupplierRegistration_PleaseChooseTradingAddress: {
      key: 'SupplierRegistration_PleaseChooseTradingAddress',
      value: 'Vui lòng nhập địa chỉ giao dịch (1-35 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập địa chỉ giao dịch (1-35 kí tự)!',
    },
    SupplierRegistration_ChooseTradingAddress: {
      key: 'SupplierRegistration_ChooseTradingAddress',
      value: 'Nhập địa chỉ giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập địa chỉ giao dịch',
    },
    SupplierRegistration_LegalRepresentative: {
      key: 'SupplierRegistration_LegalRepresentative',
      value: 'Người đại diện pháp luật (ĐDPL)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Người đại diện pháp luật (ĐDPL)',
    },
    SupplierRegistration_EnterLegalRepresentative: {
      key: 'SupplierRegistration_EnterLegalRepresentative',
      value: 'Vui lòng nhập người đại diện pháp luật (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập người đại diện pháp luật (1-50 kí tự)!',
    },
    SupplierRegistration_InputLegalRepresentative: {
      key: 'SupplierRegistration_InputLegalRepresentative',
      value: 'Nhập Người đại diện pháp luật',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Người đại diện pháp luật',
    },
    SupplierRegistration_DirectorName: {
      key: 'SupplierRegistration_DirectorName',
      value: 'Tên giám đốc',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tên giám đốc',
    },
    SupplierRegistration_PleaseEnterDirectorName: {
      key: 'SupplierRegistration_PleaseEnterDirectorName',
      value: 'Vui lòng nhập tên giám đốc (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tên giám đốc (1-250 kí tự)!',
    },
    SupplierRegistration_EnterDirectorName: {
      key: 'SupplierRegistration_EnterDirectorName',
      value: 'Nhập tên giám đốc (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập tên giám đốc (1-250 kí tự)!',
    },
    SupplierRegistration_CharterCapital: {
      key: 'SupplierRegistration_CharterCapital',
      value: 'Vốn điều lệ',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vốn điều lệ',
    },
    SupplierRegistration_PleaseEnterCharterCapital: {
      key: 'SupplierRegistration_PleaseEnterCharterCapital',
      value: 'Vui lòng nhập vốn điều lệ đúng định dạng là ký tự số !',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập vốn điều lệ đúng định dạng là ký tự số !',
    },
    SupplierRegistration_FixedAssets: {
      key: 'SupplierRegistration_FixedAssets',
      value: 'Tài sản cố định',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tài sản cố định',
    },
    SupplierRegistration_EnterFixedAssets: {
      key: 'SupplierRegistration_EnterFixedAssets',
      value: 'Vui lòng nhập tài sản cố định (tỷ đồng)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tài sản cố định (tỷ đồng)!',
    },
    SupplierRegistration_SampleInvoiceReceiptFile: {
      key: 'SupplierRegistration_SampleInvoiceReceiptFile',
      value: 'File hóa đơn mẫu/phiếu thu/biên lai',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'File hóa đơn mẫu/phiếu thu/biên lai',
    },
    SupplierRegistration_EnterSampleInvoiceReceiptFile: {
      key: 'SupplierRegistration_EnterSampleInvoiceReceiptFile',
      value: 'Vui lòng upload file hóa đơn mẫu/phiếu thu/biên lai',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng upload file hóa đơn mẫu/phiếu thu/biên lai',
    },
    SupplierRegistration_InvoiceIssuanceInforFile: {
      key: 'SupplierRegistration_InvoiceIssuanceInforFile',
      value: 'File thông tin phát hành hóa đơn',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'File thông tin phát hành hóa đơn',
    },
    SupplierRegistration_UpInvoiceIssuanceInforFile: {
      key: 'SupplierRegistration_UpInvoiceIssuanceInforFile',
      value: 'Vui lòng upload file thông tin phát hành hóa đơn',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng upload file thông tin phát hành hóa đơn',
    },
    SupplierRegistration_BusinessLicenseTaxCode: {
      key: 'SupplierRegistration_BusinessLicenseTaxCode',
      value: 'Giấy phép kinh doanh/Mã số thuế',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Giấy phép kinh doanh/Mã số thuế',
    },
    SupplierRegistration_EnterBusinessLicenseTaxCode: {
      key: 'SupplierRegistration_EnterBusinessLicenseTaxCode',
      value: 'Vui lòng upload giấy phép kinh doanh/Mã số thuế',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng upload giấy phép kinh doanh/Mã số thuế',
    },
    SupplierRegistration_BankName: {
      key: 'SupplierRegistration_BankName',
      value: 'Ngân hàng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Ngân hàng',
    },
    SupplierRegistration_EnterBankName: {
      key: 'SupplierRegistration_EnterBankName',
      value: 'Vui lòng nhập tên ngân hàng (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tên ngân hàng (1-250 kí tự)!',
    },
    SupplierRegistration_Bank: {
      key: 'SupplierRegistration_Bank',
      value: 'Tài khoản ngân hàng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tài khoản ngân hàng',
    },
    SupplierRegistration_AddBank: {
      key: 'SupplierRegistration_AddBank',
      value: 'Thêm ngân hàng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Thêm ngân hàng',
    },
    SupplierRegistration_CardHolder: {
      key: 'SupplierRegistration_CardHolder',
      value: 'Chủ thẻ',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chủ thẻ',
    },
    SupplierRegistration_SwiftCode: {
      key: 'SupplierRegistration_SwiftCode',
      value: 'Swift Code',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Swift Code',
    },
    SupplierRegistration_Iban: {
      key: 'SupplierRegistration_Iban',
      value: 'IBAN',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'IBAN',
    },
    SupplierRegistration_BankNumber: {
      key: 'SupplierRegistration_BankNumber',
      value: 'Số tài khoản ngân hàng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số tài khoản ngân hàng',
    },
    SupplierRegistration_EnterAccountNumber: {
      key: 'SupplierRegistration_EnterAccountNumber',
      value: 'Nhập bổ sung STK',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập bổ sung STK',
    },
    SupplierRegistration_EnterBankNumber: {
      key: 'SupplierRegistration_EnterBankNumber',
      value: 'Vui lòng nhập số tài khoản ngân hàng (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập số tài khoản ngân hàng (1-50 kí tự)!',
    },
    SupplierRegistration_BankBranch: {
      key: 'SupplierRegistration_BankBranch',
      value: 'Chi nhánh ngân hàng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chi nhánh ngân hàng',
    },
    SupplierRegistration_EnterBankBranch: {
      key: 'SupplierRegistration_EnterBankBranch',
      value: 'Vui lòng nhập tên chi nhánh ngân hàng (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tên chi nhánh ngân hàng (1-250 kí tự)!',
    },
    SupplierRegistration_ContactName: {
      key: 'SupplierRegistration_ContactName',
      value: 'Người liên hệ',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Người liên hệ',
    },
    SupplierRegistration_EnterContactName: {
      key: 'SupplierRegistration_EnterContactName',
      value: 'Vui lòng nhập người liên hệ (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập người liên hệ (1-50 kí tự)!',
    },
    SupplierRegistration_PhoneNumber: {
      key: 'SupplierRegistration_PhoneNumber',
      value: 'Số điện thoại',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số điện thoại',
    },
    SupplierRegistration_EnterPhoneNumber: {
      key: 'SupplierRegistration_EnterPhoneNumber',
      value: 'Vui lòng nhập số điện thoại (10-11 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập số điện thoại (10-11 kí tự)!',
    },
    SupplierRegistration_Email: {
      key: 'SupplierRegistration_Email',
      value: 'Email',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Email',
    },
    SupplierRegistration_PleaseEnterEmail: {
      key: 'SupplierRegistration_PleaseEnterEmail',
      value: 'Vui lòng nhập email hợp lệ!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập email hợp lệ!',
    },
    SupplierRegistration_EnterEmail: {
      key: 'SupplierRegistration_EnterEmail',
      value: 'Nhập email hợp lệ',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập email hợp lệ',
    },
    SupplierRegistration_ChooseProvince: {
      key: 'SupplierRegistration_ChooseProvince',
      value: 'Tỉnh thành',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tỉnh thành',
    },
    SupplierRegistration_PleaseSelectProvince: {
      key: 'SupplierRegistration_PleaseSelectProvince',
      value: 'Vui lòng chọn Tỉnh thành!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng chọn Tỉnh thành!',
    },
    SupplierRegistration_Province: {
      key: 'SupplierRegistration_Province',
      value: 'Chọn tỉnh thành',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn tỉnh thành',
    },
    SupplierRegistration_UploadFile: {
      key: 'SupplierRegistration_UploadFile',
      value: 'Upload File',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Upload File',
    },
    SupplierRegistration_ViewFile: {
      key: 'SupplierRegistration_ViewFile',
      value: 'Xem file',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Xem file',
    },

    SupplierRegistration_CompanyOrganizationalChart: {
      key: 'SupplierRegistration_CompanyOrganizationalChart',
      value: 'Sơ đồ tổ chức',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Sơ đồ tổ chức',
    },

    SupplierRegistration_FullTimeEmployees: {
      key: 'SupplierRegistration_FullTimeEmployees',
      value: 'Số lượng CB CNV làm việc toàn thời gian',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số lượng CB CNV làm việc toàn thời gian',
    },

    SupplierRegistration_OnSiteFullTimeEmployees: {
      key: 'SupplierRegistration_OnSiteFullTimeEmployees',
      value: 'Trực tiếp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Trực tiếp',
    },

    SupplierRegistration_EnterOnSiteFullTimeEmployees: {
      key: 'SupplierRegistration_EnterOnSiteFullTimeEmployees',
      value: 'Vui lòng nhập nhân sự trực tiếp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập nhân sự trực tiếp',
    },

    SupplierRegistration_RemoteFullTimeEmployees: {
      key: 'SupplierRegistration_RemoteFullTimeEmployees',
      value: 'Gián tiếp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Gián tiếp',
    },

    SupplierRegistration_EnterRemoteFullTimeEmployees: {
      key: 'SupplierRegistration_EnterRemoteFullTimeEmployees',
      value: 'Vui lòng nhập số lượng nhân sự gián tiếp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập số lượng nhân sự gián tiếp',
    },

    SupplierRegistration_OtherEmployees: {
      key: 'SupplierRegistration_OtherEmployees',
      value: 'Khác',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Khác',
    },

    SupplierRegistration_EnterOtherEmployees: {
      key: 'SupplierRegistration_EnterOtherEmployees',
      value: 'Vui lòng nhập số lượng nhân sự khác',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập số lượng nhân sự khác',
    },

    SupplierRegistration_TotalEmployees: {
      key: 'SupplierRegistration_TotalEmployees',
      value: 'Tổng cộng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tổng cộng',
    },

    SupplierRegistration_EnterTotalEmployees: {
      key: 'SupplierRegistration_EnterTotalEmployees',
      value: 'Vui lòng nhập tổng cộng nhân sự',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tổng cộng nhân sự',
    },

    SupplierRegistration_CriticalRoles: {
      key: 'SupplierRegistration_CriticalRoles',
      value: 'Các vị trí then chốt (Họ và tên)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Các vị trí then chốt (Họ và tên)',
    },

    SupplierRegistration_MarketingAndBusinessChiefDepartment: {
      key: 'SupplierRegistration_MarketingChiefDepartment',
      value: 'TP Kinh doanh/Marketing',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'TP Kinh doanh/Marketing',
    },

    SupplierRegistration_PlsEnterMarketingAndBusinessChiefDepartment: {
      key: 'SupplierRegistration_PlsEnterMarketingAndBusinessChiefDepartment',
      value: 'Vui lòng nhập TP Kinh doanh/Marketing (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập TP Kinh doanh/Marketing (1-250 kí tự)!',
    },

    SupplierRegistration_EnterMarketingAndBusinessChiefDepartment: {
      key: 'SupplierRegistration_EnterMarketingAndBusinessChiefDepartment',
      value: 'Nhập TP Kinh doanh/Marketing!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập TP Kinh doanh/Marketing!',
    },

    SupplierRegistration_SupplyAndBuyChiefDepartment: {
      key: 'SupplierRegistration_SupplyAndBuyChiefDepartment',
      value: 'TP Cung ứng/Mua',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'TP Cung ứng/Mua',
    },

    SupplierRegistration_PlsEnterSupplyAndBuyChiefDepartment: {
      key: 'SupplierRegistration_PlsEnterSupplyAndBuyChiefDepartment',
      value: 'Vui lòng nhập TP Cung ứng/Mua (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập TP Cung ứng/Mua (1-250 kí tự)!',
    },

    SupplierRegistration_EnterSupplyAndBuyChiefDepartment: {
      key: 'SupplierRegistration_EnterSupplyAndBuyChiefDepartment',
      value: 'Nhập TP Cung ứng/Mua (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập TP Cung ứng/Mua (1-250 kí tự)!',
    },

    SupplierRegistration_Supervisor: {
      key: 'SupplierRegistration_Supervisor',
      value: 'Quản Đốc',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Quản Đốc',
    },

    SupplierRegistration_PlsEnterSupervisor: {
      key: 'SupplierRegistration_PlsEnterSupervisor',
      value: 'Vui lòng nhập tên Quản Đốc (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tên Quản Đốc (1-250 kí tự)!',
    },

    SupplierRegistration_EnterSupervisor: {
      key: 'SupplierRegistration_EnterSupervisor',
      value: 'Nhập Quản Đốc!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Quản Đốc!',
    },

    SupplierRegistration_NumberOfPersonnelOfFunctionalRooms: {
      key: 'SupplierRegistration_NumberOfPersonnelOfFunctionalRooms',
      value: 'Số lượng nhân sự của các phòng chức năng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số lượng nhân sự của các phòng chức năng',
    },

    SupplierRegistration_RnDDepartment: {
      key: 'SupplierRegistration_RnDDepartment',
      value: 'Phòng RnD',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Phòng RnD',
    },

    SupplierRegistration_PlsEnterHRofRnDDepartment: {
      key: 'SupplierRegistration_PlsEnterHRofRnDDepartment',
      value: 'Vui lòng nhập nhân sự Phòng R&D',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập nhân sự Phòng R&D',
    },

    SupplierRegistration_PurchaseDepartment: {
      key: 'SupplierRegistration_PurchaseDepartment',
      value: 'Phòng mua hàng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Phòng mua hàng',
    },

    SupplierRegistration_PlsEnterHRofPurchaseDepartment: {
      key: 'SupplierRegistration_PlsEnterHRofPurchaseDepartment',
      value: 'Vui lòng nhập nhân sự Phòng Mua hàng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập nhân sự Phòng Mua hàng',
    },

    SupplierRegistration_ProductionDepartment: {
      key: 'SupplierRegistration_ProductionDepartment',
      value: 'BP Sản xuất',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'BP Sản xuất',
    },

    SupplierRegistration_PlsEnterHRofProductionDepartment: {
      key: 'SupplierRegistration_PlsEnterHRofProductionDepartment',
      value: 'Vui lòng nhập nhân sự BP Sản xuất',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập nhân sự BP Sản xuất',
    },

    SupplierRegistration_QualityDepartment: {
      key: 'SupplierRegistration_QualityDepartment',
      value: 'Phòng Chất lượng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Phòng Chất lượng',
    },

    SupplierRegistration_PlsEnterHRofQualityDepartment: {
      key: 'SupplierRegistration_PlsEnterHRofQualityDepartment',
      value: 'Vui lòng nhập nhân sự Phòng Chất lượng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập nhân sự Phòng Chất lượng',
    },

    SupplierRegistration_WarrantyMaintenanceDepartment: {
      key: 'SupplierRegistration_WarrantyMaintenanceDepartment',
      value: 'Phòng Bảo hành - Bảo trì',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Phòng Bảo hành - Bảo trì',
    },

    SupplierRegistration_PlsEnterHRofWarrantyMaintenanceDepartment: {
      key: 'SupplierRegistration_PlsEnterHRofWarrantyMaintenanceDepartment',
      value: 'Vui lòng nhập nhân sự Phòng Bảo hành - Bảo trì',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập nhân sự Phòng Bảo hành - Bảo trì',
    },

    SupplierRegistration_MarketingAndSaleDepartment: {
      key: 'SupplierRegistration_MarketingAndSaleDepartment',
      value: 'P. Marketing và Sale',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'P. Marketing và Sale',
    },

    SupplierRegistration_PlsEnterHRofMarketingAndSaleDepartment: {
      key: 'SupplierRegistration_PlsEnterHRofMarketingAndSaleDepartment',
      value: 'Vui lòng nhập nhân sự Phòng Marketing và Sale',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập nhân sự Phòng Marketing và Sale',
    },

    SupplierRegistration_TestingDepartment: {
      key: 'SupplierRegistration_TestingDepartment',
      value: 'P. Kiểm tra và thử',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'P. Kiểm tra và thử',
    },

    SupplierRegistration_PlsEnterHRofTestingDepartment: {
      key: 'SupplierRegistration_PlsEnterHRofTestingDepartment',
      value: 'Vui lòng nhập nhân sự Phòng Kiểm tra và thử',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập nhân sự Phòng Kiểm tra và thử',
    },

    SupplierRegistration_TechnicalDepartment: {
      key: 'SupplierRegistration_TechnicalDepartment',
      value: 'P. Kỹ thuật',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'P. Kỹ thuật',
    },

    SupplierRegistration_PlsEnterHRofTechnicalDepartment: {
      key: 'SupplierRegistration_PlsEnterHRofTechnicalDepartment',
      value: 'Vui lòng nhập nhân sự Phòng Kỹ thuật',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập nhân sự Phòng Kỹ thuật',
    },

    SupplierRegistration_AverageWeeklyWorkingHours: {
      key: 'SupplierRegistration_AverageWeeklyWorkingHours',
      value: 'Số giờ làm việc trung bình/tuần',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số giờ làm việc trung bình/tuần',
    },

    SupplierRegistration_PlsEnterAverageWeeklyWorkingHours: {
      key: 'SupplierRegistration_PlsEnterAverageWeeklyWorkingHours',
      value: 'Vui lòng nhập số giờ làm việc trung bình/tuần',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập số giờ làm việc trung bình/tuần',
    },

    SupplierRegistration_FinancialYear: {
      key: 'SupplierRegistration_FinancialYear',
      value: 'Năm tài chính',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Năm tài chính',
    },

    SupplierRegistration_PlsEnterDateRangeFinancialYear: {
      key: 'SupplierRegistration_PlsEnterDateRangeFinancialYear',
      value: 'Vui lòng chọn khoảng tháng!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng chọn khoảng tháng!',
    },

    SupplierRegistration_SphereOfBusiness: {
      key: 'SupplierRegistration_SphereOfBusiness',
      value: 'Lĩnh vực kinh doanh',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Lĩnh vực kinh doanh',
    },

    SupplierRegistration_PlsEnterSphereOfBusiness: {
      key: 'SupplierRegistration_PlsEnterSphereOfBusiness',
      value: 'Vui lòng nhập Lĩnh vực hoạt động chính (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập Lĩnh vực hoạt động chính (1-250 kí tự)!',
    },

    SupplierRegistration_EnterSphereOfBusiness: {
      key: 'SupplierRegistration_EnterSphereOfBusiness',
      value: 'Nhập Lĩnh vực hoạt động chính',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập Lĩnh vực hoạt động chính',
    },

    SupplierRegistration_PaymentMethod: {
      key: 'SupplierRegistration_PaymentMethod',
      value: 'Phương thức thanh toán',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Phương thức thanh toán',
    },

    SupplierRegistration_PlsEnterPaymentMethod: {
      key: 'SupplierRegistration_PlsEnterPaymentMethod',
      value: 'Vui lòng chọn phương thức thanh toán',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng chọn phương thức thanh toán',
    },

    SupplierRegistration_EnterPaymentMethod: {
      key: 'SupplierRegistration_EnterPaymentMethod',
      value: 'Chọn phương thức thanh toán',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn phương thức thanh toán',
    },

    SupplierRegistration_PaymentDate: {
      key: 'SupplierRegistration_PaymentDate',
      value: 'Thời hạn thanh toán',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Thời hạn thanh toán',
    },

    SupplierRegistration_PlsEnterPaymentDate: {
      key: 'SupplierRegistration_PlsEnterPaymentDate',
      value: 'Vui lòng chọn thời hạn thanh toán',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng chọn thời hạn thanh toán',
    },

    SupplierRegistration_AccountsPayableReceivableOrAutomaticAccounting: {
      key: 'SupplierRegistration_AccountsPayableReceivableOrAutomaticAccounting',
      value: 'Tài khoản công nợ hoặc toán tự động',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tài khoản công nợ hoặc toán tự động',
    },

    SupplierRegistration_ResearchAndDevelopmentAchievements: {
      key: 'SupplierRegistration_ResearchAndDevelopmentAchievements',
      value: 'Vui lòng nêu một vài thành tựu về Nghiên cứu và phát triển của nhà cung cấp hoặc các giải thưởng, chứng nhận đạt được',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nêu một vài thành tựu về Nghiên cứu và phát triển của nhà cung cấp hoặc các giải thưởng, chứng nhận đạt được',
    },

    SupplierRegistration_EnterResearchAndDevelopmentAchievements: {
      key: 'SupplierRegistration_EnterResearchAndDevelopmentAchievements',
      value: 'Nhập thành tựu về Nghiên cứu và phát triển của nhà cung cấp hoặc các giải thưởng, chứng nhận đạt được từ 1-2000 kí tự!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Nhập thành tựu về Nghiên cứu và phát triển của nhà cung cấp hoặc các giải thưởng, chứng nhận đạt được từ 1-2000 kí tự!',
    },

    SupplierRegistration_OverviewOfTheSupplierFormationAndDevelopment: {
      key: 'SupplierRegistration_OverviewOfTheSupplierFormationAndDevelopment',
      value: 'Sơ lược về quá trình hình thành & phát triển của Nhà Cung Cấp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Sơ lược về quá trình hình thành & phát triển của Nhà Cung Cấp',
    },

    SupplierRegistration_PleaseAttachTheMostRecentFinancialReportIfAvailable: {
      key: 'SupplierRegistration_PleaseAttachTheMostRecentFinancialReportIfAvailable ',
      value: 'Vui lòng đính kèm báo cáo tài chính năm gần nhất (Nếu có)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng đính kèm báo cáo tài chính năm gần nhất (Nếu có)',
    },

    //#endregion

    //#region supplier infomation
    SupplierInfor_GeneralInformation: {
      key: 'SupplierInfor_GeneralInformation',
      value: 'Thông tin chung',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Thông tin chung',
    },

    Bank_User_Name: {
      key: 'Bank_User_Name',
      value: 'Tên chủ tài khoản',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Tên chủ tài khoản',
    },

    Business_Field: {
      key: 'Business_Field',
      value: 'Lĩnh vực kinh doanh',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Lĩnh vực kinh doanh',
    },

    Business_Field_Name: {
      key: 'Business_Field_Name',
      value: 'Tên lĩnh vực kinh doanh',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Tên lĩnh vực kinh doanh',
    },

    Operating_Status: {
      key: 'Operating_Status',
      value: 'Trạng thái hoạt động',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Trạng thái hoạt động',
    },

    Supplier_Add_Business_Field: {
      key: 'Supplier_Add_Business_Field',
      value: 'Nhà cung cấp thêm lĩnh vực kinh doanh',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Nhà cung cấp thêm lĩnh vực kinh doanh',
    },

    SupplierAdditionalCapacity_Save: {
      key: 'SupplierAdditionalCapacity_Save',
      value: 'Lưu năng lực',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Lưu năng lực',
    },

    //#region site assessment
    SiteAssessment_Code: {
      key: 'SiteAssessment_Code',
      value: 'Mã',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Mã',
    },
    SiteAssessment_Factory: {
      key: 'SiteAssessment_Factory',
      value: 'Nhà máy',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Nhà máy',
    },
    SiteAssessment_Address: {
      key: 'SiteAssessment_Address',
      value: 'Địa chỉ',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Địa chỉ',
    },
    SiteAssessment_RequestDate: {
      key: 'SiteAssessment_RequestDate',
      value: 'Ngày yêu cầu',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Ngày yêu cầu',
    },
    SiteAssessment_ExpirationDate: {
      key: 'SiteAssessment_ExpirationDate',
      value: 'Ngày hết hạn',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Ngày hết hạn',
    },
    SiteAssessment_ViewDetail: {
      key: 'SiteAssessment_ViewDetail',
      value: 'Xem chi tiết',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Xem chi tiết',
    },
    SiteAssessment_CriteriaName: {
      key: 'SiteAssessment_CriteriaName',
      value: 'Tên tiêu chí',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Tên tiêu chí',
    },
    SiteAssessment_Density: {
      key: 'SiteAssessment_Density',
      value: 'Tỉ trọng(%)',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Tỉ trọng(%)',
    },
    SiteAssessment_ValueAchieved: {
      key: 'SiteAssessment_ValueAchieved',
      value: 'Giá trị đạt',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Giá trị đạt',
    },
    SiteAssessment_DataType: {
      key: 'SiteAssessment_DataType',
      value: 'Kiểu dữ liệu',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Kiểu dữ liệu',
    },
    SiteAssessment_SupplierReplied: {
      key: 'SiteAssessment_SupplierReplied',
      value: 'NCC trả lời',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'NCC trả lời',
    },
    SiteAssessment_EnterReason: {
      key: 'SiteAssessment_EnterReason',
      value: 'Nhập lý do',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Nhập lý do',
    },
    SiteAssessment_TotalProportion: {
      key: 'SiteAssessment_TotalProportion',
      value: 'Tổng tỉ trọng trong mục này đạt',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'Tổng tỉ trọng trong mục này đạt',
    },
    SiteAssessment_NotEnough: {
      key: 'SiteAssessment_NotEnough',
      value: 'chưa đủ 100%',
      languageType: 'VI',
      path: 'site-assessment',
      description: 'chưa đủ 100%',
    },
    //#endregion

    //#region payment
    Payment_ListPayment: {
      key: 'Payment_ListPayment',
      value: 'DANH SÁCH HỒ SƠ THANH TOÁN',
      languageType: 'VI',
      path: 'payment',
      description: 'DANH SÁCH HỒ SƠ THANH TOÁN',
    },
    Payment_ListPaymentRecords: {
      key: 'Payment_ListPayment',
      value: 'Danh sách hồ sơ thanh toán',
      languageType: 'VI',
      path: 'payment',
      description: 'Danh sách hồ sơ thanh toán',
    },
    Payment_SearchPaymentsProfileCode: {
      key: 'Payment_SearchPaymentsProfileCode',
      value: 'Tìm theo mã hồ sơ thanh toán',
      languageType: 'VI',
      path: 'payment',
      description: 'Tìm theo mã hồ sơ thanh toán',
    },
    Payment_SelectPaymentStatus: {
      key: 'Payment_SelectPaymentStatus',
      value: 'Chọn trạng thái thanh toán',
      languageType: 'VI',
      path: 'payment',
      description: 'Chọn trạng thái thanh toán',
    },
    Payment_PaymentCode: {
      key: 'Payment_PaymentCode',
      value: 'Mã hồ sơ thanh toán',
      languageType: 'VI',
      path: 'payment',
      description: 'Mã hồ sơ thanh toán',
    },
    Payment_PaymentName: {
      key: 'Payment_PaymentName',
      value: 'Tiêu đề hồ sơ thanh toán',
      languageType: 'VI',
      path: 'payment',
      description: 'Tiêu đề hồ sơ thanh toán',
    },
    Payment_ViewDetail: {
      key: 'Payment_ViewDetail',
      value: 'Xem chi tiết',
      languageType: 'VI',
      path: 'payment',
      description: 'Xem chi tiết',
    },
    Payment_Update: {
      key: 'Payment_Update',
      value: 'Cập nhật',
      languageType: 'VI',
      path: 'payment',
      description: 'Cập nhật',
    },
    Payment_CheckProfile: {
      key: 'Payment_CheckProfile',
      value: 'Kiểm tra hồ sơ',
      languageType: 'VI',
      path: 'payment',
      description: 'Kiểm tra hồ sơ',
    },
    Payment_SureCheckProfile: {
      key: 'Payment_SureCheckProfile',
      value: 'Bạn có chắc kiểm tra hồ sơ ?',
      languageType: 'VI',
      path: 'payment',
      description: 'Bạn có chắc kiểm tra hồ sơ ?',
    },
    Payment_PleaseEnterPaymentsProfile: {
      key: 'Payment_PleaseEnterPaymentsProfile',
      value: 'Vui lòng nhập tiêu đề hồ sơ thanh toán!',
      languageType: 'VI',
      path: 'payment',
      description: 'Vui lòng nhập tiêu đề hồ sơ thanh toán!',
    },
    Payment_Enter1To250Characters: {
      key: 'Payment_Enter1To250Characters',
      value: 'Nhập 1-250 kí tự',
      languageType: 'VI',
      path: 'payment',
      description: 'Nhập 1-250 kí tự',
    },
    Payment_Bill: {
      key: 'Payment_Bill',
      value: 'Hóa đơn',
      languageType: 'VI',
      path: 'payment',
      description: 'Hóa đơn',
    },
    Payment_PleaseSelectBill: {
      key: 'Payment_PleaseSelectBill',
      value: 'Vui lòng chọn danh sách hóa đơn !',
      languageType: 'VI',
      path: 'payment',
      description: 'Vui lòng chọn danh sách hóa đơn !',
    },
    Payment_SelectBill: {
      key: 'Payment_SelectBill',
      value: 'Chọn danh sách hóa đơn',
      languageType: 'VI',
      path: 'payment',
      description: 'Chọn danh sách hóa đơn',
    },
    Payment_Po: {
      key: 'Payment_Po',
      value: 'PO',
      languageType: 'VI',
      path: 'payment',
      description: 'PO',
    },
    Payment_PleaseSelectPo: {
      key: 'Payment_PleaseSelectPo',
      value: 'Vui lòng chọn PO!',
      languageType: 'VI',
      path: 'payment',
      description: 'Vui lòng chọn PO!',
    },
    Payment_Contract: {
      key: 'Payment_Contract',
      value: 'Hợp đồng',
      languageType: 'VI',
      path: 'payment',
      description: 'Hợp đồng',
    },
    Payment_PleaseSelectContract: {
      key: 'Payment_PleaseSelectContract',
      value: 'Vui lòng chọn hợp đồng !',
      languageType: 'VI',
      path: 'payment',
      description: 'Vui lòng chọn hợp đồng !',
    },
    Payment_SelectContract: {
      key: 'Payment_SelectContract',
      value: 'Chọn hợp đồng',
      languageType: 'VI',
      path: 'payment',
      description: 'Chọn hợp đồng',
    },
    Payment_AttachedFiles: {
      key: 'Payment_AttachedFiles',
      value: 'File đính kèm',
      languageType: 'VI',
      path: 'payment',
      description: 'File đính kèm',
    },
    Payment_PleaseAttachedFiles: {
      key: 'Payment_PleaseAttachedFiles',
      value: 'Vui lòng upload file đính kèm',
      languageType: 'VI',
      path: 'payment',
      description: 'Vui lòng upload file đính kèm',
    },
    Payment_PaymentRequestFile: {
      key: 'Payment_PaymentRequestFile',
      value: 'File đề nghị thanh toán',
      languageType: 'VI',
      path: 'payment',
      description: 'File đề nghị thanh toán',
    },
    Payment_PleaseUploadPaymentRequestFile: {
      key: 'Payment_PleaseUploadPaymentRequestFile',
      value: 'Vui lòng upload file đề nghị thanh toán',
      languageType: 'VI',
      path: 'payment',
      description: 'Vui lòng upload file đề nghị thanh toán',
    },
    Payment_AcceptanceRecordFile: {
      key: 'Payment_AcceptanceRecordFile',
      value: 'File biên bản nghiệm thu',
      languageType: 'VI',
      path: 'payment',
      description: 'File biên bản nghiệm thu',
    },
    Payment_PleaseUploadAcceptanceRecordFile: {
      key: 'Payment_PleaseUploadAcceptanceRecordFile',
      value: 'Vui lòng upload Giấy phép kinh doanh có điều kiện',
      languageType: 'VI',
      path: 'payment',
      description: 'Vui lòng upload Giấy phép kinh doanh có điều kiện',
    },
    Payment_Note: {
      key: 'Payment_Note',
      value: 'Ghi chú',
      languageType: 'VI',
      path: 'payment',
      description: 'Ghi chú',
    },
    Payment_EnterNote: {
      key: 'Payment_EnterNote',
      value: 'Nhập ghi chú',
      languageType: 'VI',
      path: 'payment',
      description: 'Nhập ghi chú',
    },
    Payment_Payment: {
      key: 'Payment_Payment',
      value: 'Thanh toán',
      languageType: 'VI',
      path: 'payment',
      description: 'Thanh toán',
    },
    //#endregion

    //#region bill
    Bill_ListBill: {
      key: 'Bill_ListBill',
      value: 'DANH SÁCH HÓA ĐƠN',
      languageType: 'VI',
      path: 'bill',
      description: 'DANH SÁCH HÓA ĐƠN',
    },
    Bill_BillCode: {
      key: 'Bill_BillCode',
      value: 'Tìm theo mã hóa đơn',
      languageType: 'VI',
      path: 'bill',
      description: 'Tìm theo mã hóa đơn',
    },
    Bill_SelectPaymentStatus: {
      key: 'Bill_SelectPaymentStatus',
      value: 'Chọn trạng thái thanh toán',
      languageType: 'VI',
      path: 'bill',
      description: 'Chọn trạng thái thanh toán',
    },
    Bill_Code: {
      key: 'Bill_Code',
      value: 'Mã hóa đơn',
      languageType: 'VI',
      path: 'bill',
      description: 'Mã hóa đơn',
    },
    Bill_Contract: {
      key: 'Bill_Contract',
      value: 'Hợp đồng',
      languageType: 'VI',
      path: 'bill',
      description: 'Hợp đồng',
    },
    Bill_Po: {
      key: 'Bill_Po',
      value: 'PO',
      languageType: 'VI',
      path: 'bill',
      description: 'PO',
    },
    Bill_Value: {
      key: 'Bill_Value',
      value: 'Trị giá',
      languageType: 'VI',
      path: 'bill',
      description: 'Trị giá',
    },
    Bill_CurrencyUnit: {
      key: 'Bill_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'bill',
      description: 'Đơn vị tiền tệ',
    },
    Bill_Vat: {
      key: 'Bill_Vat',
      value: 'Thuế VAT',
      languageType: 'VI',
      path: 'bill',
      description: 'Thuế VAT',
    },
    Bill_TotalValue: {
      key: 'Bill_TotalValue',
      value: 'Tổng trị giá',
      languageType: 'VI',
      path: 'bill',
      description: 'Tổng trị giá',
    },
    Bill_PaymentStatus: {
      key: 'Bill_PaymentStatus',
      value: 'Trạng thái thanh toán',
      languageType: 'VI',
      path: 'bill',
      description: 'Trạng thái thanh toán',
    },
    Bill_SearchInvoiceCode: {
      key: 'Bill_SearchInvoiceCode',
      value: 'Tìm theo mã hóa đơn',
      languageType: 'VI',
      path: 'bill',
      description: 'Tìm theo mã hóa đơn',
    },
    Bill_ViewDetail: {
      key: 'Bill_ViewDetail',
      value: 'Xem chi tiết',
      languageType: 'VI',
      path: 'bill',
      description: 'Xem chi tiết',
    },
    Bill_Update: {
      key: 'Bill_Update',
      value: 'Cập nhật',
      languageType: 'VI',
      path: 'bill',
      description: 'Cập nhật',
    },
    Bill_SendInvoice: {
      key: 'Bill_SendInvoice',
      value: 'Gửi hóa đơn',
      languageType: 'VI',
      path: 'bill',
      description: 'Gửi hóa đơn',
    },
    Bill_SureSendInvoice: {
      key: 'Bill_SureSendInvoice',
      value: 'Bạn có chắc muốn gửi hóa đơn ?',
      languageType: 'VI',
      path: 'bill',
      description: 'Bạn có chắc muốn gửi hóa đơn ?',
    },
    Bill_CancelInvoice: {
      key: 'Bill_CancelInvoice',
      value: 'Hủy Hóa Đơn',
      languageType: 'VI',
      path: 'bill',
      description: 'Hủy Hóa Đơn',
    },
    Bill_SureCancelInvoice: {
      key: 'Bill_SureCancelInvoice',
      value: 'Bạn có chắc muốn Hủy hóa đơn ?',
      languageType: 'VI',
      path: 'bill',
      description: 'Bạn có chắc muốn Hủy hóa đơn ?',
    },
    Bill_InvoiceFile: {
      key: 'Bill_InvoiceFile',
      value: 'File hóa đơn(file xml)',
      languageType: 'VI',
      path: 'bill',
      description: 'File hóa đơn(file xml)',
    },
    Bill_InvoiceFileXml: {
      key: 'Bill_InvoiceFileXml',
      value: 'File hóa đơn - File XML',
      languageType: 'VI',
      path: 'bill',
      description: 'File hóa đơn - File XML',
    },
    Bill_InvoiceFilePdf: {
      key: 'Bill_InvoiceFilePdf',
      value: 'File hóa đơn - File PDF',
      languageType: 'VI',
      path: 'bill',
      description: 'File hóa đơn - File PDF',
    },
    Bill_PleaseUploadInvoiceFile: {
      key: 'Bill_PleaseUploadInvoiceFile',
      value: 'Vui lòng upload file file hóa đơn',
      languageType: 'VI',
      path: 'bill',
      description: 'Vui lòng upload file file hóa đơn',
    },
    Bill_ExportUnitization: {
      key: 'Bill_ExportUnitization',
      value: 'Xuất hóa đơn cho',
      languageType: 'VI',
      path: 'bill',
      description: 'Xuất hóa đơn cho',
    },
    Bill_AccordingContract: {
      key: 'Bill_AccordingContract',
      value: 'Theo hợp đồng',
      languageType: 'VI',
      path: 'bill',
      description: 'Theo hợp đồng',
    },
    Bill_AccordingPo: {
      key: 'Bill_AccordingPo',
      value: 'Theo PO',
      languageType: 'VI',
      path: 'bill',
      description: 'Theo PO',
    },
    Bill_SelectPo: {
      key: 'Bill_SelectPo',
      value: 'Chọn PO',
      languageType: 'VI',
      path: 'bill',
      description: 'Chọn PO',
    },
    Bill_PleaseSelectPo: {
      key: 'Bill_PleaseSelectPo',
      value: 'Vui lòng chọn PO!',
      languageType: 'VI',
      path: 'bill',
      description: 'Vui lòng chọn PO!',
    },
    Bill_SelectContract: {
      key: 'Bill_SelectContract',
      value: 'Chọn hợp đồng',
      languageType: 'VI',
      path: 'bill',
      description: 'Chọn hợp đồng',
    },
    Bill_PleaseSelectContract: {
      key: 'Bill_PleaseSelectContract',
      value: 'Vui lòng chọn hợp đồng !',
      languageType: 'VI',
      path: 'bill',
      description: 'Vui lòng chọn hợp đồng !',
    },
    Bill_SelectCompanyInvoiceBizzi: {
      key: 'Bill_SelectCompanyInvoiceBizzi',
      value: 'Chọn công ty thêm hóa đơn từ hệ thống Bizzi',
      languageType: 'VI',
      path: 'bill',
      description: 'Chọn công ty thêm hóa đơn từ hệ thống Bizzi',
    },
    Bill_SelectCompany: {
      key: 'Bill_SelectCompany',
      value: 'Chọn công ty',
      languageType: 'VI',
      path: 'bill',
      description: 'Chọn công ty',
    },
    Bill_Description: {
      key: 'Bill_Description',
      value: 'Ghi chú',
      languageType: 'VI',
      path: 'bill',
      description: 'Ghi chú',
    },
    Bill_EnterDescription: {
      key: 'Bill_EnterDescription',
      value: 'Nhập ghi chú',
      languageType: 'VI',
      path: 'bill',
      description: 'Nhập ghi chú',
    },
    Bill_ListItems: {
      key: 'Bill_ListItems',
      value: 'Danh sách Items',
      languageType: 'VI',
      path: 'bill',
      description: 'Danh sách Items',
    },
    Bill_Material: {
      key: 'Bill_Material',
      value: 'Material',
      languageType: 'VI',
      path: 'bill',
      description: 'Material',
    },
    Bill_ShortText: {
      key: 'Bill_ShortText',
      value: 'Short text',
      languageType: 'VI',
      path: 'bill',
      description: 'Short text',
    },
    Bill_Unit: {
      key: 'Bill_Unit',
      value: 'Đơn vị',
      languageType: 'VI',
      path: 'bill',
      description: 'Đơn vị',
    },
    Bill_QuantityPr: {
      key: 'Bill_QuantityPr',
      value: 'Số lượng PR',
      languageType: 'VI',
      path: 'bill',
      description: 'Số lượng PR',
    },
    Bill_RestQuantity: {
      key: 'Bill_RestQuantity',
      value: 'Số lượng còn lại',
      languageType: 'VI',
      path: 'bill',
      description: 'Số lượng còn lại',
    },
    Bill_QuantityPo: {
      key: 'Bill_QuantityPo',
      value: 'Số lượng lên PO',
      languageType: 'VI',
      path: 'bill',
      description: 'Số lượng lên PO',
    },
    Bill_QuantityContract: {
      key: 'Bill_QuantityContract',
      value: 'Số lượng lên HD',
      languageType: 'VI',
      path: 'bill',
      description: 'Số lượng lên HD',
    },
    Bill_Price: {
      key: 'Bill_Price',
      value: 'Đơn giá',
      languageType: 'VI',
      path: 'bill',
      description: 'Đơn giá',
    },
    Bill_IntoMoney: {
      key: 'Bill_IntoMoney',
      value: 'Thành tiền',
      languageType: 'VI',
      path: 'bill',
      description: 'Thành tiền',
    },
    Bill_BusinessCode: {
      key: 'Bill_BusinessCode',
      value: 'Mã số doanh nghiệp',
      languageType: 'VI',
      path: 'bill',
      description: 'Mã số doanh nghiệp',
    },
    Bill_SupplierInformation: {
      key: 'Bill_SupplierInformation',
      value: 'Thông tin nhà cung cấp',
      languageType: 'VI',
      path: 'bill',
      description: 'Thông tin nhà cung cấp',
    },
    Bill_SupplierName: {
      key: 'Bill_SupplierName',
      value: 'Tên nhà cung cấp',
      languageType: 'VI',
      path: 'bill',
      description: 'Tên nhà cung cấp',
    },
    Bill_Address: {
      key: 'Bill_Address',
      value: 'Địa chỉ',
      languageType: 'VI',
      path: 'bill',
      description: 'Địa chỉ',
    },
    Bill_Email: {
      key: 'Bill_Email',
      value: 'Email',
      languageType: 'VI',
      path: 'bill',
      description: 'Email',
    },
    Bill_Phone: {
      key: 'Bill_Phone',
      value: 'Số điện thoại',
      languageType: 'VI',
      path: 'bill',
      description: 'Số điện thoại',
    },
    Bill_ContractName: {
      key: 'Bill_ContractName',
      value: 'Tên hợp đồng',
      languageType: 'VI',
      path: 'bill',
      description: 'Tên hợp đồng',
    },
    Bill_POName: {
      key: 'Bill_POName',
      value: 'Tên PO',
      languageType: 'VI',
      path: 'bill',
      description: 'Tên PO',
    },

    Bill_Lookup_Link: {
      key: 'Bill_Lookup_Link',
      value: 'Đường link website tra cứu',
      languageType: 'VI',
      path: 'bill',
      description: 'Đường link website tra cứu hóa đơn',
    },

    Bill_Lookup_Website: {
      key: 'Bill_Lookup_Website',
      value: 'Website tra cứu',
      languageType: 'VI',
      path: 'bill',
      description: 'Website tra cứu',
    },

    Bill_Lookup_Code: {
      key: 'Bill_Lookup_Code',
      value: 'Mã tra cứu hóa đơn',
      languageType: 'VI',
      path: 'bill',
      description: 'Mã tra cứu hóa đơn',
    },

    Error_Bill_Lookup_Code: {
      key: 'Error_Bill_Lookup_Code',
      value: 'Vui lòng nhập mã tra cứu hóa đơn',
      languageType: 'VI',
      path: 'bill',
      description: 'Vui lòng nhập mã tra cứu hóa đơn',
    },

    Error_Bill_Lookup_Link: {
      key: 'Error_Bill_Lookup_Link',
      value: 'Vui lòng chọn đường link website tra cứu hóa đơn',
      languageType: 'VI',
      path: 'bill',
      description: 'Vui lòng chọn đường link website tra cứu hóa đơn',
    },

    Error_Bill_Lookup_Website: {
      key: 'Error_Bill_Lookup_Website',
      value: 'Vui lòng nhập website tra cứu',
      languageType: 'VI',
      path: 'bill',
      description: 'Vui lòng nhập website tra cứu',
    },

    Select_Bill_Lookup_Link: {
      key: 'Select_Bill_Lookup_Link',
      value: 'Đường link website tra cứu hóa đơn',
      languageType: 'VI',
      path: 'bill',
      description: 'Đường link website tra cứu hóa đơn',
    },
    Bill_PleaseEnterInvoiceValue: {
      key: 'Bill_PleaseEnterInvoiceValue',
      value: 'Vui lòng nhập trị giá hóa đơn',
      languageType: 'VI',
      path: 'bill',
      description: 'Vui lòng nhập trị giá hóa đơn',
    },
    //#endregion

    //#region inbound
    Inbound_List: {
      key: 'Inbound_List',
      value: 'Danh sách Inbound',
      languageType: 'VI',
      path: 'inbound',
      description: 'Danh sách Inbound',
    },

    /**Search */
    Inbound_PMS_Number: {
      key: 'Inbound_PMS_Number',
      value: 'Số IB PMS',
      languageType: 'VI',
      path: 'inbound',
      description: 'Số IB PMS',
    },

    Shipment_Number: {
      key: 'Shipment_Number',
      value: 'Số Shipment',
      languageType: 'VI',
      path: 'inbound',
      description: 'Số Shipment',
    },

    Shipment_Cost_Number: {
      key: 'Shipment_Cost_Number',
      value: 'Số Shipment Cost',
      languageType: 'VI',
      path: 'inbound',
      description: 'Số Shipment Cost',
    },

    IB_Delivery_From: {
      key: 'IB_Delivery_From',
      value: 'Ngày giao hàng - Từ ngày',
      languageType: 'VI',
      path: 'inbound',
      description: 'Ngày giao hàng - Từ ngày',
    },

    IB_Delivery_To: {
      key: 'IB_Delivery_To',
      value: 'Ngày giao hàng - Đến ngày',
      languageType: 'VI',
      path: 'contract',
      description: 'Ngày giao hàng - Đến ngày',
    },

    IB_CreatedAt_From: {
      key: 'IB_CreatedAt_From',
      value: 'Ngày tạo - Từ ngày',
      languageType: 'VI',
      path: 'inbound',
      description: 'Ngày tạo - Từ ngày',
    },

    IB_CreatedAt_To: {
      key: 'IB_CreatedAt_To',
      value: 'Ngày tạo - Đến ngày',
      languageType: 'VI',
      path: 'inbound',
      description: 'Ngày tạo - Đến ngày',
    },

    Inbound_SAP_Number: {
      key: 'Inbound_SAP_Number',
      value: 'Số IB SAP',
      languageType: 'VI',
      path: 'inbound',
      description: 'Số IB SAP',
    },

    Reference_Number: {
      key: 'Reference_Number',
      value: 'Số chứng từ tham chiếu',
      languageType: 'VI',
      path: 'inbound',
      description: 'Số chứng từ tham chiếu',
    },

    Purchase_Request: {
      key: 'Purchase_Request',
      value: 'PR',
      languageType: 'VI',
      path: 'inbound',
      description: 'Text PR',
    },

    /**Pagination */
    IB_Delivery: {
      key: 'IB_Delivery',
      value: 'Ngày giao hàng',
      languageType: 'VI',
      path: 'inbound',
      description: 'Ngày giao hàng',
    },

    IB_Date_Arrival_Warehouse: {
      key: 'IB_Date_Arrival_Warehouse',
      value: 'Ngày về kho dự kiến',
      languageType: 'VI',
      path: 'inbound',
      description: 'Ngày về kho dự kiến',
    },

    IB_Date_Arrival_Port: {
      key: 'IB_Date_Arrival_Port',
      value: 'Thời gian dự kiến về cảng',
      languageType: 'VI',
      path: 'inbound',
      description: 'Thời gian dự kiến về cảng',
    },

    IB_Receiving_Warehouse: {
      key: 'IB_Receiving_Warehouse',
      value: 'Kho nhận dự kiến',
      languageType: 'VI',
      path: 'inbound',
      description: 'Kho nhận dự kiến',
    },

    IB_Person_Charge: {
      key: 'IB_Person_Charge',
      value: 'Người phụ trách',
      languageType: 'VI',
      path: 'inbound',
      description: 'Người phụ trách',
    },

    IB_Add: {
      key: 'IB_Add',
      value: 'Thêm mới inbound',
      languageType: 'VI',
      path: 'inbound',
      description: 'Thêm mới inbound',
    },
    IB_Update: {
      key: 'IB_Update',
      value: 'Chỉnh sửa inbound',
      languageType: 'VI',
      path: 'inbound',
      description: 'Chỉnh sửa inbound',
    },

    IB_Check_List_Item: {
      key: 'IB_Check_List_Item',
      value: 'Vui lòng nhập đầy đủ thông tin cho danh sách Items',
      languageType: 'VI',
      path: 'inbound',
      description: 'Vui lòng nhập đầy đủ thông tin cho danh sách Items',
    },

    IB_Code: {
      key: 'IB_Code',
      value: 'Mã inbound',
      languageType: 'VI',
      path: 'inbound',
      description: 'Mã inbound',
    },

    Error_IB_Code: {
      key: 'Error_IB_Code',
      value: 'Vui lòng nhập mã inbound',
      languageType: 'VI',
      path: 'inbound',
      description: 'Vui lòng nhập mã inbound',
    },

    IB_Title: {
      key: 'IB_Title',
      value: 'Tiêu đề inbound',
      languageType: 'VI',
      path: 'inbound',
      description: 'Tiêu đề inbound',
    },

    Error_IB_Title: {
      key: 'Error_IB_Title',
      value: 'Vui lòng nhập tiêu đề inbound!',
      languageType: 'VI',
      path: 'inbound',
      description: 'Vui lòng nhập tiêu đề inbound!',
    },

    IB_Po: {
      key: 'IB_Po',
      value: 'PO',
      languageType: 'VI',
      path: 'inbound',
      description: 'PO',
    },
    Error_IB_Po: {
      key: 'Error_IB_Po',
      value: 'Vui lòng chọn PO!',
      languageType: 'VI',
      path: 'inbound',
      description: 'Vui lòng chọn PO!',
    },

    IB_SelectPo: {
      key: 'IB_SelectPo',
      value: 'Chọn PO',
      languageType: 'VI',
      path: 'inbound',
      description: 'Chọn PO',
    },

    Error_IB_Delivery: {
      key: 'Error_IB_Delivery',
      value: 'Vui lòng nhập ngày giao hàng!',
      languageType: 'VI',
      path: 'inbound',
      description: 'Vui lòng nhập ngày giao hàng!',
    },

    Error_IB_Date_Arrival_Port: {
      key: 'Error_IB_Date_Arrival_Port',
      value: 'Vui lòng nhập Thời gian dự kiến về cảng!',
      languageType: 'VI',
      path: 'inbound',
      description: 'Vui lòng nhập Thời gian dự kiến về cảng!',
    },

    Material: {
      key: 'Material',
      value: 'Material',
      languageType: 'VI',
      path: 'inbound',
      description: 'Material',
    },
    Short_Text: {
      key: 'Short_Text',
      value: 'Short text',
      languageType: 'VI',
      path: 'inbound',
      description: 'Short text',
    },
    Unit: {
      key: 'Unit',
      value: 'Đơn vị',
      languageType: 'VI',
      path: 'inbound',
      description: 'Đơn vị',
    },

    Quantity_Po_Use: {
      key: 'Quantity_Po_Use',
      value: 'Số lượng lên PO',
      languageType: 'VI',
      path: 'inbound',
      description: 'Số lượng lên PO',
    },

    Package_Quantity: {
      key: 'Package_Quantity',
      value: 'Số kiện',
      languageType: 'VI',
      path: 'inbound',
      description: 'Số kiện',
    },

    Container_List: {
      key: 'Container_List',
      value: 'Danh sách Container',
      languageType: 'VI',
      path: 'inbound',
      description: 'Danh sách Container',
    },

    Delete_All: {
      key: 'Delete_All',
      value: 'Xóa hết',
      languageType: 'VI',
      path: 'inbound',
      description: 'Xóa hết',
    },

    IB_Check_Delivery_Date: {
      key: 'IB_Check_Delivery_Date',
      value: 'Ngày dự kiến về cảng phải lớn hơn ngày giao hàng',
      languageType: 'VI',
      path: 'inbound',
      description: 'Ngày dự kiến về cảng phải lớn hơn ngày giao hàng',
    },

    Error_Search_From_Date: {
      key: 'Error_Search_From_Date',
      value: 'Từ ngày phải sớm hơn đến ngày',
      languageType: 'VI',
      path: 'inbound',
      description: 'Từ ngày phải sớm hơn đến ngày',
    },

    //#endregion

    //#region document handover

    Title: {
      key: 'Title',
      value: 'Tiêu đề',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tiêu đề',
    },

    Error_Title: {
      key: 'Error_Title',
      value: 'Vui lòng nhập tiêu đề',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập tiêu đề',
    },

    Document_Handover_Code: {
      key: 'Document_Handover_Code',
      value: 'Mã chứng từ',
      languageType: 'VI',
      path: 'contract',
      description: 'Mã chứng từ',
    },

    Note: {
      key: 'Note',
      value: 'Ghi chú',
      languageType: 'VI',
      path: 'all_view',
      description: 'Text ghi chú',
    },

    Attach_File: {
      key: 'Attach_File',
      value: 'File đính kèm',
      languageType: 'VI',
      path: 'all_view',
      description: 'File đính kèm',
    },

    Document_Handover_Add: {
      key: 'Document_Handover_Add',
      value: 'Thêm mới chứng từ',
      languageType: 'VI',
      path: 'document_handover',
      description: 'Thêm mới chứng từ',
    },
    Document_Handover_Update: {
      key: 'IB_Update',
      value: 'Chỉnh sửa chứng từ',
      languageType: 'VI',
      path: 'document_handover',
      description: 'Chỉnh sửa chứng từ',
    },

    Error_Check_Before_Update: {
      key: 'Error_Check_Before_Update',
      value: 'Tệp có kích thước quá lớn, vui lòng chọn tệp < 10MB.',
      languageType: 'VI',
      path: 'document_handover',
      description: 'Tệp có kích thước quá lớn, vui lòng chọn tệp < 10MB.',
    },
    Error_Choose_Supplier: {
      key: 'Error_Choose_Supplier',
      value: 'Vui lòng chọn Nhà cung cấp',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng chọn Nhà cung cấp',
    },

    //#endregion "document handover"

    //#region contract appendix

    Contract_Appendix_Code: {
      key: 'Contract_Appendix_Code',
      value: 'Mã phụ lục',
      languageType: 'VI',
      path: 'contract',
      description: 'Mã phụ lục',
    },

    Enter_Contract_Appendix_Code: {
      key: 'Enter_Contract_Appendix_Code',
      value: 'Nhập mã phụ lục',
      languageType: 'VI',
      path: 'contract',
      description: 'Nhập mã phụ lục',
    },

    Type_Contract_Appendix: {
      key: 'Type_Contract_Appendix',
      value: 'Loại phụ lục',
      languageType: 'VI',
      path: 'contract',
      description: 'Loại phụ lục',
    },

    Select_Type_Contract_Appendix: {
      key: 'Select_Type_Contract_Appendix',
      value: 'Chọn loại phụ lục',
      languageType: 'VI',
      path: 'contract',
      description: 'Chọn loại phụ lục',
    },

    Contract_Appendix_Name: {
      key: 'Contract_Appendix_Name',
      value: 'Tên phụ lục',
      languageType: 'VI',
      path: 'contract',
      description: 'Tên phụ lục',
    },

    //#endregion "contract appendix"

    //#endregion "Client"

    //#region "bidding"
    Home_SuggestingBidNotification: {
      key: 'Home_SuggestingBidNotification',
      value: 'Thông báo mời thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Thông báo mời thầu',
    },

    Home_Month: {
      key: 'Home_Month',
      value: 'Tháng',
      languageType: 'VI',
      path: 'home',
      description: 'Tháng',
    },

    Home_Code: {
      key: 'Home_Code',
      value: 'Mã',
      languageType: 'VI',
      path: 'home',
      description: 'Mã',
    },

    Home_ReleasePeriod: {
      key: 'Home_ReleasePeriod',
      value: 'Thời gian phát hành',
      languageType: 'VI',
      path: 'home',
      description: 'Thời gian phát hành',
    },

    Home_SuggestedBidSide: {
      key: 'Home_SuggestedBidSide',
      value: 'Bên mời thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Bên mời thầu',
    },

    Home_OpeningBid: {
      key: 'Home_OpeningBid',
      value: 'Đang phát hành',
      languageType: 'VI',
      path: 'home',
      description: 'Đang phát hành',
    },

    Home_OpenedBid: {
      key: 'Home_OpenedBid',
      value: 'Đã mở thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Đã mở thầu',
    },

    Home_ClosedBid: {
      key: 'Home_ClosedBid',
      value: 'Đóng thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Đóng thầu',
    },

    Home_BiddingPackage: {
      key: 'Home_BiddingPackage',
      value: 'Gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Gói thầu',
    },

    //#endregion "bidding"

    //#region "bidding-detail"
    Detail_BiddingAnnoucement: {
      key: 'Detail_BiddingAnnoucement',
      value: 'THÔNG BÁO MỜI THẦU',
      languageType: 'VI',
      path: 'home',
      description: 'THÔNG BÁO MỜI THẦU',
    },

    Detail_BasicInformation: {
      key: 'Detail_BasicInformation',
      value: 'Thông tin chung',
      languageType: 'VI',
      path: 'home',
      description: 'Thông tin chung',
    },

    Detail_BiddingSide: {
      key: 'Detail_BiddingSide',
      value: 'Bên mời thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Bên mời thầu',
    },

    Detail_BiddingSubmitAddress: {
      key: 'Detail_BiddingSubmitAddress',
      value: 'Địa chỉ nộp hồ sơ thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Địa chỉ nộp hồ sơ thầu',
    },

    Detail_BiddingPlace: {
      key: 'Detail_BiddingPlace',
      value: 'Các địa điểm thực hiện gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Các địa điểm thực hiện gói thầu',
    },

    Detail_BiddingPackageInformation: {
      key: 'Detail_BiddingPackageInformation',
      value: 'Thông tin gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Thông tin gói thầu',
    },

    Detail_BiddingPackageName: {
      key: 'Detail_BiddingPackageName',
      value: 'Tên gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Tên gói thầu',
    },

    Detail_BiddingPackageCode: {
      key: 'Detail_BiddingPackageCode',
      value: 'Mã',
      languageType: 'VI',
      path: 'home',
      description: 'Mã',
    },

    Detail_BiddingPackageStatus: {
      key: 'Detail_BiddingPackageStatus',
      value: 'Trạng thái',
      languageType: 'VI',
      path: 'home',
      description: 'Trạng thái',
    },

    Detail_BiddingPackageDescription: {
      key: 'Detail_BiddingPackageDescription',
      value: 'Mô tả nội dung mời thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Mô tả nội dung mời thầu',
    },

    Detail_BiddingPackageMethod: {
      key: 'Detail_BiddingPackageMethod',
      value: 'Hình thức đấu thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Hình thức đấu thầu',
    },

    Detail_BiddingPackagePreserveMethod: {
      key: 'Detail_BiddingPackagePreserveMethod',
      value: 'Hình thức bảo lãnh dự thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Hình thức bảo lãnh dự thầu',
    },

    Detail_BiddingPackageMoneyForMethod: {
      key: 'Detail_BiddingPackageMoneyForMethod',
      value: 'Số tiền bảo lãnh dự thầu (VNĐ)',
      languageType: 'VI',
      path: 'home',
      description: 'Số tiền bảo lãnh dự thầu (VNĐ)',
    },

    Detail_BiddingPackageDeadlineForMethod: {
      key: 'Detail_BiddingPackageDeadlineForMethod',
      value: 'Thời hạn bảo lãnh dự thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Thời hạn bảo lãnh dự thầu',
    },

    Detail_Month: {
      key: 'Detail_Month',
      value: 'tháng',
      languageType: 'VI',
      path: 'home',
      description: 'tháng',
    },

    Detail_BiddingPackageUploadDate: {
      key: 'Detail_BiddingPackageUploadDate',
      value: 'Ngày đăng tải gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Ngày đăng tải gói thầu',
    },

    Detail_BiddingPackageDeadlineForConfirm: {
      key: 'Detail_BiddingPackageDeadlineForConfirm',
      value: 'Ngày hết hạn xác nhận tham gia',
      languageType: 'VI',
      path: 'home',
      description: 'Ngày hết hạn xác nhận tham gia',
    },

    Detail_BiddingPackageDeadlineForSubmit: {
      key: 'Detail_BiddingPackageDeadlineForSubmit',
      value: 'Ngày hết hạn nộp hồ sơ thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Ngày hết hạn nộp hồ sơ thầu',
    },

    Detail_BiddingPackageValidPeriod: {
      key: 'Detail_BiddingPackageValidPeriod',
      value: 'Hiệu lực hợp đồng',
      languageType: 'VI',
      path: 'home',
      description: 'Hiệu lực hợp đồng',
    },

    Detail_TechnicalSketchOrSampleImage: {
      key: 'Detail_TechnicalSketchOrSampleImage',
      value: 'Bản vẽ kỹ thuật hoặc hình ảnh minh hoạ',
      languageType: 'VI',
      path: 'home',
      description: 'Bản vẽ kỹ thuật hoặc hình ảnh minh hoạ',
    },

    Detail_ViewFile: {
      key: 'Detail_ViewFile',
      value: 'Xem file',
      languageType: 'VI',
      path: 'home',
      description: 'Xem file',
    },

    Detail_ScopeOfWork: {
      key: 'Detail_ScopeOfWork',
      value: 'Phạm vi công việc',
      languageType: 'VI',
      path: 'home',
      description: 'Phạm vi công việc',
    },

    Detail_KPI_Criteria: {
      key: 'Detail_KPI_Criteria',
      value: 'Tiêu chuẩn đánh giá KPI',
      languageType: 'VI',
      path: 'home',
      description: 'Tiêu chuẩn đánh giá KPI',
    },

    Detail_Regulations: {
      key: 'Detail_Regulations',
      value: 'Các quy định về nội quy gói thầu: an toàn, an ninh, VSTP,...',
      languageType: 'VI',
      path: 'home',
      description: 'Các quy định về nội quy gói thầu: an toàn, an ninh, VSTP,...',
    },

    Detail_SampleDocuments: {
      key: 'Detail_SampleDocuments',
      value: 'Tài liệu mẫu (mẫu báo giá, mẫu hợp đồng,...)',
      languageType: 'VI',
      path: 'home',
      description: 'Tài liệu mẫu (mẫu báo giá, mẫu hợp đồng,...)',
    },

    Detail_Other: {
      key: 'Detail_Other',
      value: 'Khác',
      languageType: 'VI',
      path: 'home',
      description: 'Khác',
    },

    Detail_ItemList: {
      key: 'Detail_ItemList',
      value: 'Danh sách Item',
      languageType: 'VI',
      path: 'home',
      description: 'Danh sách Item',
    },

    Detail_Index: {
      key: 'Detail_Index',
      value: 'STT',
      languageType: 'VI',
      path: 'home',
      description: 'STT',
    },

    Detail_BiddingNumberCreated: {
      key: 'Detail_BiddingNumberCreated',
      value: 'Số lượng tạo thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Số lượng tạo thầu',
    },

    Detail_Notice: {
      key: 'Detail_Notice',
      value: 'Lưu ý:',
      languageType: 'VI',
      path: 'home',
      description: 'Lưu ý:',
    },

    Detail_ClickToJoin: {
      key: 'Detail_ClickToJoin',
      value: 'Để tham gia gói thầu quý NCC vui lòng ấn vào link để đăng ký tham gia',
      languageType: 'VI',
      path: 'home',
      description: 'Để tham gia gói thầu quý NCC vui lòng ấn vào link để đăng ký tham gia',
    },

    Detail_AtHere: {
      key: 'Detail_AtHere',
      value: ' tại đây',
      languageType: 'VI',
      path: 'home',
      description: ' tại đây',
    },

    Detail_PleaseReadBeforeConfirm: {
      key: 'Detail_PleaseReadBeforeConfirm',
      value: 'Vui lòng đọc thật kỹ các thông tin trước khi xác nhận',
      languageType: 'VI',
      path: 'home',
      description: 'Vui lòng đọc thật kỹ các thông tin trước khi xác nhận',
    },

    Detail_DoYouWantToBid: {
      key: 'Detail_DoYouWantToBid',
      value: 'Bạn có chắc chắn muốn tham gia đấu thầu?',
      languageType: 'VI',
      path: 'home',
      description: 'Bạn có chắc chắn muốn tham gia đấu thầu?',
    },

    Detail_Participated: {
      key: 'Detail_Participated',
      value: 'Xác nhận tham gia',
      languageType: 'VI',
      path: 'home',
      description: 'Xác nhận tham gia',
    },

    Detail_AreYouSureNotBidding: {
      key: 'Detail_AreYouSureNotBidding',
      value: 'Bạn có chắc chắn muốn không tham gia đấu thầu?',
      languageType: 'VI',
      path: 'home',
      description: 'Bạn có chắc chắn muốn không tham gia đấu thầu?',
    },

    Detail_NotParticipate: {
      key: 'Detail_NotParticipate',
      value: 'Không tham gia',
      languageType: 'VI',
      path: 'home',
      description: 'Không tham gia',
    },

    Detail_MovingToSubmitBiddingDocument: {
      key: 'Detail_MovingToSubmitBiddingDocument',
      value: 'Di chuyển đến trang nộp hồ sơ thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Di chuyển đến trang nộp hồ sơ thầu',
    },

    Detail_MovingToDeal: {
      key: 'Detail_MovingToDeal',
      value: 'Di chuyển đến trang đàm phán giá',
      languageType: 'VI',
      path: 'home',
      description: 'Di chuyển đến trang đàm phán giá',
    },

    Detail_MovingToAuction: {
      key: 'Detail_MovingToAuction',
      value: 'Di chuyển đến trang đấu thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Di chuyển đến trang đấu thầu',
    },

    Detail_DenyAccess: {
      key: 'Detail_DenyAccess',
      value: 'Từ chối truy cập',
      languageType: 'VI',
      path: 'home',
      description: 'Từ chối truy cập',
    },

    Detail_YouNotAllowToViewDetailPackage: {
      key: 'Detail_YouNotAllowToViewDetailPackage',
      value: 'Bạn không được xem chi tiết gói thầu, vui lòng đăng nhập trước!',
      languageType: 'VI',
      path: 'home',
      description: 'Bạn không được xem chi tiết gói thầu, vui lòng đăng nhập trước!',
    },

    Detail_Using: {
      key: 'Detail_Using',
      value: 'Đang phát hành',
      languageType: 'VI',
      path: 'home',
      description: 'Đang phát hành',
    },

    Detail_OpenBidding: {
      key: 'Detail_OpenBidding',
      value: 'Đã mở thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Đã mở thầu',
    },

    Detail_CloseBidding: {
      key: 'Detail_CloseBidding',
      value: 'Đóng thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Đóng thầu',
    },

    Detail_NoAccessGrantPackage: {
      key: 'Detail_NoAccessGrantPackage',
      value: 'Bạn không có quyền truy cập gói thầu này!',
      languageType: 'VI',
      path: 'home',
      description: 'Bạn không có quyền truy cập gói thầu này!',
    },

    Detail_CheckedParticipatedBidding: {
      key: 'Detail_CheckedParticipatedBidding',
      value: 'Đã xác nhận tham gia đấu thầu thành công!',
      languageType: 'VI',
      path: 'home',
      description: 'Đã xác nhận tham gia đấu thầu thành công!',
    },

    Detail_CheckedNotParticipatedBidding: {
      key: 'Detail_CheckedNotParticipatedBidding',
      value: 'Đã xác nhận không tham gia đấu thầu thành công!',
      languageType: 'VI',
      path: 'home',
      description: 'Đã xác nhận không tham gia đấu thầu thành công!',
    },

    //#endregion "bidding-detail"

    //#region "Đấu giá"
    Auction_Rank: {
      key: 'Auction_Rank',
      value: 'Thứ hạng',
      languageType: 'VI',
      path: 'home',
      description: 'Thứ hạng',
    },
    Auction_NotFound: {
      key: 'Auction_NotFound',
      value: 'Chưa có',
      languageType: 'VI',
      path: 'home',
      description: 'Chưa có',
    },
    Auction_SupplierNumber: {
      key: 'Auction_SupplierNumber',
      value: 'Số nhà cung cấp',
      languageType: 'VI',
      path: 'home',
      description: 'Số nhà cung cấp',
    },

    Auction_CdTime: {
      key: 'Auction_CdTime',
      value: 'Thời gian còn',
      languageType: 'VI',
      path: 'home',
      description: 'Thời gian còn',
    },

    Auction_StartTime: {
      key: 'Auction_StartTime',
      value: 'Giá khởi điểm',
      languageType: 'VI',
      path: 'home',
      description: 'Giá khởi điểm',
    },

    Auction_StartAt: {
      key: 'Auction_StartAt',
      value: 'Bắt đầu lúc',
      languageType: 'VI',
      path: 'home',
      description: 'Bắt đầu lúc',
    },

    Auction_EndAt: {
      key: 'Auction_EndAt',
      value: 'Kết thúc lúc',
      languageType: 'VI',
      path: 'home',
      description: 'Kết thúc lúc',
    },

    Auction_SubmitPrice: {
      key: 'Auction_SubmitPrice',
      value: 'Giá mới nhất',
      languageType: 'VI',
      path: 'home',
      description: 'Giá mới nhất',
    },

    Auction_SubmitDate: {
      key: 'Auction_SubmitDate',
      value: 'Đấu giá lúc',
      languageType: 'VI',
      path: 'home',
      description: 'Đấu giá lúc',
    },

    Auction_BidName: {
      key: 'Auction_BidName',
      value: 'Gói thầu tham chiếu',
      languageType: 'VI',
      path: 'home',
      description: 'Gói thầu tham chiếu',
    },

    Auction_STT: {
      key: 'Auction_STT',
      value: 'STT',
      languageType: 'VI',
      path: 'home',
      description: 'STT',
    },

    Auction_LVMH: {
      key: 'Auction_LVMH',
      value: 'LVMH',
      languageType: 'VI',
      path: 'home',
      description: 'LVMH',
    },

    Auction_Unit: {
      key: 'Auction_Unit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tính',
    },

    Auction_Quantity: {
      key: 'Auction_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'home',
      description: 'Số lượng',
    },

    Auction_GoHome: {
      key: 'Auction_GoHome',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'home',
      description: 'Đi tới trang chủ',
    },

    Auction_Auction: {
      key: 'Auction_Auction',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'home',
      description: 'Đi tới trang chủ',
    },

    Auction_NotStart: {
      key: 'Auction_NotStart',
      value: 'Chưa bắt đầu',
      languageType: 'VI',
      path: 'home',
      description: 'Chưa bắt đầu',
    },

    Auction_End: {
      key: 'Auction_End',
      value: 'Đã kết thúc',
      languageType: 'VI',
      path: 'home',
      description: 'Đã kết thúc',
    },

    BidAuction_BiddingPackage: {
      key: 'BidAuction_BiddingPackage',
      value: 'Đấu giá gói thầu:',
      languageType: 'VI',
      path: 'home',
      description: 'Đấu giá gói thầu:',
    },

    BidAuction_CurrentStatus: {
      key: 'BidAuction_CurrentStatus',
      value: 'Thứ hạng hiện tại',
      languageType: 'VI',
      path: 'home',
      description: 'Thứ hạng hiện tại',
    },

    BidAuction_NotParticipated: {
      key: 'BidAuction_NotParticipated',
      value: 'Chưa tham gia',
      languageType: 'VI',
      path: 'home',
      description: 'Chưa tham gia',
    },

    BidAuction_NumberOfCompaniesParticipated: {
      key: 'BidAuction_NumberOfCompaniesParticipated',
      value: 'Tổng công ty tham gia',
      languageType: 'VI',
      path: 'home',
      description: 'Tổng công ty tham gia',
    },

    BidAuction_ExportExcel: {
      key: 'BidAuction_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'home',
      description: 'Xuất excel',
    },

    BidAuction_ImportExcel: {
      key: 'BidAuction_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'home',
      description: 'Nhập excel',
    },

    BidAuction_CountUnit: {
      key: 'BidAuction_CountUnit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tính',
    },

    BidAuction_CurrencyUnit: {
      key: 'BidAuction_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tiền tệ',
    },

    BidAuction_Quantity: {
      key: 'BidAuction_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'home',
      description: 'Số lượng',
    },

    BidAuction_MaximumPrice: {
      key: 'BidAuction_MaximumPrice',
      value: 'Giá tối đa',
      languageType: 'VI',
      path: 'home',
      description: 'Giá tối đa',
    },

    BidAuction_InputedValue: {
      key: 'BidAuction_InputedValue',
      value: 'Giá trị đã nhập',
      languageType: 'VI',
      path: 'home',
      description: 'Giá trị đã nhập',
    },

    BidAuction_UnitPrice: {
      key: 'BidAuction_UnitPrice',
      value: 'Đơn giá',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn giá',
    },

    BidAuction_DetailInformation: {
      key: 'BidAuction_DetailInformation',
      value: 'Thông tin chi tiết',
      languageType: 'VI',
      path: 'home',
      description: 'Thông tin chi tiết',
    },

    BidAuction_SaveAuction: {
      key: 'BidAuction_SaveAuction',
      value: 'Lưu đấu giá',
      languageType: 'VI',
      path: 'home',
      description: 'Lưu đấu giá',
    },

    BidAuction_GotoHomePage: {
      key: 'BidAuction_GotoHomePage',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'home',
      description: 'Đi tới trang chủ',
    },

    BidAuction_Index: {
      key: 'BidAuction_Index',
      value: 'Hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Hạng mục',
    },

    BidAuction_InputNotCorrect: {
      key: 'BidAuction_InputNotCorrect',
      value: 'nhập đơn giá không hợp lệ.',
      languageType: 'VI',
      path: 'home',
      description: 'nhập đơn giá không hợp lệ.',
    },

    BidAuction_InputOverMaximumPrice: {
      key: 'BidAuction_InputOverMaximumPrice',
      value: 'nhập đơn giá vượt quá giá tối đa.',
      languageType: 'VI',
      path: 'home',
      description: 'nhập đơn giá vượt quá giá tối đa.',
    },

    BidAuction_InputMustGreaterThanZero: {
      key: 'BidAuction_InputMustGreaterThanZero',
      value: 'nhập đơn giá phải lớn hơn 0.',
      languageType: 'VI',
      path: 'home',
      description: 'nhập đơn giá phải lớn hơn 0.',
    },

    BidAuction_DoYouWantToFinish: {
      key: 'BidAuction_DoYouWantToFinish',
      value: 'Bạn có thực sự muốn hoàn tất hồ sơ đã điền?',
      languageType: 'VI',
      path: 'home',
      description: 'Bạn có thực sự muốn hoàn tất hồ sơ đã điền?',
    },

    BidAuction_MakeSureAllTheInformationIsCorrectBeforeSubmit: {
      key: 'BidAuction_MakeSureAllTheInformationIsCorrectBeforeSubmit',
      value: 'Hồ sơ đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
      languageType: 'VI',
      path: 'home',
      description:
        'Hồ sơ đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
    },

    BidAuction_SendAuctionSuccess: {
      key: 'BidAuction_SendAuctionSuccess',
      value: 'Gửi đấu giá thành công!',
      languageType: 'VI',
      path: 'home',
      description: 'Gửi đấu giá thành công!',
    },

    BidAuction_TemplateInputItem: {
      key: 'BidAuction_TemplateInputItem',
      value: 'Template nhập đấu giá Item',
      languageType: 'VI',
      path: 'home',
      description: 'Template nhập đấu giá Item',
    },

    BidAuction_IndexName: {
      key: 'BidAuction_IndexName',
      value: 'Tên hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Tên hạng mục',
    },

    BidAuction_FileTemplateNotCorrect: {
      key: 'BidAuction_FileTemplateNotCorrect',
      value: 'File không đúng template đấu giá của gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'File không đúng template đấu giá của gói thầu',
    },

    BidDeal_DealBiddingPrice: {
      key: 'BidDeal_DealBiddingPrice',
      value: 'Đàm phán giá gói thầu: ',
      languageType: 'VI',
      path: 'home',
      description: 'Đàm phán giá gói thầu: ',
    },

    BidDeal_DetailPriceFile: {
      key: 'BidDeal_DetailPriceFile',
      value: 'File chi tiết giá',
      languageType: 'VI',
      path: 'home',
      description: 'File chi tiết giá',
    },

    BidDeal_PleaseUploadDetailPriceFile: {
      key: 'BidDeal_PleaseUploadDetailPriceFile',
      value: 'Vui lòng upload File chi tiết giá!',
      languageType: 'VI',
      path: 'home',
      description: 'Vui lòng upload File chi tiết giá!',
    },

    BidDeal_UploadFile: {
      key: 'BidDeal_UploadFile',
      value: 'Upload File',
      languageType: 'VI',
      path: 'home',
      description: 'Upload File',
    },

    BidDeal_ViewAttachedFile: {
      key: 'BidDeal_ViewAttachedFile',
      value: 'Xem file đính kèm',
      languageType: 'VI',
      path: 'home',
      description: 'Xem file đính kèm',
    },

    BidDeal_TechincalDetailFile: {
      key: 'BidDeal_TechincalDetailFile',
      value: 'File chi tiết kỹ thuật',
      languageType: 'VI',
      path: 'home',
      description: 'File chi tiết kỹ thuật',
    },

    BidDeal_IfHave: {
      key: 'BidDeal_IfHave',
      value: '(Nếu có)',
      languageType: 'VI',
      path: 'home',
      description: '(Nếu có)',
    },

    BidDeal_PleaseUploadTechnicalDetailFile: {
      key: 'BidDeal_PleaseUploadTechnicalDetailFile',
      value: 'Vui lòng upload File chi tiết kỹ thuật!',
      languageType: 'VI',
      path: 'home',
      description: 'Vui lòng upload File chi tiết kỹ thuật!',
    },

    BidDeal_LinkDriverToAdditionalFile: {
      key: 'BidDeal_LinkDriverToAdditionalFile',
      value: 'Link driver các file bổ sung (Nếu có)',
      languageType: 'VI',
      path: 'home',
      description: 'Link driver các file bổ sung (Nếu có)',
    },

    BidDeal_ExportExcel: {
      key: 'BidDeal_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'home',
      description: 'Xuất excel',
    },

    BidDeal_ImportExcel: {
      key: 'BidDeal_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'home',
      description: 'Nhập excel',
    },

    BidDeal_IndexName: {
      key: 'BidDeal_IndexName',
      value: 'Tên hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Tên hạng mục',
    },

    BidDeal_CountUnit: {
      key: 'BidDeal_CountUnit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tính',
    },

    BidDeal_CurrencyUnit: {
      key: 'BidDeal_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tiền tệ',
    },

    BidDeal_Quantity: {
      key: 'BidDeal_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'home',
      description: 'Số lượng',
    },

    BidDeal_MaximumPrice: {
      key: 'BidDeal_MaximumPrice',
      value: 'Giá tối đa',
      languageType: 'VI',
      path: 'home',
      description: 'Giá tối đa',
    },

    BidDeal_AgreePrice: {
      key: 'BidDeal_AgreePrice',
      value: 'Giá đã chào',
      languageType: 'VI',
      path: 'home',
      description: 'Giá đã chào',
    },

    BidDeal_SuggestedPrice: {
      key: 'BidDeal_SuggestedPrice',
      value: 'Giá đề nghị',
      languageType: 'VI',
      path: 'home',
      description: 'Giá đề nghị',
    },

    BidDeal_DealPriceIfNotInputThenAgree: {
      key: 'BidDeal_DealPriceIfNotInputThenAgree',
      value: 'Giá quý nhà cung cấp đàm phán, nếu không nhập thì chấp nhận giá đề nghị',
      languageType: 'VI',
      path: 'home',
      description: 'Giá quý nhà cung cấp đàm phán, nếu không nhập thì chấp nhận giá đề nghị',
    },

    BidDeal_DetailInformation: {
      key: 'BidDeal_DetailInformation',
      value: 'Thông tin chi tiết',
      languageType: 'VI',
      path: 'home',
      description: 'Thông tin chi tiết',
    },

    BidDeal_InputDealPrice: {
      key: 'BidDeal_InputDealPrice',
      value: 'Nhập giá đàm phán',
      languageType: 'VI',
      path: 'home',
      description: 'Nhập giá đàm phán',
    },

    BidDeal_SureToSuggestDealPrice: {
      key: 'BidDeal_SureToSuggestDealPrice',
      value: 'Bạn có chắc muốn đề nghị giá đàm phán?',
      languageType: 'VI',
      path: 'home',
      description: 'Bạn có chắc muốn đề nghị giá đàm phán?',
    },

    BidDeal_SuggestDealPrice: {
      key: 'BidDeal_SuggestDealPrice',
      value: 'Đề nghị đàm phán giá',
      languageType: 'VI',
      path: 'home',
      description: 'Đề nghị đàm phán giá',
    },

    BidDeal_SureToAgreeDealPrice: {
      key: 'BidDeal_SureToAgreeDealPrice',
      value: 'Bạn có chắc muốn chấp nhận giá đề nghị?',
      languageType: 'VI',
      path: 'home',
      description: 'Bạn có chắc muốn chấp nhận giá đề nghị?',
    },

    BidDeal_AgreeDealPrice: {
      key: 'BidDeal_AgreeDealPrice',
      value: 'Chấp nhận giá đề nghị',
      languageType: 'VI',
      path: 'home',
      description: 'Chấp nhận giá đề nghị',
    },

    BidDeal_SureToDenyDealPriceAndEndBidding: {
      key: 'BidDeal_SureToDenyDealPriceAndEndBidding',
      value: 'Bạn có chắc muốn từ chối giá đề nghị và kết thúc tham gia gói thầu?',
      languageType: 'VI',
      path: 'home',
      description: 'Bạn có chắc muốn từ chối giá đề nghị và kết thúc tham gia gói thầu?',
    },

    BidDeal_DenyDealPrice: {
      key: 'BidDeal_DenyDealPrice',
      value: 'Từ chối giá đề nghị',
      languageType: 'VI',
      path: 'home',
      description: 'Từ chối giá đề nghị',
    },

    BidDeal_Save: {
      key: 'BidDeal_Save',
      value: 'Lưu',
      languageType: 'VI',
      path: 'home',
      description: 'Lưu',
    },

    BidDeal_BackToHome: {
      key: 'BidDeal_BackToHome',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'home',
      description: 'Đi tới trang chủ',
    },

    BidDeal_DetailFileNotUpload: {
      key: 'BidDeal_DetailFileNotUpload',
      value: 'Chưa upload File chi tiết giá.<br>',
      languageType: 'VI',
      path: 'home',
      description: 'Chưa upload File chi tiết giá.<br>',
    },

    BidDeal_TechnicalDetailFileNotUpload: {
      key: 'BidDeal_TechnicalDetailFileNotUpload',
      value: 'Chưa upload File chi tiết kỹ thuật.<br>',
      languageType: 'VI',
      path: 'home',
      description: 'Chưa upload File chi tiết kỹ thuật.<br>',
    },

    BidDeal_Index: {
      key: 'BidDeal_Index',
      value: 'Hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Hạng mục',
    },

    BidDeal_DealPriceNotInput: {
      key: 'BidDeal_DealPriceNotInput',
      value: 'chưa nhập giá đàm phán.<br>',
      languageType: 'VI',
      path: 'home',
      description: 'chưa nhập giá đàm phán.<br>',
    },

    BidDeal_DealPriceMustGreaterThanZero: {
      key: 'BidDeal_DealPriceMustGreaterThanZero',
      value: 'cần chỉnh giá lớn hơn 0.<br>',
      languageType: 'VI',
      path: 'home',
      description: 'cần chỉnh giá lớn hơn 0.<br>',
    },

    BidDeal_DealPriceMustNotExceedAgreePrice: {
      key: 'BidDeal_DealPriceMustNotExceedAgreePrice',
      value: 'cần chỉnh giá đàm phán không vượt quá giá đã chào.<br>',
      languageType: 'VI',
      path: 'home',
      description: 'cần chỉnh giá đàm phán không vượt quá giá đã chào.<br>',
    },

    BidDeal_DealPriceMustNotExceedMaximumPrice: {
      key: 'BidDeal_DealPriceMustNotExceedMaximumPrice',
      value: 'cần chỉnh giá đàm phán không vượt quá giá tối đa.<br>',
      languageType: 'VI',
      path: 'home',
      description: 'cần chỉnh giá đàm phán không vượt quá giá tối đa.<br>',
    },

    BidDeal_DealPriceNotInputCannotSave: {
      key: 'BidDeal_DealPriceNotInputCannotSave',
      value: '`Chưa nhập giá đàm phán, không thể lưu.<br>',
      languageType: 'VI',
      path: 'home',
      description: '`Chưa nhập giá đàm phán, không thể lưu.<br>',
    },

    BidDeal_DealPriceInputAllowToActionDealPrice: {
      key: 'BidDeal_DealPriceInputAllowToActionDealPrice',
      value: 'Đã nhập giá đàm phán, vui lòng thao tác "Đề nghị đàm phán giá".<br>',
      languageType: 'VI',
      path: 'home',
      description: 'Đã nhập giá đàm phán, vui lòng thao tác "Đề nghị đàm phán giá".<br>',
    },

    BidDeal_TemplateInputDealPriceItem: {
      key: 'BidDeal_TemplateInputDealPriceItem',
      value: 'Template nhập đàm phán giá Item',
      languageType: 'VI',
      path: 'home',
      description: 'Template nhập đàm phán giá Item',
    },

    BidDeal_InputedValue: {
      key: 'BidDeal_InputedValue',
      value: 'Giá trị đã nhập',
      languageType: 'VI',
      path: 'home',
      description: 'Giá trị đã nhập',
    },

    BidDeal_UnitPrice: {
      key: 'BidDeal_UnitPrice',
      value: 'Đơn giá',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn giá',
    },

    BidDeal_FileTemplateNotCorrect: {
      key: 'BidDeal_FileTemplateNotCorrect',
      value: 'File không đúng template nhập đàm phán giá của gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'File không đúng template nhập đàm phán giá của gói thầu',
    },

    BidDeal_FileExceedMaxSize: {
      key: 'BidDeal_FileExceedMaxSize',
      value: 'Vượt qua kích thước tối đa.',
      languageType: 'VI',
      path: 'home',
      description: 'Vượt qua kích thước tối đa.',
    },

    BidResetPrice_DetailPriceFile: {
      key: 'BidResetPrice_DetailPriceFile',
      value: 'File chi tiết giá',
      languageType: 'VI',
      path: 'home',
      description: 'File chi tiết giá',
    },

    BidResetPrice_PleaseUploadDetailPriceFile: {
      key: 'BidResetPrice_PleaseUploadDetailPriceFile',
      value: 'Vui lòng upload File chi tiết giá!',
      languageType: 'VI',
      path: 'home',
      description: 'Vui lòng upload File chi tiết giá!',
    },

    BidResetPrice_ViewAttachedFile: {
      key: 'BidResetPrice_ViewAttachedFile',
      value: 'Xem file đính kèm',
      languageType: 'VI',
      path: 'home',
      description: 'Xem file đính kèm',
    },

    BidResetPrice_TechincalDetailFile: {
      key: 'BidResetPrice_TechincalDetailFile',
      value: 'File chi tiết kỹ thuật',
      languageType: 'VI',
      path: 'home',
      description: 'File chi tiết kỹ thuật',
    },

    BidResetPrice_PleaseUploadTechnicalDetailFile: {
      key: 'BidResetPrice_PleaseUploadTechnicalDetailFile',
      value: 'Vui lòng upload File chi tiết kỹ thuật!',
      languageType: 'VI',
      path: 'home',
      description: 'Vui lòng upload File chi tiết kỹ thuật!',
    },

    BidResetPrice_ExportExcel: {
      key: 'BidResetPrice_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'home',
      description: 'Xuất excel',
    },

    BidResetPrice_ImportExcel: {
      key: 'BidResetPrice_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'home',
      description: 'Nhập excel',
    },

    BidResetPrice_IndexName: {
      key: 'BidResetPrice_IndexName',
      value: 'Tên hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Tên hạng mục',
    },

    BidResetPrice_CountUnit: {
      key: 'BidResetPrice_CountUnit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tính',
    },

    BidResetPrice_CurrencyUnit: {
      key: 'BidResetPrice_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tiền tệ',
    },

    BidResetPrice_Quantity: {
      key: 'BidResetPrice_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'home',
      description: 'Số lượng',
    },

    BidResetPrice_UnitPrice: {
      key: 'BidResetPrice_UnitPrice',
      value: 'Đơn giá',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn giá',
    },

    BidResetPrice_DetailInformation: {
      key: 'BidResetPrice_DetailInformation',
      value: 'Thông tin chi tiết',
      languageType: 'VI',
      path: 'home',
      description: 'Thông tin chi tiết',
    },

    BidResetPrice_RequestAdditionalPrice: {
      key: 'BidResetPrice_RequestAdditionalPrice',
      value: 'Yêu cầu bảng giá bổ sung sau',
      languageType: 'VI',
      path: 'home',
      description: 'Yêu cầu bảng giá bổ sung sau',
    },

    BidResetPrice_SubmitAdditionalPrice: {
      key: 'BidResetPrice_SubmitAdditionalPrice',
      value: 'Nộp chào giá bổ sung',
      languageType: 'VI',
      path: 'home',
      description: 'Nộp chào giá bổ sung',
    },

    BidResetPrice_NoAccessGrantOrTimeOutForPackage: {
      key: 'BidResetPrice_NoAccessGrantOrTimeOutForPackage',
      value: 'Bạn không có quyền truy cập hoặc đã ngưng nộp giá bổ sung cho gói thầu.',
      languageType: 'VI',
      path: 'home',
      description: 'Bạn không có quyền truy cập hoặc đã ngưng nộp giá bổ sung cho gói thầu.',
    },

    BidResetPrice_BackToHome: {
      key: 'BidResetPrice_BackToHome',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'home',
      description: 'Đi tới trang chủ',
    },

    BidResetPrice_SubmitAdditionalSuccessfully: {
      key: 'BidResetPrice_SubmitAdditionalSuccessfully',
      value: 'giá bổ sung thành công.',
      languageType: 'VI',
      path: 'home',
      description: 'giá bổ sung thành công.',
    },

    BidResetPrice_TemplateSuggestPrice: {
      key: 'BidResetPrice_TemplateSuggestPrice',
      value: 'Template nhập bảng chào giá',
      languageType: 'VI',
      path: 'home',
      description: 'Template nhập bảng chào giá',
    },

    BidResetPrice_FileTemplateNotCorrect: {
      key: 'BidResetPrice_FileTemplateNotCorrect',
      value: 'File không đúng template chào giá của gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'File không đúng template chào giá của gói thầu',
    },

    BidResetPrice_PleaseInput: {
      key: 'BidResetPrice_PleaseInput',
      value: 'Vui lòng nhập dữ liệu trước',
      languageType: 'VI',
      path: 'home',
      description: 'Vui lòng nhập dữ liệu trước',
    },

    BidResetPrice_NotFilledInformation: {
      key: 'BidResetPrice_NotFilledInformation',
      value: 'Chưa nhập dữ liệu hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Chưa nhập dữ liệu hạng mục',
    },

    BidResetPrice_PleaseInputValueGreaterThanZero: {
      key: 'BidResetPrice_PleaseInputValueGreaterThanZero',
      value: 'Vui lòng nhập giá lớn hơn 0',
      languageType: 'VI',
      path: 'home',
      description: 'Vui lòng nhập giá lớn hơn 0',
    },

    BidResetPrice_PriceGreaterThanZero: {
      key: 'BidResetPrice_PriceGreaterThanZero',
      value: 'giá phải lớn hơn 0',
      languageType: 'VI',
      path: 'home',
      description: 'giá phải lớn hơn 0',
    },

    BidResetPrice_PleaseInputRequiredColumn: {
      key: 'BidResetPrice_PleaseInputRequiredColumn',
      value: 'Vui lòng điền đủ thông tin các cột động bắt buộc',
      languageType: 'VI',
      path: 'home',
      description: 'Vui lòng điền đủ thông tin các cột động bắt buộc',
    },

    BidResetPrice_NotFilledEnoughInformation: {
      key: 'BidResetPrice_NotFilledEnoughInformation',
      value: 'Chưa điền đủ thông tin hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Chưa điền đủ thông tin hạng mục',
    },

    BidResetPrice_DetailFileNotUpload: {
      key: 'BidResetPrice_DetailFileNotUpload',
      value: 'Chưa upload File chi tiết giá.<br>',
      languageType: 'VI',
      path: 'home',
      description: 'Chưa upload File chi tiết giá.<br>',
    },

    BidResetPrice_TechnicalDetailFileNotUpload: {
      key: 'BidResetPrice_TechnicalDetailFileNotUpload',
      value: 'Chưa upload File chi tiết kỹ thuật.<br>',
      languageType: 'VI',
      path: 'home',
      description: 'Chưa upload File chi tiết kỹ thuật.<br>',
    },

    BidResetPrice_DataInputNotCorrect: {
      key: 'BidResetPrice_DataInputNotCorrect',
      value: 'Dữ liệu nhập vào không hợp lệ vui lòng kiểm tra lại',
      languageType: 'VI',
      path: 'home',
      description: 'Dữ liệu nhập vào không hợp lệ vui lòng kiểm tra lại',
    },

    BidResetPrice_DoYouWantToFinish: {
      key: 'BidResetPrice_DoYouWantToFinish',
      value: 'Bạn có thực sự muốn hoàn tất bảng chào giá đã điền?',
      languageType: 'VI',
      path: 'home',
      description: 'Bạn có thực sự muốn hoàn tất bảng chào giá đã điền?',
    },

    BidResetPrice_MakeSureAllTheInformationIsCorrectBeforeSubmit: {
      key: 'BidResetPrice_MakeSureAllTheInformationIsCorrectBeforeSubmit',
      value:
        'Bảng chào giá đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu. Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
      languageType: 'VI',
      path: 'home',
      description:
        'Bảng chào giá đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu. Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
    },

    BidResetPrice_FileExceedMaxSize: {
      key: 'BidResetPrice_FileExceedMaxSize',
      value: 'Vượt qua kích thước tối đa.',
      languageType: 'VI',
      path: 'home',
      description: 'Vượt qua kích thước tối đa.',
    },

    BiddingCustomPrice_AddIndexConfigPrice: {
      key: 'BiddingCustomPrice_AddIndexConfigPrice',
      value: 'Thêm hạng mục cơ cấu giá',
      languageType: 'VI',
      path: 'home',
      description: 'Thêm hạng mục cơ cấu giá',
    },

    BiddingCustomPrice_ExportExcel: {
      key: 'BiddingCustomPrice_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'home',
      description: 'Xuất excel',
    },

    BiddingCustomPrice_ImportExcel: {
      key: 'BiddingCustomPrice_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'home',
      description: 'Nhập excel',
    },

    BiddingCustomPrice_IndexName: {
      key: 'BiddingCustomPrice_IndexName',
      value: 'Tên hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Tên hạng mục',
    },

    BiddingCustomPrice_CountUnit: {
      key: 'BiddingCustomPrice_CountUnit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tính',
    },

    BiddingCustomPrice_CurrencyUnit: {
      key: 'BiddingCustomPrice_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tiền tệ',
    },

    BiddingCustomPrice_Quantity: {
      key: 'BiddingCustomPrice_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'home',
      description: 'Số lượng',
    },

    BiddingCustomPrice_UnitPrice: {
      key: 'BiddingCustomPrice_UnitPrice',
      value: 'Đơn giá',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn giá',
    },

    BiddingCustomPrice_Select: {
      key: 'BiddingCustomPrice_Select',
      value: 'Tuỳ chọn',
      languageType: 'VI',
      path: 'home',
      description: 'Tuỳ chọn',
    },

    BiddingCustomPrice_DeleteIndex: {
      key: 'BiddingCustomPrice_DeleteIndex',
      value: 'Xoá hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Xoá hạng mục',
    },

    BiddingCustomPrice_RequestConfigAdditionalPrice: {
      key: 'BiddingCustomPrice_RequestConfigAdditionalPrice',
      value: 'Yêu cầu cơ cấu giá bổ sung sau',
      languageType: 'VI',
      path: 'home',
      description: 'Yêu cầu cơ cấu giá bổ sung sau',
    },

    BiddingCustomPrice_DownloadTemplateExcelSuccessfully: {
      key: 'BiddingCustomPrice_DownloadTemplateExcelSuccessfully',
      value: 'Tải template thành công!',
      languageType: 'VI',
      path: 'home',
      description: 'Tải template thành công!',
    },

    BiddingCustomPrice_FileTemplateNotCorrect: {
      key: 'BiddingCustomPrice_FileTemplateNotCorrect',
      value: 'File không đúng template (cột đã bị thay đổi)!',
      languageType: 'VI',
      path: 'home',
      description: 'File không đúng template (cột đã bị thay đổi)!',
    },

    BiddingCustomPrice_IndexNameMustNotBeNull: {
      key: 'BiddingCustomPrice_IndexNameMustNotBeNull',
      value: 'Tên hạng mục không được để trống',
      languageType: 'VI',
      path: 'home',
      description: 'Tên hạng mục không được để trống',
    },

    BiddingCustomPrice_NotExist: {
      key: 'BiddingCustomPrice_NotExist',
      value: 'không tồn tại',
      languageType: 'VI',
      path: 'home',
      description: 'không tồn tại',
    },

    BiddingCustomPrice_InputNumber: {
      key: 'BiddingCustomPrice_InputNumber',
      value: 'Số lượng là số, không được để trống',
      languageType: 'VI',
      path: 'home',
      description: 'Số lượng là số, không được để trống',
    },

    BiddingCustomPrice_NotNull: {
      key: 'BiddingCustomPrice_NotNull',
      value: 'là bắt buộc không được để trống!',
      languageType: 'VI',
      path: 'home',
      description: 'là bắt buộc không được để trống!',
    },

    BiddingCustomPrice_NotNumber: {
      key: 'BiddingCustomPrice_NotNumber',
      value: 'không phải kiểu Number',
      languageType: 'VI',
      path: 'home',
      description: 'không phải kiểu Number',
    },

    BiddingPrice_ExportExcel: {
      key: 'BiddingPrice_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'home',
      description: 'Xuất excel',
    },

    BiddingPrice_ImportExcel: {
      key: 'BiddingPrice_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'home',
      description: 'Nhập excel',
    },

    BiddingPrice_IndexName: {
      key: 'BiddingPrice_IndexName',
      value: 'Tên hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Tên hạng mục',
    },

    BiddingPrice_CountUnit: {
      key: 'BiddingPrice_CountUnit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tính',
    },

    BiddingPrice_CurrencyUnit: {
      key: 'BiddingPrice_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tiền tệ',
    },

    BiddingPrice_Quantity: {
      key: 'BiddingPrice_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'home',
      description: 'Số lượng',
    },

    BiddingPrice_UnitPrice: {
      key: 'BiddingPrice_UnitPrice',
      value: 'Đơn giá',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn giá',
    },

    BiddingPrice_DetailInformation: {
      key: 'BiddingPrice_DetailInformation',
      value: 'Thông tin chi tiết',
      languageType: 'VI',
      path: 'home',
      description: 'Thông tin chi tiết',
    },

    BiddingPrice_RequestAdditionalPrice: {
      key: 'BiddingPrice_RequestAdditionalPrice',
      value: 'Yêu cầu bảng giá bổ sung sau',
      languageType: 'VI',
      path: 'home',
      description: 'Yêu cầu bảng giá bổ sung sau',
    },

    BiddingPrice_ItemNotSettleIndexOfSuggestPrice: {
      key: 'BiddingPrice_ItemNotSettleIndexOfSuggestPrice',
      value: 'Item chưa có thiết lập hạng mục chào giá.',
      languageType: 'VI',
      path: 'home',
      description: 'Item chưa có thiết lập hạng mục chào giá.',
    },

    BiddingPrice_DownloadTemplateExcelSuccessfully: {
      key: 'BiddingPrice_DownloadTemplateExcelSuccessfully',
      value: 'Tải template thành công!',
      languageType: 'VI',
      path: 'home',
      description: 'Tải template thành công!',
    },

    BiddingPrice_FileTemplateNotCorrect: {
      key: 'BiddingPrice_FileTemplateNotCorrect',
      value: 'File không đúng template (cột đã bị thay đổi)!',
      languageType: 'VI',
      path: 'home',
      description: 'File không đúng template (cột đã bị thay đổi)!',
    },

    BiddingPrice_Index: {
      key: 'BiddingPrice_Index',
      value: 'Hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Hạng mục',
    },

    BiddingPrice_NotNull: {
      key: 'BiddingPrice_NotNull',
      value: 'Hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Hạng mục',
    },

    BiddingPrice_Column: {
      key: 'BiddingPrice_Column',
      value: 'cột',
      languageType: 'VI',
      path: 'home',
      description: 'cột',
    },

    BiddingPrice_InputNumber: {
      key: 'BiddingPrice_InputNumber',
      value: 'phải nhập số',
      languageType: 'VI',
      path: 'home',
      description: 'phải nhập số',
    },

    BiddingTrade_ExportExcel: {
      key: 'BiddingTrade_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'home',
      description: 'Xuất excel',
    },

    BiddingTrade_ImportExcel: {
      key: 'BiddingTrade_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'home',
      description: 'Nhập excel',
    },

    BiddingTrade_ID: {
      key: 'BiddingTrade_ID',
      value: 'ID',
      languageType: 'VI',
      path: 'home',
      description: 'ID',
    },

    BiddingTrade_STT: {
      key: 'BiddingTrade_STT',
      value: 'STT',
      languageType: 'VI',
      path: 'home',
      description: 'STT',
    },

    BiddingTrade_ConditionType: {
      key: 'BiddingTrade_ConditionType',
      value: 'Condition type',
      languageType: 'VI',
      path: 'home',
      description: 'Condition type',
    },

    BiddingTech_ExportExcel: {
      key: 'BiddingTech_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'home',
      description: 'Xuất excel',
    },

    BiddingTech_ImportExcel: {
      key: 'BiddingTech_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'home',
      description: 'Nhập excel',
    },

    BiddingTech_Criteria: {
      key: 'BiddingTech_Criteria',
      value: 'Tiêu chí',
      languageType: 'VI',
      path: 'home',
      description: 'Tiêu chí',
    },

    BiddingTech_DataType: {
      key: 'BiddingTech_DataType',
      value: 'Kiểu dữ liệu',
      languageType: 'VI',
      path: 'home',
      description: 'Kiểu dữ liệu',
    },

    BiddingTech_Value: {
      key: 'BiddingTech_Value',
      value: 'Giá trị',
      languageType: 'VI',
      path: 'home',
      description: 'Giá trị',
    },

    BiddingTech_RequestAdditionalTechnical: {
      key: 'BiddingTech_RequestAdditionalTechnical',
      value: 'Yêu cầu kỹ thuật bổ sung sau',
      languageType: 'VI',
      path: 'home',
      description: 'Yêu cầu kỹ thuật bổ sung sau',
    },

    BiddingTech_FileExceedMaxSize: {
      key: 'BiddingTech_FileExceedMaxSize',
      value: 'Kích thước tối đa để upload là 50MB, vui lòng chọn file khác',
      languageType: 'VI',
      path: 'home',
      description: 'Kích thước tối đa để upload là 50MB, vui lòng chọn file khác',
    },

    BiddingTech_ItemNotSettleTachnicalCriteria: {
      key: 'BiddingTech_ItemNotSettleTachnicalCriteria',
      value: 'Item chưa có thiết lập tiêu chí kỹ thuật.',
      languageType: 'VI',
      path: 'home',
      description: 'Item chưa có thiết lập tiêu chí kỹ thuật.',
    },

    BiddingTech_NoticeAtFileField: {
      key: 'BiddingTech_NoticeAtFileField',
      value: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
      languageType: 'VI',
      path: 'home',
      description: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
    },

    BiddingTech_TemplateTechnicalDocument: {
      key: 'BiddingTech_TemplateTechnicalDocument',
      value: 'Template nhập hồ sơ kỹ thuật',
      languageType: 'VI',
      path: 'home',
      description: 'Template nhập hồ sơ kỹ thuật',
    },

    BiddingTech_DownloadTemplateExcelSuccessfully: {
      key: 'BiddingTech_DownloadTemplateExcelSuccessfully',
      value: 'Tải template thành công!',
      languageType: 'VI',
      path: 'home',
      description: 'Tải template thành công!',
    },

    BiddingTech_IsRequired: {
      key: 'BiddingTech_IsRequired',
      value: 'Bắt buộc?',
      languageType: 'VI',
      path: 'home',
      description: 'Bắt buộc?',
    },

    BiddingTech_FileTemplateNotCorrect: {
      key: 'BiddingTech_FileTemplateNotCorrect',
      value: 'File không đúng template (cột đã bị thay đổi)!',
      languageType: 'VI',
      path: 'home',
      description: 'File không đúng template (cột đã bị thay đổi)!',
    },

    BiddingTech_NotType: {
      key: 'BiddingTech_NotType',
      value: 'không phải kiểu',
      languageType: 'VI',
      path: 'home',
      description: 'không phải kiểu',
    },

    BiddingTech_DateFormatYYYY_MM_DD: {
      key: 'BiddingTech_DateFormatYYYY_MM_DD',
      value: 'ngày phải có định dạng yyyy-mm-dd',
      languageType: 'VI',
      path: 'home',
      description: 'ngày phải có định dạng yyyy-mm-dd',
    },

    BiddingTech_NotInList: {
      key: 'BiddingTech_NotInList',
      value: 'ngày phải có định dạng yyyy-mm-dd',
      languageType: 'VI',
      path: 'home',
      description: 'ngày phải có định dạng yyyy-mm-dd',
    },

    BiddingItem_TechnicalReuire: {
      key: 'BiddingItem_TechnicalReuire',
      value: 'Yêu cầu kỹ thuật',
      languageType: 'VI',
      path: 'home',
      description: 'Yêu cầu kỹ thuật',
    },

    BiddingItem_SuggestPrice: {
      key: 'BiddingItem_SuggestPrice',
      value: 'Chào giá',
      languageType: 'VI',
      path: 'home',
      description: 'Chào giá',
    },

    BiddingItem_ConfigPrice: {
      key: 'BiddingItem_ConfigPrice',
      value: 'Cơ cấu giá',
      languageType: 'VI',
      path: 'home',
      description: 'Cơ cấu giá',
    },

    BiddingItem_TradeCondition: {
      key: 'BiddingItem_TradeCondition',
      value: 'Điều kiện thương mại',
      languageType: 'VI',
      path: 'home',
      description: 'Điều kiện thương mại',
    },

    BiddingItem_Prev: {
      key: 'BiddingItem_Prev',
      value: 'Quay lại',
      languageType: 'VI',
      path: 'home',
      description: 'Quay lại',
    },

    BiddingItem_Next: {
      key: 'BiddingItem_Next',
      value: 'Tiếp tục',
      languageType: 'VI',
      path: 'home',
      description: 'Tiếp tục',
    },

    BiddingItem_SubmitDocument: {
      key: 'BiddingItem_SubmitDocument',
      value: 'Nộp hồ sơ',
      languageType: 'VI',
      path: 'home',
      description: 'Nộp hồ sơ',
    },

    BiddingItem_DoYouWantToFinish: {
      key: 'BiddingItem_DoYouWantToFinish',
      value: 'Bạn có thực sự muốn hoàn tất bảng chào giá đã điền?',
      languageType: 'VI',
      path: 'home',
      description: 'Bạn có thực sự muốn hoàn tất bảng chào giá đã điền?',
    },

    BiddingItem_MakeSureAllTheInformationIsCorrectBeforeSubmit: {
      key: 'BiddingItem_MakeSureAllTheInformationIsCorrectBeforeSubmit',
      value:
        'Bảng chào giá đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu. Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
      languageType: 'VI',
      path: 'home',
      description:
        'Bảng chào giá đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu. Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
    },

    BiddingItem_DataInputNotCorrect: {
      key: 'BiddingItem_DataInputNotCorrect',
      value: 'Dữ liệu nhập vào không hợp lệ vui lòng kiểm tra lại.',
      languageType: 'VI',
      path: 'home',
      description: 'Dữ liệu nhập vào không hợp lệ vui lòng kiểm tra lại.',
    },

    BiddingItem_SubmitDocumentSuccessfully: {
      key: 'BiddingItem_SubmitDocumentSuccessfully',
      value: 'Đã bổ sung hồ sơ thành công!',
      languageType: 'VI',
      path: 'home',
      description: 'Đã bổ sung hồ sơ thành công!',
    },

    BiddingItem_PleaseInput: {
      key: 'BiddingItem_PleaseInput',
      value: 'Vui lòng nhập dữ liệu trước',
      languageType: 'VI',
      path: 'home',
      description: 'Vui lòng nhập dữ liệu trước',
    },

    BiddingItem_NotFilledInformation: {
      key: 'BiddingItem_NotFilledInformation',
      value: 'Chưa nhập dữ liệu hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Chưa nhập dữ liệu hạng mục',
    },

    BiddingItem_PleaseInputValueGreaterThanZero: {
      key: 'BiddingItem_PleaseInputValueGreaterThanZero',
      value: 'Vui lòng nhập giá lớn hơn 0',
      languageType: 'VI',
      path: 'home',
      description: 'Vui lòng nhập giá lớn hơn 0',
    },

    BiddingItem_Index: {
      key: 'BiddingItem_Index',
      value: 'Hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Hạng mục',
    },

    BiddingItem_PriceGreaterThanZero: {
      key: 'BiddingItem_PriceGreaterThanZero',
      value: 'giá phải lớn hơn 0',
      languageType: 'VI',
      path: 'home',
      description: 'giá phải lớn hơn 0',
    },

    BiddingItem_PleaseInputRequiredColumn: {
      key: 'BiddingItem_PleaseInputRequiredColumn',
      value: 'Vui lòng điền đủ thông tin các cột động bắt buộc',
      languageType: 'VI',
      path: 'home',
      description: 'Vui lòng điền đủ thông tin các cột động bắt buộc',
    },

    Bidding_IndexNumber: {
      key: 'Bidding_IndexNumber',
      value: 'STT',
      languageType: 'VI',
      path: 'home',
      description: 'STT',
    },

    Bidding_ItemName: {
      key: 'Bidding_ItemName',
      value: 'Tên Item',
      languageType: 'VI',
      path: 'home',
      description: 'Tên Item',
    },

    Bidding_Status: {
      key: 'Bidding_Status',
      value: 'Trạng thái',
      languageType: 'VI',
      path: 'home',
      description: 'Trạng thái',
    },

    Bidding_SubmitDocumentPackage: {
      key: 'Bidding_SubmitDocumentPackage',
      value: 'Nhập hồ sơ thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Nhập hồ sơ thầu',
    },

    Bidding_NoAccessGrant: {
      key: 'Bidding_NoAccessGrant',
      value: 'Không có quyền truy cập',
      languageType: 'VI',
      path: 'home',
      description: 'Không có quyền truy cập',
    },

    Bidding_BackToHome: {
      key: 'Bidding_BackToHome',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'home',
      description: 'Đi tới trang chủ',
    },

    Bidding_CannotDetectPackage: {
      key: 'Bidding_CannotDetectPackage',
      value: 'Không xác định được gói thầu!',
      languageType: 'VI',
      path: 'home',
      description: 'Không xác định được gói thầu!',
    },

    Bidding_SelectItemForPackage: {
      key: 'Bidding_SelectItemForPackage',
      value: 'Chọn Item để nhập hồ sơ gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Chọn Item để nhập hồ sơ gói thầu',
    },

    SEARCH_BID_CODE_NAME: {
      key: 'SEARCH_BID_CODE_NAME',
      value: 'Tìm theo mã hoặc tên gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Tìm theo mã hoặc tên gói thầu',
    },

    BID_CODE: {
      key: 'BID_CODE',
      value: 'Mã gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Mã gói thầu',
    },

    BID_NAME: {
      key: 'BID_NAME',
      value: 'Tên gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Tên gói thầu',
    },

    JOIN_DATE: {
      key: 'JOIN_DATE',
      value: 'Ngày tham gia',
      languageType: 'VI',
      path: 'home',
      description: 'Ngày tham gia',
    },

    BID_STATUS: {
      key: 'BID_STATUS',
      value: 'Trạng thái gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Trạng thái gói thầu',
    },

    BID_PASS_LETTER: {
      key: 'BID_PASS_LETTER',
      value: 'Thư thông báo trúng thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Thư thông báo trúng thầu',
    },

    THANK_LETTER: {
      key: 'THANK_LETTER',
      value: 'Thư cảm ơn',
      languageType: 'VI',
      path: 'home',
      description: 'Thư cảm ơn',
    },

    Bidding_STT: {
      key: 'Bidding_STT',
      value: 'Chọn Item để nhập hồ sơ gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Chọn Item để nhập hồ sơ gói thầu',
    },

    Bidding_Quantity: {
      key: 'Bidding_Quantity',
      value: 'Tên Item',
      languageType: 'VI',
      path: 'home',
      description: 'Tên Item',
    },

    Bidding_StatusFile: {
      key: 'Bidding_StatusFile',
      value: 'Trạng thái nộp thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Trạng thái nộp thầu',
    },
    Bidding_statusFileName: {
      key: 'Bidding_statusFileName',
      value: 'Trạng thái hồ sơ',
      languageType: 'VI',
      path: 'home',
      description: 'Trạng thái hồ sơ',
    },

    Bidding_SuccessBidStatus: {
      key: 'Bidding_SuccessBidStatus',
      value: 'Trạng thái hồ sơ',
      languageType: 'VI',
      path: 'home',
      description: 'Trạng thái hồ sơ',
    },

    DEAR: {
      key: 'DEAR',
      value: 'Kính gửi : ',
      languageType: 'VI',
      path: 'home',
      description: 'Kính gửi : ',
    },

    DATE: {
      key: 'DATE',
      value: 'Kính gửi : ',
      languageType: 'VI',
      path: 'home',
      description: 'Kính gửi : ',
    },

    MONTH: {
      key: 'MONTH',
      value: 'tháng',
      languageType: 'VI',
      path: 'home',
      description: 'tháng',
    },
    YEAR: {
      key: 'YEAR',
      value: 'năm',
      languageType: 'VI',
      path: 'home',
      description: 'năm',
    },

    WELCOME_REGARD: {
      key: 'WELCOME_REGARD',
      value: 'Trân trọng kính chào',
      languageType: 'VI',
      path: 'home',
      description: 'Trân trọng kính chào',
    },

    BIDDING_BOARD: {
      key: 'BIDDING_BOARD',
      value: 'CT. HỘI ĐỒNG XÉT THẦU',
      languageType: 'VI',
      path: 'home',
      description: 'CT. HỘI ĐỒNG XÉT THẦU',
    },

    SIGNED: {
      key: 'SIGNED',
      value: 'ĐÃ KÝ',
      languageType: 'VI',
      path: 'home',
      description: 'ĐÃ KÝ',
    },

    RECIPIENT: {
      key: 'RECIPIENT',
      value: 'Nơi nhận',
      languageType: 'VI',
      path: 'home',
      description: 'Nơi nhận',
    },

    SUPPLIER: {
      key: 'SUPPLIER',
      value: 'Nhà thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Nhà thầu',
    },

    INTERNAL_SAVE: {
      key: 'INTERNAL_SAVE',
      value: 'Lưu nội bộ',
      languageType: 'VI',
      path: 'home',
      description: 'Lưu nội bộ',
    },

    ITEM_LIST_BID_HISTORY: {
      key: 'ITEM_LIST_BID_HISTORY',
      value: 'Lưu nội bộ',
      languageType: 'VI',
      path: 'home',
      description: 'Lưu nội bộ',
    },

    PRICE_ITEM_BID_HISTORY: {
      key: 'PRICE_ITEM_BID_HISTORY',
      value: 'Lịch sử nộp giá Item',
      languageType: 'VI',
      path: 'home',
      description: 'Lịch sử nộp giá Item',
    },

    PASS_PACKAGE_BID_LETTER: {
      key: 'PASS_PACKAGE_BID_LETTER',
      value: 'Thư thông báo trúng thầu gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Thư thông báo trúng thầu gói thầu',
    },

    THANK_JOIN_BID_LETTER: {
      key: 'THANK_JOIN_BID_LETTER',
      value: 'Thư cảm ơn tham gia gói thầu',
      languageType: 'VI',
      path: 'home',
      description: 'Thư cảm ơn tham gia gói thầu',
    },

    //#endregion "Đấu giá"

    //#region bid_history

    PRICE_TABLE_REPORT: {
      key: 'PRICE_TABLE_REPORT',
      value: 'BÁO CÁO BẢNG CHÀO GIÁ',
      languageType: 'VI',
      path: 'home',
      description: 'BÁO CÁO BẢNG CHÀO GIÁ',
    },

    BID_PACKAGE_NAME: {
      key: 'BID_PACKAGE_NAME',
      value: 'TÊN GÓI THẦU',
      languageType: 'VI',
      path: 'home',
      description: 'TÊN GÓI THẦU',
    },

    NO: {
      key: 'NO',
      value: 'STT',
      languageType: 'VI',
      path: 'home',
      description: 'STT',
    },

    CATEGORY_NAME: {
      key: 'CATEGORY_NAME',
      value: 'Tên hạng mục',
      languageType: 'VI',
      path: 'home',
      description: 'Tên hạng mục',
    },

    UNIT: {
      key: 'UNIT',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tính',
    },

    CURRENCY: {
      key: 'CURRENCY',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn vị tiền tệ',
    },

    QUANTITY: {
      key: 'QUANTITY',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'home',
      description: 'Số lượng',
    },

    PRICE: {
      key: 'PRICE',
      value: 'Đơn giá',
      languageType: 'VI',
      path: 'home',
      description: 'Đơn giá',
    },

    SUM: {
      key: 'SUM',
      value: 'Tổng cộng',
      languageType: 'VI',
      path: 'home',
      description: 'Tổng cộng',
    },

    PRICE_HISTORY: {
      key: 'PRICE_HISTORY',
      value: 'Lịch sử nộp giá',
      languageType: 'VI',
      path: 'home',
      description: 'Lịch sử nộp giá',
    },

    PRICE_HISTORY_REPORT: {
      key: 'PRICE_HISTORY_REPORT',
      value: 'BÁO CÁO LỊCH SỬ NỘP GIÁ',
      languageType: 'VI',
      path: 'home',
      description: 'BÁO CÁO LỊCH SỬ NỘP GIÁ',
    },

    //#endregion "bid_history"

    //#region request update supplier

    Request_Update_List: {
      key: 'Request_Update_List',
      value: 'DANH SÁCH YÊU CẦU CHỈNH SỬA',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'DANH SÁCH YÊU CẦU CHỈNH SỬA',
    },

    Request_Update_Type: {
      key: 'Request_Update_Type',
      value: 'Loại chỉnh sửa',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Loại chỉnh sửa',
    },

    Error_Request_Update_Type: {
      key: 'Error_Request_Update_Type',
      value: 'Chọn loại chỉnh sửa',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Chọn loại chỉnh sửa',
    },

    Request_Update_Type_Select: {
      key: 'Request_Update_Type_Select',
      value: 'Chọn loại chỉnh sửa',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Chọn loại chỉnh sửa',
    },

    Request_Update_Type_Code: {
      key: 'Request_Update_Type_Code',
      value: 'Mã điều chỉnh',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Mã điều chỉnh',
    },

    Request_Update_Date: {
      key: 'Request_Update_Date',
      value: 'Ngày yêu cầu',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Ngày yêu cầu',
    },

    Update_Law: {
      key: 'Update_Law',
      value: 'Điều chỉnh pháp lý',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Điều chỉnh pháp lý',
    },

    Update_Capacity: {
      key: 'Update_Capacity',
      value: 'Điều chỉnh năng lực',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Điều chỉnh năng lực',
    },

    Are_You_Sure_Approve: {
      key: 'Are_You_Sure_Approve',
      value: 'Bạn có chắc GỬI DUYỆT ?',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Bạn có chắc GỬI DUYỆT ?',
    },

    Send_Approve: {
      key: 'Send_Approve',
      value: 'GỬI DUYỆT',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'GỬI DUYỆT',
    },

    Are_You_Sure_Remove_Approve: {
      key: 'Are_You_Sure_Remove_Approve',
      value: 'Bạn có chắc xóa yêu cầu này khỏi hệ thống?',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Bạn có chắc xóa yêu cầu này khỏi hệ thống?',
    },

    Update_Request_Update_Supplier: {
      key: 'Update_Request_Update_Supplier',
      value: 'Chỉnh sửa yêu cầu nhà cung cấp',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Chỉnh sửa yêu cầu nhà cung cấp',
    },

    Add_Request_Update_Supplier: {
      key: 'Add_Request_Update_Supplier',
      value: 'Thêm yêu cầu nhà cung cấp',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Thêm yêu cầu nhà cung cấp',
    },

    Reason_Update: {
      key: 'Reason_Update',
      value: 'Lý do điều chỉnh',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Lý do điều chỉnh',
    },

    Enter_Reason_Update: {
      key: 'Enter_Reason_Update',
      value: 'Nhập lý do điều chỉnh',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Nhập lý do điều chỉnh',
    },

    Factory_List: {
      key: 'Factory_List',
      value: 'Danh sách nhà máy',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Danh sách nhà máy',
    },

    Supplier_Code: {
      key: 'Supplier_Code',
      value: 'Mã nhà cung cấp',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Mã nhà cung cấp',
    },

    Content_Update: {
      key: 'Content_Update',
      value: 'Nội dung điều chỉnh',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Nội dung điều chỉnh',
    },

    Lock_UnLock_Service: {
      key: 'Lock_UnLock_Service',
      value: 'Khóa/Mở khóa lĩnh vực kinh doanh',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Khóa/Mở khóa lĩnh vực kinh doanh',
    },

    Lock_UnLock_Supplier: {
      key: 'Lock_UnLock_Supplier',
      value: 'Khóa/Mở khóa nhà cung cấp',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Khóa/Mở khóa nhà cung cấp',
    },

    Law: {
      key: 'Law',
      value: 'Pháp lý',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Pháp lý',
    },

    Capacity: {
      key: 'Capacity',
      value: 'Pháp lý',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Pháp lý',
    },

    Upgrade_Supplier: {
      key: 'Upgrade_Supplier',
      value: 'Nâng cấp nhà cung cấp',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Nâng cấp nhà cung cấp',
    },
    Content_Name: {
      key: 'Content_Name',
      value: 'Tên nội dung',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Tên nội dung',
    },
    Content_New_Update: {
      key: 'Content_New_Update',
      value: 'Nội dung sau khi điều chỉnh',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Nội dung sau khi điều chỉnh',
    },

    Content_Old_Update: {
      key: 'Content_Old_Update',
      value: 'Nội dung trước khi điều chỉnh',
      languageType: 'VI',
      path: 'request-update-supplier',
      description: 'Nội dung trước khi điều chỉnh',
    },
    PageHeader_Auction: {
      key: 'PageHeader_Auction',
      value: 'Đấu giá',
      languageType: 'VI',
      path: 'all-view',
      description: 'Đấu giá',
    },

    //#endregion "Yêu cầu chỉnh sửa"
  },
}
