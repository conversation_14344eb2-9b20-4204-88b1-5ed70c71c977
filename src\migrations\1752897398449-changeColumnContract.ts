import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeColumnContract1752897398449 implements MigrationInterface {
    name = 'ChangeColumnContract1752897398449'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "exchangeRate"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "exchangeRate" float`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "exchangeRate"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "exchangeRate" int`);
    }

}
