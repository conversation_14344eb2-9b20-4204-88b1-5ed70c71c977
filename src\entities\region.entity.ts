import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CountryEntity } from './country.entity'
import { BankBranchEntity } from './bankBranch.entity'
import { SupplierEntity } from './supplier.entity'
import { SupplierBankEntity } from './supplierBank.entity'

@Entity('region')
export class RegionEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  countryId: string
  @ManyToOne(() => CountryEntity, (p) => p.regions)
  @JoinColumn({ name: 'countryId', referencedColumnName: 'id' })
  country: Promise<CountryEntity>

  @OneToMany(() => BankBranchEntity, (p) => p.region)
  bankBranchs: Promise<BankBranchEntity[]>

  @OneToMany(() => SupplierEntity, (p) => p.region)
  suppliers: Promise<SupplierEntity[]>

  @OneToMany(() => SupplierBankEntity, (p) => p.region)
  supplierBanks: Promise<SupplierBankEntity[]>
}
