import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { ShipmentCostEntity } from './shipmentCost.entity'
import { SupplierEntity } from './supplier.entity'

/**danh sách nhập chi phí cho shipment cost*/
@Entity('shipment_cost_detail')
export class ShipmentCostDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  conditionType: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentCostId: string
  @ManyToOne(() => ShipmentCostEntity, (p) => p.shipmentCostDetails)
  @JoinColumn({ name: 'shipmentCostId', referencedColumnName: 'id' })
  shipmentCost: Promise<ShipmentCostEntity>

  /**<PERSON><PERSON> báo chi phí theo(Delivery item/ HU Group/ Shipment cost item) enum dataTypeDeclaration*/
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  typeDeclaration: string

  /**Chi phí dự kiến */
  @Column({
    type: 'float',
    nullable: true,
  })
  price: number

  /**chi phí thực tế*/
  @Column({
    nullable: true,
  })
  costActual: number

  /**chi phí hoàn ứng*/
  @Column({
    nullable: true,
  })
  costActualComplete: number

  /**lệch phí*/
  @Column({
    nullable: true,
  })
  differentPercentCost: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.shipmentCostDetails)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>
}
