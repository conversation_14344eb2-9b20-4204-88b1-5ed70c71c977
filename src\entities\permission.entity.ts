import { BaseEntity } from './base.entity'
import { Entity, Column } from 'typeorm'

@Entity('permission')
export class PermissionEntity extends BaseEntity {
  /**Mã nhóm quyền */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  /*Tên nhóm quyền */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: false,
  })
  name: string

  /**<PERSON><PERSON><PERSON> tảng */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  platform: string

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /**JSON Stringify of phân quyền */
  @Column({ type: 'varchar', length: 'max', nullable: true })
  roleStringify: string
}
