import { ArrayMinSize, IsArray, IsNotEmpty, IsString } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class UpdateCompanyBlockDto {
  @ApiProperty({ description: 'Id' })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1, { message: 'Vui lòng thêm ít nhất 1 khối vào công ty' })
  lstBlockId: string[]

  @ApiProperty({ description: 'Id company' })
  @IsNotEmpty()
  @IsString()
  companyId: string
}
