import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { KpiEntity } from './kpi.entity'
import { PositionEntity } from './position.entity'
import { KpiPermissionEntity } from './kpiPermission.entity'
import { KpiDetailEntity } from './kpiDetail.entity'

/**  kpi phân quyền vị trí*/
@Entity('kpi_permission_position')
export class KpiPermissionPositionEntity extends BaseEntity {
  /** id vị trí */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  positionId: string
  @ManyToOne(() => PositionEntity, (p) => p.kpiPermissionPoitions)
  @JoinColumn({ name: 'positionId', referencedColumnName: 'id' })
  position: Promise<PositionEntity>

  /** id kpi mua hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  kpiId: string
  @ManyToOne(() => KpiEntity, (p) => p.kpiPermissionPoitions)
  @JoinColumn({ name: 'kpiId', referencedColumnName: 'id' })
  kpi: Promise<KpiEntity>

  /** id kpi mua hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  kpiDetailId: string
  @ManyToOne(() => KpiDetailEntity, (p) => p.kpiPermissionPoitions)
  @JoinColumn({ name: 'kpiDetailId', referencedColumnName: 'id' })
  kpiDetail: Promise<KpiDetailEntity>

  /** id cây sơ đồ */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  organnizationId: string
}
