import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, JoinColumn, ManyToOne, Index } from 'typeorm'
import { PlantEntity } from './plant.entity'
import { MediaFileEntity } from './mediaFile.entity'
import { MaterialGroupEntity } from './materialGroup.entity'
import { PrItemEntity } from './prItem.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { PrItemCompomentEntity } from './prItemComponent.entity'
import { UomEntity } from './uom.entity'
import { PurchasingGroupEntity } from './purchasingGroup.entity'
import { OfferServiceEntity } from './offerService.entity'
import { ContractItemEntity } from './contractItem.entity'
import { ContractInspectionItemEntity } from './contractInspectionItem.entity'
import { POProductEntity } from './poProduct.entity'
import {
  BusinessPlanColValueEntity,
  ContractAppendixItemEntity,
  CurrencyEntity,
  InboundItemEntity,
  MaterialTypeEntity,
  PoProductPriceListEntity,
  ProductHierarchyEntity,
  PurchasingOrgEntity,
} from '.'
import { BidPrItemEntity } from './bidPrItem.entity'
import { ShipmentItemEntity } from './shipmentItem.entity'
import { MaterialUomEntity } from './materialUom.entity'
import { RecommendedPurchaseColValueEntity } from './recommendedPurchaseColValue.entity'
import { RfqEntity } from './rfq.entity'
import { ReservationNormEntity } from './reservationNorm.entity'
import { ReservationItemEntity } from './reservationItem.entity'
import { RfqDetailsEntity } from './rfqDetails.entity'
import { PrItemChildEntity } from './prItemChild.entity'
import { MaterialPriceEntity } from './materialPrice.entity'
import { Transform } from 'class-transformer'
import { MaterialStorageLocationEntity } from './materialStorageLocation.entity'
import { MaterialValtypeEntity } from './materialValtype.entity'
import { ReservationNormDetailEntity } from './reservationNormDetail.entity'
import { LeadTimeDetailEntity } from './leadtimeDetail.entity'
import { RequestQuoteDetailEntity } from './requestQuoteDetail.entity'

@Entity('material')
export class MaterialEntity extends BaseEntity {
  /** Material Group */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string

  /** Tên vật tư = shortext (Material Description) */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: false,
  })
  name: string

  /** Tên dài vật tư */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  longText: string

  /** Material number */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  code: string

  /** Old material number */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  oldCode: string

  /** Material Type */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  materialTypeCode: string

  /** Giá */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({
    type: 'float',
    nullable: true,
  })
  price: number

  /** Lead Time */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({
    type: 'float',
    nullable: true,
  })
  leadTime: number

  /** Mô tả */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /** LVMH tương ứng khi đấu thầu */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId: string

  /** Tên nhóm vật tư */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  matGroup: string

  /** Mã sourcing */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  sourcingCode: string

  /** Công ty */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialTypeId: string

  /** Ext.matl group */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  externalMaterialGroupCode: string

  // Ext.matl group
  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.materials)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  @ManyToOne(() => PlantEntity, (p) => p.materials)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @ManyToOne(() => MaterialGroupEntity, (p) => p.materials)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>

  /** Các hình ảnh của vật tư */
  @OneToMany(() => MediaFileEntity, (p) => p.material)
  files: Promise<MediaFileEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.material)
  prItems: Promise<PrItemEntity[]>

  @OneToMany(() => BidPrItemEntity, (p) => p.material)
  bidItems: Promise<BidPrItemEntity[]>

  @OneToMany(() => RfqEntity, (p) => p.material)
  rfqs: Promise<RfqEntity[]>

  @OneToMany(() => RfqDetailsEntity, (p) => p.material)
  rfqDetails: Promise<RfqDetailsEntity[]>

  @OneToMany(() => OfferServiceEntity, (p) => p.material)
  offerService: Promise<OfferServiceEntity[]>

  @OneToMany(() => PrItemCompomentEntity, (p) => p.material)
  prItemCompoments: Promise<PrItemCompomentEntity[]>

  /** Loại vật tư */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  type: string

  /** Nguồn mua enumData */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  purchasingSource: string

  /** Base Unit of Measure */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  unitCode: string

  /** Base Unit of Measure */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string

  @ManyToOne(() => UomEntity, (p) => p.materials)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  /** valuation Type */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  valuationType: string

  /** có đồng bộ ? */
  @Column({
    nullable: true,
    default: false,
  })
  isSync: boolean

  /** Dung sai ? */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, default: 0, type: 'decimal', scale: 4, precision: 20 })
  tolerance: number

  // Purchasing Group
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string
  @ManyToOne(() => PurchasingGroupEntity, (p) => p.materials)
  @JoinColumn({ name: 'purchasingGroupId', referencedColumnName: 'id' })
  purchasingGroup: Promise<PurchasingGroupEntity>

  @OneToMany(() => ContractItemEntity, (p) => p.material)
  contractItems: Promise<ContractItemEntity[]>

  @OneToMany(() => ContractInspectionItemEntity, (p) => p.material)
  contractInspectionItems: Promise<ContractInspectionItemEntity[]>

  @OneToMany(() => POProductEntity, (p) => p.material)
  products: Promise<POProductEntity[]>

  @OneToMany(() => InboundItemEntity, (p) => p.material)
  inboundItems: Promise<InboundItemEntity[]>

  @OneToMany(() => ShipmentItemEntity, (p) => p.material)
  shipmentItems: Promise<ShipmentItemEntity[]>

  @OneToMany(() => MaterialUomEntity, (p) => p.material)
  materialUoms: Promise<MaterialUomEntity[]>

  // Manufacturer number
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  numberMachinesBox: string

  /** Chiều dài (L) */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  lngth: number

  /** Chiều rộng (W) */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  width: number

  /** Chiều cao (H) */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  height: number

  /** Thể tích */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, default: 0, type: 'decimal', scale: 4, precision: 20 })
  cmb: number

  @ManyToOne(() => MaterialTypeEntity, (p) => p.materials)
  @JoinColumn({ name: 'materialTypeId', referencedColumnName: 'id' })
  materialType: Promise<MaterialTypeEntity>

  @OneToMany(() => BusinessPlanColValueEntity, (p) => p.material)
  businessPlanColValues: Promise<BusinessPlanColValueEntity[]>

  @OneToMany(() => RecommendedPurchaseColValueEntity, (p) => p.material)
  recommendedPurchaseColValues: Promise<RecommendedPurchaseColValueEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string
  @ManyToOne(() => PurchasingOrgEntity, (p) => p.materials)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  @OneToMany(() => PoProductPriceListEntity, (p) => p.material)
  poProductPriceList: Promise<PoProductPriceListEntity[]>

  @OneToMany(() => ReservationNormEntity, (p) => p.material)
  reservationNorms: Promise<ReservationNormEntity[]>

  @OneToMany(() => ReservationItemEntity, (p) => p.material)
  reservationItems: Promise<ReservationItemEntity[]>

  @OneToMany(() => ReservationItemEntity, (p) => p.material)
  reservationItemChilds: Promise<ReservationItemEntity[]>

  @OneToMany(() => PrItemChildEntity, (p) => p.material)
  prItemChilds: Promise<PrItemChildEntity[]>

  /**  Thuế suất mua hàng */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({
    type: 'float',
    nullable: true,
  })
  purchaseTaxRate: number

  // Division
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  division: string

  // Name of Division
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  divisionName: string

  /** Standard Price */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  standardPrice: number

  // Valuation Class
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  valuationClass: string

  /** Min Safety Stock */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  minSafetyStock: number

  /**Gross Weight*/
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  grossWeight: number

  /** Net Weight */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  netWeight: number

  // Transportation Group
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  transportationGroup: string

  @OneToMany(() => MaterialPriceEntity, (p) => p.material)
  materialPrices: Promise<MaterialPriceEntity[]>

  /** Tên tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  englishName: string

  /** Tên hải quan */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  customsName: string

  /** Đơn vị tính khối lượng */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  unitOfMassCode: string

  /** Đơn vị tính khối lượng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitOfMassId: string

  @ManyToOne(() => UomEntity, (p) => p.unitOfMassMaterials)
  @JoinColumn({ name: 'unitOfMassId', referencedColumnName: 'id' })
  unitOfMass: Promise<UomEntity>

  /** Đơn vị tính thể tích */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  unitOfVolumeCode: string

  /** Đơn vị tính thể tích */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitOfVolumeId: string

  @ManyToOne(() => UomEntity, (p) => p.unitOfVolumeMaterials)
  @JoinColumn({ name: 'unitOfVolumeId', referencedColumnName: 'id' })
  unitOfVolume: Promise<UomEntity>

  /** Size/dimensions (Quy cách đóng gói) */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  size: string

  /** Đơn vị đo kích thước */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  unitOfMeasurementCode: string

  /** Đơn vị tính khối lượng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitOfMeasurementId: string

  @ManyToOne(() => UomEntity, (p) => p.unitOfMeasurementMaterials)
  @JoinColumn({ name: 'unitOfMeasurementId', referencedColumnName: 'id' })
  unitOfMeasurement: Promise<UomEntity>

  /** Product Hierarchy */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  productHierarchyId: string

  @ManyToOne(() => ProductHierarchyEntity, (p) => p.material)
  @JoinColumn({ name: 'productHierarchyId', referencedColumnName: 'id' })
  productHierarchy: Promise<ProductHierarchyEntity>

  /** Procument Type */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  procumentType: string

  @Column({
    nullable: true,
    type: 'datetime',
  })
  lastChange: Date

  // Lot size
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  lotSize: string

  // MRP Profile
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  mrpProfile: string

  // MRP Controller
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  mrpController: string

  /** Minimum Lot Size */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  minimumLotSize: number

  // MRP Group
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  mrpGroup: string

  // Planning strategy group
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  planningStrategyGroup: string

  // Origin Group
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  originGroup: string

  /**rounding Value */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  roundingValue: number

  /** Maximum Lot Size */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  maximumLotSize: number

  /**   Costing Lot Size */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  costingLotSize: number

  /** Safety Stock */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  safetyStock: number

  /** Reorder Point */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  reorderPoint: number

  /** Planned Delivery Time in Days (BTCI) */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'float' })
  deliveryTime: number

  /**   Maximum stock level */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  maximumStockLevel: number

  /**  Plant */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  plantCode: string

  /** Fixed Lot size */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  fixedLotSize: number

  /** Storage Location */
  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  storageLocation: string

  /** Tên khoa học */
  @Column({
    type: 'nvarchar',
    length: 200,
    nullable: true,
  })
  scientificName: string

  /**  Tên đại diện */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  representativeName: string

  /** P9 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p9Name: string

  /** P9 Packing*/
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p9Packing: string

  /** p8 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p8Name: string

  /** P8 - Bao bì */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p8BaoBi: string

  /** P7 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p7Name: string

  /** P7 - Cơ lý tính */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p7CoLyTinh: string

  /** P6 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p6Name: string

  /** P6 - Décor color */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p6DecorColor: string

  /** P5 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p5Name: string

  /** P5 Model */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p5Model: string

  /** P20 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p20Name: string

  /** P20 Nhom Mau */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p20NhomMau: string

  /** P19 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p19Name: string

  /** P19 Loại ván */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p19LoaiVan: string

  /** P18 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p18Name: string

  /** P18 Giấy Balance */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p18GiayBalance: string

  /** P17 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p17Name: string

  /** P17 Giấy Overlay */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p17GiayOverlay: string

  /** P16 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p16Name: string

  /** P16  Phẩm cấp SX */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p16PhamCapSX: string

  /** Batch management requirement indicator */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  batch: string

  /** p15Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p15Name: string

  /** link */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  link: string

  /** P15 - Góc vát/ Cấp phát tải  */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p15GocVatCapPhatTai: string

  /** hs Code */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  hsCode: string

  /** p14 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p14Name: string

  /** P14 - Góc vát/ Cấp phát tải  */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p14HoaVanAndKhuon: string

  /** p13 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p13Name: string

  /** P13 - Dòng SP theo QC */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p13SpTheoQc: string

  /** p12 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p12Name: string

  /** P12 - Loại keo/Loại khuôn/ Hèm khóa */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p12KhuonHemKhoa: string

  /** p11 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p11Name: string

  /** P11 Quy cách 2 */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p11QuyCach2: string

  /** P10 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p10Name: string

  /** P10  Quy cách 1 */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p10QuyCach: string

  // Mã tham chiếu
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  referenceCode: string

  // Flag for detete
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  flagForDetete: string

  // Loading Group
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  loadingGroup: string

  // Order Unit Description
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  orderUnitDescription: string

  // Variable Purchase Order Unit Active
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  variablePurchaseOrderUnitActive: string

  // Diễn giải thuế suất mua hàng
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  salesTaxRateInterpretation: string

  /** ĐVT_ Cấp 2 */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  unitCode2: string

  //  MRP Profile Description
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  mRPProfileDescription: string

  //  Indiv./coll.
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  indivColl: string

  // MRP Type
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  mRPType: string

  // Co-product
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  coProduct: string

  // SchedMargin key
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  schedMarginKey: string

  // GR processing time (days)
  @Column({
    type: 'nvarchar',
    length: 10,
    nullable: true,
  })
  gRProcessingTime: string

  // GR processing time (Hours)
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({
    type: 'float',
    nullable: true,
  })
  gRProcessingHour: number

  // QM Material Auth.
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({
    type: 'float',
    nullable: true,
  })
  qMMaterialAuth: number

  // QM material authorization group descript
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  qMMaterialAuthorizationGroup: string

  // Price Control Indicator
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  priceControlIndicator: string

  // Material Price Determination: Control
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({
    type: 'float',
    nullable: true,
  })
  materialPriceDetermination: number

  /** Price unit */

  @Column({
    type: 'float',
    nullable: true,
  })
  priceUnit: number

  //Order Unit.
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  orderUnit: string

  /** Planned Price 1 */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  plannedPrice1: number

  /** Planned Price Date */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  plannedPriceDate: Date

  // Minimum Remaining Shelf Life
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  minimumRemainingShelfLife: string

  // Total Shelf Life
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({
    type: 'float',
    nullable: true,
  })
  totalShelfLife: number

  // Variance Key
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  varianceKey: string

  // X-Plant Material Status
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  xPlantMaterialStatus: string

  //Flag for detete at plant level
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  flagForDeteteAtPlantLevel: string

  // Purchase Order Text
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  purchaseOrderText: string

  // Unit of Issue
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  unitOfIssue: string

  // Mã khách hàng
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  customerCode: string

  // Profit Center
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  profitCenter: string

  // Profit Center Description
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  profitCenterDescription: string

  // Critical part
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  criticalPart: string

  // Special Procurement Key
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  specialProcurementKey: string

  // Planning time fence
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({
    type: 'float',
    nullable: true,
  })
  planningTimeFence: number

  // In-house production
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({
    type: 'float',
    nullable: true,
  })
  inHouseProduction: number

  /** Underdely tol? */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, default: 0, type: 'decimal', scale: 4, precision: 20 })
  underdelyTol: number

  /** Overdely tol? */
  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({ nullable: true, default: 0, type: 'decimal', scale: 4, precision: 20 })
  overdelyTol: number

  // Production unit
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  productionUnit: string

  /** P21 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p21Name: string

  /** P21 Nhom Mau */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p21NhomMauM2: string

  /** P22 - Khuôn hiệu ứng M2 */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p22Name: string

  /** P22 - Khuôn hiệu ứng M2 */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  p22KhuonHieuUng: string

  /** PH3 Code */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  ph3Code: string

  /** PH3 Name */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  ph3Name: string

  /**Fund Program */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  fundProgram: string

  /** Fund Program Descr */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  fundProgramDescr: string

  /** Fund Center */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  fundCenter: string

  /** Fund Center Descr */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  fundCenterDescr: string

  /** Commitment */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  commitment: string

  /** Commitment Descr */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  commitmentDescr: string

  /** Create by */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  createBy: string

  /** Create by */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  createOn: Date

  /** Currency */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  currencyCode: string

  /** Currency */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currencyId: string

  @ManyToOne(() => CurrencyEntity, (p) => p.materials)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  // ABC Indicator
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  abcIndicator: string

  // Maintenance status
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  maintenanceStatus: string

  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({
    type: 'float',
    nullable: true,
  })
  currentPeriod: number

  @Transform(({ value }) => {
    // If value is empty or undefined, return null (or undefined)
    return value === '' || value === undefined ? null : value
  })
  @Column({
    type: 'float',
    nullable: true,
  })
  unrestricted: number

  // DF stor. loc. level
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  dfStorLocLevel: string

  // Country Origin
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  countryOrigin: string

  // Do not Cost
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  doNotCost: string

  // Material-related origin
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  materialRelatedOrigin: string

  //   Material Is Costed with Quantity Structu
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  materialIsCosted: string

  // Inspection text.

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  inspectionText: string

  // Price Control
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  priceControl: string

  /**Thông số kỹ thuật */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  technicalSpec: string

  @OneToMany(() => MaterialStorageLocationEntity, (p) => p.material)
  materialStorageLocations: Promise<MaterialStorageLocationEntity[]>

  @OneToMany(() => MaterialStorageLocationEntity, (p) => p.material)
  materialStorageLocation: Promise<MaterialStorageLocationEntity[]>

  @OneToMany(() => MaterialValtypeEntity, (p) => p.material)
  materialValtypes: Promise<MaterialValtypeEntity[]>

  @OneToMany(() => ReservationNormDetailEntity, (p) => p.material)
  reservationNormDetails: Promise<ReservationNormDetailEntity[]>

  @OneToMany(() => LeadTimeDetailEntity, (p) => p.material)
  leadtimeDetails: Promise<LeadTimeDetailEntity[]>

  @OneToMany(() => RequestQuoteDetailEntity, (p) => p.material)
  requestQuoteDetails: Promise<RequestQuoteDetailEntity[]>

  @OneToMany(() => ContractAppendixItemEntity, (p) => p.material)
  contractAppendixItems: Promise<ContractAppendixItemEntity[]>
}
