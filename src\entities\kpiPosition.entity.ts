import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CompanyEntity } from './company.entity'
import { KpiEntity } from './kpi.entity'
import { PositionEntity } from './position.entity'

/**  kpi mua hàng vị trí*/
@Entity('kpi_position')
export class KpiPositionEntity extends BaseEntity {
  /** id vị trí */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  positionId: string
  @ManyToOne(() => PositionEntity, (p) => p.kpiPoitions)
  @JoinColumn({ name: 'positionId', referencedColumnName: 'id' })
  position: Promise<PositionEntity>

  /** id kpi mua hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  kpiId: string
  @ManyToOne(() => KpiEntity, (p) => p.kpiPoitions)
  @JoinColumn({ name: 'kpiId', referencedColumnName: 'id' })
  kpi: Promise<KpiEntity>

  /** id cây sơ đồ */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  organnizationId: string
}
