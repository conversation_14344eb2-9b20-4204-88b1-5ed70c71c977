import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne, OneToMany } from 'typeorm'
import { MaterialEntity } from './material.entity'
import { DepartmentEntity } from './department.entity'
import { UomEntity } from './uom.entity'
import { BaseEntity } from './base.entity'
import { PlantEntity } from './plant.entity'
import { ReservationNormDetailEntity } from './reservationNormDetail.entity'
/** Phiếu đề xuất định mức dành cho mua hàng VPP */
@Entity('reservation_norm')
export class ReservationNormEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  code: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.reservationNorms)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  departmentId: string
  @ManyToOne(() => DepartmentEntity, (p) => p.reservationNorms)
  @JoinColumn({ name: 'departmentId', referencedColumnName: 'id' })
  department: Promise<DepartmentEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.reservationNorms)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  @Column({ nullable: true, default: 0, type: 'float' })
  norm: number

  // Tháng định mức
  @Column({
    type: 'varchar',
    nullable: true,
  })
  time: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  timeNote: string

  /** Ghi chú */
  @Column({
    length: 'max',
    nullable: true,
  })
  description: string

  /**Nhà máy */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (plant) => plant.norms)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @Column({ nullable: true, type: 'datetime' })
  startDate: Date

  @Column({ nullable: true, type: 'datetime' })
  expireDate: Date

  @Column({ type: 'bigint', nullable: true })
  totalEmployee: number

  // Số lượng quy đổi, tổng số lượng
  @Column({ nullable: true, default: 0, type: 'float' })
  quantityRemind: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  baseUnitId: string
  @ManyToOne(() => UomEntity, (p) => p.reservationBaseUnitNorms)
  @JoinColumn({ name: 'baseUnitId', referencedColumnName: 'id' })
  baseUnit: Promise<UomEntity>

  @OneToMany(() => ReservationNormDetailEntity, (p) => p.reservationNorm)
  reservationNormDetails: Promise<ReservationNormDetailEntity[]>

  @Column({
    nullable: true,
    default: 0,
    type: 'float',
  })
  year: number
}
