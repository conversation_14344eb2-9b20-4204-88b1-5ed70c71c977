import { Injectable, NotFoundException, MethodNotAllowedException, NotAcceptableException, BadRequestException } from '@nestjs/common'
import { enumData, ERROR_YOU_DO_NOT_HAVE_PERMISSION, ERROR_NOT_FOUND_DATA } from '../../../constants'
import { EmailService } from '../../email/email.service'
import {
  BidRepository,
  BidEmployeeAccessRepository,
  BidSupplierRepository,
  BidDealRepository,
  BidPriceColRepository,
  BidTechRepository,
  BidTradeRepository,
  BidPriceRepository,
  BidPrItemRepository,
  BidExMatGroupRepository,
  ServiceRepository,
  BidSupplierItemRepository,
  BidDealSupplierRepository,
} from '../../../repositories'
import { PaginationDto, UserDto } from '../../../dto'
import {
  BidSupplierTechValueEntity,
  BidSupplierTradeValueEntity,
  BidSupplierPriceValueEntity,
  BidSupplierCustomPriceValueEntity,
  BidHistoryEntity,
  SupplierCapacityEntity,
  SupplierEntity,
  SupplierServiceEntity,
  BidAuctionSupplierEntity,
  BidDealSupplierEntity,
  BidSupplierEntity,
  BidDealEntity,
  BidAuctionEntity,
  PrEntity,
  BidEntity,
} from '../../../entities'
import { coreHelper } from '../../../helpers'
import { In, IsNull, Like, Raw } from 'typeorm'
import * as moment from 'moment'
import { FlowApproveService } from '../../flowApprove/flowApprove.service'
import { RfqEntity } from '../../../entities/rfq.entity'
import { RfqDetailsRepository, RfqRepository } from '../../../repositories/rfq.repository'
import { RfqDetailsEntity } from '../../../entities/rfqDetails.entity'
import { v4 as uuidv4 } from 'uuid'

@Injectable()
export class BidRateService2 {
  constructor(
    private readonly repo: BidRepository,
    private readonly bidDealRepo: BidDealRepository,
    private readonly bidTechRepo: BidTechRepository,
    private readonly bidTradeRepo: BidTradeRepository,
    private readonly bidPriceRepo: BidPriceRepository,
    private readonly bidPriceColRepo: BidPriceColRepository,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly emailService: EmailService,
    // private readonly bidPrItemRepository: BidPrItemRepository,
    private readonly rfqDetailsRepository: RfqDetailsRepository,
    private readonly rfqRepository: RfqRepository,
    private readonly bidDealSupplierRepository: BidDealSupplierRepository,
    private readonly serviceRepository: ServiceRepository,
    private readonly bidExMatGroupRepository: BidExMatGroupRepository,
    private readonly bidSupplierItemRepository: BidSupplierItemRepository,

    private readonly flowService: FlowApproveService,
  ) {}

  //#region Phân tích giá

  /** Xếp hạng theo giá thấp nhất (Giá theo từng hạng mục) */
  async loadRankByMinPrice(user: UserDto, filter: { bidId: string; lstId: string[] }) {
    const bidItem = await this.bidExMatGroupRepository.findOne({ where: { id: filter.bidId } })
    const bid = await this.repo.findOne({ where: { id: bidItem.bidId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    // Danh sách bidPrice
    let bidPrices = await this.bidPriceRepo.getPrice(user, filter.bidId)
    if (filter?.lstId?.length > 0) {
      bidPrices = bidPrices.filter((c) => filter.lstId.includes(c.id))
    }

    const bidPriceCols = await this.bidPriceColRepo.getBidPriceColMPO(user, filter.bidId)

    // Lấy giá mới nhất các NCC chào (cùng số lượng)
    const lstDataPrice = await this.bidDealRepo.getPriceValueNewest(user, bid.id)

    let lstResult: any[] = []
    const length = bidPrices.length
    // Lọc qua danh sách bidPrice
    for (let i = 0; i < length; i++) {
      // Lấy từng hạng mục để xử lý và map dữ liệu
      const item = bidPrices[i]

      // Các giá trị mà NCC đã đàm phán hoặc có hồ sơ hợp lệ
      const lstBidSupplierPriceValueByItem = lstDataPrice
        .filter((p) => p.bidPriceId === item.id)
        .sort((a, b) => {
          if (a.value === '' || a.value === '0') return 1
          return +a.value - +b.value
        })

      const len = lstBidSupplierPriceValueByItem.length
      let rank = 1
      const lstRank = []
      for (let j = 0; j < len; ) {
        const itemTemp = lstBidSupplierPriceValueByItem[j]
        let temp: any = {}
        temp.unitPrice = +itemTemp.value
        temp.number = itemTemp.number
        item.number = itemTemp.number
        temp.price = temp.unitPrice * item.number
        temp.priceDiff = 0
        if (lstRank.length > 0) {
          temp.priceDiff = temp.price - lstRank[0].price
        }
        temp.lstSupplier = []
        temp.lstSupplier.push({
          supplierName: itemTemp.supplierName,
          supplierScorePrice: itemTemp.supplierScorePrice,
        })
        if (j < len - 1) {
          for (let k = j + 1; k < len; k++) {
            if (lstBidSupplierPriceValueByItem[k].value === itemTemp.value) {
              const itemTempC = lstBidSupplierPriceValueByItem[k]
              temp.lstSupplier.push({
                supplierName: itemTempC.supplierName,
                supplierScorePrice: itemTempC.supplierScorePrice,
              })
              j = k + 1
            } else {
              j = k
              rank = rank + 1
              k = len
            }
          }
          lstRank.push(temp)
        } else {
          lstRank.push(temp)
          j = len
        }
      }
      lstResult.push({ ...item, lstRank })
    }

    // find num rank to gen col
    let numRank = 0
    for (const item of lstResult) {
      if (item.lstRank && item.lstRank.length > numRank) {
        numRank = item.lstRank.length
      }
    }

    const lstTitleLv1 = []
    for (let i = 0; i < numRank; i++) {
      lstTitleLv1.push(0)
    }

    for (const item of lstResult) {
      for (let i = 0; i < numRank; i++) {
        const itemRank = item.lstRank[i]
        if (itemRank) {
          lstTitleLv1[i] += itemRank.price
        }
      }
      for (const col of bidPriceCols) {
        item[col.id] = ''
        if (item.__bidPriceColValue__?.length > 0) {
          const cell = item.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
          if (cell) item[col.id] = cell.value
        }
      }
    }

    return [lstResult, bidPriceCols, lstTitleLv1]
  }

  /** Xếp hạng theo tổng điểm giá dạng 1 (Giá theo từng NCC) */
  async loadRankBySumPrice(user: UserDto, filter: { bidId: string; lstId: string[] }) {
    let lstResult: any[] = []
    const bidItem = await this.bidExMatGroupRepository.findOne({ where: { id: filter.bidId } })
    const bid = await this.repo.findOne({ where: { id: bidItem.bidId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    // Danh sách bidPrice
    let bidPrices = await this.bidPriceRepo.getPrice(user, filter.bidId)
    if (filter?.lstId?.length > 0) {
      bidPrices = bidPrices.filter((c) => filter.lstId.includes(c.id))
    }
    const bidPriceCols: any[] = await this.bidPriceColRepo.getBidPriceColMPO(user, filter.bidId)
    const bidPriceColTypeSup: any[] = await this.bidPriceColRepo.find({
      where: { bidItemId: filter.bidId, colType: enumData.ColType.Supplier.code, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
      relations: { bidSupplierPriceColValue: true },
    })

    // Lấy giá mới nhất các NCC chào (cùng số lượng)
    const lstDataPrice = await this.bidDealRepo.getPriceValueNewest(user, bid.id)

    // sắp xếp giảm dần theo điểm giá
    let lstBidSupplier: any[] = await bid.bidSuppliers

    // sắp xếp nếu k nhập giá thì đưa xuống dưới
    const lenListBidSupplier = lstBidSupplier.length
    const lenBidPrices = bidPrices.length
    for (let i = 0; i < lenListBidSupplier; i++) {
      const bidSupplier = lstBidSupplier[i]
      bidSupplier.totalPrice = 0
      for (let j = 0; j < lenBidPrices; j++) {
        const bidPrice = bidPrices[j]
        const bidSupplierPriceValue = lstDataPrice.find((c) => c.supplierId === bidSupplier.supplierId && c.bidPriceId === bidPrice.id)
        if (!bidSupplierPriceValue || bidSupplierPriceValue.value === '' || bidSupplierPriceValue.value === '0') {
          bidSupplier.isNotFillPrice = true
        } else {
          // Tính tổng giá
          bidPrice.number = bidSupplierPriceValue.number
          bidSupplier.totalPrice += +bidSupplierPriceValue.value * bidPrice.number
        }
      }
    }

    // sắp xếp theo tổng giá, giá thấp lên trước
    lstBidSupplier = lstBidSupplier.sort((a, b) => a.totalPrice - b.totalPrice)

    // sắp xếp không nhập đủ giá thì đưa xuống dưới
    const lstSupplierOk = lstBidSupplier.filter((c) => !c.isNotFillPrice)
    const lstSupplierNotOk = lstBidSupplier.filter((c) => c.isNotFillPrice === true)
    const lstSupplier = [...lstSupplierOk, ...lstSupplierNotOk]

    // Lọc qua danh sách bidPrice
    for (let i = 0; i < lenBidPrices; i++) {
      // Lấy từng hạng mục để xử lý và map dữ liệu
      const item = bidPrices[i]

      const len = lstSupplier.length
      let rank = 0
      let totalPrice = -1
      const lstRank = []
      for (let j = 0; j < len; j++) {
        const itemTemp = lstSupplier[j]
        if (itemTemp.totalPrice !== totalPrice) {
          totalPrice = itemTemp.totalPrice
          rank++
        }

        const bidSupplierPriceValue = lstDataPrice.find((c) => c.supplierId === itemTemp.supplierId && c.bidPriceId === item.id)
        if (bidSupplierPriceValue) {
          let temp: any = {}
          temp.supplierName = bidSupplierPriceValue.supplierName
          temp.totalPrice = itemTemp.totalPrice
          temp.rank = rank
          temp.unitPrice = +bidSupplierPriceValue.value
          temp.price = temp.unitPrice * item.number
          temp.priceDiff = 0
          if (lstRank.length > 0) {
            temp.priceDiff = temp.price - lstRank[0].price
          }
          temp.isNotFillPrice = itemTemp.isNotFillPrice
          temp.bidSupplierId = itemTemp.id
          lstRank.push(temp)
        }
      }

      lstResult.push({ ...item, lstRank })
    }

    let bidSuppliers: any[] = []
    if (lstResult[0]?.lstRank.length > 0) {
      bidSuppliers = [...lstResult[0].lstRank]
      for (const bidSupplier of bidSuppliers) {
        bidSupplier.totalPriceDiff = 0
      }
    }
    for (const item of lstResult) {
      for (const itemRank of item.lstRank) {
        const bidSupplier = bidSuppliers.find((c) => c.bidSupplierId == itemRank.bidSupplierId)
        if (bidSupplier) {
          bidSupplier.totalPriceDiff += itemRank.priceDiff
        }
        for (const col of bidPriceColTypeSup) {
          itemRank[col.id] = ''
          const cell = col.__bidSupplierPriceColValue__.find(
            (c: { bidPriceId: string; bidSupplierId: any }) => c.bidPriceId === item.id && c.bidSupplierId === itemRank.bidSupplierId,
          )
          if (cell) {
            itemRank[col.id] = cell.value
          }
        }
      }

      for (const col of bidPriceCols) {
        item[col.id] = ''
        if (item.__bidPriceColValue__?.length > 0) {
          const cell = item.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
          if (cell) item[col.id] = cell.value
        }
      }
    }

    return [lstResult, bidPriceCols, bidPriceColTypeSup, bid, bidSuppliers]
  }

  /** Xếp hạng theo tổng giá dạng 2 (Giá theo từng NCC) (Mỗi dòng 1 NCC) */
  async loadSupplierRankBySumPrice(user: UserDto, filter: { bidId: string; lstId: string[] }) {
    const bidItem = await this.bidExMatGroupRepository.findOne({ where: { id: filter.bidId } })
    const bid = await this.repo.findOne({ where: { id: bidItem.bidId } })

    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    // Danh sách bidPrice
    let bidPrices = await this.bidPriceRepo.getPrice(user, filter.bidId)
    if (filter?.lstId?.length > 0) {
      bidPrices = bidPrices.filter((c) => filter.lstId.includes(c.id))
    }

    // Lấy giá mới nhất các NCC chào (cùng số lượng)
    const lstDataPrice = await this.bidDealRepo.getPriceValueNewest(user, bid.id)

    let lstBidSupplier = (await bid.bidSuppliers) as any[]
    const lstSupplierId = lstBidSupplier.map((c) => c.supplierId)
    if (lstSupplierId.length == 0) throw new Error('Chưa có doanh nghiệp tham gia gói thầu')
    const lstSupplier = await this.repo.manager.getRepository(SupplierEntity).find({ where: { id: In(lstSupplierId) } })

    // sắp xếp nếu k nhập giá thì đưa xuống dưới
    const lenListBidSupplier = lstBidSupplier.length
    const lenBidPrices = bidPrices.length
    for (let i = 0; i < lenListBidSupplier; i++) {
      const bidSupplier = lstBidSupplier[i]
      const supplier = lstSupplier.find((c) => c.id == bidSupplier.supplierId)
      bidSupplier.supplierName = supplier?.name || ''
      bidSupplier.totalPrice = 0
      for (let j = 0; j < lenBidPrices; j++) {
        const bidPrice = bidPrices[j]
        const bidSupplierPriceValue = lstDataPrice.find((c) => c.supplierId === bidSupplier.supplierId && c.bidPriceId === bidPrice.id)
        bidSupplier[bidPrice.id] = {}
        if (!bidSupplierPriceValue) {
          bidSupplier.isNotFillPrice = true
          bidSupplier[bidPrice.id].unitPrice = 0
          bidSupplier[bidPrice.id].totalPrice = 0
        } else {
          // Tính tổng giá
          bidPrice.number = bidSupplierPriceValue.number

          const unitPrice = +bidSupplierPriceValue.value
          bidSupplier[bidPrice.id].unitPrice = unitPrice
          const totalPrice = unitPrice * bidPrice.number
          bidSupplier[bidPrice.id].totalPrice = totalPrice

          bidSupplier.totalPrice += totalPrice
        }
      }
    }

    // sắp xếp theo tổng giá, giá thấp lên trước
    lstBidSupplier = lstBidSupplier.sort((a, b) => a.totalPrice - b.totalPrice)

    // sắp xếp không nhập đủ giá thì đưa xuống dưới
    const lstSupplierOk = lstBidSupplier.filter((c) => !c.isNotFillPrice)
    const lstSupplierNotOk = lstBidSupplier.filter((c) => c.isNotFillPrice === true)

    lstBidSupplier = [...lstSupplierOk, ...lstSupplierNotOk]
    return [lstBidSupplier, bidPrices]
  }

  //#endregion

  //#region Báo cáo kết quả đánh giá
  private checkSameCapacity(dataReport: any) {
    dataReport.errorNotSameCapacity = ''
    const len = dataReport.lstBidSupplier.length
    if (len == 0) return true

    var dic: any[] = []
    for (let i = 0; i < len; i++) {
      dic[i] = {}
      const templates = dataReport.supplierCapacities
      // ds tiêu chí năng lực NCC i
      dic[i].templates = templates
      for (const capacity of dic[i].templates) {
        dic[i][capacity.name] = capacity
      }
    }

    for (let i = 1; i < len; i++) {
      // số lượng các tiêu chí k đồng nhất
      if (dic[i].templates.length != dic[0].templates.length) {
        dataReport.errorNotSameCapacity = 'Số lượng các tiêu chí không đồng nhất!'
        return false
      }
      for (const capacity of dic[i].templates) {
        // name k đồng nhất
        if (dic[i - 1][capacity.name] == null) {
          dataReport.errorNotSameCapacity = `Tên tiêu chí ${capacity.name} không đồng nhất!`
          return false
        }
        // level k đồng nhất
        if ((dic[i][capacity.name].parentId != null) != (dic[0][capacity.name].parentId != null)) {
          dataReport.errorNotSameCapacity = `Cấp tiêu chí ${capacity.name} không đồng nhất!`
          return false
        }
        // percent k đồng nhất
        if (dic[i][capacity.name].percent != dic[0][capacity.name].percent) {
          dataReport.errorNotSameCapacity = `Tỉ trọng tiêu chí ${capacity.name} không đồng nhất!`
          return false
        }
        // type k đồng nhất
        if (dic[i][capacity.name].type != dic[0][capacity.name].type) {
          dataReport.errorNotSameCapacity = `Loại dữ liệu tiêu chí ${capacity.name} không đồng nhất!`
          return false
        }
        // percentRule k đồng nhất
        if (dic[i][capacity.name].percentRule != dic[0][capacity.name].percentRule) {
          dataReport.errorNotSameCapacity = `Điều kiện tiêu chí ${capacity.name} đạt không đồng nhất!`
          return false
        }
      }
    }
    return true
  }

  private calScore(item: any) {
    let score = 0
    if (item.__childs__?.length > 0) {
      const length = item.__childs__.length
      let scoreC = 0
      for (let i = 0; i < length; i++) {
        // tslint:disable-next-line: no-shadowed-variable
        let temp = this.calScore(item.__childs__[i])
        if (isNaN(temp) || !isFinite(temp)) temp = 0
        scoreC += temp
      }
      const temp = (item.percent * scoreC) / 100
      score += temp
    } else {
      // tslint:disable-next-line:triple-equals
      if (item.type === enumData.DataType.Number.code && item.value && item.value.trim() != '') {
        let temp = 0
        const x = +item.value
        if (item.isCalUp) {
          if (x >= item.percentRule) {
            temp = item.percent
          } else {
            temp = (x * item.percent) / item.percentRule // giá trị * tỉ trọng / điều kiện b
          }
        } else {
          if (x <= item.percentRule) {
            temp = item.percent
          } else if (x >= item.percentDownRule) {
            temp = 0
          } else {
            temp = ((item.percentDownRule - x) * item.percent) / (item.percentDownRule - item.percentRule)
          }
        }
        if (isNaN(temp) || !isFinite(temp)) score += 0
        else score += temp
      } else if (item.type === enumData.DataType.List.code) {
        const chose = item.__supplierCapacityListDetails__.find((p: any) => p.isChosen)
        const temp = chose ? chose.value : 0
        const finalTemp = (temp * item.percent) / 100
        score += finalTemp
      }
    }
    if (isNaN(score) || !isFinite(score)) return 0

    return score
  }

  // Tính điểm năng lực
  private getSupplierScoreCapacity(row: any, bidSupplier: any, dataReport: any) {
    // lấy năng lực NCC
    const supplierCapacity = dataReport.supplierCapacities.filter((c: any) => c.supplierId == bidSupplier.supplierId)
    let supplierCapacityByRow = supplierCapacity.find((c: any) => c.name == row.name)
    if (supplierCapacityByRow) {
      const objCopy = { ...supplierCapacityByRow }
      if (!objCopy.parentId) {
        objCopy.__childs__ = supplierCapacity.filter((c: any) => c.parentId == objCopy.id)
      }

      let score = this.calScore(objCopy)
      return score
    }

    return ''
  }

  // Tính điểm kỹ thuật
  private getSupplierScoreTech(row: any, bidSupplier: any, dataReport: any) {
    const bidTech = dataReport.bidTechs.find((p: any) => p.id === row.id)
    if (bidTech && bidTech.percent > 0 && bidTech.__childs__.length > 0) {
      let temp = 0
      for (const child of bidTech.__childs__) {
        const find = bidSupplier.__bidSupplierTechValue__.find((p: { bidTechId: any }) => p.bidTechId === child.id)
        if (find && find.score > 0) {
          temp += find.score
        }
      }
      temp = (temp * bidTech.percent) / 100
      return temp
    } else if (bidSupplier.__bidSupplierTechValue__) {
      const find = bidSupplier.__bidSupplierTechValue__.find((p: { bidTechId: any }) => p.bidTechId === row.id)
      if (find) {
        return find.score
      }
      return ''
    } else {
      return ''
    }
  }

  // Tính điểm thương mại
  private getSupplierScoreTrade(row: any, bidSupplier: any, dataReport: any) {
    const bidTrade = dataReport.bidTrades.find((p: any) => p.id === row.id)
    if (bidTrade && bidTrade.percent > 0 && bidTrade.__childs__.length > 0) {
      let temp = 0
      for (const child of bidTrade.__childs__) {
        const find = bidSupplier.__bidSupplierTradeValue__.find((p: { bidTradeId: any }) => p.bidTradeId === child.id)
        if (find && find.score > 0) {
          temp += find.score
        }
      }
      temp = (temp * bidTrade.percent) / 100
      return temp
    } else if (bidSupplier.__bidSupplierTradeValue__) {
      const find = bidSupplier.__bidSupplierTradeValue__.find((p: { bidTradeId: any }) => p.bidTradeId === row.id)
      if (find) {
        return find.score
      }
      return ''
    } else {
      return ''
    }
  }

  // Tính điểm giá
  private getSupplierValue(row: any, bidSupplier: any) {
    const res = {
      number: 0,
      value: 0,
      price: 0,
    }
    if (bidSupplier.__bidSupplierPriceValue__) {
      const find = bidSupplier.__bidSupplierPriceValue__.find((p: { bidPriceId: any }) => p.bidPriceId === row.id)
      if (find && find.value !== '' && find.value != null) {
        res.value = +find.value
        res.number = +find.number
        if (!find.number) res.number = row.number
        res.price = res.value * res.number
      }
    }
    return res
  }

  /** Lấy giá mới nhất của từng NCC tham gia gói thầu */
  private async getDataPriceNewest(user: UserDto, bidId: string) {
    const result: any[] = []
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: bidId } })
    if (!bid) throw new NotFoundException('Không tìm thấy thông tin gói thầu.')

    const bidSupplierRepo = this.repo.manager.getRepository(BidSupplierEntity)
    const bidDealRepo = this.repo.manager.getRepository(BidDealEntity)
    const bidDealSupplierRepo = this.repo.manager.getRepository(BidDealSupplierEntity)
    const bidAuctionRepo = this.repo.manager.getRepository(BidAuctionEntity)
    const bidAuctionSupplierRepo = this.repo.manager.getRepository(BidAuctionSupplierEntity)

    // Lấy hồ sơ của các NCC tham gia
    const lstBidSupplier = await bidSupplierRepo.find({ where: [{ bidId: bid.bidId }, { bidItemId: bidId }] })
    if (lstBidSupplier.length == 0) return result

    // Danh sách các lần đàm phán giá của gói thầu
    const lstBidDeal = await bidDealRepo.find({
      where: [{ bidId: bid.bidId }],
      order: { createdAt: 'DESC' },
    })
    const lstBidDealId = lstBidDeal.map((c) => c.id)
    let lstBidDealSupplier: BidDealSupplierEntity[] = []
    if (lstBidDealId.length > 0) {
      lstBidDealSupplier = await bidDealSupplierRepo.find({
        where: {
          bidDealId: In(lstBidDealId),
          status: enumData.BidDealSupplierStatus.DaGuiGiaMoi.code,
          companyId: user.companyId,
        },
        order: { createdAt: 'DESC' },
      })
    }

    // Danh sách các lần đấu giá của gói thầu
    const lstBidAuction = await bidAuctionRepo.find({
      where: { bidId },
      order: { createdAt: 'DESC' },
    })
    const lstBidAuctionId = lstBidAuction.map((c) => c.id)
    let lstBidAuctionSupplier: BidAuctionSupplierEntity[] = []
    if (lstBidAuctionId.length > 0) {
      lstBidAuctionSupplier = await bidAuctionSupplierRepo.find({
        where: {
          bidAuctionId: In(lstBidAuctionId),
          status: enumData.BidAuctionSupplierStatus.DaDauGia.code,
          companyId: user.companyId,
        },
        order: { createdAt: 'DESC' },
      })
    }

    const lstPrice: any[] = []
    for (const bidSupplier of lstBidSupplier) {
      let submitType = 0 //0: hồ sơ giá, 1: đàm phán giá, 2: đấu giá
      // Nếu tham gia cả 2 thì so sánh lấy lần gần nhất
      const bidDealSupplier = lstBidDealSupplier.find((c) => c.supplierId == bidSupplier.supplierId)
      const bidAuctionSupplier = lstBidAuctionSupplier.find((c) => c.supplierId == bidSupplier.supplierId)
      if (bidDealSupplier != null && bidAuctionSupplier != null) {
        if (bidDealSupplier.createdAt > bidAuctionSupplier.createdAt) {
          submitType = 1
        } else {
          submitType = 2
        }
      } else if (bidDealSupplier != null) submitType = 1
      else if (bidAuctionSupplier != null) submitType = 2

      if (submitType == 1 && bidDealSupplier) {
        const bidDeal = lstBidDeal.find((c) => c.id == bidDealSupplier.bidDealId)
        if (bidDeal) {
          const lstBidDealPrice = await bidDeal.bidDealPrices
          const lstPriceValue: any[] = await bidDealSupplier.bidDealSupplierPriceValue
          // Lấy số lượng theo số lượng đàm phán
          for (const itemPriceValue of lstPriceValue) {
            const bidDealPrice = lstBidDealPrice.find((c) => c.bidPriceId == itemPriceValue.bidPriceId)
            if (bidDealPrice && bidDealPrice.number) {
              itemPriceValue.number = bidDealPrice.number
            }
          }
          const submitDate = bidDealSupplier.submitDate || bidDealSupplier.createdAt
          lstPrice.push({ ...bidSupplier, lstPriceValue, submitType, submitDate })
        }
      } else if (submitType == 2 && bidAuctionSupplier) {
        const lstPriceValue = await bidAuctionSupplier.bidAuctionSupplierPriceValue
        const submitDate = bidAuctionSupplier.submitDate || bidAuctionSupplier.createdAt
        lstPrice.push({ ...bidSupplier, lstPriceValue, submitType, submitDate })
      } else if (
        bidSupplier.status === enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code ||
        bidSupplier.status === enumData.BidSupplierStatus.DangDanhGia.code ||
        bidSupplier.status === enumData.BidSupplierStatus.DaDanhGia.code
      ) {
        const submitDate = bidSupplier.submitDate || bidSupplier.createdAt
        // nếu chưa reset giá thì lấy kết quả chào giá, còn không thì lấy kết quả chào giá bổ sung
        if (bidSupplier.statusResetPrice == enumData.BidSupplierResetPriceStatus.DaBoSung.code) {
          const lstPriceValue = await bidSupplier.bidSupplierPriceValue
          lstPrice.push({ ...bidSupplier, lstPriceValue, submitType, submitDate })
        }
      }
    }

    return lstPrice
  }

  /** Báo cáo kết quả đánh giá */
  async getDataReportRateBid(user: UserDto, bidId: string) {
    const res: any = {}
    const bid: any = await this.repo.findOne({
      where: { id: bidId },
      select: { id: true, name: true, status: true, approveChooseSupplierWinDate: true },
    })
    if (!bid) throw new NotFoundException('Không tìm thấy thông tin gói thầu.')
    res.id = bid.id
    res.bidName = bid.name
    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    res.statusName = dicStatus[res.status]

    res.approveChooseSupplierWinDate = bid.approveChooseSupplierWinDate
    res.bidTechs = await this.bidTechRepo.getTech(user, bid.id)

    // Lấy template ĐKTM của gói thầu
    res.bidTrades = await this.bidTradeRepo.getTrade(user, bid.id)
    res.lstBidEx = await this.bidExMatGroupRepository.find({
      where: { bidId: bid.id },
    })
    for (const bidChild of res.lstBidEx) {
      // bidChild.itemName = bidChild.__service__.code + ' - ' + bidChild.__service__.name
      // delete bidChild.__service__

      const service = await this.serviceRepository.findOne({ where: { externalMaterialGroupId: bidChild.externalMaterialGroupId.toLowerCase() } })
      if (service?.code) {
        bidChild.itemName = service?.code + ' - ' + service?.name
      }

      // Lấy template hồ sơ kỹ thuật của gói thầu

      bidChild.bidTechs = await this.bidTechRepo.getTech(user, bid.id)
      bidChild.bidTrades = await this.bidTradeRepo.getTrade(user, bid.id)
      // Lấy template bảng chào giá của gói thầu
      bidChild.bidPrices = await this.bidPriceRepo.getPrice(user, bidChild.id)
      // Lấy hồ sơ của các NCC tham gia
      bidChild.lstBidSupplier = await this.bidSupplierRepo.find({
        where: [{ bidId: bidChild.bidId }, { bidItemId: bidChild.id }],
        relations: { supplier: true, bidSupplierTechValue: true, bidSupplierTradeValue: true },
      })
      bidChild.supplierCapacities = []
      const lstBidSupplierTemp = []
      const bidSupplierFileStatus: any = enumData.BidSupplierFileStatus
      const lstBidSupplierPriceValue = await this.getDataPriceNewest(user, bidChild.id)
      for (const bidSupplier of bidChild.lstBidSupplier) {
        bidSupplier.supplierName = bidSupplier.__supplier__.name
        bidSupplier.successBidStatus = bidSupplier.isSuccessBid ? 'Trúng thầu' : 'Không trúng thầu'
        bidSupplier.validStatus = bidSupplierFileStatus[bidSupplier.statusFile].name
        const bidSupplierValue = lstBidSupplierPriceValue.find((c) => c.id == bidSupplier.id)
        if (bidSupplierValue) {
          lstBidSupplierTemp.push({
            ...bidSupplier,
            __bidSupplierPriceValue__: bidSupplierValue.lstPriceValue,
          })
        }
      }

      if (lstBidSupplierTemp.length == 0) continue

      const lstValue = lstBidSupplierTemp.map((c) => c.scorePrice)
      const maxValue = Math.max(...lstValue)
      const dlc = coreHelper.calDLC(lstValue)

      const lstValueManual = lstBidSupplierTemp.map((c) => c.scoreManualPrice)
      const maxValueManual = Math.max(...lstValueManual)
      const dlcManual = coreHelper.calDLC(lstValueManual)

      const lstSupplierId = lstBidSupplierTemp.map((c) => c.supplierId).filter((value, idx, self) => self.indexOf(value) == idx)
      const lstSupplierService = await this.repo.manager.getRepository(SupplierServiceEntity).find({
        where: {
          supplierId: In(lstSupplierId),
          serviceId: bidChild.serviceId,
          isDeleted: false,
        },
      })

      bidChild.lstBidSupplier = []
      for (const item of lstBidSupplierTemp) {
        let isHasTotal = false
        let total = 0
        let totalManual = 0
        if (item.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code || item.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code) {
          isHasTotal = true
          total += (item.scoreTech * bidChild.percentTech) / 100
          totalManual += (item.scoreManualTech * bidChild.percentTech) / 100
        } else {
          item.scoreTech = -1
          item.scoreManualTech = -1
        }

        if (
          item.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code ||
          item.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code
        ) {
          isHasTotal = true
          total += (item.scoreTrade * bidChild.percentTrade) / 100
          totalManual += (item.scoreManualTrade * bidChild.percentTrade) / 100
        } else {
          item.scoreTrade = -1
          item.scoreManualTrade = -1
        }

        if (
          item.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code ||
          item.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code
        ) {
          isHasTotal = true
          let priceScore = 0
          if (dlc > 0) {
            priceScore = bidChild.percentPrice - (maxValue - item.scorePrice) / dlc
          } else {
            priceScore = bidChild.percentPrice
          }
          total += priceScore

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = bidChild.percentPrice - (maxValueManual - item.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = bidChild.percentPrice
          }
          totalManual += priceManualScore
        } else {
          item.scorePrice = -1
          item.scoreManualPrice = -1
        }

        if (!isHasTotal) {
          total = -1
          totalManual = -1
        }

        const supplierService = lstSupplierService.find((c) => c.supplierId == item.supplierId)
        var totalScoreCapacity = 0
        if (supplierService) totalScoreCapacity = supplierService.score

        bidChild.lstBidSupplier.push({
          ...item,
          scoreTotal: total,
          scoreManualTotal: totalManual,
          scoreCapacity: totalScoreCapacity,
        })
      }

      bidChild.supplierCapacities = await this.repo.manager.getRepository(SupplierCapacityEntity).find({
        where: { supplierId: In(lstSupplierId), serviceId: bidChild.serviceId, isDeleted: false },
        relations: { supplierCapacityListDetails: true },
      })

      //#region code FE
      let isSameCapacity = true
      //Kiểm tra hồ sơ năng lực đồng nhất
      if (bidChild.lstBidSupplier.length > 0) {
        isSameCapacity = await this.checkSameCapacity(bidChild)
      }
      //#region Tính tổng tỉ trọng

      // Template theo NCC
      bidChild.capacities = []

      if (isSameCapacity && bidChild.lstBidSupplier.length > 0) {
        // lấy template năng lực theo supplier đầu tiên
        const bidSupplierFirst = bidChild.lstBidSupplier[0]
        const templateCapacity = bidChild.supplierCapacities.filter((c: any) => c.supplierId == bidSupplierFirst.supplierId)
        bidChild.capacities = templateCapacity.filter((c: any) => c.parentId == null)
        bidChild.capacities = bidChild.capacities.sort((a: any, b: any) => a.sort - b.sort)
        for (const capacity of bidChild.capacities) {
          const lstChild = templateCapacity.filter((c: any) => c.parentId == capacity.id)
          capacity.__childs__ = lstChild.sort((a: any, b: any) => a.sort - b.sort)
        }
      }
      bidChild.totalPercentTech = 0
      for (const tech of res.bidTechs) {
        tech.__childs__ = tech.__childs__.filter((c: any) => c.isDeleted === false)
        if (tech.percent > 0) bidChild.totalPercentTech += tech.percent
      }

      bidChild.totalPercentTrade = 0
      for (const trade of res.bidTrades) {
        trade.__childs__ = trade.__childs__.filter((c: any) => c.isDeleted === false)
        if (trade.percent > 0) bidChild.totalPercentTrade += trade.percent
      }

      for (const priceLv1 of bidChild.bidPrices) {
        priceLv1.__childs__ = priceLv1.__childs__.filter((c: any) => c.isDeleted === false)
        for (const priceLv2 of priceLv1.__childs__) {
          priceLv2.__childs__ = priceLv2.__childs__.filter((c: any) => c.isDeleted === false)
        }
      }
      //#endregion
      //#region Tính tổng điểm
      for (const bidSupplier of bidChild.lstBidSupplier) {
        bidSupplier.supplierName = bidSupplier.__supplier__.name
        if (isSameCapacity) {
          for (const capacity of bidChild.capacities) {
            capacity[bidSupplier.id] = this.getSupplierScoreCapacity(capacity, bidSupplier, bidChild)
            for (const child of capacity.__childs__) {
              child[bidSupplier.id] = this.getSupplierScoreCapacity(child, bidSupplier, bidChild)
            }
          }
        }

        for (const tech of res.bidTechs) {
          tech[bidSupplier.id] = this.getSupplierScoreTech(tech, bidSupplier, res)
          for (const child of tech.__childs__) {
            child[bidSupplier.id] = this.getSupplierScoreTech(child, bidSupplier, res)
          }
        }

        for (const trade of res.bidTrades) {
          trade[bidSupplier.id] = this.getSupplierScoreTrade(trade, bidSupplier, res)
          for (const child of trade.__childs__) {
            child[bidSupplier.id] = this.getSupplierScoreTrade(child, bidSupplier, res)
          }
        }

        bidSupplier.totalPrice = 0
        for (const priceLv1 of bidChild.bidPrices) {
          priceLv1[bidSupplier.id] = this.getSupplierValue(priceLv1, bidSupplier)
          const price = priceLv1[bidSupplier.id].price
          if (price > 0) bidSupplier.totalPrice += price
          for (const priceLv2 of priceLv1.__childs__) {
            priceLv2[bidSupplier.id] = this.getSupplierValue(priceLv2, bidSupplier)
            for (const priceLv3 of priceLv2.__childs__) {
              priceLv3[bidSupplier.id] = this.getSupplierValue(priceLv3, bidSupplier)
            }
          }
        }
      }
      //#endregion

      //#region Tính xếp loại

      let len = bidChild.lstBidSupplier.length
      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort(
        (a: { scoreCapacity: number }, b: { scoreCapacity: number }) => b.scoreCapacity - a.scoreCapacity,
      )
      let rank = 0
      let currentScore = 0
      for (let i = 0; i < len; i++) {
        let scoreCapacity = bidChild.lstBidSupplier[i].scoreCapacity
        if (scoreCapacity != currentScore) {
          rank++
          currentScore = scoreCapacity
        }
        bidChild.lstBidSupplier[i].rankCapacity = rank
      }

      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort((a: any, b: any) => b.scoreTech - a.scoreTech)
      rank = 0
      currentScore = 0
      for (let i = 0; i < len; i++) {
        let scoreTech = bidChild.lstBidSupplier[i].scoreTech
        if (scoreTech >= 0 && scoreTech != currentScore) {
          rank++
          currentScore = scoreTech
        }
        bidChild.lstBidSupplier[i].rankTech = rank
      }

      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort((a: any, b: any) => b.scoreTrade - a.scoreTrade)
      rank = 0
      currentScore = 0
      for (let i = 0; i < len; i++) {
        let scoreTrade = bidChild.lstBidSupplier[i].scoreTrade
        if (scoreTrade >= 0 && scoreTrade != currentScore) {
          rank++
          currentScore = scoreTrade
        }
        bidChild.lstBidSupplier[i].rankTrade = rank
      }

      // xếp hạng theo tổng giá
      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort((a: any, b: any) => a.totalPrice - b.totalPrice)
      rank = 0
      currentScore = 0
      for (let i = 0; i < len; i++) {
        let totalPrice = bidChild.lstBidSupplier[i].totalPrice
        if (totalPrice != currentScore) {
          rank++
          currentScore = totalPrice
        }
        bidChild.lstBidSupplier[i].rankPrice = rank
      }

      // xếp hạng theo điểm giá
      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort((a: any, b: any) => b.scorePrice - a.scorePrice)
      rank = 0
      currentScore = 0
      for (let i = 0; i < len; i++) {
        let score = bidChild.lstBidSupplier[i].scorePrice
        if (score >= 0 && score != currentScore) {
          rank++
          currentScore = score
        }
        bidChild.lstBidSupplier[i].rankScorePrice = rank
      }

      // xếp hạng tổng
      bidChild.lstBidSupplier = bidChild.lstBidSupplier.sort((a: any, b: any) => b.scoreTotal - a.scoreTotal)
      rank = 0
      currentScore = 0
      for (let i = 0; i < len; i++) {
        let scoreTotal = bidChild.lstBidSupplier[i].scoreTotal
        if (scoreTotal > 0 && scoreTotal != currentScore) {
          rank++
          currentScore = scoreTotal
        }
        bidChild.lstBidSupplier[i].rankTotalScore = rank
      }

      //#endregion

      //#endregion
    }

    return res
  }

  /** In kết quả đánh giá */
  async getDataPrintRateBid(user: UserDto, bidId: string) {
    const res: any = await this.repo.findOne({
      where: { id: bidId },
      relations: { employeeAccess: { employee: true }, childs: { service: true }, bidType: true, masterBidGuarantee: true },
    })
    if (!res) throw new NotFoundException('Không tìm thấy thông tin gói thầu.')

    const lstAccess = res.__employeeAccess__ || []
    delete res.__employeeAccess__
    res.listMember = lstAccess
      .filter((c) => c.type === enumData.BidRuleType.Memmber.code)
      .map((c) => c.__employee__.name)
      .join(' - ')

    res.listOther = lstAccess
      .filter((c) => c.type === enumData.BidRuleType.Other.code)
      .map((c) => c.__employee__.name)
      .join(' - ')

    if (res.prId) {
      const pr = await this.repo.manager.getRepository(PrEntity).findOne({ where: { id: res.prId }, select: { id: true, code: true } })
      if (pr) res.prCode = pr.code
    }

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }
    res.statusName = dicStatus[res.status]

    res.bidTypeName = res.__bidType__?.name
    delete res.__bidType__
    res.masterBidGuaranteeName = res.__masterBidGuarantee__?.name
    delete res.__masterBidGuarantee__

    const mpoObj = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
    if (mpoObj) res.mpo = mpoObj.__employee__.name

    const mpoLeaderObj = lstAccess.find((c) => c.type === enumData.BidRuleType.MPOLeader.code)
    if (mpoLeaderObj) res.mpoLeader = mpoLeaderObj.__employee__.name

    const techObj = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
    if (techObj) res.tech = techObj.__employee__.name

    const techLeaderObj = lstAccess.find((c) => c.type === enumData.BidRuleType.TechLeader.code)
    if (techLeaderObj) res.techLeader = techLeaderObj.__employee__.name

    res.listItem = []
    for (const item of res.__childs__) {
      res.listItem.push({
        id: item.id,
        itemName: item.__service__?.code + ' - ' + item.__service__?.name,
        quantityItem: item.quantityItem,
        percentTech: item.percentTech,
        percentTrade: item.percentTrade,
        percentPrice: item.percentPrice,
        serviceId: item.serviceId,
      })
    }
    delete res.__childs__

    for (const bidItem of res.listItem) {
      // Lấy hồ sơ của các NCC tham gia
      bidItem.lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: bidItem.id },
        relations: { supplier: true },
      })
      if (bidItem.lstBidSupplier.length == 0) continue

      const bidSupplierFileStatus: any = enumData.BidSupplierFileStatus
      for (const bidSupplier of bidItem.lstBidSupplier) {
        bidSupplier.supplierName = bidSupplier.__supplier__.name
        delete bidSupplier.__supplier__
        bidSupplier.validStatus = bidSupplierFileStatus[bidSupplier.statusFile]?.name
        bidSupplier.successBidStatus = bidSupplier.isSuccessBid ? 'Trúng thầu' : 'Không trúng thầu'
      }

      const lstValue = bidItem.lstBidSupplier.map((c) => c.scorePrice)
      const maxValue = Math.max(...lstValue)
      const dlc = coreHelper.calDLC(lstValue)

      const lstValueManual = bidItem.lstBidSupplier.map((c) => c.scoreManualPrice)
      const maxValueManual = Math.max(...lstValueManual)
      const dlcManual = coreHelper.calDLC(lstValueManual)

      const lstSupplierId = bidItem.lstBidSupplier.map((c) => c.supplierId)
      const lstSupplierService = await this.repo.manager.getRepository(SupplierServiceEntity).find({
        where: {
          supplierId: In(lstSupplierId),
          serviceId: bidItem.serviceId,
          companyId: user.companyId,
          isDeleted: false,
        },
        select: { supplierId: true, score: true },
      })

      for (const item of bidItem.lstBidSupplier) {
        let isHasTotal = false
        item.scoreTotal = 0
        item.scoreManualTotal = 0
        if (item.statusTech === enumData.BidSupplierTechStatus.DaXacNhan.code || item.statusTech === enumData.BidSupplierTechStatus.DaDuyet.code) {
          isHasTotal = true
          item.scoreTotal += (item.scoreTech * bidItem.percentTech) / 100
          item.scoreManualTotal += (item.scoreManualTech * bidItem.percentTech) / 100
        } else {
          item.scoreTech = -1
          item.scoreManualTech = -1
        }

        if (
          item.statusTrade === enumData.BidSupplierTradeStatus.DaXacNhan.code ||
          item.statusTrade === enumData.BidSupplierTradeStatus.DaDuyet.code
        ) {
          isHasTotal = true
          item.scoreTotal += (item.scoreTrade * bidItem.percentTrade) / 100
          item.scoreManualTotal += (item.scoreManualTrade * bidItem.percentTrade) / 100
        } else {
          item.scoreTrade = -1
          item.scoreManualTrade = -1
        }

        if (
          item.statusPrice === enumData.BidSupplierPriceStatus.DaXacNhan.code ||
          item.statusPrice === enumData.BidSupplierPriceStatus.DaDuyet.code
        ) {
          isHasTotal = true
          let priceScore = 0
          if (dlc > 0) {
            priceScore = bidItem.percentPrice - (maxValue - item.scorePrice) / dlc
          } else {
            priceScore = bidItem.percentPrice
          }
          item.scoreTotal += priceScore

          let priceManualScore = 0
          if (dlcManual > 0) {
            priceManualScore = bidItem.percentPrice - (maxValueManual - item.scoreManualPrice) / dlcManual
          } else {
            priceManualScore = bidItem.percentPrice
          }
          item.scoreManualTotal += priceManualScore
        } else {
          item.scorePrice = -1
          item.scoreManualPrice = -1
        }

        if (!isHasTotal) {
          item.scoreTotal = -1
          item.scoreManualTotal = -1
        }

        const supplierService = lstSupplierService.find((c) => c.supplierId == item.supplierId)
        item.scoreCapacity = 0
        if (supplierService) item.scoreCapacity = supplierService.score
      }
    }

    return res
  }

  //#endregion

  //#region Phê duyệt kết thúc thầu

  /** Kiểm tra quyền Gửi yêu cầu phê duyệt kết thúc thầu */
  private async checkPermissionSendRequestFinishBid(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid) {
      if (bid.status === enumData.BidStatus.DuyetNCCThangThau.code) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bidId)
        if (!result) message = 'Bạn không có quyền gửi yêu cầu phê duyệt kết thúc thầu.'
      } else message = 'Gói thầu đã được gửi yêu cầu phê duyệt kết thúc thầu.'
    }

    return { hasPermission: result, message }
  }

  /** Kiểm tra quyền Phê duyệt kết thúc thầu */
  async checkPermissionApproveFinishBid(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid) {
      if (bid.status === enumData.BidStatus.DangDuyetKetThucThau.code) {
        // result = await this.bidEmployeeAccessRepo.isMPOLeader(user, bidId)
        result = true
        if (!result) message = 'Bạn không có quyền phê duyệt kết thúc thầu.'
      } else message = 'Gói thầu đã được phê duyệt kết thúc thầu.'
    }

    return { hasPermission: result, message }
  }

  /** Gửi yêu cầu phê duyệt kết thúc thầu */
  async sendRequestFinishBid(user: UserDto, data: { id: string; fileScan: string; noteFinishBidMPO: string }) {
    if (!data.id) throw new BadRequestException(ERROR_NOT_FOUND_DATA)
    // kiểm tra quyền
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionSendRequestFinishBid(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(data.id, {
      status: enumData.BidStatus.DangDuyetKetThucThau.code,
      noteFinishBidMPO: data.noteFinishBidMPO,
      fileScan: data.fileScan,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.GuiYeuCauPheDuyetKetThucThau.code
    bidHistory.save()

    // Gửi email thông báo mpoLeader
    // await this.emailService.ThongBaoKetQuaDauThauDuocDuyet(data.id)

    // tạo quyền phê duyệt kết thúc thầu
    let flowType: string
    flowType = enumData.FlowCode.FINISH_BID.code
    await this.flowService.setRoleRule(user, {
      targetId: data.id,
      target: data,
      entityName: BidEntity.name,
      flowType: flowType,
      companyId: user.orgCompanyId,
      // departmentId: user?.departmentId,
    })

    return { message: 'Gửi yêu cầu phê duyệt kết thúc thầu thành công.' }
  }

  /** Phê duyệt kết thúc thầu */
  async approveFinishBid(user: UserDto, data: { id: string }) {
    // if (false) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: data.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.FINISH_BID.code,
    })
    if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
      return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    } else {
      if (!data.id) throw new BadRequestException(ERROR_NOT_FOUND_DATA)
      const supplierServiceRepo = this.repo.manager.getRepository(SupplierServiceEntity)
      // kiểm tra quyền
      if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
      const objPermission = await this.checkPermissionApproveFinishBid(user, data.id)
      if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

      const bid = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })

      if (!bid) throw new Error('Gói thầu không còn tồn tại.')

      await this.repo.update(data.id, {
        status: enumData.BidStatus.HoanTat.code,
        bidCloseDate: new Date(),
        updatedBy: user.id,
      })

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = data.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.PheDuyetKetThucThau.code
      bidHistory.save()

      /* lưu RFQ */
      /* tìm ra RFQ */
      const listSupplierWin = await this.bidSupplierItemRepository.find({ where: { bidId: data.id } })
      const dicSupplierItemWin: any = {}
      for (const supplierWin of listSupplierWin) {
        if (!dicSupplierItemWin[supplierWin.supplierId]) dicSupplierItemWin[supplierWin.supplierId] = { lstItemWin: [] }
        dicSupplierItemWin[supplierWin.supplierId].lstItemWin.push(supplierWin.materialId)
      }
      const listSupplierWinId = listSupplierWin.map((e) => e.supplierId)
      /* tìm ra bidDeal mới nhất bằng bidPrice */
      const lastedBidDeal = await this.bidDealRepo.findOne({ where: { bidId: data.id }, order: { createdAt: 'DESC' } })
      /* sau đó tìm ra những deal của nhà cung cấp thằng đấu thầu này */
      const bidDealSupplierWin: any = await this.bidDealSupplierRepository.find({
        where: { bidDealId: lastedBidDeal.id, supplierId: In(listSupplierWinId) },
        relations: { bidDealSupplierPriceValue: { bidPrice: { baseItem: { prItem: true } } } },
      })
      const lstData = []
      const lstDataDetails = []

      for (const item of bidDealSupplierWin) {
        const listItemWinning = dicSupplierItemWin[item.supplierId].lstItemWin
        /* 1 nhà cung cấp 1 item */
        /* lưư lại cho nhà cung cấp  */
        const newRFQ = new RfqEntity()
        newRFQ.targetId = data.id
        newRFQ.id = uuidv4()
        newRFQ.entityName = BidEntity.name
        // newRFQ.materialCode = bidPrice.__bidPrice__.baseItemId
        newRFQ.dealId = lastedBidDeal.id
        newRFQ.type = 'AN'
        newRFQ.supplierId = item.supplierId
        newRFQ.deadLine = new Date()
        for (const bidPrice of item.__bidDealSupplierPriceValue__) {
          if (listItemWinning.includes(bidPrice.__bidPrice__.__baseItem__.__prItem__.materialId)) {
            const bidPriceItem = bidPrice.__bidPrice__
            const bidIem = await bidPriceItem.bidItem
            const prItem = await bidIem.prItem
            const newRFQDetails = new RfqDetailsEntity()
            newRFQDetails.rfqId = newRFQ.id
            newRFQDetails.deliveryDate = new Date(prItem.deliveryDate)
            if (new Date(newRFQ.deadLine) <= new Date(prItem.deliveryDate)) newRFQ.deadLine = prItem.deliveryDate
            newRFQDetails.materialId = bidPrice.__bidPrice__.__baseItem__.__prItem__.materialId
            // newRFQDetails.materialCode = bidPrice.__bidPrice__.baseItemId
            newRFQDetails.rfqQuantity = bidPrice.__bidPrice__.number
            newRFQDetails.dealId = lastedBidDeal.id
            newRFQDetails.supplierId = item.supplierId
            // const itemPr = item.
            newRFQDetails.netPrice = bidPrice.value
            lstDataDetails.push(newRFQDetails)
          }
        }
        lstData.push(newRFQ)
      }
      await this.rfqRepository.save(lstData)
      await this.rfqDetailsRepository.save(lstDataDetails)

      // Gửi email thông báo nội bộ
      this.emailService.ThongBaoKetQuaDauThauDuocDuyet(data.id)
      // Gửi email NCC trúng thầu
      this.emailService.ThongBaoTrungThau(data.id)
      // Gửi email cảm ơn NCC tham gia đấu thầu nhưng không trúng
      this.emailService.ThuCamOnThamGiaThau(data.id)

      return { message: 'Phê duyệt kết thúc thầu thành công.' }
    }
  }

  //#endregion

  //#region Đàm phán/ Đấu giá

  /** DS item khi Đàm phán/ Đấu giá */
  async itemPagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.where.bidId) throw new BadRequestException('Không xác định được gói thầu!')

    const res: any[] = await this.bidExMatGroupRepository.findAndCount({
      where: { bidId: data.where.bidId, isDeleted: false },
      skip: data.skip,
      take: data.take,
    })
    if (res[0].length == 0) return res

    const setStatus = new Set()
    setStatus.add(enumData.BidStatus.DangDamPhanGia.code)
    setStatus.add(enumData.BidStatus.DongDamPhanGia.code)
    setStatus.add(enumData.BidStatus.DangDauGia.code)
    setStatus.add(enumData.BidStatus.DongDauGia.code)

    for (const item of res[0]) {
      const service = await this.serviceRepository.findOne({ where: { externalMaterialGroupId: item.externalMaterialGroupId.toLowerCase() } })
      if (service?.code) {
        item.itemName = service?.code + ' - ' + service?.name
      }
      // item.itemName = item.__service__.code + ' - ' + item.__service__.name
      // delete item.__service__

      if (!setStatus.has(item.status)) {
        item.statusColor = '#28a745'
        item.statusName = 'Sẵn sàng'
        continue
      }

      item.statusColor = enumData.BidStatus[item.status].color
      item.statusName = enumData.BidStatus[item.status].name
    }

    return res
  }

  //#endregion
}
