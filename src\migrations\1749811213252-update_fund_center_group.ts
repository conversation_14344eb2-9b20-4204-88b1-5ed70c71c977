import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateFundCenterGroup1749811213252 implements MigrationInterface {
  name = 'UpdateFundCenterGroup1749811213252'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the column exists first
    const columnExists = await queryRunner.hasColumn('site_assessment', 'count')

    if (!columnExists) {
      await queryRunner.query('ALTER TABLE "site_assessment" ADD "count" bigint')
    }

    const columnExists2 = await queryRunner.hasColumn('group_permission', 'purchasingOrgIdTemp')

    if (!columnExists2) {
      await queryRunner.query(`ALTER TABLE "group_permission" ADD "purchasingOrgIdTemp" varchar(max)`)
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "purchasingOrgIdTemp"`)
    await queryRunner.query('ALTER TABLE "site_assessment" DROP COLUMN "count"')
  }
}
