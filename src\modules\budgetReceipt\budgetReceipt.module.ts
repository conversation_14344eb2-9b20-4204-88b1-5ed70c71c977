import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BudgetReceiptRepository, CompanyRepository, EmployeeRepository } from '../../repositories'
import { BudgetReceiptController } from './budgetReceipt.controller'
import { BudgetReceiptService } from './budgetReceipt.service'
import { CurrencyRepository } from '../../repositories/currency.repository'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([BudgetReceiptRepository, CompanyRepository, CurrencyRepository, EmployeeRepository]),
    OrganizationalPositionModule,
  ],
  controllers: [BudgetReceiptController],
  providers: [BudgetReceiptService],
  exports: [BudgetReceiptService],
})
export class BudgetReceiptModule {}
