import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { RequestQuoteEntity } from './requestQuote.entity'
import { SupplierEntity } from './supplier.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'

/** Yêu cầu báo giá */
@Entity('request_quote_supplier')
export class RequestQuoteSupplierEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  requestQuoteId: string
  @ManyToOne(() => RequestQuoteEntity, (p) => p.requestQuoteSuppliers)
  @JoinColumn({ name: 'requestQuoteId', referencedColumnName: 'id' })
  requestQuote: Promise<RequestQuoteEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.requestQuoteSuppliers)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.requestQuoteSuppliers)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId: string

  /* shipment conditionTypeid compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactId: string[]

  /* shipment conditionType code compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactCode: string[]

  /* shipment conditionType price compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactValue: string[]

  @Column({
    type: 'nvarchar',
    length: 20,
    nullable: true,
  })
  status: string

  // Thời gian xác nhận tham gia báo giá
  @Column({
    nullable: true,
    type: 'datetime',
  })
  timeConfirmParticipationQuotation: Date

  // Thời gian xác nhận tham gia báo giá
  @Column({
    nullable: true,
    type: 'datetime',
  })
  timeRejectParticipationQuotation: Date
}
