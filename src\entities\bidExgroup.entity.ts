import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { BidPrItemEntity } from './bidPrItem.entity'
import { BidPriceEntity } from './bidPrice.entity'
import { BidCustomPriceEntity } from './bidCustomPrice.entity'
import { BidPriceColEntity } from './bidPriceCol.entity'
import { enumData } from '../constants'

@Entity('bid_exmatgroup')
export class BidExMatGroupEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.bidEx)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  currency: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.bidEx)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  @OneToMany(() => BidPrItemEntity, (p) => p.bidExgroup)
  bidPrItems: Promise<BidPrItemEntity[]>

  @OneToMany(() => BidPriceEntity, (p) => p.bidExgroup)
  prices: Promise<BidPriceEntity[]>

  /** Công thức tính cột đơn giá */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fomular: string

  @OneToMany(() => BidPriceColEntity, (p) => p.bidExgroup)
  bidPriceCol: Promise<BidPriceColEntity[]>

  @OneToMany(() => BidCustomPriceEntity, (p) => p.bidExgroup)
  customPrices: Promise<BidCustomPriceEntity[]>

  /** Cách tính điểm giá */
  @Column({ type: 'varchar', length: 50, nullable: true, default: enumData.PriceScoreCalculateWay.SumScore.code })
  wayCalScorePrice: string

  /** Tỉ trọng % kỹ thuật */
  @Column({
    nullable: true,
    default: 0,
  })
  percentTech: number

  /** Tỉ trọng % DKTM */
  @Column({
    nullable: true,
    default: 0,
  })
  percentTrade: number

  /** Tỉ trọng % giá */
  @Column({
    nullable: true,
    default: 0,
  })
  percentPrice: number

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number
}
