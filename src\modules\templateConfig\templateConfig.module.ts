import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'

import { TypeOrmModule } from '@nestjs/typeorm'
import { EntityManager } from 'typeorm'
import { DatabaseModule } from '../../database/database.module'
import { TemplateConfigController } from './templateConfig.controller'
import { TemplateConfigService } from './templateConfig.service'
import { TemplateConfigRepository } from '../../repositories/templateConfig.repository'

@Module({
  imports: [TypeOrmModule.forFeature([]), TypeOrmExModule.forCustomRepository([TemplateConfigRepository])],
  controllers: [TemplateConfigController],
  providers: [TemplateConfigService, EntityManager],
  exports: [TemplateConfigService],
})
export class TemplateConfigModule {}
