import { <PERSON>ti<PERSON>, Column, Jo<PERSON><PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { TemplateCriterialEntity } from './templateCriteria.entity'

/**Template đánh giá ncc tiềm năng */
@Entity('template_evaluation_potential')
export class TemplateEvaluationPotentialEntity extends BaseEntity {
  /**Nhân viên đánh giá pháp lý */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeLawId: string

  @ManyToOne(() => EmployeeEntity, (p) => p.templateEvaluationLaws)
  @JoinColumn({ name: 'employeeLawId', referencedColumnName: 'id' })
  employeeLaw: Promise<EmployeeEntity>

  /**Nhân viên đánh giá năng lực*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeCapacityId: string

  @ManyToOne(() => EmployeeEntity, (p) => p.templateEvaluationCapacities)
  @JoinColumn({ name: 'employeeCapacityId', referencedColumnName: 'id' })
  employeeCapacity: Promise<EmployeeEntity>

  /**Ngưỡng đạt điểm đánh giá */
  @Column({
    type: 'float',
    nullable: true,
  })
  passingScore: number

  @OneToMany(() => TemplateCriterialEntity, (p) => p.templateEvaluationPotential)
  templateCriterions: Promise<TemplateCriterialEntity[]>
}
