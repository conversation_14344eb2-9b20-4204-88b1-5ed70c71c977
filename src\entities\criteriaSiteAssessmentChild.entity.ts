import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { CriteriaSiteAssessmentEntity } from './criteriaSiteAssessment.entity'
import { CriteriaSiteAssessmentListDetailEntity } from './criteriaSiteAssessmentListDetail.entity'
import { MediaFileEntity } from './mediaFile.entity'

// nội dung đánh giá template hiện trường
@Entity('criteria_site_assessment_child')
export class CriteriaSiteAssessmentChildEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  name: string

  @Column({
    nullable: true,
    default: false,
  })
  isToStore: boolean

  @Column({
    nullable: true,
    default: 0,
  })
  maxScore: number

  @Column({
    nullable: true,
    default: false,
  })
  isSenSupplier: boolean

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  dataType: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  document: string

  /** Nhà cung cấp trả lời */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  supplierReply: string

  /** Nhà cung cấp trả lời list */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  supplierReplyList: string

  /** Nhà cung cấp trả lời Number */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  supplierReplyNumber: number

  /** Điểm đánh giá điều chỉnh do nhà cung cấp */
  @Column({
    nullable: true,
  })
  supplierScoreEvaluation: number

  /** Đánh giá điều chỉnh do nhân viên đánh giá */
  @Column({
    type: 'varchar',
    length: 400,
    nullable: true,
  })
  evaluation: string

  /** Đường dẫn file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileUrl: string

  /** Tên file */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileName: string

  /** Điểm đánh giá */
  @Column({
    nullable: true,
  })
  scoreEvaluation: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeEvaluationId: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  criteriaSiteAssessmentId: string
  @ManyToOne(() => CriteriaSiteAssessmentEntity, (p) => p.criteriaSiteAssessmentChilds)
  @JoinColumn({ name: 'criteriaSiteAssessmentId', referencedColumnName: 'id' })
  criteriaSiteAssessment: Promise<CriteriaSiteAssessmentEntity>

  // các list detail của các câu hỏi con
  @OneToMany(() => CriteriaSiteAssessmentListDetailEntity, (p) => p.criteriaSiteAssessmentChild)
  criteriaSiteAssessmentListDetails: Promise<CriteriaSiteAssessmentListDetailEntity[]>

  // các file đính kèm
  @OneToMany(() => MediaFileEntity, (p) => p.criteriaSiteAssessmentChild)
  mediaFiles: Promise<MediaFileEntity[]>
}
