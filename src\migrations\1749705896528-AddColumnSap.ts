import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnSap1749705896528 implements MigrationInterface {
  name = 'AddColumnSap1749705896528'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "supplier_number_request_approve" ADD "isLockBusinessPartner" bit CONSTRAINT "DF_86d3f3bb1d556f1f83538a56aff" DEFAULT 0`,
    )
    await queryRunner.query(
      `ALTER TABLE "supplier_number_request_approve" ADD "isLockFISupplier" bit CONSTRAINT "DF_03dab1e241f2a256a9a862e83fd" DEFAULT 0`,
    )
    await queryRunner.query(
      `ALTER TABLE "supplier_number_request_approve" ADD "isLockSupplier" bit CONSTRAINT "DF_792e9a1df416d1f4eb8b2f57574" DEFAULT 0`,
    )
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "DF_792e9a1df416d1f4eb8b2f57574"`)
    await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP COLUMN "isLockSupplier"`)
    await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "DF_03dab1e241f2a256a9a862e83fd"`)
    await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP COLUMN "isLockFISupplier"`)
    await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "DF_86d3f3bb1d556f1f83538a56aff"`)
    await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP COLUMN "isLockBusinessPartner"`)
  }
}
