import { Column, Entity, Index, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ShipmentContainerEntity } from './shipmentContainer.entity'
import { ShipmentConditionTypeTemplateEntity } from './shipmentCondictionTypeTemplate.entity'
import { TransportationPlanDetailEntity } from './transportationPlanDetail.entity'
import { ShipmentConfigTemplateDetailEntity } from './shipmentConfigTemplateDetail.entity'

@Entity('shipment_condition_type')
export class ShipmentConditionTypeEntity extends BaseEntity {
  /* mã condition type*/
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string
  /* name condition type */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  name: string

  /* description condition type */
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  description: string

  /* danh sách mã (Đi<PERSON>u kiện thiết lập phí) - Shipment_Fee_Conditions */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    /* transform từ array sang string và ngược lại */
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionsMonth: string[]

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    /* transform từ array sang string và ngược lại */
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionsYear: string[]

  @OneToMany(() => ShipmentConditionTypeTemplateEntity, (p) => p.shipmentConditionType)
  shipmentConditionTypeTemplates: Promise<ShipmentConditionTypeTemplateEntity[]>

  @OneToMany(() => TransportationPlanDetailEntity, (p) => p.shipmentConditionType)
  transportationPlanDetails: Promise<TransportationPlanDetailEntity[]>

  @OneToMany(() => ShipmentConfigTemplateDetailEntity, (p) => p.shipmentConditionType)
  shipmentConfigTemplateDetails: Promise<ShipmentConfigTemplateDetailEntity[]>
}
