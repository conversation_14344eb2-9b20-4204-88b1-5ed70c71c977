import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ComplaintEntity } from './complaint.entity'

/** Phòng ngừa khiếu nại */
@Entity('complaint_handling_plan')
export class ComplaintHandlingPlanEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  complaintId: string
  @ManyToOne(() => ComplaintEntity, (p) => p.complaintHandlingPlans)
  @JoinColumn({ name: 'complaintId', referencedColumnName: 'id' })
  complaint: Promise<ComplaintEntity>

  /** Tiêu đề */
  @Column({ type: 'varchar', length: 250, nullable: true })
  title: string

  /** Mô tả  */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  description: string

  /** File đính kèm */
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  attachedFile: string
}
