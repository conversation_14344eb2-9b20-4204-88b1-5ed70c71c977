import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnSupplier1748331641075 implements MigrationInterface {
  name = 'AddColumnSupplier1748331641075'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "companyId"`)
    await queryRunner.query(`ALTER TABLE "supplier" ADD "companyId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "supplier" ADD CONSTRAINT "FK_860a390e2874a2150121f36ae9d" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_860a390e2874a2150121f36ae9d"`)
    await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "companyId"`)
    await queryRunner.query(`ALTER TABLE "supplier" ADD "companyId" varchar(255)`)
  }
}
