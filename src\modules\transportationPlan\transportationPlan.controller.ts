import { Controller, Post, UseInterceptors, UploadedFile } from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { TransportationPlanService } from './transportationPlan.service'

@ApiBearerAuth()
@ApiTags('TransportationPlan')
@Controller('transportationPlan')
export class TransportationPlanController {
  constructor(private readonly service: TransportationPlanService) {}
}
