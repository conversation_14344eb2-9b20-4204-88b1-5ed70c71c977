import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyToOne, Join<PERSON><PERSON>um<PERSON>, OneToMany } from 'typeorm'
import { OfferTechListDetailEntity } from './offerTechDetail.entity'
import { OfferEntity } from './offer.entity'
import { OfferServiceEntity } from './offerService.entity'
import { OfferSupplierTechValueEntity } from './offerSupTechValue.entity'

@Entity('offer_tech')
export class OfferTechEntity extends BaseEntity {
  @Column({
    nullable: false,
    default: 0,
  })
  sort: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** <PERSON><PERSON> bắt buộc nhập hay không */
  @Column({
    nullable: false,
    default: false,
  })
  isRequired: boolean

  /** Cách tính điểm theo loại càng cao càng tốt */
  @Column({
    nullable: false,
    default: true,
  })
  isCalUp: boolean

  /** <PERSON>ểu dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: false,
    default: 'string',
  })
  type: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  percent: number

  /** % điều kiện đạt tỉ trọng */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentRule: number

  /** % điều kiện liệt tỉ trọng khi tính theo chiều giảm dần */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  percentDownRule: number

  /** Cấp độ */
  @Column({
    nullable: false,
    default: 1,
  })
  level: number

  /** Mô tả */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => OfferTechEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: OfferTechEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => OfferTechEntity, (p) => p.parent)
  childs: Promise<OfferTechEntity[]>

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Giá trị nhỏ nhất */
  @Column({
    nullable: true,
  })
  requiredMin: number

  /** Thuộc tính của tiêu chí thể hệ Doanh nghiệp sẽ được highlight màu xanh nếu đạt giá trị X khi xếp hạng năng lực */
  @Column({
    nullable: false,
    default: false,
  })
  isHighlight: boolean

  @Column({
    nullable: true,
  })
  hightlightValue: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string
  @ManyToOne(() => OfferEntity, (p) => p.techs)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerServiceId: string
  @ManyToOne(() => OfferServiceEntity, (p) => p.techs)
  @JoinColumn({ name: 'offerServiceId', referencedColumnName: 'id' })
  offerService: Promise<OfferServiceEntity>

  /** Con - 1 công thức sẽ có thể có nhiều detail list */
  @OneToMany(() => OfferTechListDetailEntity, (p) => p.offerTech)
  offerTechListDetails: Promise<OfferTechListDetailEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => OfferSupplierTechValueEntity, (p) => p.offerTech)
  offerSupplierTechValue: Promise<OfferSupplierTechValueEntity[]>
}
