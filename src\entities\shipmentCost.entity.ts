import { BidEntity, RecommendedPurchaseEntity, ShipmentCostTypeEntity, ShipmentEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany, Index } from 'typeorm'
import { ShipmentCostStageEntity } from './shipmentCostStage.entity'
import { ShipmentCostStageCostEntity } from './shipmentCostStageCost.entity'
import { ShipmentCostPriceEntity } from './shipmentCostPrice.entity'
import { ShipmentCostDetailEntity } from './shipmentCostDetail.entity'

/** Thông tin shipment cost*/
@Entity('shipment_cost')
export class ShipmentCostEntity extends BaseEntity {
  /**Trạng thái duyệt */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Mã Shipment cost */
  @Index({ unique: true })
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 2000,
    nullable: true,
  })
  fileUrl: string

  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  fileName: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  /** Loại bảng giá */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  priceListType: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentId: string
  @ManyToOne(() => ShipmentEntity, (p) => p.shipmentCosts)
  @JoinColumn({ name: 'shipmentId', referencedColumnName: 'id' })
  shipment: Promise<ShipmentEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentCostTypeId: string
  @ManyToOne(() => ShipmentCostTypeEntity, (p) => p.shipmentCosts)
  @JoinColumn({ name: 'shipmentCostTypeId', referencedColumnName: 'id' })
  shipmentCostType: Promise<ShipmentCostTypeEntity>

  @OneToMany(() => ShipmentCostStageEntity, (p) => p.shipmentCost)
  shipmentCostStages: Promise<ShipmentCostStageEntity[]>

  /**Giá bảng kê của từng shipment cost stage */
  @OneToMany(() => ShipmentCostStageCostEntity, (p) => p.shipmentCost)
  shipmentCostStageCosts: Promise<ShipmentCostStageCostEntity[]>

  /**Template giá dùng cho đấu thầu shipment cost */
  @OneToMany(() => ShipmentCostPriceEntity, (p) => p.shipmentCost)
  prices: Promise<ShipmentCostPriceEntity[]>

  @OneToMany(() => RecommendedPurchaseEntity, (p) => p.shipmentCost)
  recommendedPurchase: Promise<RecommendedPurchaseEntity[]>

  @OneToMany(() => BidEntity, (p) => p.shipment)
  bids: Promise<BidEntity[]>

  @OneToMany(() => ShipmentCostDetailEntity, (p) => p.shipmentCost)
  shipmentCostDetails: Promise<ShipmentCostDetailEntity>
}
