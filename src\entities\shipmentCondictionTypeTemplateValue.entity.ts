import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ShipmentConditionTypeTemplateEntity } from './shipmentCondictionTypeTemplate.entity'

@Entity('shipment_condition_type_template_value')
export class ShipmentConditionTypeTemplateValueEntity extends BaseEntity {
  /*condition type*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentConditionTypeTemplateId: string
  @ManyToOne(() => ShipmentConditionTypeTemplateEntity, (p) => p.shipmentConditionTypeTemplateValues)
  @JoinColumn({ name: 'shipmentConditionTypeTemplateId', referencedColumnName: 'id' })
  shipmentConditionTypeTemplate: Promise<ShipmentConditionTypeTemplateEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentFeeConditionId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentFeeConditionListId: string
}
