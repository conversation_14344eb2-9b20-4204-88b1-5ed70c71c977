import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PurchasingOrgEntity } from './purchasingOrg.entity'
import { SchemaConfigEntity } from './schemaConfig.entity'

/** Tổ chức mua hàng
 */
@Entity('purchasing_org_schema')
export class PurchasingOrgSchemaEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => PurchasingOrgEntity, (p) => p.purchasingOrgSchema)
  purchasingOrgs: Promise<PurchasingOrgEntity[]>

  @OneToMany(() => SchemaConfigEntity, (p) => p.purchasingOrgSchema)
  schemaConfig: Promise<SchemaConfigEntity[]>
}
