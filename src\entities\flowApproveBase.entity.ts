import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { FlowApproveDetailEntity } from './flowApproveDetail.entity'

@Entity({ name: 'flow_approve_base' })
export class FlowApproveBaseEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  flowCode: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: 'None',
  })
  type: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  departmentId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  blockId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  partId: string

  @Column({
    type: 'int',
    nullable: true,
  })
  numberLevel: number

  @Column({
    nullable: true,
    default: false,
  })
  applyCompany: boolean

  @Column({
    nullable: true,
    default: false,
  })
  applyBlock: boolean

  @Column({
    nullable: true,
    default: false,
  })
  applyAnother: boolean

  @Column({
    nullable: true,
    default: false,
  })
  applyDepartment: boolean

  @Column({
    nullable: true,
    default: false,
  })
  applyPart: boolean

  @Column({
    nullable: true,
    default: false,
  })
  applyPlant: boolean

  @Column({
    nullable: true,
    default: false,
  })
  applyPurOrg: boolean

  @Column({
    nullable: true,
    default: false,
  })
  applyPurGr: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purGrId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purOrgId: string

  @Column({
    nullable: true,
    default: false,
  })
  level1ApproveAll: boolean

  @Column({
    nullable: true,
    default: false,
  })
  level2ApproveAll: boolean

  @Column({
    nullable: true,
    default: false,
  })
  level3ApproveAll: boolean

  @Column({
    nullable: true,
    default: false,
  })
  level4ApproveAll: boolean

  @Column({
    nullable: true,
    default: false,
  })
  level5ApproveAll: boolean

  @Column({
    nullable: true,
    default: false,
  })
  level6ApproveAll: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  status: string

  @OneToMany(() => FlowApproveDetailEntity, (p) => p.flowBase)
  flowApprovieDetails: Promise<FlowApproveDetailEntity[]>
}
