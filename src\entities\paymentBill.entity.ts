import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, Join<PERSON>olum<PERSON>, ManyToOne } from 'typeorm'
import { PaymentEntity } from './payment.entity'
import { BillEntity } from './bill.entity'

@Entity('payment_bill')
export class PaymentBillEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  billId: string
  @ManyToOne(() => BillEntity, (p) => p.paymentBills)
  @JoinColumn({ name: 'billId', referencedColumnName: 'id' })
  bill: Promise<BillEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  paymentId: string
  @ManyToOne(() => PaymentEntity, (p) => p.paymentBills)
  @JoinColumn({ name: 'paymentId', referencedColumnName: 'id' })
  payment: Promise<PaymentEntity>
}
