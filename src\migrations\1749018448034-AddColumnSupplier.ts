import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnSupplier1749018448034 implements MigrationInterface {
    name = 'AddColumnSupplier1749018448034'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "supplier_revenue" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a94b543df8460489e85303892cd" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_207f94c13f61c91cf507b19df09" DEFAULT 0, "companyId" varchar(255), "year" datetime, "revenue" bigint CONSTRAINT "DF_a2d24e0d945fc3ad4ae623cf81b" DEFAULT 0, "supplierId" uniqueidentifier NOT NULL, CONSTRAINT "PK_a94b543df8460489e85303892cd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "paymentMethodId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "paymentTermId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "decider" nvarchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "deciderPosition" nvarchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "deciderPhone" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "deciderFax" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "deciderEmail" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "deciderNote" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "trader" nvarchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "traderPosition" nvarchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "traderPhone" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "traderFax" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "traderEmail" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "traderNote" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "bizType" nvarchar(100)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "visionAndMission" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "midLongTermGoals" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "mainBusinessArea" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "fileOrganization" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "directEmpCount" int CONSTRAINT "DF_94668c1633eaef56a8f97591aea" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "indirectEmpCount" int CONSTRAINT "DF_4612c387b4c5c2d867215405bde" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "otherEmpCount" int CONSTRAINT "DF_346f09ee7ff21d60007f0641ee9" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "totalEmpCount" int CONSTRAINT "DF_f0145a5b5ed6adb2a907020e467" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "director" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "marketingManager" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "procurementManager" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "foreman" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "rndDepartmentCount" int CONSTRAINT "DF_38e9db79ca33f1bef2816495a40" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "purchasingDepartmentCount" int CONSTRAINT "DF_76adbed8a2af4d0bab43e6b2b47" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "productionDepartmentCount" int CONSTRAINT "DF_da0128ab84bb46f75e14293916e" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "qualityDepartmentCount" int CONSTRAINT "DF_8df197568e1668af8d1e8274c30" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "warrantyMaintenanceDepartmentCount" int CONSTRAINT "DF_9a6bc94f7a9e39067c5ddcfcd78" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "testingDepartmentCount" int CONSTRAINT "DF_c350b83bce6b860432d6edc939a" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "marketingSalesDepartmentCount" int CONSTRAINT "DF_6457ca4f02651912e81caf8f8ea" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "technicalDepartmentCount" int CONSTRAINT "DF_207085fd47da77f9b5f3a39ee68" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "avgWeeklyWorkingHours" int CONSTRAINT "DF_f1ed1fbd03bd81473bd29527264" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "fiscalYearRangeFrom" int CONSTRAINT "DF_35275c5c968859fad1cfe19d57d" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "fiscalYearRangeTo" int CONSTRAINT "DF_0970309110eb2d251742f16bc6e" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "capitalCurrencyCode" nvarchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "assetsCurrencyCode" nvarchar(50)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "financialReportFile" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "hasResearchDept" bit CONSTRAINT "DF_a150954b35582daa772e77b9429" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "researchDeptMemberCount" int CONSTRAINT "DF_9f74aa494b0d9a2fe0bdb6c9f80" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD "researchAchievements" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD CONSTRAINT "FK_1bd8e213d76928537f9b4d4584b" FOREIGN KEY ("paymentMethodId") REFERENCES "payment_method"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD CONSTRAINT "FK_371ac8b246bc2186d82e84424f7" FOREIGN KEY ("paymentTermId") REFERENCES "payment_term"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_revenue" ADD CONSTRAINT "FK_686b9f98d30871d94f985dd1c64" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier_revenue" DROP CONSTRAINT "FK_686b9f98d30871d94f985dd1c64"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP CONSTRAINT "FK_371ac8b246bc2186d82e84424f7"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP CONSTRAINT "FK_1bd8e213d76928537f9b4d4584b"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "researchAchievements"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_9f74aa494b0d9a2fe0bdb6c9f80"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "researchDeptMemberCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_a150954b35582daa772e77b9429"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "hasResearchDept"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "financialReportFile"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "assetsCurrencyCode"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "capitalCurrencyCode"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_0970309110eb2d251742f16bc6e"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "fiscalYearRangeTo"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_35275c5c968859fad1cfe19d57d"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "fiscalYearRangeFrom"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_f1ed1fbd03bd81473bd29527264"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "avgWeeklyWorkingHours"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_207085fd47da77f9b5f3a39ee68"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "technicalDepartmentCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_6457ca4f02651912e81caf8f8ea"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "marketingSalesDepartmentCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_c350b83bce6b860432d6edc939a"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "testingDepartmentCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_9a6bc94f7a9e39067c5ddcfcd78"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "warrantyMaintenanceDepartmentCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_8df197568e1668af8d1e8274c30"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "qualityDepartmentCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_da0128ab84bb46f75e14293916e"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "productionDepartmentCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_76adbed8a2af4d0bab43e6b2b47"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "purchasingDepartmentCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_38e9db79ca33f1bef2816495a40"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "rndDepartmentCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "foreman"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "procurementManager"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "marketingManager"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "director"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_f0145a5b5ed6adb2a907020e467"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "totalEmpCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_346f09ee7ff21d60007f0641ee9"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "otherEmpCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_4612c387b4c5c2d867215405bde"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "indirectEmpCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "DF_94668c1633eaef56a8f97591aea"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "directEmpCount"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "fileOrganization"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "mainBusinessArea"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "midLongTermGoals"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "visionAndMission"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "bizType"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "traderNote"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "traderEmail"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "traderFax"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "traderPhone"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "traderPosition"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "trader"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "deciderNote"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "deciderEmail"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "deciderFax"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "deciderPhone"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "deciderPosition"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "decider"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "paymentTermId"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "paymentMethodId"`);
        await queryRunner.query(`DROP TABLE "supplier_revenue"`);
    }

}
