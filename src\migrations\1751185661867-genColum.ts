import { MigrationInterface, QueryRunner } from "typeorm";

export class GenColum1751185661867 implements MigrationInterface {
    name = 'GenColum1751185661867'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "dataPrAllocation" nvarchar(max)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "dataPrAllocation"`);
    }

}
