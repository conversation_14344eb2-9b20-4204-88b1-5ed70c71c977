import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeMigra1748514120041 implements MigrationInterface {
  name = 'ChangeMigra1748514120041'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "name" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "subtotal" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "accountKey" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "acctKeyAccruals" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "required" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "relevantForAccountDetermination" varchar(50)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "relevantForAccountDetermination" varchar(50) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "required" varchar(50) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "acctKeyAccruals" varchar(50) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "accountKey" varchar(50) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "subtotal" varchar(50) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "master_condition_type" ALTER COLUMN "name" varchar(50) NOT NULL`)
  }
}
