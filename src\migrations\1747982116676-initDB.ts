import { MigrationInterface, QueryRunner } from "typeorm";

export class InitDB1747982116676 implements MigrationInterface {
    name = 'InitDB1747982116676'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "service_capacity_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3409baede5c741bc46a05a3bdfa" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c8b9ebb7152e5fdc34030eb514e" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "code" varchar(250), "value" int NOT NULL, "serviceCapacityId" uniqueidentifier NOT NULL, CONSTRAINT "PK_3409baede5c741bc46a05a3bdfa" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_expertise_year_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f8c8a4da29985e7ffe908b48c36" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_17c130fded863129ebe5cfe5ecf" DEFAULT 0, "companyId" varchar(255), "value" nvarchar(255) NOT NULL, "year" nvarchar(255) NOT NULL, "supplierExpertiseDetailId" uniqueidentifier NOT NULL, CONSTRAINT "PK_f8c8a4da29985e7ffe908b48c36" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_expertise_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a559d310ad0eb30560f5f0f3c8e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_dfa8a76529cf6c6ba561a4f9fc7" DEFAULT 0, "companyId" varchar(255), "comment" nvarchar(max), "sort" int NOT NULL CONSTRAINT "DF_a7f8a152382a4d7abea197b0722" DEFAULT 0, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_e71134318733a5946b90cb5e307" DEFAULT 'string', "value" varchar(250), "supplierCapacityId" uniqueidentifier NOT NULL, "supplierExpertiseId" uniqueidentifier NOT NULL, CONSTRAINT "PK_a559d310ad0eb30560f5f0f3c8e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "item_tech_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3470af44f2225ba3c5e3b59795d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_107e4c252b3d61a53328023b4fa" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "value" int NOT NULL, "itemTechId" uniqueidentifier NOT NULL, CONSTRAINT "PK_3470af44f2225ba3c5e3b59795d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "item_tech" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_2b99df052f9721f308bbd807346" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a0403b9b516f841331c0fc22f8a" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_481294e7f175558bce225580020" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_0ee345f83ed3deb34adb14fb3e8" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_6e489fc409ccafc29e1260e9c43" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_48f0c6c6789b410e8e6750385a4" DEFAULT 'string', "percent" float CONSTRAINT "DF_d121d2f23d48182aa589b6d5c0e" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_95bd5e8fafb07288c8020488f68" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "isHighlight" bit NOT NULL CONSTRAINT "DF_76ae701304a4b3a71d549dfe2db" DEFAULT 0, "hightlightValue" int, "prItemId" uniqueidentifier NOT NULL, CONSTRAINT "PK_2b99df052f9721f308bbd807346" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "pr_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f32da0d99e5830733775b3e241d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_bd3bdcc6550eeb09816dc7cc27d" DEFAULT 0, "companyId" varchar(255), "createdByName" varchar(250) NOT NULL, "prId" uniqueidentifier NOT NULL, "status" varchar(150), "description" varchar(500), CONSTRAINT "PK_f32da0d99e5830733775b3e241d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract_appendix_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5b216f9f85f5901b84317974940" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_0b8c0d4259f52c7032c40d6cb57" DEFAULT 0, "companyId" varchar(255), "type" varchar(50) NOT NULL, "quantity" int CONSTRAINT "DF_109ab3e82144e10b09bad1b1dcd" DEFAULT 0, "price" int, "totalPrice" bigint, "contractAppendixId" uniqueidentifier NOT NULL, CONSTRAINT "PK_5b216f9f85f5901b84317974940" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract_appendix_payment_progress" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_4f3d95d226d01ef9a468780ef91" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6d745f968aed33df7b7cdc77be7" DEFAULT 0, "companyId" varchar(255), "type" varchar(50) NOT NULL, "time" datetime, "newTime" datetime, "contractAppendixId" uniqueidentifier NOT NULL, CONSTRAINT "PK_4f3d95d226d01ef9a468780ef91" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract_appendix" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ae8f9a7f3291b0e6dd63cb89b4b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_0eb4bd730408b9833b47faac3df" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(250) NOT NULL, "title" varchar(250) NOT NULL, "type" varchar(50) NOT NULL, "contractId" uniqueidentifier NOT NULL, "fileAttach" varchar(250), "effectiveDate" datetime, "expiredDate" datetime, "description" varchar(250), "effectiveAppendixDateOld" datetime, "effectiveAppendixDateNew" datetime, "expiredAppendixDateOld" datetime, "expiredAppendixDateNew" datetime, "fileAttachChange" varchar(250), "descriptionChange" varchar(250), "status" varchar(50), "isEffContract" bit NOT NULL CONSTRAINT "DF_fcda78c9754659e34f09dd6b927" DEFAULT 0, "isPaymentProgress" bit NOT NULL CONSTRAINT "DF_5305867920f627bf7082fb5ff7a" DEFAULT 0, CONSTRAINT "PK_ae8f9a7f3291b0e6dd63cb89b4b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_237093a8ac0e3a8c7b1ea04651e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_52c323b296fc9f337269c212607" DEFAULT 0, "companyId" varchar(255), "statusCurrent" varchar(150), "statusConvert" varchar(150), "contractId" uniqueidentifier NOT NULL, "employeeId" uniqueidentifier, "description" varchar(500), CONSTRAINT "PK_237093a8ac0e3a8c7b1ea04651e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b3784a178e1895f0896f7d75421" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_595e0aab93042a3c80fc4f22c94" DEFAULT 0, "companyId" varchar(255), "statusCurrent" varchar(150), "statusConvert" varchar(150), "poId" uniqueidentifier NOT NULL, "employeeId" uniqueidentifier, "supplierId" uniqueidentifier, "description" varchar(500), CONSTRAINT "PK_b3784a178e1895f0896f7d75421" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_member" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c169285fe3ae3329bce7aea59a7" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ca2e79c98831c246697d4e07ae4" DEFAULT 0, "companyId" varchar(255), "employeeId" uniqueidentifier, "poId" uniqueidentifier, "poRoleCode" varchar(50), "description" varchar(250), CONSTRAINT "PK_c169285fe3ae3329bce7aea59a7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "auction_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3a4460a6dfd20f0677fa40cee10" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e66fc4273838a52b6652177b5bd" DEFAULT 0, "companyId" varchar(255), "auctionId" uniqueidentifier NOT NULL, "description" varchar(250), CONSTRAINT "PK_3a4460a6dfd20f0677fa40cee10" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_supplier_service" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_530277c7d635e1f78d338acd3ec" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_20ddfff137306716f91b4483bd1" DEFAULT 0, "companyId" varchar(255), "offerSupplierId" uniqueidentifier, "offerServiceId" uniqueidentifier, CONSTRAINT "PK_530277c7d635e1f78d338acd3ec" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_tech_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_510e38995cd261c3309d1f191de" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b62cce3b9583022aec0ad5623b7" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "value" int NOT NULL, "bidTechId" uniqueidentifier NOT NULL, CONSTRAINT "PK_510e38995cd261c3309d1f191de" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_tech" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_7f09531c5a53a68a9056024c48f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_5cde9476e8d59ed5456134aad58" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_ee77b7aee0e91407cf5406f33e3" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_4e7113ae5e791a1165ea9856a0a" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_1c9c19eb3b06c24a310ee2e2818" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_aff209756d5410006ff8bfec2d2" DEFAULT 'string', "percent" float CONSTRAINT "DF_4eb15be7f51d3792fc8cfeb39f9" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_d950d70fdd693a02e5997847fd5" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "isHighlight" bit NOT NULL CONSTRAINT "DF_38858520d4408832ec2c7c9755e" DEFAULT 0, "hightlightValue" int, "offerId" uniqueidentifier, "offerServiceId" uniqueidentifier, CONSTRAINT "PK_7f09531c5a53a68a9056024c48f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_supplier_tech_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3a48455db0e0af167d2e9e43777" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_29a710faa33a8244459211db7fc" DEFAULT 0, "companyId" varchar(255), "score" float, "value" varchar(250), "offerSupplierId" uniqueidentifier, "offerTechId" uniqueidentifier, CONSTRAINT "PK_3a48455db0e0af167d2e9e43777" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_trade_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_70c2b939c4bb335360a867b113f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_8f098d52ea0b2be18aed006fe8a" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "value" int NOT NULL, "offerTradeId" uniqueidentifier NOT NULL, CONSTRAINT "PK_70c2b939c4bb335360a867b113f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_trade" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_865f65b90d95d3ee28437bb1911" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f6ecca6cc45a1bc66f2ae3f7dc1" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_acc93c56d4bf4540b694d3f924c" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_9fdebe136a3e2965654283f206f" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_3784f25dd5d42e934a401046320" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_dd80f70c7722815ff8c2b46a64b" DEFAULT 'string', "percent" float CONSTRAINT "DF_14618a2f77555f57d225b09c079" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_f84b364489ad3af3800b965e929" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "offerId" uniqueidentifier, "offerServiceId" uniqueidentifier, CONSTRAINT "PK_865f65b90d95d3ee28437bb1911" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_supplier_trade_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_34d154a0ddb8ba03dd634006de6" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c41099234944406420d0133dfc7" DEFAULT 0, "companyId" varchar(255), "score" float, "value" varchar(250), "offerSupplierId" uniqueidentifier, "offerTradeId" uniqueidentifier, CONSTRAINT "PK_34d154a0ddb8ba03dd634006de6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_supplier_price_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3c9dbed2ff7c9c75a1db8a56780" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6073433a08714928f847c4201a4" DEFAULT 0, "companyId" varchar(255), "offerSupplierId" uniqueidentifier, "offerPriceId" uniqueidentifier, "offerPriceName" varchar(250), "offerPriceLevel" int CONSTRAINT "DF_5fd9d24eccaca92808158f50980" DEFAULT 1, "offerId" varchar(255), "unit" varchar(255), "currency" varchar(255), "name" varchar(250), "serviceId" varchar(255), "supplierId" varchar(255), "submitDate" datetime, "submitType" int, "number" int, "unitPrice" bigint, "score" float, "value" varchar(250), "price" bigint, CONSTRAINT "PK_3c9dbed2ff7c9c75a1db8a56780" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_price_col_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_88df2eccf1e2298d93102813a42" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_fcf3add1476b307ba94e72f68b9" DEFAULT 0, "companyId" varchar(255), "value" varchar(250) NOT NULL, "offerPriceId" uniqueidentifier NOT NULL, "offerPriceColId" uniqueidentifier NOT NULL, CONSTRAINT "PK_88df2eccf1e2298d93102813a42" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_price_col" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_84aec959ab6998ce2bde520bd8c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_212392989c09200615094626bb6" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "fomular" nvarchar(max), "isRequired" bit NOT NULL CONSTRAINT "DF_3ddeb1ff886b66aa869b54bea7a" DEFAULT 0, "sort" int NOT NULL CONSTRAINT "DF_c6c7cd87f4c874667058ad44d8f" DEFAULT 0, "name" varchar(250) NOT NULL, "description" nvarchar(max), "type" nvarchar(50) NOT NULL, "colType" nvarchar(50) NOT NULL, "offerId" uniqueidentifier, "offerServiceId" uniqueidentifier, CONSTRAINT "PK_84aec959ab6998ce2bde520bd8c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_supplier_price_col_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_dc8b6c9794aff9d4defe275023b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_44e6bdf4f1725b3f5494c4ef51b" DEFAULT 0, "companyId" varchar(255), "value" varchar(250), "offerSupplierId" uniqueidentifier, "offerPriceId" uniqueidentifier, "offerPriceColId" uniqueidentifier, CONSTRAINT "PK_dc8b6c9794aff9d4defe275023b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_supplier_custom_price_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_97cd234f412b8e7eb9643e377fe" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_59cc202fca35474ebcaddbff1ac" DEFAULT 0, "companyId" varchar(255), "unit" varchar(255), "currency" varchar(255), "number" int CONSTRAINT "DF_45d65cb8060121aefc8a9fdac01" DEFAULT 0, "sort" int CONSTRAINT "DF_5e0259e49e6b02a81e7025ee96b" DEFAULT 0, "name" varchar(250), "value" varchar(250), "offerSupplierId" uniqueidentifier, CONSTRAINT "PK_97cd234f412b8e7eb9643e377fe" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shipment_cost_stage" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_e1fb619c4306d39fa678e0c5a85" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_fa75628d38c81fa725aeecdb416" DEFAULT 0, "companyId" varchar(255), "jsonState" varchar(max), "shipmentCostId" uniqueidentifier, "referenceSource" varchar(100), CONSTRAINT "PK_e1fb619c4306d39fa678e0c5a85" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shipment_cost_stage_cost" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_bb434a1ef43b5288cb3f5b21dad" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ac568e0b3c81b607b416118bd83" DEFAULT 0, "companyId" varchar(255), "conditionType" varchar(150), "description" varchar(4000), "amount" int, "crcy" varchar(150), "per" int, "conditionValue" int, "curr" varchar(150), "cConDe" int, "numCCo" int, "shipmentCostId" uniqueidentifier, "shipmentCostStageId" uniqueidentifier, "isCheck" bit NOT NULL CONSTRAINT "DF_839969a806e916fa6b8320c53a0" DEFAULT 0, "priceReference" int, "shipmentCostPriceId" uniqueidentifier, "isUsePrice" bit NOT NULL CONSTRAINT "DF_1c65affc4d4867c2edbcb68f9d2" DEFAULT 0, CONSTRAINT "PK_bb434a1ef43b5288cb3f5b21dad" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_68c5f86ef5a45aaf9c605cf1e7" ON "shipment_cost_stage_cost" ("shipmentCostPriceId") WHERE "shipmentCostPriceId" IS NOT NULL`);
        await queryRunner.query(`CREATE TABLE "bid_shipment_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_6dddc1efa2ccd615d684f26c382" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ee75d617411926a0bd4ad54cd6b" DEFAULT 0, "companyId" varchar(255), "bidId" uniqueidentifier, "shipmentPriceId" uniqueidentifier, "value" int NOT NULL, CONSTRAINT "PK_6dddc1efa2ccd615d684f26c382" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shipment_cost_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_249dc4b16a5512339c6ba21462e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_51491eb718016574fdf16c26e18" DEFAULT 0, "companyId" varchar(255), "conditionType" varchar(150), "description" varchar(4000), "amount" int, "crcy" varchar(150), "per" int, "conditionValue" int, "curr" varchar(150), "cConDe" int, "numCCo" int, "shipmentCostId" uniqueidentifier NOT NULL, "shipmentCostStageCostId" uniqueidentifier, CONSTRAINT "PK_249dc4b16a5512339c6ba21462e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_6c9750056dbad47e31b34c0580" ON "shipment_cost_price" ("shipmentCostStageCostId") WHERE "shipmentCostStageCostId" IS NOT NULL`);
        await queryRunner.query(`CREATE TABLE "offer_supplier_shipment_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cf6010339cc290d8524450f0742" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_34e7e2e4a734f2cac23e7c45d1f" DEFAULT 0, "companyId" varchar(255), "score" float, "value" varchar(250), "offerSupplierId" uniqueidentifier NOT NULL, "shipmentPriceId" uniqueidentifier, CONSTRAINT "PK_cf6010339cc290d8524450f0742" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shipment_stage" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_4928d40252704d8014f44414306" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e8b173d052c38deb5443f8233c0" DEFAULT 0, "companyId" varchar(255), "jsonState" varchar(max), "shipmentId" uniqueidentifier, "referenceSource" varchar(100), "bidId" uniqueidentifier, "bidSupplierId" uniqueidentifier, "offerId" uniqueidentifier, "offerSupplierId" uniqueidentifier, CONSTRAINT "PK_4928d40252704d8014f44414306" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a0e7d03ab2657ec511a51620994" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_dc42cd1ad4497ccebd0df18430b" DEFAULT 0, "companyId" varchar(255), "offerId" uniqueidentifier, "isSuccessBid" bit CONSTRAINT "DF_38f00cb27965e274638e4a24d85" DEFAULT 0, "supplierId" uniqueidentifier, "scoreTech" float NOT NULL CONSTRAINT "DF_cda3a29a2a07e0d0db49f88f84c" DEFAULT 0, "scorePrice" float NOT NULL CONSTRAINT "DF_c7b763378377c67efa0481d513e" DEFAULT 0, "scoreTrade" float NOT NULL CONSTRAINT "DF_9b04560857d5d2653bc4206a183" DEFAULT 0, "scoreManualTech" float NOT NULL CONSTRAINT "DF_1b65d4c993fa240d0737a34eebe" DEFAULT 0, "scoreManualPrice" float NOT NULL CONSTRAINT "DF_2fe2adcf4fa42d378e5362538ed" DEFAULT 0, "scoreManualTrade" float NOT NULL CONSTRAINT "DF_36f8e0c95b11506986e2456a4bd" DEFAULT 0, "supplierCode" varchar(50), "supplierName" varchar(250), "parentId" uniqueidentifier, "statusFile" varchar(50), "statusTech" varchar(50), "statusTrade" varchar(50), "isPriceValid" bit NOT NULL CONSTRAINT "DF_84ad418cf1cd15789bfd56e0124" DEFAULT 1, "isJoin" bit CONSTRAINT "DF_4d83b25d706d7712ad4bd5571af" DEFAULT 0, "statusResetPrice" varchar(50) NOT NULL CONSTRAINT "DF_4ca5427db32b1a1d7c3530eef6f" DEFAULT 'KhongYeuCau', "noteMPOLeader" nvarchar(max), "noteTrade" nvarchar(max), "isTradeValid" bit NOT NULL CONSTRAINT "DF_813a1fb6ada222aa4a021ba0119" DEFAULT 1, "status" varchar(50), "notePrice" nvarchar(max), "statusPrice" varchar(50), "fileAttach" varchar(250), "fileTech" varchar(250), "filePrice" varchar(250), "linkDrive" varchar(400), "note" nvarchar(max), CONSTRAINT "PK_a0e7d03ab2657ec511a51620994" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_supplier_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5e72ee060286ec8b04b59858997" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b8564a781dc1f6688ddd471c82f" DEFAULT 0, "companyId" varchar(255), "offerSupplierId" uniqueidentifier, "offerPriceId" uniqueidentifier, "offerPriceName" varchar(250), "offerPriceLevel" int CONSTRAINT "DF_a8f33cfdab850e8690a0fe65c06" DEFAULT 1, "offerId" varchar(255), "serviceId" varchar(255), "supplierId" varchar(255), "submitDate" datetime, "submitType" int, "number" int, "unitPrice" bigint, "price" bigint, CONSTRAINT "PK_5e72ee060286ec8b04b59858997" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_price_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0d672f736307e2c4c2611895101" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_fc3ad54298991368909217f1a9a" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "description" nvarchar(max), "type" nvarchar(50) NOT NULL, "value" varchar(250), "offerPriceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_0d672f736307e2c4c2611895101" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_deal_supplier_price_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_03e768c76df5ad55545912cc713" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f6ef118e13ffc9761c45df2cf6b" DEFAULT 0, "companyId" varchar(255), "score" float, "value" varchar(250), "offerDealSupplierId" uniqueidentifier NOT NULL, "offerPriceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_03e768c76df5ad55545912cc713" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_deal_supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_8b70709c5a376bcdf7563db34a2" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_972ee713bbc26cabae3c09e4d30" DEFAULT 0, "companyId" varchar(255), "offerDealId" uniqueidentifier NOT NULL, "score" float NOT NULL CONSTRAINT "DF_547a3bea7585cc4ef4bcd14bf6e" DEFAULT 0, "supplierId" uniqueidentifier NOT NULL, "status" varchar(50) NOT NULL, "filePriceDetail" varchar(500), "fileTechDetail" varchar(500), "linkDriver" varchar(500), "submitDate" datetime, CONSTRAINT "PK_8b70709c5a376bcdf7563db34a2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_deal" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_fff860807c0488895787c39a6bb" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b540217a1c845f3ea74bb4ebaf4" DEFAULT 0, "companyId" varchar(255), "status" varchar(50) NOT NULL, "endDate" datetime NOT NULL, "isSendDealPrice" bit NOT NULL CONSTRAINT "DF_c79943d63fd9d057988faf6d6ed" DEFAULT 0, "offerId" uniqueidentifier NOT NULL, "parentId" uniqueidentifier, "isRequireFilePriceDetail" bit CONSTRAINT "DF_f3e892623b317c3e5a58f7930b2" DEFAULT 0, "isRequireFileTechDetail" bit CONSTRAINT "DF_f6a65740978faac977c4627aa7b" DEFAULT 0, CONSTRAINT "PK_fff860807c0488895787c39a6bb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_deal_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f629943baab37700dec31c93e58" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6af0409298d466338a49ca35a5c" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_13f58697c23dad31e6b50853a8e" DEFAULT 0, "number" int NOT NULL CONSTRAINT "DF_965a50bb0aef6881ee0c2539b7a" DEFAULT 0, "bestPrice" float, "bestPriceHistory" float, "bestPriceCurrent" float, "suggestPrice" float, "maxPrice" float, "offerDealId" uniqueidentifier NOT NULL, "offerPriceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_f629943baab37700dec31c93e58" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_572bbfedc194d92c4eb111cfd15" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ea7f1e21704f8e14d913dae1266" DEFAULT 0, "companyId" varchar(255), "code" varchar(50), "fomular" nvarchar(max), "isRequired" bit CONSTRAINT "DF_177d05e2ec4c75db22b967ee1c7" DEFAULT 0, "sort" int CONSTRAINT "DF_4158dbb9dcc448e8236bae395f6" DEFAULT 0, "name" varchar(250), "description" nvarchar(max), "unit" varchar(255), "currency" varchar(255), "percent" float CONSTRAINT "DF_acab2d66a3c786310bbbaa0fc66" DEFAULT 100, "level" int CONSTRAINT "DF_b40503944c4471b3046025df98b" DEFAULT 1, "parentId" uniqueidentifier, "type" nvarchar(50), "colType" nvarchar(50), "offerId" uniqueidentifier, "offerServiceId" uniqueidentifier, "offerItemId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "number" int CONSTRAINT "DF_8f37f1c1d637c5091c6200df9b0" DEFAULT 0, "isSetup" bit CONSTRAINT "DF_33a9257fc93460c73811177aeb6" DEFAULT 0, "isTemplate" bit CONSTRAINT "DF_d8a77add78ec0ef399b4c2db277" DEFAULT 1, CONSTRAINT "PK_572bbfedc194d92c4eb111cfd15" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "material_group" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_341d1f2225a855963b943627cf8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ceb0b5fa03eb1c7313535658535" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "name2" nvarchar(500), "code" varchar(50) NOT NULL, "description" nvarchar(max), "externalMaterialGroupId" uniqueidentifier, CONSTRAINT "PK_341d1f2225a855963b943627cf8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_scene_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c852aac044ae117047a7d9fd65c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_939c2f99bc2aab117aa8193709f" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "value" int NOT NULL, "serviceSceneId" uniqueidentifier NOT NULL, CONSTRAINT "PK_c852aac044ae117047a7d9fd65c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_scene" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_771b48599173e44a902ac36caa0" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_83131d1fcb8bbc537a17cddf6aa" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_0bef0705fd8bf8dea7740304c47" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_36a2270e8360502d51ec190368f" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_17675cf347c4a850e439b12057d" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_448c6b82ea92a6b2b62429995bd" DEFAULT 'string', "percent" float CONSTRAINT "DF_b2d2563f765115c21db5a2298f3" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_7cff858482b7ebd2d670b7d8d73" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "isHighlight" bit NOT NULL CONSTRAINT "DF_031931b600c4f5184096f159d78" DEFAULT 0, "hightlightValue" int, "serviceId" uniqueidentifier, "exMatGroupId" uniqueidentifier, CONSTRAINT "PK_771b48599173e44a902ac36caa0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_purchase_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_fb61485b13a5b6f63ba66eb4bb5" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_dcb0666e91ab6dae7c910b5241b" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_8a4ba7b2964ae8771b153826171" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_a11061581ad0aeb488acb902485" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_56b4ca506c93f27b645afd6f2aa" DEFAULT 1, "percent" float CONSTRAINT "DF_9ceed1e60f49128ec0fb1c37964" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_946f908ef49aeb2df2ae4dee102" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "isHighlight" bit NOT NULL CONSTRAINT "DF_64cc21fbec070376d51f1340b42" DEFAULT 0, "hightlightValue" int, "serviceId" uniqueidentifier, "exMatGroupId" uniqueidentifier, CONSTRAINT "PK_fb61485b13a5b6f63ba66eb4bb5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "round_up_cont_col_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_074fea38f432acd1cd90d61d08c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_5566c7a30b259722fd56a6d94d9" DEFAULT 0, "companyId" varchar(255), "value" varchar(250), "prId" uniqueidentifier, "materialId" uniqueidentifier, "roundUpContId" uniqueidentifier, "roundUpContTemplateId" uniqueidentifier, "roundUpContTemplateColId" uniqueidentifier, "quantity" decimal(20,2) CONSTRAINT "DF_cea5e2d78a2ef0f353811c7d586" DEFAULT 0, CONSTRAINT "PK_074fea38f432acd1cd90d61d08c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract_inspection_employee" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_430e2dca3e2b3f68db2dbd4deaf" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6548fdb4c99d2da4173cf3e7746" DEFAULT 0, "companyId" varchar(255), "employeeId" uniqueidentifier NOT NULL, "contractInspectionId" uniqueidentifier NOT NULL, CONSTRAINT "PK_430e2dca3e2b3f68db2dbd4deaf" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract_inspection_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_315e1366d28e1737589aed24f4f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_0cdff31236e16fc1420aebe17a4" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(250) NOT NULL, "materialId" uniqueidentifier NOT NULL, "shortText" varchar(250) NOT NULL, "quantity" int CONSTRAINT "DF_d4482ad01713d6b0ac607b840e1" DEFAULT 0, "inspectionQuantity" int CONSTRAINT "DF_4ae5a81c0e26bd07b0a08ba513e" DEFAULT 0, "unitId" uniqueidentifier, "price" int NOT NULL, "totalPrice" bigint, "contractInspectionId" uniqueidentifier NOT NULL, CONSTRAINT "PK_315e1366d28e1737589aed24f4f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract_inspection" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_118dbd1bbfe8572a259eb5b4027" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_49cbbc5f8f5ca7fb5f17ae89055" DEFAULT 0, "companyId" varchar(255), "code" varchar(100) NOT NULL, "contractId" uniqueidentifier, "supplierId" uniqueidentifier, "address" varchar(300), "time" datetime, "status" varchar(50) NOT NULL, "note" varchar(250), "object" varchar(250), "resultInspection" varchar(250), "fileAttach" varchar(250), "inspectionType" varchar(10), CONSTRAINT "PK_118dbd1bbfe8572a259eb5b4027" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract_inspection_payment_progress" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_11226adf5b99dfdeafa04419b4d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_60a2cf6482e39c6ae5106134ef7" DEFAULT 0, "companyId" varchar(255), "code" varchar(100) NOT NULL, "name" varchar(250) NOT NULL, "percent" int NOT NULL CONSTRAINT "DF_ee6ef2e9d8d7430e5a5a4c78de1" DEFAULT 0, "money" float CONSTRAINT "DF_cffb1d985532677dab625b96ac1" DEFAULT 0, "paymentMethodId" uniqueidentifier, "requiredDocument" varchar(250), "oldTime" datetime, "newTime" datetime, "isChoose" bit CONSTRAINT "DF_df0e026a1330889127b3e33dd16" DEFAULT 0, "contractInspectionId" uniqueidentifier, CONSTRAINT "PK_11226adf5b99dfdeafa04419b4d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "title" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_30e6ea2dcc2aae4a4d1f5d9e183" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7464d168bf634461d8f55a0d6e6" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(4000), CONSTRAINT "PK_30e6ea2dcc2aae4a4d1f5d9e183" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "stakeholder_category" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_69db6d01a4dd297811924244309" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_aa0046948b16a4b2ff0518ab943" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(4000), CONSTRAINT "PK_69db6d01a4dd297811924244309" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "industry_standard" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_88943d0b66b2f21b2124e44cdb3" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_8d0d53da5b273f2a0628088ba3c" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(4000), CONSTRAINT "PK_88943d0b66b2f21b2124e44cdb3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "gl_account" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_d9b20bd909d62227a43f5aa40cf" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_eabea01a8dcb6e10db429228b95" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(max), CONSTRAINT "PK_d9b20bd909d62227a43f5aa40cf" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_number_request_approve" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_936bcb7a655f643a88fd6e8004a" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_13eac8319544536b441f43efdaa" DEFAULT 0, "companyId" uniqueidentifier, "shortName" nvarchar(50), "searchTerm" nvarchar(50), "supplierNumberId" uniqueidentifier, "titleId" uniqueidentifier, "stakeholderCategoryId" uniqueidentifier, "industryStandardId" uniqueidentifier, "businessPartnerGroupId" uniqueidentifier, "purchasingOrgId" uniqueidentifier, "purchasingGroupId" uniqueidentifier, "currencyId" uniqueidentifier, "incotermId" uniqueidentifier, "paymentTermId" uniqueidentifier, "paymentMethodId" uniqueidentifier, "planningGroupId" uniqueidentifier, "glAccountId" uniqueidentifier, CONSTRAINT "PK_936bcb7a655f643a88fd6e8004a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_765f50e4842172a1b7787f5bc3" ON "supplier_number_request_approve" ("supplierNumberId") WHERE "supplierNumberId" IS NOT NULL`);
        await queryRunner.query(`CREATE TABLE "employee_purchasing_group" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_540b029b7803e537ae89b526337" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_d3630d31474ddccb227c58ed923" DEFAULT 0, "companyId" varchar(255), "employeeId" uniqueidentifier, "purchasingGroupId" uniqueidentifier, "description" varchar(250), CONSTRAINT "PK_540b029b7803e537ae89b526337" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "purchasing_group" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_56063d6eb7ac67ca31fa1ffb844" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7998599a507f3f39ff3c84902e6" DEFAULT 0, "companyId" uniqueidentifier, "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(max), "purchasingOrgId" uniqueidentifier, CONSTRAINT "PK_56063d6eb7ac67ca31fa1ffb844" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "complaint_department" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_7b1dd465c62ba742a427a65a09d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c92b581cd7aaf1c071524447e7f" DEFAULT 0, "companyId" varchar(255), "departmentId" uniqueidentifier NOT NULL, "complaintId" uniqueidentifier NOT NULL, CONSTRAINT "PK_7b1dd465c62ba742a427a65a09d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3b581d411ba5feb5194d0c4b4cf" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_83b37b2889d0fb41173acc53be5" DEFAULT 0, "companyId" varchar(255), "name" varchar(250), "materialId" uniqueidentifier, "shortText" varchar(250), "unitId" uniqueidentifier, "quantity" int CONSTRAINT "DF_a2713b7daa6502081da22774a48" DEFAULT 0, "restQuantity" int CONSTRAINT "DF_5cb78fa4865c116e2ba9d09a50a" DEFAULT 0, "contractQuantity" int CONSTRAINT "DF_151906535fc7ff7ca45e1c8aea9" DEFAULT 0, "price" int, "totalPrice" bigint, "itemCode" varchar(250), "contractId" uniqueidentifier, "unitName" nvarchar(255), "prId" varchar(255), "prItemId" varchar(255), "recommendedPurchaseId" varchar(255), "recommendedPurchasePrId" varchar(255), CONSTRAINT "PK_3b581d411ba5feb5194d0c4b4cf" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "complaint_line_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_edf8faae80d43d0a4d03261c58e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6e8c41873244abcf91a9b9f9705" DEFAULT 0, "companyId" varchar(255), "poItemId" uniqueidentifier, "contractItemId" uniqueidentifier, "complaintId" uniqueidentifier NOT NULL, CONSTRAINT "PK_edf8faae80d43d0a4d03261c58e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "complaint_fix" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_e4821074ee9dea62e6a58147d57" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_0d455032af436d7c2fe1b91778e" DEFAULT 0, "companyId" varchar(255), "complaintId" uniqueidentifier NOT NULL, "complaintItemId" uniqueidentifier, "codeGroup" varchar(50), "taskGroup" varchar(50), "taskCodeText" varchar(250), "taskText" varchar(250), "employeeUser" varchar(250), "startDate" datetime, "endDate" datetime, CONSTRAINT "PK_e4821074ee9dea62e6a58147d57" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "complaint_prevention" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_2cb95c6bf3af2b98ce1374155f5" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_50c5e417c948da661990c478cc5" DEFAULT 0, "companyId" varchar(255), "complaintId" uniqueidentifier NOT NULL, "complaintItemId" uniqueidentifier, "activityCode" varchar(50), "activityGroup" varchar(50), "activityCodeText" varchar(250), "activityText" varchar(250), "employeeUser" varchar(250), "startDate" datetime, "endDate" datetime, CONSTRAINT "PK_2cb95c6bf3af2b98ce1374155f5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "complaint_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_49f86ef70f35e4ce0860e836ca2" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a44e63f2421699f1c5bccf816a9" DEFAULT 0, "companyId" varchar(255), "complaintId" uniqueidentifier NOT NULL, "itemCode" varchar(250), "shortText" varchar(250), "codingType" varchar(30), "choiceType" varchar(30), "description" nvarchar(max), "note" nvarchar(max), "fileAttached" varchar(4000), CONSTRAINT "PK_49f86ef70f35e4ce0860e836ca2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "complaint_item_cargo" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_46914a5fa8bf42ad780604b8102" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3b7eeec1aa3dfde5e14fb0de39e" DEFAULT 0, "companyId" varchar(255), "complaintId" uniqueidentifier NOT NULL, "itemCode" varchar(250), "shortText" varchar(250), "unitName" varchar(30), "quantity" int, "quantityErrorInt" int, "quantityErrorExt" int, "degreeChange" int, "numberReturns" int, "priorityLevel" varchar(250), "supplierProductCode" varchar(250), "lotNumber" varchar(250), "supplierLotNumber" varchar(250), "serialNumber" varchar(250), "responseDepartmentId" uniqueidentifier, "receptEmployeeId" uniqueidentifier, CONSTRAINT "PK_46914a5fa8bf42ad780604b8102" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "complaint_employee" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_34ad8b95e532ee665d7a9a01589" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a213c80a009531a448166ba623a" DEFAULT 0, "companyId" varchar(255), "employeeId" uniqueidentifier NOT NULL, "complaintId" uniqueidentifier NOT NULL, CONSTRAINT "PK_34ad8b95e532ee665d7a9a01589" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "complaint_handling_plan" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3423bf7cf45018f865940aa4899" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_738484c59613571525fc840bf2a" DEFAULT 0, "companyId" varchar(255), "complaintId" uniqueidentifier NOT NULL, "title" varchar(250), "description" nvarchar(max), "attachedFile" varchar(4000), CONSTRAINT "PK_3423bf7cf45018f865940aa4899" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "complaint_notify" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_6f2ce3826fd471c6b945fa345c9" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_876ee4df54a7eec06a7462f5613" DEFAULT 0, "companyId" varchar(255), "isNew" bit NOT NULL CONSTRAINT "DF_f34729453817e4275d8be5c35aa" DEFAULT 0, "employeeId" uniqueidentifier, "supplierId" uniqueidentifier, "complaintId" uniqueidentifier, "complaintChatId" uniqueidentifier, "status" varchar(50), CONSTRAINT "PK_6f2ce3826fd471c6b945fa345c9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "complaint_chat" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_56cc46fe0036802b03cac679dbc" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_55f261176b7a7f86928ace1248d" DEFAULT 0, "companyId" varchar(255), "content" nvarchar(max), "attachedFile" varchar(4000), "senderId" uniqueidentifier, "receiverId" uniqueidentifier, "complaintId" uniqueidentifier, CONSTRAINT "PK_56cc46fe0036802b03cac679dbc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "complaint" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a9c8dbc2ab4988edcc2ff0a7337" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_44fe96898fafa30ec2c4253dd7c" DEFAULT 0, "companyId" varchar(255), "code" varchar(250) NOT NULL, "name" varchar(250) NOT NULL, "complaintType" varchar(30), "complaintReferenceType" varchar(250), "referenceSource" varchar(250), "status" varchar(50), "poId" uniqueidentifier, "contractId" uniqueidentifier, "warehouseReceipt" varchar(250), "plantId" uniqueidentifier, "warehouseDate" datetime, "qualityInspection" varchar(250), "organization" varchar(250), "purchasingOrgId" uniqueidentifier, "purchasingGroupId" uniqueidentifier, "createDate" datetime, "employeeId" uniqueidentifier, "supplierId" uniqueidentifier, "description" nvarchar(max), "isSendSupplier" bit CONSTRAINT "DF_45128a9cb57a43ab943185916e3" DEFAULT 0, "title" varchar(250), "note" nvarchar(max), "attachedFile" varchar(4000), "isConfirm" bit CONSTRAINT "DF_32ac3df3e4e596ca0222408d39e" DEFAULT 0, CONSTRAINT "PK_a9c8dbc2ab4988edcc2ff0a7337" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "plant_purchasing_org" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_79b5899c949a45d6f1eb5fc4108" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_82e15f31569e8e76db529784c5b" DEFAULT 0, "companyId" varchar(255), "plantId" uniqueidentifier NOT NULL, "purchasingOrgId" uniqueidentifier NOT NULL, CONSTRAINT "PK_79b5899c949a45d6f1eb5fc4108" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "pr_keep_budget" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f858d659ebc78e1fccdf15994e6" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_87a905b05a0d0b58927a85bc142" DEFAULT 0, "companyId" uniqueidentifier, "prId" uniqueidentifier, "prCode" varchar(255), "code" varchar(255), "proposeType" varchar(50), "documentDate" datetime, "postingDate" datetime, "documentType" varchar(255), "documentTypeName" nvarchar(max), "companyName" nvarchar(max), "documentText" nvarchar(max), "grandTotal" bigint CONSTRAINT "DF_1bc1205ad9f477ceff415ce9a5a" DEFAULT 0, "totalAmount" bigint CONSTRAINT "DF_b75591d6cb51cd9614b8ace72d9" DEFAULT 0, "text" nvarchar(max), "commitmentItem" nvarchar(max), "fundsCenter" nvarchar(max), "fund" nvarchar(max), "budgetPeriod" nvarchar(max), "fundArea" nvarchar(max), "currencyId" uniqueidentifier, "currency" varchar(250), CONSTRAINT "PK_f858d659ebc78e1fccdf15994e6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "master_condition_type" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_134ed0113a929393f352bfb238b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7ae2069476ed8e6225011495a20" DEFAULT 0, "companyId" varchar(255), "stepNumber" int, "counter" int, "code" varchar(50), "name" varchar(50) NOT NULL, "fromStep" int, "toStep" int, "manualOnly" bit CONSTRAINT "DF_6cd0b4a4c5cafa4d45e8e1caa18" DEFAULT 0, "requirement" int, "subtotal" varchar(50) NOT NULL, "statistical" bit CONSTRAINT "DF_2bf8bd8fc2f09bca4c78c3775fa" DEFAULT 0, "cndnAmount" int, "cndnBaseValue" int, "accountKey" varchar(50) NOT NULL, "acctKeyAccruals" varchar(50) NOT NULL, "printType" bit CONSTRAINT "DF_da877fce996a76b6315dc8c2770" DEFAULT 0, "required" varchar(50) NOT NULL, "relevantForAccountDetermination" varchar(50) NOT NULL, "description" varchar(250), "procedureId" uniqueidentifier, CONSTRAINT "PK_134ed0113a929393f352bfb238b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_product_price_list" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b508eb4c3d1766460de71e7d190" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3fdf55ec430c5e4804ade831024" DEFAULT 0, "companyId" varchar(255), "poId" uniqueidentifier, "procedureId" uniqueidentifier, "poProductId" uniqueidentifier, "poPriceListId" uniqueidentifier, "materialId" uniqueidentifier, "quantity" decimal(20,2) CONSTRAINT "DF_4964ede1453fb36190d990eba46" DEFAULT 0, "conditionTypeCode" varchar(250), "conditionTypeName" varchar(1000), "amountType" varchar(10), "amountFormula" nvarchar(max), "amountCode" nvarchar(max), "valueAmountFormula" varchar(250), "conditionValueType" varchar(10), "conditionValueFormula" nvarchar(max), "conditionValueCode" nvarchar(max), "valueConditionValueFormula" varchar(250), "per" nvarchar(max), "valuePer" varchar(250), "percent" varchar(10), "conditionUnit" nvarchar(max), "valueConditionUnit" varchar(250), "qtyCode" varchar(50), "valueQty" varchar(250), CONSTRAINT "PK_b508eb4c3d1766460de71e7d190" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "procedure" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9888785b528492e7539d96e3894" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_00e2166c687c00dc59b59309bc3" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(50) NOT NULL, "description" varchar(250), CONSTRAINT "PK_9888785b528492e7539d96e3894" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_price_list" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_767a5a95904467b64257ab4d6c2" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b8c523751cedc6a12f5252775e1" DEFAULT 0, "companyId" varchar(255), "procedureId" uniqueidentifier, "masterConditionTypeId" uniqueidentifier, "conditionTypeCode" varchar(250), "conditionTypeName" varchar(1000), "currencyId" uniqueidentifier, "amountType" varchar(10), "amountFormula" nvarchar(max), "amountCode" nvarchar(max), "conditionValueType" varchar(10), "conditionValueCode" nvarchar(max), "conditionValueFormula" nvarchar(max), "per" nvarchar(max), "percent" varchar(10), "conditionUnit" nvarchar(max), "srcType" varchar(10), "typeOf" varchar(10), "stepNumber" int, "counter" int, "fromStep" int, "toStep" int, "accountKey" varchar(50), "acctKeyAccruals" varchar(50), "cndnAmount" int, "qtyCode" varchar(50), CONSTRAINT "PK_767a5a95904467b64257ab4d6c2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bill_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9462a962e4171a3a08408d6b268" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_51dfce1337589e681134f6d4570" DEFAULT 0, "companyId" varchar(255), "createdByName" varchar(250) NOT NULL, "billId" uniqueidentifier NOT NULL, "status" varchar(150), "description" varchar(500), CONSTRAINT "PK_9462a962e4171a3a08408d6b268" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bill_lookup" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_455e5577fb354d56b24114e2217" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_bd4d543dc413472a885dd66f755" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(200), "abbreviation" varchar(100), "link" varchar(4000), "description" varchar(max), CONSTRAINT "PK_455e5577fb354d56b24114e2217" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bill" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_683b47912b8b30fe71d1fa22199" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_8450b6d3fb5fa317899008689d9" DEFAULT 0, "companyId" varchar(255), "code" varchar(100) NOT NULL, "description" varchar(max), "fileXml" varchar(max), "status" varchar(50), "paymentStatus" varchar(50), "supplierId" uniqueidentifier, "poId" uniqueidentifier, "contractId" uniqueidentifier, "invoiceValue" bigint, "totalInvoiceValue" float, "vat" bigint, "referencesInvoice" varchar(50), "currencyName" varchar(100), "bizziCompanyId" varchar(255), "fileAttach" varchar(max), "billLookupCode" varchar(100), "billLookupId" uniqueidentifier, CONSTRAINT "PK_683b47912b8b30fe71d1fa22199" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "payment_bill" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_bd6e1adc29607b591acd798995c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a4d72eed04aa8042de65ff8c400" DEFAULT 0, "companyId" varchar(255), "billId" uniqueidentifier NOT NULL, "paymentId" uniqueidentifier NOT NULL, CONSTRAINT "PK_bd6e1adc29607b591acd798995c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "payment_po" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c91adc45053eee03ce984026eea" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2d99717e36d90669f6ea3cc801f" DEFAULT 0, "companyId" varchar(255), "poId" uniqueidentifier NOT NULL, "paymentId" uniqueidentifier NOT NULL, CONSTRAINT "PK_c91adc45053eee03ce984026eea" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "payment_contract" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_4a85b9df866c49165311420908e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_14d1e8f7d59082e84d0f825cf14" DEFAULT 0, "companyId" varchar(255), "contractId" uniqueidentifier NOT NULL, "paymentId" uniqueidentifier NOT NULL, CONSTRAINT "PK_4a85b9df866c49165311420908e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "payment" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_fcaec7df5adf9cac408c686b2ab" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_43a2333864dad955594f92e93df" DEFAULT 0, "companyId" varchar(255), "status" varchar(50), "code" varchar(50) NOT NULL, "name" varchar(250), "fileAttach" varchar(4000), "filePaymentRequest" varchar(4000), "fileAcceptanceReport" varchar(4000), "note" nvarchar(max), "supplierId" uniqueidentifier, "currencyId" uniqueidentifier, "moneyAdvance" bigint CONSTRAINT "DF_987535ac6d4496d0c52c6f0d519" DEFAULT 0, "paymentType" varchar(10), "isSupplierCreate" bit, CONSTRAINT "PK_fcaec7df5adf9cac408c686b2ab" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "material_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c8b7d4ccbc0b0d2a33f1239d7d0" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_79f40a5e1852c37813b24478d1a" DEFAULT 0, "companyId" varchar(255), "materialId" uniqueidentifier NOT NULL, "plantId" uniqueidentifier NOT NULL, "materialCode" varchar(250), "plantCode" varchar(250), "price" float, "lastChange" datetime, "currencyCode" varchar(250), "currencyId" uniqueidentifier NOT NULL, "valuationClass" varchar(250), "priceControl" varchar(250), "valuationType" varchar(10), "mrpType" varchar(250), "abcIndicator" varchar(250), "priceUnit" float, CONSTRAINT "PK_c8b7d4ccbc0b0d2a33f1239d7d0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "currency" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3cda65c731a6264f0e444cc9b91" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_eb800ff3c641233f9fe906633e5" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(50) NOT NULL, "description" varchar(250), CONSTRAINT "PK_3cda65c731a6264f0e444cc9b91" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_list_price_po" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_6b9cee4f4aad4c94fb08f40c0b7" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_70444b00437e7fb7034fd080192" DEFAULT 0, "companyId" varchar(255), "supplierSchemaId" uniqueidentifier, "currencyId" uniqueidentifier NOT NULL, "supplierId" uniqueidentifier NOT NULL, CONSTRAINT "PK_6b9cee4f4aad4c94fb08f40c0b7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_schema" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_2eb09a1360838957c64f78f475d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_425b2986c3ea28193e1738438e6" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500), "code" varchar(250), "description" nvarchar(max), CONSTRAINT "PK_2eb09a1360838957c64f78f475d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "schemaConfig" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f987d71e0af857ba0b8ea49271e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3ec8cc3d38d37e2d0ebd1dc07c3" DEFAULT 0, "companyId" varchar(255), "purchasingOrgSchemaId" uniqueidentifier, "supplierSchemaId" uniqueidentifier, "procedureId" uniqueidentifier, "description" nvarchar(max), CONSTRAINT "PK_f987d71e0af857ba0b8ea49271e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "purchasing_org_schema" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0c786eb2e78dd3c73d76e3290f5" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_32b555027174eee260081229de8" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500), "code" varchar(250), "description" nvarchar(max), CONSTRAINT "PK_0c786eb2e78dd3c73d76e3290f5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "purchasing_org" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ffd1aba47f5e083c206972a89f0" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c2221bc81cd111d337a2c98b235" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(4000), "purchasingOrgSchemaId" uniqueidentifier, CONSTRAINT "PK_ffd1aba47f5e083c206972a89f0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "incoterm" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c95b5490b848a3fd8fc4c2bb5c1" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3a4aff5fcabe2e514709d1f1863" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(max), CONSTRAINT "PK_c95b5490b848a3fd8fc4c2bb5c1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "role_supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0a74082f6ef2db34b794a5b114c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a09fd7a69d6de0228f2d7436116" DEFAULT 0, "companyId" uniqueidentifier, "purchasingOrgId" uniqueidentifier, "purchasingGroupId" uniqueidentifier, "incotermId" uniqueidentifier, "currencyId" uniqueidentifier, "supplierPlantId" uniqueidentifier, CONSTRAINT "PK_0a74082f6ef2db34b794a5b114c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_plant" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_1afe257721bc372462af826670d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e7e9ad075a799aa4d7cb12060a0" DEFAULT 0, "companyId" varchar(255), "supplierId" uniqueidentifier NOT NULL, "plantId" uniqueidentifier NOT NULL, CONSTRAINT "PK_1afe257721bc372462af826670d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "payment_term" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_e06d6ccc9db17416919b5f46d6d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_1aa74ae65c879abd9e3c8c02549" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(max), CONSTRAINT "PK_e06d6ccc9db17416919b5f46d6d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "planning_group" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_1256861f09fd596acf1746e31ba" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_af3f912832e75c6aa975bcd5e93" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(max), CONSTRAINT "PK_1256861f09fd596acf1746e31ba" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "role_fi_supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9b71b312ec61b1cf9c4f59f4d10" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_87bdc2c071cbb391b2fa5c9d63f" DEFAULT 0, "companyId" varchar(255), "glAccountId" uniqueidentifier, "paymentTermId" uniqueidentifier, "paymentMethodId" uniqueidentifier, "planningGroupId" uniqueidentifier, "supplierPlantId" uniqueidentifier, CONSTRAINT "PK_9b71b312ec61b1cf9c4f59f4d10" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "payment_method" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_7744c2b2dd932c9cf42f2b9bc3a" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a439532f2ff9ce98472b369e09e" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(max), CONSTRAINT "PK_7744c2b2dd932c9cf42f2b9bc3a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "recommended_purchase_col_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_bbe703c2025ad512823e0540b2e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_18e5e04e63630729827f8b724b2" DEFAULT 0, "companyId" varchar(255), "value" varchar(250), "materialId" uniqueidentifier, "recommendedPurchaseId" uniqueidentifier, "recommendedPurchaseTemplateId" uniqueidentifier, "recommendedPurchaseTemplateColId" uniqueidentifier, "quantity" decimal(20,2) CONSTRAINT "DF_056ff7f9242ee1bf80814cc6e91" DEFAULT 0, CONSTRAINT "PK_bbe703c2025ad512823e0540b2e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "recommended_purchase_template_col" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3c1c685f83bd71f84ea5f6811d8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2ba2e02dc6dd2aa0d65188e1f3c" DEFAULT 0, "companyId" varchar(255), "code" varchar(50), "fomular" nvarchar(max), "isRequired" bit NOT NULL CONSTRAINT "DF_56e97a0501055be731e374564a1" DEFAULT 1, "isEdited" bit NOT NULL CONSTRAINT "DF_426b948302c8950c7c3d055f023" DEFAULT 1, "sort" int CONSTRAINT "DF_bf76a1f76ac670adba2e432f21f" DEFAULT 0, "name" varchar(250), "colType" nvarchar(50), "type" nvarchar(50), "isSystemData" bit CONSTRAINT "DF_3ed18e560df3c4b8142ed15b171" DEFAULT 0, "dataMapping" nvarchar(250), "recommendedPurchaseTemplateId" uniqueidentifier, "recommendedPurchaseTemplateCode" nvarchar(250), CONSTRAINT "PK_3c1c685f83bd71f84ea5f6811d8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "recommended_purchase_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5090e951cc769872cd0d1e71c05" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a64f3fbc7ca16efdb461ec6caec" DEFAULT 0, "companyId" varchar(255), "createdByName" varchar(250) NOT NULL, "status" varchar(150), "description" varchar(500), "recommendedPurchaseTemplateId" uniqueidentifier, "recommendedPurchaseId" uniqueidentifier, CONSTRAINT "PK_5090e951cc769872cd0d1e71c05" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "recommended_purchase_template" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c3a4bd704a10e9db12d56f2593b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a4dd2356a1d9eea771beacae60d" DEFAULT 0, "companyId" varchar(255), "code" varchar(250) NOT NULL, "status" nvarchar(50), "name" nvarchar(max) NOT NULL, "description" nvarchar(max), "type" nvarchar(20), "employeeId" uniqueidentifier, CONSTRAINT "PK_c3a4bd704a10e9db12d56f2593b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "business_plan_col_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cd0e4f42ee4e2a4a24bfcd0b8af" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3df0287bad46c9967cc86a03665" DEFAULT 0, "companyId" varchar(255), "value" varchar(250), "prId" uniqueidentifier, "materialId" uniqueidentifier, "businessPlanId" uniqueidentifier, "businessPlanTemplateId" uniqueidentifier, "businessPlanTemplateColId" uniqueidentifier, "quantity" decimal(20,2) CONSTRAINT "DF_a7a98f9f2754c7d214fbcd879d8" DEFAULT 0, CONSTRAINT "PK_cd0e4f42ee4e2a4a24bfcd0b8af" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "business_plan_setting_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cc1d4ff25a914e0b18b3fee9850" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_d24489324808cee2123558cfda0" DEFAULT 0, "companyId" varchar(255), "settingStringId" uniqueidentifier, "businessPlanId" uniqueidentifier, "value" decimal(20,2) CONSTRAINT "DF_f503f2acf23c6b39922fb607559" DEFAULT 0, "settingCode" varchar(150), "businessPlanTemplateColId" uniqueidentifier, CONSTRAINT "PK_cc1d4ff25a914e0b18b3fee9850" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "business_plan_template_col" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a2c0631edbb2632fc75dea29b8c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f887fb723dd4d6f740236567d2d" DEFAULT 0, "companyId" varchar(255), "code" varchar(50), "fomular" nvarchar(max), "isRequired" bit NOT NULL CONSTRAINT "DF_e35717ac74c38eb5efb2032c3af" DEFAULT 1, "isEdited" bit NOT NULL CONSTRAINT "DF_f1027a2ec19f9c30d4a4c9c5ed5" DEFAULT 1, "sort" int CONSTRAINT "DF_274655e24cd069936fe525adadc" DEFAULT 0, "name" varchar(250), "colType" nvarchar(50), "type" nvarchar(50), "isSystemData" bit CONSTRAINT "DF_b1ac37419033dde4cd02b453983" DEFAULT 0, "dataMapping" nvarchar(250), "businessPlanTemplateId" uniqueidentifier, "businessPlanTemplateCode" nvarchar(250), CONSTRAINT "PK_a2c0631edbb2632fc75dea29b8c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "business_plan_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_409308eef240130ff86ee1ac1aa" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_914894af94dbc82997d82208fed" DEFAULT 0, "companyId" varchar(255), "createdByName" varchar(250) NOT NULL, "status" varchar(150), "description" varchar(500), "businessPlanTemplateId" uniqueidentifier, "businessPlanId" uniqueidentifier, CONSTRAINT "PK_409308eef240130ff86ee1ac1aa" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "business_plan_template" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_8080b84f3ade97f4e6e83ccbd18" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_40ddb40faab12e84e200c5c6bf1" DEFAULT 0, "companyId" varchar(255), "code" varchar(250) NOT NULL, "status" nvarchar(50), "name" nvarchar(max) NOT NULL, "description" nvarchar(max), "type" nvarchar(20), "employeeId" uniqueidentifier, CONSTRAINT "PK_8080b84f3ade97f4e6e83ccbd18" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "rfq_details" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_97fd8622ff24b2f4453a634d2db" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_14840f1ebf7bf851935d4760960" DEFAULT 0, "companyId" varchar(255), "rfqId" uniqueidentifier, "materialCode" varchar(255), "materialId" uniqueidentifier, "netPrice" bigint, "isSynchronizing" bit CONSTRAINT "DF_cd2226bb2ad984e951937d1666f" DEFAULT 0, "rfqQuantity" int CONSTRAINT "DF_e4410b5e8ee8663510366642002" DEFAULT 0, "dealId" varchar(255), "deliveryDate" datetime, "supplierId" varchar(255), CONSTRAINT "PK_97fd8622ff24b2f4453a634d2db" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "recommended_purchase_rfq" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_460c000db5c542770a52d944441" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_14dca866ddf3dd6b3e4c1fb147d" DEFAULT 0, "companyId" varchar(255), "recommendedPurchaseId" uniqueidentifier, "rfqId" uniqueidentifier, CONSTRAINT "PK_460c000db5c542770a52d944441" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "rfq" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_395d59069aefd34028301b81af1" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f218ca9e87f9c8580e7350bd73e" DEFAULT 0, "companyId" varchar(255), "supplierCode" varchar(255), "type" varchar(255), "supplierId" uniqueidentifier, "rfqQuantity" int CONSTRAINT "DF_20a5c56162c8e522b1b9b2e0a31" DEFAULT 0, "deliveryDate" datetime, "deadLine" datetime, "materialCode" varchar(255), "materialId" uniqueidentifier, "netPrice" bigint, "targetId" varchar(255), "entityName" nvarchar(50), "dealId" varchar(255), "isSynchronizing" bit CONSTRAINT "DF_9c40fdaaf6a86b47a4f8cac9590" DEFAULT 0, "sapCode" varchar(255), CONSTRAINT "PK_395d59069aefd34028301b81af1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "business_plan_rfq" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_de3cd53ca451ef81ddd3f1cab50" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_aece86616da2b79095092982b2a" DEFAULT 0, "companyId" varchar(255), "businessPlanId" uniqueidentifier, "rfqId" uniqueidentifier, CONSTRAINT "PK_de3cd53ca451ef81ddd3f1cab50" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "business_plan" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0cb2a70adba3455e230021ae5c2" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_4d7c71fd4dd708a462a8c530d4b" DEFAULT 0, "companyId" uniqueidentifier, "code" varchar(100) NOT NULL, "name" varchar(250) NOT NULL, "status" nvarchar(50), "description" nvarchar(max), "employeeId" uniqueidentifier, "businessPlanTemplateId" uniqueidentifier, "currencyFromId" uniqueidentifier, "currencyToId" uniqueidentifier, "exchangeRate" decimal(20,2) CONSTRAINT "DF_38e0ba1f6d3129847f786687b2d" DEFAULT 0, "paymentMethodId" uniqueidentifier, "incotermId" uniqueidentifier, "incotermDescription" nvarchar(max), "deliveryAddress" nvarchar(max), "estimatedTime" datetime, "purchasingSource" nvarchar(max), "sellingSource" nvarchar(max), "intermediarySupplier" nvarchar(max), "sloc" nvarchar(max), "contractId" uniqueidentifier, "plantId" uniqueidentifier, "externalMaterialGroupId" uniqueidentifier, CONSTRAINT "PK_0cb2a70adba3455e230021ae5c2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shipment_cost_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_4145eb8dce42f67ac438cdb5047" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7cc667ba45b62ce819b398631e0" DEFAULT 0, "companyId" varchar(255), "conditionType" varchar(150), "description" varchar(4000), "shipmentCostId" uniqueidentifier, "typeDeclaration" varchar(100), "price" float, "costActual" int, "costActualComplete" int, "differentPercentCost" int, "supplierId" uniqueidentifier, CONSTRAINT "PK_4145eb8dce42f67ac438cdb5047" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shipment_cost" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a243a400b2c8cee3e7e80229c9d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_592a05dddba647c34e0124790d7" DEFAULT 0, "companyId" varchar(255), "status" varchar(50), "code" varchar(50) NOT NULL, "fileUrl" varchar(2000), "fileName" varchar(1000), "description" varchar(4000), "priceListType" varchar(250), "shipmentId" uniqueidentifier, "shipmentCostTypeId" uniqueidentifier, CONSTRAINT "PK_a243a400b2c8cee3e7e80229c9d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_8ab33728a839e919b07186dc63" ON "shipment_cost" ("code") `);
        await queryRunner.query(`CREATE TABLE "recommended_purchase_shipment_cost_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_444639e5d7979b1bf7572b76c17" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_57c889047fb777989feff984819" DEFAULT 0, "companyId" varchar(255), "conditionType" varchar(150), "description" varchar(4000), "amount" int, "crcy" varchar(150), "per" int, "conditionValue" int, "curr" varchar(150), "cConDe" int, "numCCo" int, "referenceSource" varchar(100), "recommendedPurchaseId" uniqueidentifier, "supplierId" uniqueidentifier, "price" bigint, "recommendedPurchaseShipmentStageId" varchar(255), "shipmentCostId" varchar(255), "shipmentPriceId" varchar(255), CONSTRAINT "PK_444639e5d7979b1bf7572b76c17" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "recommended_purchase_shipment_stage" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_032f7726af3a002780086021910" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_314911fd4296e84987b892363aa" DEFAULT 0, "companyId" varchar(255), "recommendedPurchaseId" uniqueidentifier, "referenceSource" varchar(100), "bidId" uniqueidentifier, "offerId" uniqueidentifier, "shipmentCostId" varchar(255), "startPoint" nvarchar(max), "destination" nvarchar(max), "fwdAgent" nvarchar(max), "fwdAgentName" nvarchar(max), "distance" bigint, "shippingType" nvarchar(max), "shippingTypeDesc" nvarchar(max), "tcBid" bit CONSTRAINT "DF_2b77a2b849a3ae127e8a4de1077" DEFAULT 0, "tcOffer" bit CONSTRAINT "DF_d7a1343628c2b5784bec94fe8aa" DEFAULT 0, "referenceCode" nvarchar(max), "estimatedCost" bigint, "supplierId" uniqueidentifier, CONSTRAINT "PK_032f7726af3a002780086021910" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "recommended_purchase" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_2bef5df1724d6485131bed0346a" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_5bf2cd35cf254e7bc28d5aa4296" DEFAULT 0, "companyId" uniqueidentifier, "code" varchar(100) NOT NULL, "name" varchar(250) NOT NULL, "status" nvarchar(50), "description" nvarchar(max), "employeeId" uniqueidentifier, "recommendedPurchaseTemplateId" uniqueidentifier, "paymentMethodId" uniqueidentifier, "incotermId" uniqueidentifier, "incotermDescription" nvarchar(max), "deliveryAddress" nvarchar(max), "estimatedTime" datetime, "purchasingSource" nvarchar(max), "sellingSource" nvarchar(max), "purpose" nvarchar(max), "propose" nvarchar(max), "intermediarySupplier" nvarchar(max), "plantId" uniqueidentifier, "offerId" uniqueidentifier, "businessPlanId" uniqueidentifier, "referenceSource" nvarchar(50), "bidId" uniqueidentifier, "shipmentCostId" uniqueidentifier, CONSTRAINT "PK_2bef5df1724d6485131bed0346a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "recommended_purchase_setting_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ab338bc5732d764585c18bf298f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e7d26e514572a5526ca74e147aa" DEFAULT 0, "companyId" varchar(255), "settingStringId" uniqueidentifier, "recommendedPurchaseId" uniqueidentifier, "value" decimal(20,2) CONSTRAINT "DF_4d5ea20857514b30c9f1d763adb" DEFAULT 0, "settingCode" varchar(150), "recommendedPurchaseTemplateColId" uniqueidentifier, CONSTRAINT "PK_ab338bc5732d764585c18bf298f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "setting_string" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cd993a95b69adc4fcb1c4e779c3" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_5e03d6409eb9a4f442b0eb99703" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(250), "type" varchar(50) NOT NULL, "description" varchar(250), "value" float, "valueString" nvarchar(400), CONSTRAINT "PK_cd993a95b69adc4fcb1c4e779c3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "round_up_cont_setting_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9da605032b6f49d26ac9452121e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ed9dfe5b87ea56db5dc179c5823" DEFAULT 0, "companyId" varchar(255), "settingStringId" uniqueidentifier, "roundUpContId" uniqueidentifier, "value" decimal(20,2) CONSTRAINT "DF_6e1bca7cb02e7b4dae6f621f584" DEFAULT 0, "settingCode" varchar(150), "roundUpContTemplateColId" uniqueidentifier, CONSTRAINT "PK_9da605032b6f49d26ac9452121e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "round_up_cont_template_col" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_6efff6056ca45daf556b3f46831" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ba5c888b2bcd806264480201b46" DEFAULT 0, "companyId" varchar(255), "code" varchar(50), "fomular" nvarchar(max), "isRequired" bit NOT NULL CONSTRAINT "DF_af642c2423e2a5affe34334b7f7" DEFAULT 1, "isEdited" bit NOT NULL CONSTRAINT "DF_43b578a5694bef5eab4ac5b7e1c" DEFAULT 1, "sort" int CONSTRAINT "DF_5349071f0efc80c8172ae389238" DEFAULT 0, "name" varchar(250), "colType" nvarchar(50), "type" nvarchar(50), "isSystemData" bit CONSTRAINT "DF_c813f4ab36385d773976fff20e2" DEFAULT 0, "dataMapping" nvarchar(250), "roundUpContTemplateId" uniqueidentifier, "roundUpContTemplateCode" nvarchar(250), CONSTRAINT "PK_6efff6056ca45daf556b3f46831" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "round_up_cont_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cabefd26c08a882a76ea8f3bb3e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_86e3ff4273d7b5b399a273788e7" DEFAULT 0, "companyId" varchar(255), "createdByName" varchar(250) NOT NULL, "roundUpContId" uniqueidentifier, "status" varchar(150), "description" varchar(500), "roundUpContTemplateId" uniqueidentifier, CONSTRAINT "PK_cabefd26c08a882a76ea8f3bb3e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "round_up_cont_template" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c5481068a7a2b553b8cedc7d599" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_8be5af24892a2087c41a08058a8" DEFAULT 0, "companyId" varchar(255), "code" varchar(250) NOT NULL, "status" nvarchar(50), "name" nvarchar(max) NOT NULL, "description" nvarchar(max), "type" nvarchar(20), "employeeId" uniqueidentifier, CONSTRAINT "PK_c5481068a7a2b553b8cedc7d599" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "round_up_cont_pr_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_dc0f65bb0ff54b8498c68622b72" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e45f7959c3798eb0774a28db546" DEFAULT 0, "companyId" varchar(255), "prId" uniqueidentifier, "roundUpContId" uniqueidentifier, "prItemId" uniqueidentifier, "quantity" decimal(20,2) CONSTRAINT "DF_e17ecf92a6a2b5659df8f888269" DEFAULT 0, "materialId" uniqueidentifier, "roundUpContPrId" uniqueidentifier, CONSTRAINT "PK_dc0f65bb0ff54b8498c68622b72" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "round_up_cont_pr" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_492388f85edd8528c58a497a3f6" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c9691b95d8a164f31758d2e7ec2" DEFAULT 0, "companyId" varchar(255), "prId" uniqueidentifier, "roundUpContId" uniqueidentifier, CONSTRAINT "PK_492388f85edd8528c58a497a3f6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "budget_receipt_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ec044693773c18c1be700f91359" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_50d0a1f55768361cfff8d91ba7f" DEFAULT 0, "companyId" varchar(255), "budgetReceiptId" uniqueidentifier, "isIncrease" bit NOT NULL CONSTRAINT "DF_75c717b13dfc134e7f872d62934" DEFAULT 1, "funCenter" varchar(250), "CICode" varchar(250), "CIName" varchar(250), "moneyAvailable" bigint CONSTRAINT "DF_aafe4cb307b3cb19dbe9a66c6a0" DEFAULT 0, "moneyPropose" bigint CONSTRAINT "DF_206cd6998615ac5bbbd2b56c979" DEFAULT 0, "moneyApproved" bigint CONSTRAINT "DF_d9728dda1bba5ff4e15f801532f" DEFAULT 0, "baseAdjust" varchar(250), "reasonAdjust" varchar(500), "tntcMonth" bigint CONSTRAINT "DF_14ff05816fa27ae77c74c696b6a" DEFAULT 0, "tntcYear" bigint CONSTRAINT "DF_b1d33a9037d6b653345b2372e2d" DEFAULT 0, "useAccumulate" bigint CONSTRAINT "DF_a307f486cd4c14dbce782573ecf" DEFAULT 0, "proposeMonth" bigint CONSTRAINT "DF_8c3d34b2b3e3fbde0af2d8bc1c2" DEFAULT 0, "proposeYear" bigint CONSTRAINT "DF_f3339bb57fc07d98331dce3a920" DEFAULT 0, "difference" bigint CONSTRAINT "DF_59b64246a1f0a17644b10284078" DEFAULT 0, "fp" nvarchar(max), "fc" nvarchar(max), "ci" nvarchar(max), "budgetPeriod" nvarchar(max), "prItemId" uniqueidentifier, CONSTRAINT "PK_ec044693773c18c1be700f91359" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "budget_receipt_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_1d1e6da8d8fc950f7e3fa7ca41d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a62a3f6c373ec08cf7d12952ed2" DEFAULT 0, "companyId" varchar(255), "createdByName" varchar(250) NOT NULL, "budgetReceiptId" uniqueidentifier NOT NULL, "status" varchar(150), "description" varchar(500), CONSTRAINT "PK_1d1e6da8d8fc950f7e3fa7ca41d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "budget_receipt" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_fdd5fce44850ad9a8580c2e73f0" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_290b4fc388147e8f699ba2f16d2" DEFAULT 0, "companyId" uniqueidentifier, "prId" uniqueidentifier, "poId" uniqueidentifier, "code" varchar(250) NOT NULL, "date" datetime, "dateBudget" datetime, "isMultiCompany" bit NOT NULL CONSTRAINT "DF_a1fb57da9bec195276ce18de33f" DEFAULT 1, "lstCompanyId" varchar(500), "proposeType" varchar(50), "receiptType" varchar(50), "employeeId" uniqueidentifier, "departmentQLNSId" varchar(50), "departmentQLNS" varchar(250), "currencyId" uniqueidentifier, "currency" varchar(250), "interpretation" varchar(500), "description" nvarchar(max), "descriptionOverview" nvarchar(max), "descriptionApproved" nvarchar(max), "descriptionOther" nvarchar(max), "fileAttachmentUrl" varchar(500), "totalMoneyAvailable" bigint CONSTRAINT "DF_62e1b88cd7a269dcf96c17c9ce4" DEFAULT 0, "totalMoneyPropose" bigint CONSTRAINT "DF_c032f3e372c08c265fd80dad841" DEFAULT 0, "totalMoneyApproved" bigint CONSTRAINT "DF_6f7c4ac9c7b09a8fc1a14b21165" DEFAULT 0, "totalMoneyRemaining" bigint CONSTRAINT "DF_c38699e6255da215cc207a6c232" DEFAULT 0, "status" varchar(50), "requestFrom" varchar(250), "podNumber" varchar(250), "employeeApprovedId" uniqueidentifier, "dateApproved" datetime, "lstHistoryApproved" varchar(max), "lstEmployeeApprovedId" varchar(250), "lstDateApproved" varchar(250), "roundUpContId" uniqueidentifier, CONSTRAINT "PK_fdd5fce44850ad9a8580c2e73f0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "round_up_cont" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_413e1e758a461c6de3e1fe2cb36" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_927335cbed614832d1df10faddc" DEFAULT 0, "companyId" uniqueidentifier, "code" varchar(100) NOT NULL, "name" varchar(250) NOT NULL, "status" nvarchar(50), "description" nvarchar(max), "roundUpContTemplateId" uniqueidentifier, "plantId" uniqueidentifier, "month" datetime, "quarterly" varchar(10), "year" datetime, "numberCont" int, "externalMaterialGroupId" uniqueidentifier, "type" nvarchar(20), "cbmRoundUp" decimal(20,2) CONSTRAINT "DF_c0302d7d07d62d39ee8c7d690bb" DEFAULT 0, "contType" varchar(250), "cbmNeedRoundUp" decimal(20,2) CONSTRAINT "DF_8c945479647a6d1ee5445ee7e5c" DEFAULT 0, "averageCbmSku" decimal(20,2) CONSTRAINT "DF_9a2fc7a821dc93e2495192577ba" DEFAULT 0, "numberOfMachines" decimal(20,2) CONSTRAINT "DF_727c1d51d08a1897a11f8270e91" DEFAULT 0, CONSTRAINT "PK_413e1e758a461c6de3e1fe2cb36" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "material_type" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5a4135c8b2d9e33f55b25e78e21" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a8fafd884c90ef7393a46e52de0" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" varchar(250), "portalName" nvarchar(250), CONSTRAINT "PK_5a4135c8b2d9e33f55b25e78e21" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_price_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cacbde2a1083256cc973e9fb2fd" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_0d7029e9d7e506bdacddcfef37a" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "description" nvarchar(max), "type" nvarchar(50) NOT NULL, "value" varchar(250), "bidPriceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_cacbde2a1083256cc973e9fb2fd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_deal_supplier_price_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cfe8a0c954aaad024e08f2c4f99" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_02bddc81b199e0936b02d3e2f04" DEFAULT 0, "companyId" varchar(255), "score" float, "value" varchar(250), "bidDealSupplierId" uniqueidentifier NOT NULL, "bidPriceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_cfe8a0c954aaad024e08f2c4f99" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_deal_supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f4effdf8985d330b19b5117f93b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a18036fd3dee47b5e870965e827" DEFAULT 0, "companyId" varchar(255), "bidDealId" uniqueidentifier NOT NULL, "score" float NOT NULL CONSTRAINT "DF_efbcedd853748780c31b8eae780" DEFAULT 0, "supplierId" uniqueidentifier NOT NULL, "status" varchar(50) NOT NULL, "filePriceDetail" varchar(500), "fileTechDetail" varchar(500), "linkDriver" varchar(500), "submitDate" datetime, CONSTRAINT "PK_f4effdf8985d330b19b5117f93b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_deal" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_dbd08fd8dbcd1b2e30cf8b05dd6" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_57e8f80f087c44195dbe47e8c72" DEFAULT 0, "companyId" varchar(255), "status" varchar(50) NOT NULL, "endDate" datetime NOT NULL, "isSendDealPrice" bit NOT NULL CONSTRAINT "DF_ceaee229b36d75671e98e730207" DEFAULT 0, "bidId" uniqueidentifier NOT NULL, "parentId" uniqueidentifier, "isRequireFilePriceDetail" bit CONSTRAINT "DF_98924f74fd57534109c0aae9dd3" DEFAULT 0, "isRequireFileTechDetail" bit CONSTRAINT "DF_f8977966ce164496deb7a5f64d6" DEFAULT 0, CONSTRAINT "PK_dbd08fd8dbcd1b2e30cf8b05dd6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_deal_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a98b6c931cf91a43fee0fb008ca" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_9605ad8dbd1945151f99633bbe4" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_b25cb34d6596faf169aeccf4a76" DEFAULT 0, "number" int NOT NULL CONSTRAINT "DF_2c67e0b9b236bb68c4995c46537" DEFAULT 0, "bestPrice" float, "bestPriceHistory" float, "bestPriceCurrent" float, "suggestPrice" float, "maxPrice" float, "bidDealId" uniqueidentifier NOT NULL, "bidPriceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_a98b6c931cf91a43fee0fb008ca" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_price_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_346f51aef10bb365327bba13d51" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ae9ec9ebde32602bc70e9621206" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "description" nvarchar(max), "type" nvarchar(50) NOT NULL, "value" varchar(250), "servicePriceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_346f51aef10bb365327bba13d51" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_price_col" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c6835bf592adf876b924387cede" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_fff8d3adf761c10a70f70204d62" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "fomular" nvarchar(max), "isRequired" bit NOT NULL CONSTRAINT "DF_f74a0e8c8c410b6b0db9167a956" DEFAULT 0, "sort" int NOT NULL CONSTRAINT "DF_cb8cf4cc7edcf27e67103fe9672" DEFAULT 0, "name" varchar(250) NOT NULL, "description" nvarchar(max), "type" nvarchar(50) NOT NULL, "colType" nvarchar(50) NOT NULL, "serviceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_c6835bf592adf876b924387cede" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_price_col_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f3ee07684aa611a0032141d4b09" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_aa9d42fe2ce7b8c3ba9997e50d8" DEFAULT 0, "companyId" varchar(255), "value" varchar(250) NOT NULL, "servicePriceId" uniqueidentifier NOT NULL, "servicePriceColId" uniqueidentifier NOT NULL, CONSTRAINT "PK_f3ee07684aa611a0032141d4b09" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_6e01b20baff3810d78bc3385f8f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_96661f7a8433fc1017d5fd8b6e6" DEFAULT 0, "companyId" varchar(255), "number" int NOT NULL CONSTRAINT "DF_a330c75ba81c325d772fb021828" DEFAULT 0, "sort" int NOT NULL CONSTRAINT "DF_1fad4d6c55e303334de40eed32f" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_299e213c7371a0d37dd714797b3" DEFAULT 0, "isSetup" bit NOT NULL CONSTRAINT "DF_a62c0f914d9b3178e70d95b25b1" DEFAULT 0, "isTemplate" bit NOT NULL CONSTRAINT "DF_5f40548efc133a548bc634c057a" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_89a106617fb5fa0eb55c967934b" DEFAULT 'string', "unit" varchar(255), "currency" varchar(255), "percent" float CONSTRAINT "DF_c5593522ee237d2d1e5399ec922" DEFAULT 0, "level" int NOT NULL CONSTRAINT "DF_9c589ffa54d843f3d7f89e3b790" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "serviceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_6e01b20baff3810d78bc3385f8f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_auction_supplier_price_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_647c908be8ef2ecf926320cb6f8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f0c2456f0f1bce4f88abc1b52a4" DEFAULT 0, "companyId" varchar(255), "score" float, "value" varchar(250), "bidAuctionSupplierId" uniqueidentifier NOT NULL, "bidPriceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_647c908be8ef2ecf926320cb6f8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_auction_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_fb5511e52291109c85e855dfb6d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3e364fd7785b6d99382a0f74c88" DEFAULT 0, "companyId" varchar(255), "maxPrice" float, "name" varchar(255), "unit" varchar(255), "number" int, "bidAuctionId" uniqueidentifier, "bidPriceId" uniqueidentifier, CONSTRAINT "PK_fb5511e52291109c85e855dfb6d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_price_col_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_22d2659c1276144605fc3ea83fe" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_bb6d37384ad210b9f18d3fc8f2b" DEFAULT 0, "companyId" varchar(255), "value" varchar(250) NOT NULL, "bidPriceId" uniqueidentifier NOT NULL, "bidPriceColId" uniqueidentifier NOT NULL, CONSTRAINT "PK_22d2659c1276144605fc3ea83fe" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_price_col" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_2d761c4e0d2c25dff0861bde217" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2bae0de7229b3c58802f710db03" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "fomular" nvarchar(max), "isRequired" bit NOT NULL CONSTRAINT "DF_f01f8acd8390bbf0b6ac4bccad4" DEFAULT 0, "sort" int NOT NULL CONSTRAINT "DF_4c4854d3b81821879f7e1f5594e" DEFAULT 0, "name" varchar(250) NOT NULL, "description" nvarchar(max), "type" nvarchar(50) NOT NULL, "colType" nvarchar(50) NOT NULL, "bidId" uniqueidentifier, "bidItemId" uniqueidentifier, "bidExgroupId" uniqueidentifier, CONSTRAINT "PK_2d761c4e0d2c25dff0861bde217" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_supplier_price_col_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_53fb6406c97a329d44586b95b7b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_bf8ac48d8c10652b8a9778e9974" DEFAULT 0, "companyId" varchar(255), "value" varchar(250), "bidSupplierId" uniqueidentifier NOT NULL, "bidPriceId" uniqueidentifier, "bidPriceColId" uniqueidentifier, CONSTRAINT "PK_53fb6406c97a329d44586b95b7b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_supplier_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_128299d7332079b707a4c9ac57c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_10896aa460931a10cdc5b3edb0c" DEFAULT 0, "companyId" varchar(255), "bidSupplierId" uniqueidentifier NOT NULL, "bidPriceId" uniqueidentifier NOT NULL, "bidPriceName" varchar(250) NOT NULL, "bidPriceLevel" int NOT NULL CONSTRAINT "DF_061fa3516973e39dfceb53925ee" DEFAULT 1, "bidId" varchar(255) NOT NULL, "serviceId" varchar(255), "supplierId" varchar(255) NOT NULL, "submitDate" datetime NOT NULL, "submitType" int NOT NULL, "number" int NOT NULL, "unitPrice" bigint NOT NULL, "price" bigint NOT NULL, CONSTRAINT "PK_128299d7332079b707a4c9ac57c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_22e936dcd733627319831fd6d0d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_d024710b29214fdb22f30d5f142" DEFAULT 0, "companyId" varchar(255), "number" int NOT NULL CONSTRAINT "DF_9fdbb4ba4ff398282d87c27872f" DEFAULT 0, "sort" int NOT NULL CONSTRAINT "DF_8f05f5d6791e084d1841a0e6758" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_85a27eef9e9914afb4b1905787a" DEFAULT 0, "isSetup" bit NOT NULL CONSTRAINT "DF_9332f8f5b29ca5804f828a35e13" DEFAULT 0, "isTemplate" bit NOT NULL CONSTRAINT "DF_31eee9e8c6701fe2ba6a306a833" DEFAULT 1, "value" varchar(250), "type" nvarchar(255) NOT NULL CONSTRAINT "DF_d76448d64f2d85afa40dd7c8254" DEFAULT 'string', "unit" varchar(255), "currency" varchar(255), "percent" float CONSTRAINT "DF_0f15baa5f35bb48b89c858f60c5" DEFAULT 100, "level" int NOT NULL CONSTRAINT "DF_8ea64550205c5330b06cd6a3054" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "bidId" uniqueidentifier, "bidExgroupId" uniqueidentifier, "servicePriceId" uniqueidentifier, "bidItemId" uniqueidentifier, "baseItemId" uniqueidentifier, CONSTRAINT "PK_22e936dcd733627319831fd6d0d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_supplier_price_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_6dcc2c5f321b96af0aaecb69f8a" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_83886f6400abbeed18a407a4871" DEFAULT 0, "companyId" varchar(255), "unit" varchar(255), "currency" varchar(255), "number" int NOT NULL CONSTRAINT "DF_a6287cdc26881211ab70bb5ec12" DEFAULT 0, "name" varchar(250), "score" float, "value" varchar(250), "bidSupplierId" uniqueidentifier NOT NULL, "bidPriceId" uniqueidentifier, "bidPriceParentId" varchar(255), CONSTRAINT "PK_6dcc2c5f321b96af0aaecb69f8a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_trade_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a44df4a9a6545c3eccba9e2ea0e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3948f81725cd7c3be68cd5087d7" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "value" int NOT NULL, "bidTradeId" uniqueidentifier NOT NULL, CONSTRAINT "PK_a44df4a9a6545c3eccba9e2ea0e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_trade" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c9478b7e5323c033068ad8fd43b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_575d55d94445f67cc6685d2eaba" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_26dcf90a1f641458a158346ab12" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_e8f0620e03862c1cbf48c9e35fe" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_16c1114c5857b505d3b32a3dbe7" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_32cbdc0fc39845d597b4a3bd426" DEFAULT 'string', "percent" float CONSTRAINT "DF_9f40f650993f1a7acaa1def3e54" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_76fd8f425fdebec25af75e71141" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "bidId" uniqueidentifier, "bidItemId" uniqueidentifier, CONSTRAINT "PK_c9478b7e5323c033068ad8fd43b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_supplier_trade_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_26cd9a139e1bd30c0b31009f7fc" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c9bab98815622c47c7cec6c4778" DEFAULT 0, "companyId" varchar(255), "score" float, "value" varchar(250), "bidSupplierId" uniqueidentifier NOT NULL, "bidTradeId" uniqueidentifier NOT NULL, CONSTRAINT "PK_26cd9a139e1bd30c0b31009f7fc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_supplier_custom_price_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_7351763fcde5d7c04bc91aed9ea" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_0c470bf2d65e35fea5af0a9b9d9" DEFAULT 0, "companyId" varchar(255), "unit" varchar(255), "currency" varchar(255), "number" int NOT NULL CONSTRAINT "DF_5563cdfa411acafb111d4974861" DEFAULT 0, "sort" int NOT NULL CONSTRAINT "DF_b16ec72c4b70cf2d9524b70eff0" DEFAULT 0, "name" varchar(250), "value" varchar(250), "bidSupplierId" uniqueidentifier NOT NULL, CONSTRAINT "PK_7351763fcde5d7c04bc91aed9ea" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_supplier_shipment_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_d3e5edccfd95803016ee71b677d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_5ad685fe794d4d0f91111557a2f" DEFAULT 0, "companyId" varchar(255), "score" float, "value" varchar(250), "bidSupplierId" uniqueidentifier NOT NULL, "shipmentPriceId" uniqueidentifier, CONSTRAINT "PK_d3e5edccfd95803016ee71b677d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_095da6315656279ff96382f199c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_aace52dc239a6d8e774c4a7af7d" DEFAULT 0, "companyId" varchar(255), "bidId" uniqueidentifier NOT NULL, "note" nvarchar(max), "noteTrade" nvarchar(max), "noteTech" nvarchar(max), "notePrice" nvarchar(max), "noteMPOLeader" nvarchar(max), "noteTechLeader" nvarchar(max), "scoreTech" float NOT NULL CONSTRAINT "DF_fa23db7baf58d213ca8b92bda08" DEFAULT 0, "scorePrice" float NOT NULL CONSTRAINT "DF_03327c036d2ac4313324cdc7a4f" DEFAULT 0, "scoreTrade" float NOT NULL CONSTRAINT "DF_9d30151a886585591dccb38268b" DEFAULT 0, "scoreManualTech" float NOT NULL CONSTRAINT "DF_478ee3ac32442db0223feb4a0d2" DEFAULT 0, "scoreManualPrice" float NOT NULL CONSTRAINT "DF_3f1cd2f571e7199b4b881ade9f3" DEFAULT 0, "scoreManualTrade" float NOT NULL CONSTRAINT "DF_dbf0311350a96ab87bc80e75f36" DEFAULT 0, "supplierId" uniqueidentifier NOT NULL, "status" varchar(50) NOT NULL, "statusFile" varchar(50) NOT NULL, "statusTech" varchar(50) NOT NULL, "statusTrade" varchar(50) NOT NULL, "statusPrice" varchar(50) NOT NULL, "isHighlight" bit NOT NULL CONSTRAINT "DF_87a8f831979eb33d1ecaf45a205" DEFAULT 0, "isNotHaveMinValue" bit NOT NULL CONSTRAINT "DF_3d5fc85fe9379efd83ea3afe407" DEFAULT 0, "isSuccessBid" bit NOT NULL CONSTRAINT "DF_a2469736826d1dfc23b80e9d0f9" DEFAULT 0, "noteSuccessBid" nvarchar(max), "isTechValid" bit NOT NULL CONSTRAINT "DF_e94ac9a482c4c336d51fe0a1e5b" DEFAULT 1, "isTradeValid" bit NOT NULL CONSTRAINT "DF_76dd972a505ae8fa56c4b1c3170" DEFAULT 1, "isPriceValid" bit NOT NULL CONSTRAINT "DF_48f103f220172f2a468b19f583d" DEFAULT 1, "statusResetPrice" varchar(50) NOT NULL CONSTRAINT "DF_de2926d7874a71f40808abdfb2d" DEFAULT 'KhongYeuCau', "dataJson" nvarchar(max), "filePriceDetail" varchar(500), "fileTechDetail" varchar(500), "submitDate" datetime, "totalPrice" bigint, "serviceId" varchar(255), "bidItemId" uniqueidentifier, CONSTRAINT "PK_095da6315656279ff96382f199c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_supplier_tech_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_860c27ac41e5db7e44bb441fa20" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3c246b0d3e23b356067d7e2aca2" DEFAULT 0, "companyId" varchar(255), "score" float, "value" varchar(250), "bidSupplierId" uniqueidentifier NOT NULL, "bidTechId" uniqueidentifier NOT NULL, CONSTRAINT "PK_860c27ac41e5db7e44bb441fa20" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_tech_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_174fd8644a460ce095580b25466" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7b66da122642702fad28f7b0b45" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "value" int NOT NULL, "bidTechId" uniqueidentifier NOT NULL, CONSTRAINT "PK_174fd8644a460ce095580b25466" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_tech" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_84e9848112a795afca5036d1b77" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6e8a311afb97a5c5264099e7876" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_418bd8bad1e4b4489ec2fabb544" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_a743320acfe1c0d5edb76e3b223" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_5bcdf1a17a14b2a4cd3a5cc38f5" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_7626607fe132771075dc7ff3e68" DEFAULT 'string', "percent" float CONSTRAINT "DF_edf597f9aee191e744f5a094285" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_21723fca5052848e22f1206789b" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "isHighlight" bit NOT NULL CONSTRAINT "DF_fefc586eb64f9f48b40e20e3258" DEFAULT 0, "hightlightValue" int, "bidId" uniqueidentifier, "bidItemId" uniqueidentifier, CONSTRAINT "PK_84e9848112a795afca5036d1b77" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_custom_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a03934c00964f1acabdf9037ef4" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_4a37e43c3d9c399b2559a81ea9e" DEFAULT 0, "companyId" varchar(255), "number" int NOT NULL CONSTRAINT "DF_8ffbdd44f745614e296190c6ae6" DEFAULT 0, "sort" int NOT NULL CONSTRAINT "DF_d9efd79f781b7e20d3d3f51e96e" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_4f7afdcfc6e0c6425a99c55b3df" DEFAULT 0, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_4bf20559982d4e7ae888de1f45b" DEFAULT 'Number', "unit" varchar(255), "currency" varchar(255), "bidId" uniqueidentifier, "bidExgroupId" uniqueidentifier, "bidItemId" uniqueidentifier, CONSTRAINT "PK_a03934c00964f1acabdf9037ef4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_pr_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_1b9857d93594434b8e149853431" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_dca6d36c14c27647d77b8625b6e" DEFAULT 0, "companyId" varchar(255), "code" varchar(50), "currency" varchar(50), "bidId" uniqueidentifier, "prItemId" uniqueidentifier, "shipmentPriceId" varchar(255), "serviceId" uniqueidentifier, "quantityItem" int CONSTRAINT "DF_a09007b0af66df3b675d201ab54" DEFAULT 0, "quantity" int CONSTRAINT "DF_8ca7803bfe0817cef55e27d8788" DEFAULT 0, "percentTech" int CONSTRAINT "DF_b1b135a65430eddabb7a4d88421" DEFAULT 0, "percentTrade" int CONSTRAINT "DF_453163bcdcc2fea467b7cfde51d" DEFAULT 0, "percentPrice" int CONSTRAINT "DF_189a188fc2e9f41d0a2563cb99e" DEFAULT 0, "scoreDLC" int, "status" varchar(50), "category" varchar(1), "shortText" varchar(1000), "categoryName" varchar(50), "materialId" uniqueidentifier, "materialCode" varchar(50), "unitCode" varchar(10), "unitName" varchar(200), "unitId" uniqueidentifier, "materialGroupId" uniqueidentifier, "materialGroupName" varchar(100), "materialGroupCode" varchar(100), "fomular" nvarchar(max), "isExmatgroup" bit CONSTRAINT "DF_ec56a185f7d1c1912a41760b06b" DEFAULT 0, "conditionType" varchar(150), "description" varchar(4000), "amount" int, "crcy" varchar(150), "per" int, "conditionValue" int, "curr" varchar(150), "cConDe" int, "numCCo" int, "bidExgroupId" uniqueidentifier, CONSTRAINT "PK_1b9857d93594434b8e149853431" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_exmatgroup" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_d4ecc4fa489c70d9de964ddd157" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f9d86d9129a670c14fdef52162f" DEFAULT 0, "companyId" varchar(255), "bidId" uniqueidentifier NOT NULL, "currency" varchar(50), "externalMaterialGroupId" uniqueidentifier, "fomular" nvarchar(max), "wayCalScorePrice" varchar(50) CONSTRAINT "DF_3f03a73a6587a9d0646749b435d" DEFAULT 'SumScore', "percentTech" int CONSTRAINT "DF_0679187bb45a4ac252b10ad49ce" DEFAULT 0, "percentTrade" int CONSTRAINT "DF_d2ab648e1619d561938632a78fc" DEFAULT 0, "percentPrice" int CONSTRAINT "DF_bc016693dc89eeff6a069b001a7" DEFAULT 0, "scoreDLC" int, CONSTRAINT "PK_d4ecc4fa489c70d9de964ddd157" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "external_material_group" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ad35d2d28839c8e6d3055916ec6" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ed627bd090c36a7e23e17b3e2d1" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(max), "fomular" nvarchar(max), "wayCalScorePrice" varchar(50) CONSTRAINT "DF_a1a7b8e157ccfb9ca3c8ee21557" DEFAULT 'SumScore', "statusCapacity" varchar(100) CONSTRAINT "DF_388a572d0fdf5b2088d2946d3b0" DEFAULT 'ChuaThamDinh', "isSync" bit CONSTRAINT "DF_f1c8c875e1c0265ecd9ae92ea1f" DEFAULT 0, "materialTypeId" uniqueidentifier, CONSTRAINT "PK_ad35d2d28839c8e6d3055916ec6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_custom_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9861233220514ab3e8835514587" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6b4ecda279302ae1ecc13cf4d48" DEFAULT 0, "companyId" varchar(255), "number" int NOT NULL CONSTRAINT "DF_ff0924f0e44e3bf4e8c605be9d6" DEFAULT 0, "sort" int NOT NULL CONSTRAINT "DF_5f35469d600a0d401a76ae97c62" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_56f480560b5f9daaef79300e241" DEFAULT 0, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_948c370665df65ef203a7204074" DEFAULT 'Number', "unit" varchar(255), "currency" varchar(255), "offerId" uniqueidentifier, "offerServiceId" uniqueidentifier, CONSTRAINT "PK_9861233220514ab3e8835514587" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_shipment_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3adff600e1b0aa8fce672948c51" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6d2ed890fbea7560ebfc329e25e" DEFAULT 0, "companyId" varchar(255), "offerId" uniqueidentifier, "shipmentPriceId" uniqueidentifier, "value" int NOT NULL, CONSTRAINT "PK_3adff600e1b0aa8fce672948c51" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_57c6ae1abe49201919ef68de900" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_53992f8c8cb057f054d9faea4ba" DEFAULT 0, "companyId" varchar(255), "code" varchar(50), "name" varchar(250), "title" varchar(250), "employeeId" uniqueidentifier, "effectiveDate" datetime, "endDate" datetime, "currency" varchar(250), "timePeriod" int CONSTRAINT "DF_2c0e7697fa9837d2f95515e9462" DEFAULT 0, "condition" varchar(250), "scoreDLC" int, "address" varchar(250), "isHaveVat" bit CONSTRAINT "DF_47bdb08661a088714974003b6c3" DEFAULT 0, "isCompleteAll" bit CONSTRAINT "DF_b4e0d4cb3ada7463f9a1e67122c" DEFAULT 0, "isNotConfigTrade" bit CONSTRAINT "DF_a784a82b952838a7de5a887421c" DEFAULT 0, "isShowClient" bit CONSTRAINT "DF_9cbc20807bd698e9d013f7d97d3" DEFAULT 0, "isGetFromPr" bit CONSTRAINT "DF_dd06fbbcc605faf9f4ac6eeccf9" DEFAULT 0, "description" varchar(250), "externalMaterialGroupId" uniqueidentifier, "prId" uniqueidentifier, "businessPlantId" uniqueidentifier, "refType" varchar(50), "status" varchar(50), "isLoadFromItem" bit CONSTRAINT "DF_be7e52d32d4e6b24ca61e1de26d" DEFAULT 0, "publicDate" datetime, "statusRateTech" varchar(50), "statusRateTrade" varchar(50), "statusRatePrice" varchar(50), "statusResetPrice" varchar(50) CONSTRAINT "DF_93f97e64504fc79c0b3e1bab715" DEFAULT 'ChuaTao', "isRequestDelete" bit CONSTRAINT "DF_cf4966807e7258a87def652344f" DEFAULT 0, "noteRequestDelete" nvarchar(max), "fileScan" varchar(500), "noteFinishBidMPO" nvarchar(max), "statusTrade" varchar(50), "resetPriceEndDate" datetime, "statusPrice" varchar(50), "isRequireFileTechDetail" bit CONSTRAINT "DF_d47bcd9db00343a1b0d284d3f91" DEFAULT 0, "isRequireFilePriceDetail" bit CONSTRAINT "DF_e3a7c53298511ebb804d2a70c70" DEFAULT 0, "wayCalScorePrice" varchar(50) CONSTRAINT "DF_012ff7c51e8836eaee4206a4524" DEFAULT 'SumScore', "fomular" nvarchar(max), "statusChooseSupplier" varchar(50) CONSTRAINT "DF_ddd4cd2f0f7adc8486868de0aae" DEFAULT 'ChuaChon', "dateStart" datetime, "dateEnd" datetime, "noteTrade" nvarchar(max), "fileAttach" varchar(250), "notePrice" nvarchar(max), "approveChooseSupplierWinDate" datetime, "bidCloseDate" datetime, "noteTechLeader" nvarchar(max), "noteMPOLeader" nvarchar(max), "shipmentId" varchar(255), "offerTypeCode" varchar(250), CONSTRAINT "PK_57c6ae1abe49201919ef68de900" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "offer_service" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_d1d5fd00b75a956ec8fb5f2b5e3" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_aa714e117a57db0951292db2a48" DEFAULT 0, "companyId" varchar(255), "itemNo" bigint, "materialGroupName" varchar(100), "categoryName" varchar(50), "category" varchar(1), "materialId" uniqueidentifier, "externalMaterialGroupId" uniqueidentifier, "shortText" varchar(1000), "quantity" int CONSTRAINT "DF_5a33a3b536e76cef69137a72c58" DEFAULT 0, "status" varchar(50), "plantId" uniqueidentifier, "unitCode" varchar(10), "unitId" uniqueidentifier, "ounId" uniqueidentifier, "materialGroupId" uniqueidentifier, "offerId" uniqueidentifier, "prItemId" uniqueidentifier, "serviceId" uniqueidentifier, "percentTech" int CONSTRAINT "DF_1ffeaa0dffecbd535ed625662d9" DEFAULT 0, "percentTrade" int CONSTRAINT "DF_b13426ce47bba83d0c211e0f9c7" DEFAULT 0, "percentPrice" int CONSTRAINT "DF_8c034191e67d87d5a1f27f81a0b" DEFAULT 0, "scoreDLC" int, "serviceName" varchar(250), "fomular" nvarchar(max), "isExGr" bit NOT NULL CONSTRAINT "DF_47b4265e857f615fe5e50306c6d" DEFAULT 0, "shipmentPriceId" varchar(255), "conditionType" varchar(150), "description" varchar(4000), "amount" int, "crcy" varchar(150), "per" int, "conditionValue" int, "curr" varchar(150), "cConDe" int, "numCCo" int, "value" int, "deliveryDate" datetime, CONSTRAINT "PK_d1d5fd00b75a956ec8fb5f2b5e3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "material_uom" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_8aae73a5e38ed16bf01b138f511" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c49c525c5c1c3c161ac5edf5192" DEFAULT 0, "companyId" varchar(255), "materialId" uniqueidentifier NOT NULL, "uomId" uniqueidentifier NOT NULL, "isDefault" bit CONSTRAINT "DF_37af218358e85df1fad8669d9e1" DEFAULT 0, "coefficientX" decimal(20,2) CONSTRAINT "DF_3380d3ce111cfc000c5363aa680" DEFAULT 0, "coefficientY" decimal(20,2) CONSTRAINT "DF_0a07daac5beeaa55313247c8a9b" DEFAULT 0, "uomAlternativeId" uniqueidentifier, CONSTRAINT "PK_8aae73a5e38ed16bf01b138f511" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "reservation_norm" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b0d3708209aea43aa302472d423" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_75b8057ce0e2f67d4253eff5f7f" DEFAULT 0, "companyId" varchar(255), "code" varchar(250), "materialCode" varchar(255), "materialId" uniqueidentifier, "departmentId" uniqueidentifier, "unitId" uniqueidentifier, "norm" decimal(20,2) CONSTRAINT "DF_a75a480d9be5ba2e06424de4365" DEFAULT 0, "time" varchar(255), "timeNote" varchar(255), "description" nvarchar(max), CONSTRAINT "PK_b0d3708209aea43aa302472d423" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "warehouse" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_965abf9f99ae8c5983ae74ebde8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e41374d51025c4739bb6d65b5ea" DEFAULT 0, "companyId" varchar(255), "name" varchar(50) NOT NULL, "code" varchar(50) NOT NULL, "description" varchar(250), CONSTRAINT "PK_965abf9f99ae8c5983ae74ebde8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "reservation_item_child" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_e2fa153fbcbe0a1bdc9269161bf" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e56cfbfbc46c306714a0aad44eb" DEFAULT 0, "companyId" varchar(255), "reservationId" uniqueidentifier, "reservationItemId" uniqueidentifier, "quantity" int CONSTRAINT "DF_bc2e34739382ba8298d74444695" DEFAULT 0, "shortText" varchar(500), "materialId" uniqueidentifier, "assetCode" nvarchar(max), "assetDesc" nvarchar(max), "io" nvarchar(max), "ioName" nvarchar(max), "iotype" nvarchar(max), "relstatus" nvarchar(max), "reservationItemNewId" varchar(255), "reservationItemOldId" varchar(255), CONSTRAINT "PK_e2fa153fbcbe0a1bdc9269161bf" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "reservation" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_48b1f9922368359ab88e8bfa525" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_58aa2135b2168eace1eb33dfced" DEFAULT 0, "companyId" varchar(255), "code" varchar(250), "sourceType" varchar(50), "status" varchar(50), "plantId" uniqueidentifier, "requisitionerId" uniqueidentifier, "departmentId" uniqueidentifier, "warehouseReceivingId" uniqueidentifier, "warehouseReceivingSloc" nvarchar(max), "warehouseIssueId" uniqueidentifier, "warehouseIssueSloc" nvarchar(max), "uses" nvarchar(max), "description" nvarchar(max), "reservationNo" nvarchar(max), "isCheckBudget" bit CONSTRAINT "DF_c156a5809618bbc4aea55d25151" DEFAULT 0, "isApprovedDone" bit CONSTRAINT "DF_2e8afad872ac72e5e895a2e921f" DEFAULT 0, "reservationParentId" varchar(255), "isParent" bit NOT NULL CONSTRAINT "DF_4cb8753ffc1ea64b7a174fb98e5" DEFAULT 0, "lstReservationChild" nvarchar(max), "lstDepartmentId" nvarchar(max), "lstRequisitionerId" nvarchar(max), "budget" decimal(20,2) CONSTRAINT "DF_d1589b173c0623391ee01b08a3a" DEFAULT 0, "quantity" int CONSTRAINT "DF_d6789db112b7f28a313d659990c" DEFAULT 0, "lstPlantId" nvarchar(max), CONSTRAINT "PK_48b1f9922368359ab88e8bfa525" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "reservation_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0080a75ff7fac5703092e675f50" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_31dc5bcbe1e1b9db696b07c2893" DEFAULT 0, "companyId" varchar(255), "reservationId" uniqueidentifier, "materialId" uniqueidentifier, "shortText" varchar(2000), "quantity" int CONSTRAINT "DF_57a3f1cafd3ff6aed57c8d178f8" DEFAULT 0, "uomCode" varchar(10), "uomId" uniqueidentifier, "quantityAlternative" decimal(20,2) CONSTRAINT "DF_48bc6f4e2c5f0e7671a72d32f7d" DEFAULT 0, "uomAlternativeCode" varchar(10), "uomAlternativeId" uniqueidentifier, "actualQuantity" decimal(20,2) CONSTRAINT "DF_89b852c81557d4eb6e4283fa72a" DEFAULT 0, "batch" varchar(500), "expiryDate" datetime, "time" datetime, "referenceNo" varchar(500), "description" nvarchar(max), "assetCode" nvarchar(max), "assetDesc" nvarchar(max), "io" nvarchar(max), "ioName" nvarchar(max), "iotype" nvarchar(max), "relstatus" nvarchar(max), "norm" int, "reservationParentId" varchar(255), "budgetPeriod" nvarchar(max), "fp" nvarchar(max), "fc" nvarchar(max), "ci" nvarchar(max), "budget" decimal(20,2) CONSTRAINT "DF_2d4c999cd3ab5b978bd53e6be81" DEFAULT 0, "fundCenter" varchar(1000), "fundProgram" varchar(1000), CONSTRAINT "PK_0080a75ff7fac5703092e675f50" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "uom" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_87729daf4a43ad4efdbdef69ff9" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_62388aadfc30a3e764183f482cc" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(50) NOT NULL, "description" varchar(250), "baseUnitId" uniqueidentifier, CONSTRAINT "PK_87729daf4a43ad4efdbdef69ff9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "auction_supplier_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5d8ea0146ac460fa75be02459d7" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_02daf767a8b4d2f7f38a8b8542b" DEFAULT 0, "companyId" varchar(255), "auctionSupplierId" uniqueidentifier, "name" varchar(250), "unitId" uniqueidentifier, "unitName" varchar(255), "quantity" int CONSTRAINT "DF_ba65534cb0fce87e6ab040958b9" DEFAULT 0, "maxPrice" bigint, "submitPriceOld" bigint, "submitPrice" bigint, "submitDate" datetime, "prItemId" uniqueidentifier, "bidItemId" uniqueidentifier, "belongAuction" bit NOT NULL CONSTRAINT "DF_2b7edef2f2fc07531683ff391b5" DEFAULT 0, "isMaterial" bit NOT NULL CONSTRAINT "DF_6dfde9600ba3e851a3a5b64bc55" DEFAULT 0, "auctionId" uniqueidentifier, "exMatGroupId" uniqueidentifier, CONSTRAINT "PK_5d8ea0146ac460fa75be02459d7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "auction_supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b409d9e334c61023dd4a45eef06" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c07f6a40676ea1e353515588f08" DEFAULT 0, "companyId" varchar(255), "auctionId" uniqueidentifier NOT NULL, "supplierId" uniqueidentifier NOT NULL, "submitPriceOld" bigint, "submitPrice" bigint, "submitDate" datetime, "rank" int, "status" varchar(50) CONSTRAINT "DF_557d1024bd45bd9bf9a9b321735" DEFAULT 'WAITING', "isWinner" bit NOT NULL CONSTRAINT "DF_005c5e9c123a52fc38e406c138d" DEFAULT 0, CONSTRAINT "PK_b409d9e334c61023dd4a45eef06" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "auction" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9dc876c629273e71646cf6dfa67" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_0c63707cef73ac74a1c9b325cdc" DEFAULT 0, "companyId" varchar(255), "code" varchar(50), "title" varchar(500), "bidAutionType" varchar(250), "price" bigint, "submitPrice" bigint, "dateStartPlus" datetime, "timePlus" bigint, "timePlusType" varchar(250), "currency" varchar(250), "timeApprove" bigint, "step" bigint, "fileUrl" varchar(250), "timeApproveType" varchar(250), "prId" uniqueidentifier, "externalMaterialGroupId" uniqueidentifier, "isShowSup" bit CONSTRAINT "DF_9c43a3b5661ce30fc82d2de299b" DEFAULT 0, "isShowDetailClient" bit CONSTRAINT "DF_68436ca06c1188d6aa703386ecf" DEFAULT 0, "isShowDetailEmail" bit CONSTRAINT "DF_26e2570f632139d99218b987ed9" DEFAULT 0, "isShowDetailSMS" bit CONSTRAINT "DF_ffe9f15961aa0c7a63726d01998" DEFAULT 0, "minPrice" bigint, "dateStart" datetime, "dateEnd" datetime, "status" varchar(10) CONSTRAINT "DF_5a167d57fb2a0f83c345e6d6c03" DEFAULT 'NEW', "description" nvarchar(max), "numSupplier" int CONSTRAINT "DF_aaec1007f21eeae89fcb64742a0" DEFAULT 0, "bidId" uniqueidentifier, CONSTRAINT "PK_9dc876c629273e71646cf6dfa67" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "banner_client" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_389d4a756a1b100afd5aa48a3d9" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_1902cac03cb91984c1bc8500bb4" DEFAULT 0, "companyId" varchar(255), "name" varchar(50) NOT NULL, "atr" varchar(250) NOT NULL, "type" varchar(50) NOT NULL, "position" varchar(50) NOT NULL, "url" varchar(250), "description" varchar(250), "domain" varchar(250), CONSTRAINT "PK_389d4a756a1b100afd5aa48a3d9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a92bce070b3e68a7b60e1595a5e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_14094d68431e387216794ef48ef" DEFAULT 0, "companyId" varchar(255), "bidId" uniqueidentifier NOT NULL, "employeeId" uniqueidentifier, "status" varchar(50) NOT NULL, "description" varchar(250), CONSTRAINT "PK_a92bce070b3e68a7b60e1595a5e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_employee_rate" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_6ae1bf855ecb5b042e78058a6b9" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_bf2b82785dff7090bd1bb7f7a14" DEFAULT 0, "companyId" varchar(255), "employeeAccessId" uniqueidentifier NOT NULL, "supplierId" uniqueidentifier NOT NULL, "scoreManualTech" float CONSTRAINT "DF_fe0e75df58ea9712bf5a4db5698" DEFAULT 0, "scoreManualPrice" float CONSTRAINT "DF_98179ef2441df9fd5b5cb867398" DEFAULT 0, "scoreManualTrade" float CONSTRAINT "DF_e35e9da9a2310bec3722ceae499" DEFAULT 0, "scoreTechLock" bit CONSTRAINT "DF_8d536f341edcb962b48b427f249" DEFAULT 0, "scoreTradeBlock" bit CONSTRAINT "DF_f05ff528ee8a22cef1889c84941" DEFAULT 0, "scorePriceBlock" bit CONSTRAINT "DF_ba1aef209c146600870825813b8" DEFAULT 0, CONSTRAINT "PK_6ae1bf855ecb5b042e78058a6b9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_employee_access" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_95bacf043cac2acaf0a8f971712" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_eddbe8706bc4d6c577a9165bdde" DEFAULT 0, "companyId" varchar(255), "type" varchar(50) NOT NULL, "employeeId" uniqueidentifier NOT NULL, "bidId" uniqueidentifier NOT NULL, "isScore" bit NOT NULL CONSTRAINT "DF_040dc52c579966060bd37619d16" DEFAULT 0, "isMember" bit CONSTRAINT "DF_b684de1ed2b501a15a65c222903" DEFAULT 0, "scoreManualTech" float CONSTRAINT "DF_bb9f225486da6920bdf18a6d8ea" DEFAULT 0, "scoreManualPrice" float CONSTRAINT "DF_ee01835e1decdda5f1a77e397e4" DEFAULT 0, "scoreManualTrade" float CONSTRAINT "DF_801971e6f26cc82a6249082ed3a" DEFAULT 0, CONSTRAINT "PK_95bacf043cac2acaf0a8f971712" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_type" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5cf4871252a7d8572b08bf8ee3c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ead1749fb7da2de4c51a7944817" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "code" varchar(50) NOT NULL, "description" varchar(250), CONSTRAINT "PK_5cf4871252a7d8572b08bf8ee3c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "employee_notify" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b0380e66bb34b4bf9962f38b88f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_d9f79b13b0ac45682ab6ece6551" DEFAULT 0, "companyId" varchar(255), "message" nvarchar(max), "messageFull" nvarchar(max), "isNew" bit NOT NULL CONSTRAINT "DF_bbd0041613f8b760fe5841df68b" DEFAULT 1, "employeeId" uniqueidentifier NOT NULL, CONSTRAINT "PK_b0380e66bb34b4bf9962f38b88f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "employee_warning" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3e21665ac46e4ee1eb3693d82eb" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c51de9139204838ba4cbe4b7c5f" DEFAULT 0, "companyId" varchar(255), "message" nvarchar(max), "messageFull" nvarchar(max), "isNew" bit NOT NULL CONSTRAINT "DF_a80a967099f4b2ed0b56a08edd6" DEFAULT 1, "warningType" varchar(50), "dataType" varchar(50), "dataId" varchar(255), "employeeId" uniqueidentifier NOT NULL, CONSTRAINT "PK_3e21665ac46e4ee1eb3693d82eb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "faq_category" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3f3f88890b5286bf53f9e3fb6b8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_4385d12e0c7a3697db63c86dac8" DEFAULT 0, "companyId" varchar(255), "name" varchar(500) NOT NULL, "description" nvarchar(max), CONSTRAINT "PK_3f3f88890b5286bf53f9e3fb6b8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "faq" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_d6f5a52b1a96dd8d0591f9fbc47" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_4bcc51b6048505d0d2d9e616bd7" DEFAULT 0, "companyId" varchar(255), "title" varchar(500) NOT NULL, "description" nvarchar(max), "categoryId" uniqueidentifier, CONSTRAINT "PK_d6f5a52b1a96dd8d0591f9fbc47" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "link_client" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_1917536eb872b25ef751bd46cd8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_21f2336ded77e1a5a6ee8f48105" DEFAULT 0, "companyId" varchar(255), "name" varchar(50) NOT NULL, "url" varchar(250) NOT NULL, "description" varchar(250), "domain" varchar(250), CONSTRAINT "PK_1917536eb872b25ef751bd46cd8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "email_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_abbda109218969deb4e9c90ac99" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e13a895051a83eef75073174b77" DEFAULT 0, "companyId" varchar(255), "status" varchar(50) NOT NULL, "type" varchar(50) NOT NULL, "count" int NOT NULL CONSTRAINT "DF_55ecc4f0e276960f977d11b0aad" DEFAULT 0, "toAddresses" varchar(250) NOT NULL CONSTRAINT "DF_11cf3b782f8830af9d53c365d9c" DEFAULT '', "ccAddresses" varchar(250), "bccAddresses" varchar(250), "subject" varchar(250) NOT NULL CONSTRAINT "DF_cd7c12970eed65ff67cae0005a9" DEFAULT '', "body_text" nvarchar(max) NOT NULL, "body_html" nvarchar(max) NOT NULL, "result" nvarchar(max) NOT NULL, CONSTRAINT "PK_abbda109218969deb4e9c90ac99" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "email_template" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c90815fd4ca9119f19462207710" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3f7aa4d8aca7f6961bd21eb1f13" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "code" varchar(150) NOT NULL, "description" nvarchar(max), CONSTRAINT "PK_c90815fd4ca9119f19462207710" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_custom_price" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_950e75d3bb91ee0d21bf54658d8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_46b437becadf7e78c48c0305c94" DEFAULT 0, "companyId" varchar(255), "number" int NOT NULL CONSTRAINT "DF_c01675f5b494c451fea315c2c89" DEFAULT 0, "sort" int NOT NULL CONSTRAINT "DF_4e795531e0cff5afa467f48dea6" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_67d4b9257ae7480f41091ffa2b8" DEFAULT 0, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_aededea33812dc50c83f128c670" DEFAULT 'Number', "unit" varchar(255), "currency" varchar(255), "serviceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_950e75d3bb91ee0d21bf54658d8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_tech_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3346581d11f0a3655dbce85cf30" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3d0c27eeaf28d3ccdc03be0ce74" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "value" int NOT NULL, "serviceTechId" uniqueidentifier NOT NULL, CONSTRAINT "PK_3346581d11f0a3655dbce85cf30" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_tech" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_e4b112fb3b3bfcf59b9bbb4e792" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f9cbfba00e341aeeeee67c0c587" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_2cdd868c01a1dc288d2e4397464" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_d444040e6b475e303a021c2258a" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_7bb1ede9ebe307a2f79da28f499" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_564379f5aace91c2279256a5c9b" DEFAULT 'string', "percent" float CONSTRAINT "DF_70d4ce126c886586f05436b5d50" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_f2df2542c72d5694e4d9863f8c1" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "isHighlight" bit NOT NULL CONSTRAINT "DF_4f4d05fbe30edac6ea9f8c6da6b" DEFAULT 0, "hightlightValue" int, "serviceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_e4b112fb3b3bfcf59b9bbb4e792" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_trade_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a390dd28132742e738969d9aeb1" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a491bd0b77dd5c4443557c63c6d" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "value" int NOT NULL, "serviceTradeId" uniqueidentifier NOT NULL, CONSTRAINT "PK_a390dd28132742e738969d9aeb1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_trade" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5e3449212527c8df730078d8117" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_798f2b5dfbab6349cd49c3f8db7" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_e4cd55ca1bbe5172ff9783747eb" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_7fbca1f69623ff2005a87a4d58e" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_c34db611852c25eed3f6741af4e" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_af79ba3f6150e9dd32696af3190" DEFAULT 'string', "percent" float CONSTRAINT "DF_c06f519222a51e9e1a5793901a7" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_7035877470e4c63773e77f18f68" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "serviceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_5e3449212527c8df730078d8117" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "setting_string_client" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_00624cd6b39bfad254f76bdfd65" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_bc84fbd11ee12d3790bc63ff102" DEFAULT 0, "companyId" varchar(255), "name" varchar(250), "type" varchar(50) NOT NULL, "description" varchar(250), "domain" varchar(250), CONSTRAINT "PK_00624cd6b39bfad254f76bdfd65" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_expertise_member" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cdc1d642fbe80069bea1aced76f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_4107e4e4a616cfb80b6f9fbda33" DEFAULT 0, "companyId" varchar(255), "employeeId" uniqueidentifier NOT NULL, "supplierExpertiseId" uniqueidentifier NOT NULL, CONSTRAINT "PK_cdc1d642fbe80069bea1aced76f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_expertise_law_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b664c578b373fac52328203995f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_064c6c4aa99086a9230172ce040" DEFAULT 0, "companyId" varchar(255), "code" varchar(50), "name" varchar(250), "dealName" varchar(250), "address" varchar(250), "dealAddress" varchar(250), "fileMST" varchar(250), "represen" varchar(50), "chief" varchar(50), "bankNumber" varchar(50), "bankname" varchar(250), "bankBrand" varchar(250), "fileAccount" varchar(250), "contactName" varchar(250), "email" varchar(50), "phone" varchar(50), "createYear" datetime, "capital" int, "assets" int, "fileBill" varchar(250), "fileInfoBill" varchar(250), "supplierExpertiseId" uniqueidentifier NOT NULL, CONSTRAINT "PK_b664c578b373fac52328203995f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_e99a304491bc41175120b95e8e3" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_cc11722a3e3d7157d5275d33561" DEFAULT 0, "companyId" varchar(255), "supplierId" uniqueidentifier NOT NULL, "description" varchar(250), CONSTRAINT "PK_e99a304491bc41175120b95e8e3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_capacity_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_70392d53a546fb8bbddf935f663" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f74998ab660d2b3a4c46ca5fcdc" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "code" varchar(250), "value" int NOT NULL, "isChosen" bit NOT NULL CONSTRAINT "DF_8778b0e0c095068f54b693e21cf" DEFAULT 0, "supplierCapacityId" uniqueidentifier NOT NULL, CONSTRAINT "PK_70392d53a546fb8bbddf935f663" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_capacity_year_value" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ddc5be6d40f310cb890018cc460" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_520b5f6556093e8b9d0d91d792d" DEFAULT 0, "companyId" varchar(255), "value" nvarchar(255) NOT NULL, "year" nvarchar(255) NOT NULL, "supplierCapacityId" uniqueidentifier NOT NULL, CONSTRAINT "PK_ddc5be6d40f310cb890018cc460" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_notify" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f67711705a153029b934d361e6c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_dac687b0eacf2fe3ad4d975f369" DEFAULT 0, "companyId" varchar(255), "message" nvarchar(max), "messageFull" nvarchar(max), "url" nvarchar(max), "status" varchar(50) NOT NULL, "supplierId" uniqueidentifier NOT NULL, CONSTRAINT "PK_f67711705a153029b934d361e6c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "setting_role" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0819b0b74fb3d6f28b925aed289" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_9869387045f7339fcec474aece9" DEFAULT 0, "companyId" varchar(255), "role" varchar(50), "orgCompanyId" varchar(255), "orgBlockId" varchar(255), "orgDepartmentId" varchar(255), "orgPartId" varchar(255), "orgPositionId" varchar(255), "orgTreeId" varchar(255), "employeeId" uniqueidentifier, "jsonSetting" varchar(4000), CONSTRAINT "PK_0819b0b74fb3d6f28b925aed289" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_number" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a7836ddf7c1c6dd49367b0b6d24" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6830a0bacffd0cb6c05a0e553ae" DEFAULT 0, "companyId" varchar(255), "syncStatus" varchar(50), "approvalStatus" varchar(50), "code" int, "businessPartnerGroupType" varchar(30), "supplierId" uniqueidentifier, "supplierNumberRequestApproveId" uniqueidentifier, "businessPartnerGroupId" uniqueidentifier, "isRequestApprove" bit CONSTRAINT "DF_c6ffe5c637fb132b9e38076f5c5" DEFAULT 0, CONSTRAINT "PK_a7836ddf7c1c6dd49367b0b6d24" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_90209110c6739b8ad68a20030d" ON "supplier_number" ("supplierId") WHERE "supplierId" IS NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_f9011f5cd49dcab82a8faebb3b" ON "supplier_number" ("supplierNumberRequestApproveId") WHERE "supplierNumberRequestApproveId" IS NOT NULL`);
        await queryRunner.query(`CREATE TABLE "shipment_po" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_409fa159554620c245f7ee5eb86" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_65ba6279d990ec2175dec21ea52" DEFAULT 0, "companyId" varchar(255), "poId" uniqueidentifier NOT NULL, "shipmentId" uniqueidentifier NOT NULL, CONSTRAINT "PK_409fa159554620c245f7ee5eb86" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "inbound_document_handover" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_4b5783c7a7a62494a45d1c04672" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ce8e9176d6282e9a26277fb103c" DEFAULT 0, "companyId" varchar(255), "code" varchar(50), "name" varchar(150), "time" datetime, "fileAttach" varchar(250), "note" varchar(max), "inboundId" uniqueidentifier, CONSTRAINT "PK_4b5783c7a7a62494a45d1c04672" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shipment_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f6228898b4578ba672a2f794d11" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_089cdad0ba98306cd92d127c47c" DEFAULT 0, "companyId" varchar(255), "shipmentId" uniqueidentifier, "materialId" uniqueidentifier, "quantityInput" int, "quantityDocuments" int, "packageQuantity" int, "inboundId" uniqueidentifier, CONSTRAINT "PK_f6228898b4578ba672a2f794d11" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "inbound" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_837651a56a588fd82392d68a5fd" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7f631698ad22f4d64ecc5edb720" DEFAULT 0, "companyId" varchar(255), "status" varchar(50), "code" varchar(50), "name" varchar(150), "inboundNumber" varchar(36), "referenceId" varchar(50), "deliveryDate" datetime, "dateArrivalWarehouse" datetime, "employeeInchargeId" uniqueidentifier, "poId" uniqueidentifier, "expectWarehouseId" uniqueidentifier, "isSupplierCreate" bit CONSTRAINT "DF_a0bf610de6aa3c466f1af44f208" DEFAULT 0, "dateArrivalPort" datetime, CONSTRAINT "PK_837651a56a588fd82392d68a5fd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_08cefbb982da97567edebfa78e" ON "inbound" ("inboundNumber") `);
        await queryRunner.query(`CREATE TABLE "shipment_inbound" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_28a3a9a64f4b7976d97791b9dc0" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f79a16c225fa9c749501a08cb91" DEFAULT 0, "companyId" varchar(255), "inboundId" uniqueidentifier NOT NULL, "shipmentId" uniqueidentifier NOT NULL, CONSTRAINT "PK_28a3a9a64f4b7976d97791b9dc0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shipment_container" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c968af6ac10c1051f9d01cd14b8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a51902e08214e3b7e35a0a64a0f" DEFAULT 0, "companyId" varchar(255), "shipmentId" uniqueidentifier, "containerNumber" varchar(100), "sealNumber" varchar(100), "shipSealNumber" varchar(100), "containerType" varchar(100), "packageQuantity" int, CONSTRAINT "PK_c968af6ac10c1051f9d01cd14b8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shipment" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f51f635db95c534ca206bf7a0a4" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_1b8e4acadfe952422e0d874f009" DEFAULT 0, "companyId" varchar(255), "status" varchar(50), "code" varchar(50) NOT NULL, "shipmentNumber" varchar(50), "shipmentType" varchar(100), "routeId" varchar(36), "shippingType" varchar(100), "phone" varchar(20), "pwdAgent" varchar(150), "carRegNoInternal" varchar(50), "carRegNoExternal" varchar(50), "driverPhoneExternal" varchar(20), "driverPhoneInternal" varchar(20), "driverNameExternal" varchar(150), "driverNameInternal" varchar(150), "identityDriver" varchar(150), "fileUrl" varchar(2000), "fileName" varchar(1000), "description" varchar(4000), "shipmentRouteId" uniqueidentifier, "shipmentCostTypeId" uniqueidentifier, CONSTRAINT "PK_f51f635db95c534ca206bf7a0a4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_0557c7d7bfcedb7e91dc6dd890" ON "shipment" ("code") `);
        await queryRunner.query(`CREATE TABLE "shipment_type" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f2609a45bcd2f034fd9372cf46e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_63a0a4df51db366241a7b85d0a6" DEFAULT 0, "companyId" varchar(255), "status" varchar(50), "code" varchar(50) NOT NULL, "name" varchar(50), "description" varchar(4000), CONSTRAINT "PK_f2609a45bcd2f034fd9372cf46e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_49797bb4bc2ff5063b0b222849" ON "shipment_type" ("code") `);
        await queryRunner.query(`CREATE TABLE "shipment_route" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cb58558c5c365b5ae3bedd185ba" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_50374644b2e62ed1d04b87d433b" DEFAULT 0, "companyId" varchar(255), "status" varchar(50), "code" varchar(50) NOT NULL, "name" varchar(50), "description" varchar(4000), CONSTRAINT "PK_cb58558c5c365b5ae3bedd185ba" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_282584715bfdf3cb328b29fbe8" ON "shipment_route" ("code") `);
        await queryRunner.query(`CREATE TABLE "shipment_cost_type" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cd3ee7af9900b323d6d9c4f99cb" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a471d62d44b239cd594c0f1958a" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "status" varchar(50), "name" varchar(50), "description" varchar(4000), CONSTRAINT "PK_cd3ee7af9900b323d6d9c4f99cb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "inbound_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3f099067f3b718ea896a11f836a" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c55545361dedfc351bc85376ee5" DEFAULT 0, "companyId" varchar(255), "inboundId" uniqueidentifier, "materialId" uniqueidentifier, "poProductId" uniqueidentifier, "packageQuantity" int, "batchNumber" int, "quantityDelivery" int, CONSTRAINT "PK_3f099067f3b718ea896a11f836a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "inbound_container" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_18567b63d7551d9d09a1913baa7" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_659c436044e223089b1960c8543" DEFAULT 0, "companyId" varchar(255), "inboundId" uniqueidentifier, "containerNumber" varchar(100), "sealNumber" varchar(100), "shipSealNumber" varchar(100), "containerType" varchar(100), "packageQuantity" int, CONSTRAINT "PK_18567b63d7551d9d09a1913baa7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_confirm_code" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_490135bdc78c9553e7cda2d465e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7bf7b24c178944a464cb7d74e00" DEFAULT 0, "companyId" varchar(255), "userId" uniqueidentifier NOT NULL, "code" varchar(255) NOT NULL, "exDate" datetime NOT NULL, CONSTRAINT "PK_490135bdc78c9553e7cda2d465e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_access" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_089164b21580060b818f14c8386" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_4603ba74988d1c0dbbbb18254c6" DEFAULT 0, "companyId" varchar(255), "employeeId" uniqueidentifier NOT NULL, "serviceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_089164b21580060b818f14c8386" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "data_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f2191e6059b061c95cacb32c598" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_bac11dee3c1570865b730adae92" DEFAULT 0, "companyId" varchar(255), "tableName" varchar(250) NOT NULL, "relationId" varchar(255) NOT NULL, "description" varchar(250), "dataJson" nvarchar(max), CONSTRAINT "PK_f2191e6059b061c95cacb32c598" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract_member" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_74b83fa883c60039d3ffc7e8f97" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_dcdc5b32a449991b061cb560b58" DEFAULT 0, "companyId" varchar(255), "employeeId" uniqueidentifier NOT NULL, "contractRoleCode" varchar(50) NOT NULL, "description" varchar(250), CONSTRAINT "PK_74b83fa883c60039d3ffc7e8f97" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "language" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cc0a99e710eb3733f6fb42b1d4c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_4dc4cf2a978f9f5d197ae01ed61" DEFAULT 0, "companyId" varchar(255), "key" varchar(250) NOT NULL, "value" varchar(1000) NOT NULL, "languageType" varchar(50) NOT NULL, "path" varchar(500), "description" nvarchar(max), CONSTRAINT "PK_cc0a99e710eb3733f6fb42b1d4c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "language_key" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b5b3f368d1fcbb5a7e754d7f7e4" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7550d3bca6672a5fb3d32a742ea" DEFAULT 0, "companyId" varchar(255), "key" varchar(250) NOT NULL, "value" varchar(1000) NOT NULL, "languageType" varchar(50) NOT NULL, "path" varchar(500), "description" nvarchar(max), CONSTRAINT "PK_b5b3f368d1fcbb5a7e754d7f7e4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "condition_type" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9ef9146180d03a68d2f0c877864" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f9b745d2e25bf9f67ac3ae6e119" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(250) NOT NULL, "description" varchar(255), "percentageType" varchar(50), "headerItemType" varchar(50), CONSTRAINT "PK_9ef9146180d03a68d2f0c877864" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_bank" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_adfadb9382a02ed3f0c12c64597" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_0c57943c058d1f89a51882bbcef" DEFAULT 0, "companyId" varchar(255), "bankNumber" varchar(50) NOT NULL, "accountNumber" varchar(50), "bankUsername" varchar(250), "swiftCode" varchar(50), "iban" varchar(50), "fileAccount" varchar(4000), "supplierId" uniqueidentifier NOT NULL, "bankBranchId" uniqueidentifier NOT NULL, "bankId" uniqueidentifier NOT NULL, "regionId" uniqueidentifier NOT NULL, "countryId" uniqueidentifier NOT NULL, CONSTRAINT "PK_adfadb9382a02ed3f0c12c64597" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bank" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_7651eaf705126155142947926e8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_cf93523d1b17dee1c8bf84ca794" DEFAULT 0, "companyId" varchar(255), "name" varchar(250), "code" varchar(50) NOT NULL, "description" nvarchar(4000), CONSTRAINT "PK_7651eaf705126155142947926e8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bank_branch" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_758e63cf6e1278222f4c6538561" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_31782b47be48596359076e23208" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "countryId" uniqueidentifier NOT NULL, "regionId" uniqueidentifier, "address" nvarchar(4000), "areaCode" varchar(50), "bankBranch" varchar(250), "swift" varchar(250), "bankGroupType" varchar(100), "accountNumber" varchar(50), "postbank" bit CONSTRAINT "DF_a99b5f80513211bc627fa14dcfd" DEFAULT 0, "description" nvarchar(4000), "bankId" uniqueidentifier NOT NULL, CONSTRAINT "PK_758e63cf6e1278222f4c6538561" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "country" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_bf6e37c231c4f4ea56dcd887269" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e3728bd73268ee70fbdd515e756" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(max), CONSTRAINT "PK_bf6e37c231c4f4ea56dcd887269" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "region" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5f48ffc3af96bc486f5f3f3a6da" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2dfa9939deab8dc13ba15032c57" DEFAULT 0, "companyId" varchar(255), "code" varchar(100) NOT NULL, "name" varchar(250) NOT NULL, "description" nvarchar(max), "countryId" uniqueidentifier, CONSTRAINT "PK_5f48ffc3af96bc486f5f3f3a6da" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "business_partner_group" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_730be7460fe5a3224951cafb7eb" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_87975354b66bf29f938b7396b35" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(50) NOT NULL, "description" varchar(250), "startCode" bigint, "endCode" bigint, CONSTRAINT "PK_730be7460fe5a3224951cafb7eb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "purchasing_area" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3e69e700c4432e9d5b3cd21fbd8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_541fa7ae41c64c8c94a51f68cdd" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(500) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(max), CONSTRAINT "PK_3e69e700c4432e9d5b3cd21fbd8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "factory_supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_8b7c7c2bed03605b4ac7a17b49f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_9ccff25e95305f4596dd3234e7f" DEFAULT 0, "companyId" varchar(255), "name" varchar(250), "code" varchar(50), "address" varchar(250), "phone" varchar(50), "fax" varchar(50), "supplierId" uniqueidentifier NOT NULL, CONSTRAINT "PK_8b7c7c2bed03605b4ac7a17b49f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "request_update_supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_61a26dddcd3d2a8034936949e3c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_bfdee03b608910b566160e43138" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(255), "updateType" varchar(100) NOT NULL, "reasonUpdate" varchar(250), "isPL" bit CONSTRAINT "DF_4df8ebd9fd4c65ee0e0c45278e1" DEFAULT 0, "isNL" bit CONSTRAINT "DF_c30024beda8775b4ebb8289f3c1" DEFAULT 0, "isLockSupplier" bit CONSTRAINT "DF_a9db9457722330a7f8775f1cec5" DEFAULT 0, "isLockSupplierService" bit CONSTRAINT "DF_92a6556d76dfb5881094b151a3c" DEFAULT 0, "lockType" varchar(100), "status" varchar(100), "jsonLaw" nvarchar(max), "jsonCapacity" nvarchar(max), "isApproved" bit NOT NULL CONSTRAINT "DF_bad10c1c99da61f4bb2f6c83c60" DEFAULT 0, "oldJson" nvarchar(max), "newJson" nvarchar(max), "supplierId" uniqueidentifier, "supplierServiceId" uniqueidentifier, CONSTRAINT "PK_61a26dddcd3d2a8034936949e3c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "plan_site_assessment" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b9d4767393f9dfcb29324425366" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_599302e045ba52ac30aa7df850b" DEFAULT 0, "companyId" varchar(255), "status" nvarchar(50), "code" nvarchar(50), "name" nvarchar(150), "description" nvarchar(max), CONSTRAINT "PK_b9d4767393f9dfcb29324425366" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "site_assessment" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5dedc38aaa3f3804e387cea2306" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a798457491e30cec9b759f32cbd" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "dateAssessment" date NOT NULL, "deadlineSupplierReply" date, "status" varchar(100), "supplierReplyStatus" varchar(50), "isSendSupplier" bit NOT NULL CONSTRAINT "DF_90187ca380aba90e102ddf48b04" DEFAULT 0, "isApprovedSent" bit NOT NULL CONSTRAINT "DF_117f242a6e202a30e2d830fe25f" DEFAULT 0, "description" nvarchar(4000), "comment" nvarchar(4000), "suggestion" nvarchar(4000), "supplierId" uniqueidentifier, "factorySupplierId" uniqueidentifier, "listEmployeeId" varchar(4000), "plantId" uniqueidentifier, "serviceId" uniqueidentifier, "plantSiteAssessmentId" uniqueidentifier, CONSTRAINT "PK_5dedc38aaa3f3804e387cea2306" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "criteria_site_assessment_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_12100bae224d1f9baaff9aecdef" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_0bd618c91e34a0ca6c76a92ee5b" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "value" int NOT NULL, "criteriaSiteAssessmentId" uniqueidentifier NOT NULL, CONSTRAINT "PK_12100bae224d1f9baaff9aecdef" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "criteria_site_assessment" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_2abee685a3e18ea6cd316c08403" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_561fbea44018517b9ea79e6cda4" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_19b5c652611eb9259248b7f2c8b" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_75c14bd7486d4443a93bc5e2794" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_a2195f1ce0ffb06fd7c750381fa" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_a74ef2d5af877cb765e34204890" DEFAULT 'string', "percent" float CONSTRAINT "DF_0cd78d16949e8a7f9a54743d897" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_e59bef9cd1b0429df3cb852dda2" DEFAULT 1, "description" varchar(250), "supplierReply" varchar(400), "supplierReplyList" varchar(100), "supplierReplyNumber" int, "evaluation" varchar(400), "evaluationNumber" int, "evaluationList" varchar(50), "parentId" uniqueidentifier, "scoreDLC" int, "score" int, "scoreEvaluation" int, "requiredMin" int, "isHighlight" bit NOT NULL CONSTRAINT "DF_2a9d4b74efc63267f2048eac1c2" DEFAULT 0, "hightlightValue" int, "fileUrl" varchar(250), "fileName" varchar(250), "siteAssessmentId" uniqueidentifier NOT NULL, CONSTRAINT "PK_2abee685a3e18ea6cd316c08403" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_upgrade" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b12b960daf222d2f863f4c3f40e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ab3f57b3c910c723937164d4e32" DEFAULT 0, "companyId" varchar(255), "dateApprove" datetime, "status" varchar(50), "supplierId" uniqueidentifier, "fileUrl" nvarchar(1000), "fileName" nvarchar(250), "description" nvarchar(250), CONSTRAINT "PK_b12b960daf222d2f863f4c3f40e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_upgrade_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a26652f1cc0a70c33d86a5a5081" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_dbb766f8609e8a299d94335d7aa" DEFAULT 0, "companyId" varchar(255), "evaluationHistoryPurchaseId" uniqueidentifier, "supplierUpgradeId" uniqueidentifier, CONSTRAINT "PK_a26652f1cc0a70c33d86a5a5081" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "pr_item_compoment" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_2d53f8908f37b89b0c6113dfc40" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_5c23bb750ee4e097570aac79dcf" DEFAULT 0, "companyId" varchar(255), "prId" uniqueidentifier, "prItemId" uniqueidentifier, "quantity" int CONSTRAINT "DF_3485bfdf7128fe1e9fb18a88312" DEFAULT 0, "shortText" varchar(500), "materialId" uniqueidentifier, CONSTRAINT "PK_2d53f8908f37b89b0c6113dfc40" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "evaluation_history_purchase_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_62f316ebfd3e0813c319ef9b49b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_795ec4e61197c20beee31f0a3de" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "value" int NOT NULL, "evaluationHistoryPurchaseDetailId" varchar(255) NOT NULL, "evaluationHistoryPurchaseId" uniqueidentifier, CONSTRAINT "PK_62f316ebfd3e0813c319ef9b49b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "evaluation_history_purchase_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0f012ff70acb0c783cbe9a49791" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_92b0bc54d2ae98a289ad9e715e4" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_cfce9395bad1238609978bfcc1f" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_8cb9bda26cc9d91da6699b0d80a" DEFAULT 0, "percent" float CONSTRAINT "DF_e343ae9c47dd8d2f737dc808c19" DEFAULT 0, "pointEvaluation" int CONSTRAINT "DF_f5fbf3c1c516302b933d4b7c44a" DEFAULT 0, "level" int NOT NULL CONSTRAINT "DF_d5425db19702b8cd53eb25fd4b2" DEFAULT 1, "scoreEvaluation" int, "fileUrl" varchar(250), "fileName" varchar(250), "description" nvarchar(max), "note" nvarchar(max), "evaluationHistoryPurchaseId" uniqueidentifier NOT NULL, "parentId" uniqueidentifier, CONSTRAINT "PK_0f012ff70acb0c783cbe9a49791" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "evaluation_history_purchase_employee" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9b311d4613267b36efc3a1e1cbe" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_54d68c33035e2aa946dcb3a4df5" DEFAULT 0, "companyId" varchar(255), "evaluationHistoryPurchaseId" uniqueidentifier NOT NULL, "employeeId" uniqueidentifier NOT NULL, CONSTRAINT "PK_9b311d4613267b36efc3a1e1cbe" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "evaluation_history_purchase" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_8672b9c0db7e93bc0809ea2ead2" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_06a8b1b0abef75da1b5e2e088d4" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "status" varchar(50) NOT NULL, "evaluationDate" datetime NOT NULL, "reasonUpdate" varchar(250), "serviceId" uniqueidentifier, "supplierId" uniqueidentifier NOT NULL, "evaluationStartMonth" datetime NOT NULL, "evaluationEndMonth" datetime NOT NULL, "description" nvarchar(max), "comment" nvarchar(max), "suggestion" nvarchar(max), "totalScore" float CONSTRAINT "DF_ecdc4fe78e1a24cc4e446c4621d" DEFAULT 0, "rating" varchar(50), CONSTRAINT "PK_8672b9c0db7e93bc0809ea2ead2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "kpi_company" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_99bbcf3bc6eb66484b01bb9bfcb" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_8d7e12509d375d4d36d629a84ba" DEFAULT 0, "companyId" uniqueidentifier, "kpiId" uniqueidentifier, CONSTRAINT "PK_99bbcf3bc6eb66484b01bb9bfcb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "kpi_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_03b6929f8d4136e5fe69d60a576" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b450dad6d15677e58cf22d11f7d" DEFAULT 0, "companyId" varchar(255), "name" varchar(250), "value" int, "kpiDetailId" uniqueidentifier, CONSTRAINT "PK_03b6929f8d4136e5fe69d60a576" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "kpi_position" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_821bc8688ea083077d333264d5e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_43eec311a819f0351d521904288" DEFAULT 0, "companyId" varchar(255), "positionId" uniqueidentifier, "kpiId" uniqueidentifier, "organnizationId" varchar(255), CONSTRAINT "PK_821bc8688ea083077d333264d5e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "position" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b7f483581562b4dc62ae1a5b7e2" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_8e754df7ca7446f6d479ecd47c2" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "code" varchar(50) NOT NULL, "description" varchar(max), "plantId" uniqueidentifier, CONSTRAINT "PK_b7f483581562b4dc62ae1a5b7e2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "kpi_permission_position" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cce6cd5e053e6c5e6ad60200cd1" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7ed73377d8d4b417b24e9ffc711" DEFAULT 0, "companyId" varchar(255), "positionId" uniqueidentifier, "kpiId" uniqueidentifier, "kpiDetailId" uniqueidentifier, "organnizationId" varchar(255), CONSTRAINT "PK_cce6cd5e053e6c5e6ad60200cd1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "kpi_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b34b7c62438ee7506b79104fdc5" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6389fab66eb69b43a2e4b37b946" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_de2ec814bf6b5f0ce80effe507d" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_4cb7dd04c3caa38da34dab1a999" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_89b87be179ad9d8a97332ed9c4f" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_804d09c09bedde00d08e91182a3" DEFAULT 'string', "percent" float CONSTRAINT "DF_2612d36aab24280355ca5a99644" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_a9a2c368fe166cabad98dca220a" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "isHighlight" bit NOT NULL CONSTRAINT "DF_44d4e01c37f3493c591d4883357" DEFAULT 0, "hightlightValue" int, "kpiId" uniqueidentifier, CONSTRAINT "PK_b34b7c62438ee7506b79104fdc5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "kpi_scale" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ebb16ff0acfe40525dcd5ba5568" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_01dd599f1427bcd02711890426b" DEFAULT 0, "companyId" varchar(255), "kpiId" uniqueidentifier, "blockBelow" float CONSTRAINT "DF_9040b790bf5b5e55750c7f6e6a3" DEFAULT 0, "blockOn" float CONSTRAINT "DF_b5b22c26765b6f498ab6a5efc05" DEFAULT 0, "point" varchar(50), CONSTRAINT "PK_ebb16ff0acfe40525dcd5ba5568" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "kpi_permission" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_19b19e19432550c20e81a88014e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e298b169e73d9eceba31117ed38" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_7fd3e65e86f77a7ac268fc585d0" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_04c39b43f1d08674793a4286a95" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_b130636575c418bb7319534d152" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_a689cb5f24dddb839de3b69fd37" DEFAULT 'string', "percent" float CONSTRAINT "DF_519824e93325f70f665f9a0f17e" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_bd877f98ededf1c1c55de0b31f3" DEFAULT 1, "description" varchar(250), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "isHighlight" bit NOT NULL CONSTRAINT "DF_c45f5571fd5df6ce48579c9f530" DEFAULT 0, "hightlightValue" int, "kpiId" uniqueidentifier, CONSTRAINT "PK_19b19e19432550c20e81a88014e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "kpi" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_56589835e31cc0331684d2d28a7" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3d2df9a40711b1d11bacf172456" DEFAULT 0, "companyId" uniqueidentifier, "code" varchar(50) NOT NULL, "name" varchar(250) NOT NULL, "type" varchar(50) NOT NULL, "status" varchar(50) NOT NULL, "dateFrom" datetime, "description" varchar(max), CONSTRAINT "PK_56589835e31cc0331684d2d28a7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "ticket_evaluation_kpi_list_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a359546c8d081299742c405cfb1" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_852b1b527f3631e3b94efc8ba71" DEFAULT 0, "companyId" varchar(255), "name" varchar(250), "value" int, "ticketEvaluationKpiDetailId" uniqueidentifier, CONSTRAINT "PK_a359546c8d081299742c405cfb1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "ticket_evaluation_kpi_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c2c76d643167ba675bb5780065d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_4f9bee6767e53484a9f6d5d72dd" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_a8a727f7d50fb580c41dbcc304e" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_47c1a442ab1ac83ba0ddca6c59d" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_ff7a8de5d9fac90cb678d2692fa" DEFAULT 1, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_931ca08cfc45ce6b3bfc434f982" DEFAULT 'string', "percent" float CONSTRAINT "DF_8a1cdc203eb7515e96fa49ad2c3" DEFAULT 0, "percentRule" bigint, "percentDownRule" bigint, "level" int NOT NULL CONSTRAINT "DF_2d12a3e00b5054441edf825a121" DEFAULT 1, "description" varchar(250), "evaluate" float CONSTRAINT "DF_f5235321cf88f97802a5084d84a" DEFAULT 0, "evaluationString" varchar(400), "evaluationNumber" int, "evaluationList" varchar(50), "attachedFile" varchar(4000), "parentId" uniqueidentifier, "scoreDLC" int, "requiredMin" int, "isHighlight" bit NOT NULL CONSTRAINT "DF_9fa61c9a7f16f2868232a6b020d" DEFAULT 0, "hightlightValue" int, "isReason" bit CONSTRAINT "DF_849c4b0dea886afcf517cdcba51" DEFAULT 0, "reason" varchar(max), "ticketEvaluationKpiId" uniqueidentifier, "kpiDetailId" varchar(255), "isReChecked" bit CONSTRAINT "DF_74d818947b49628ad6f47d6be8a" DEFAULT 0, CONSTRAINT "PK_c2c76d643167ba675bb5780065d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "ticket_evaluation_kpi" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_e73413847fda1bbc0552cf22af8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_8b2122883f4e6464348718665ac" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "status" varchar(50) NOT NULL, "dateFrom" datetime, "typePermissionKpi" varchar(250), "quarterType" varchar(250), "sumTimeData" datetime, "sumDayData" datetime, "evaluate" varchar(max), "comment" varchar(max), "isResultLock" bit CONSTRAINT "DF_ee679f8fc8b5428a3a3d8844033" DEFAULT 0, "isCheckLock" bit CONSTRAINT "DF_e88e726a2631c742085804ec6f3" DEFAULT 0, "totalScore" float CONSTRAINT "DF_127c9d5211fbd796a531679b4f3" DEFAULT 0, "ratingType" varchar(50), "kpiId" uniqueidentifier, CONSTRAINT "PK_e73413847fda1bbc0552cf22af8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "part" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_58888debdf048d2dfe459aa59da" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e6395546d258661ac27d7329e56" DEFAULT 0, "companyId" varchar(255), "name" varchar(250) NOT NULL, "code" varchar(50) NOT NULL, "description" varchar(max), CONSTRAINT "PK_58888debdf048d2dfe459aa59da" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "ticket_evaluation_kpi_employee" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9f88ab703462f80f9a716a7904f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b528585713b663fbae3984c2e28" DEFAULT 0, "companyId" uniqueidentifier, "ticketEvaluationKpiId" uniqueidentifier, "employeeId" uniqueidentifier, "blockId" uniqueidentifier, "departmentId" uniqueidentifier, "partId" uniqueidentifier, "positionId" uniqueidentifier, CONSTRAINT "PK_9f88ab703462f80f9a716a7904f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "block" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_d0925763efb591c2e2ffb267572" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ba5b8c0bfe01fd5de73e6764086" DEFAULT 0, "companyId" uniqueidentifier, "name" varchar(250) NOT NULL, "code" varchar(50) NOT NULL, "description" varchar(max) NOT NULL, "plantId" uniqueidentifier, CONSTRAINT "PK_d0925763efb591c2e2ffb267572" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_service_history" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_712fda2ea52bdf37c9952e2cfbe" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ea27c149d304a903dea59ffbbce" DEFAULT 0, "companyId" varchar(255), "approver" varchar(200) NOT NULL, "dataJson" nvarchar(max), "supplierServiceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_712fda2ea52bdf37c9952e2cfbe" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_acceptance_employee" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b75eacf663a2fb43791ada1459d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7599118cfc504cdf83c817bf02a" DEFAULT 0, "companyId" varchar(255), "employeeId" uniqueidentifier NOT NULL, "poAcceptanceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_b75eacf663a2fb43791ada1459d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_acceptance" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_eb222484c689f78c66f6607b98b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_839a0da864133b7e092ee054a5c" DEFAULT 0, "companyId" varchar(255), "poId" uniqueidentifier NOT NULL, "acceptanceNumber" varchar(50), "acceptanceObject" varchar(50), "address" varchar(250), "handoverTime" datetime, "descriptionAcceptance" varchar(500), "acceptanceResults" varchar(500), "attachedFile" varchar(500), CONSTRAINT "PK_eb222484c689f78c66f6607b98b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "permission" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3b8b97af9d9d8807e41e6f48362" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c08ab637721a267cc7c24486820" DEFAULT 0, "companyId" varchar(255), "code" varchar(100) NOT NULL, "name" varchar(150) NOT NULL, "platform" varchar(100) NOT NULL, "description" varchar(max), "roleStringify" varchar(max), CONSTRAINT "PK_3b8b97af9d9d8807e41e6f48362" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "permission_employee" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_da6ed3d90b11272e35b9f65b15a" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_5a3a0d457f8d3d5f9de9b315c89" DEFAULT 0, "companyId" varchar(255), "roleStringify" varchar(max), "employeeId" uniqueidentifier NOT NULL, CONSTRAINT "PK_da6ed3d90b11272e35b9f65b15a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_799ca14e17fb8bbb87908b577f" ON "permission_employee" ("employeeId") WHERE "employeeId" IS NOT NULL`);
        await queryRunner.query(`CREATE TABLE "permission_approve" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ae1140b13aa35b8d5373eae0ef6" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2121c2f8c3e6380ac7d046f5493" DEFAULT 0, "companyId" varchar(255), "targetId" varchar(255), "entityName" nvarchar(50) NOT NULL, "type" nvarchar(50) NOT NULL, "level" int, "approveType" nvarchar(50) NOT NULL, "mustApproveAll" bit CONSTRAINT "DF_da39d2d01fe15d07b26b004c59f" DEFAULT 0, "departmentCode" varchar(255), "approved" bit CONSTRAINT "DF_bfcee5a5b89194acc0bf55777c7" DEFAULT 0, "reject" bit CONSTRAINT "DF_26312fc5e986605f881a3602db2" DEFAULT 0, "comment" varchar(max), "employeeId" uniqueidentifier, CONSTRAINT "PK_ae1140b13aa35b8d5373eae0ef6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "organizational_tree" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_7c522f5f0f7e725959937c2d6a0" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_b568deca1a13a21262c3f07727e" DEFAULT 0, "companyId" varchar(255), "type" nvarchar(max), "name" nvarchar(50), "lstLevelPrevious" nvarchar(max), "entityName" nvarchar(50) NOT NULL, "targetId" varchar(255), "parentId" varchar(255), "level" int, "lft" int NOT NULL, "rgt" int NOT NULL, CONSTRAINT "PK_7c522f5f0f7e725959937c2d6a0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "permission_individual" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_80cb1226cfa331f7acd428178c5" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_8de01923aefb3acc97565f77ccd" DEFAULT 0, "companyId" varchar(255), "roleStringify" nvarchar(max), "employeeId" uniqueidentifier NOT NULL, CONSTRAINT "PK_80cb1226cfa331f7acd428178c5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_47dbeb860b692c2984bd1ee520" ON "permission_individual" ("employeeId") WHERE "employeeId" IS NOT NULL`);
        await queryRunner.query(`CREATE TABLE "business_type" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_dcc57dda934350221e1ff807bfa" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_47426c1bb8d7df7bc095b0eb891" DEFAULT 0, "companyId" varchar(255), "status" varchar(50), "code" varchar(150) NOT NULL, "name" varchar(150), "description" varchar(max), CONSTRAINT "PK_dcc57dda934350221e1ff807bfa" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_potential_upgrade" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b2fbefb0eb15e955b16357617dd" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_fec40aed21f70b7b6e963cf3e1c" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "status" varchar(200), "supplierId" uniqueidentifier, "jsonSupplier" nvarchar(max), CONSTRAINT "PK_b2fbefb0eb15e955b16357617dd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "action_log" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_63cffa5d8af90621882f0388359" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_0e6fff93e336f81c99b5dd36ea0" DEFAULT 0, "companyId" varchar(255), "createdByName" nvarchar(250) NOT NULL, "status" nvarchar(150), "description" nvarchar(max), "planSiteAssessmentId" varchar(36), "siteAssessmentId" varchar(36), "targetId" varchar(255), "entityName" nvarchar(50), "jsonOld" nvarchar(max), "jsonNew" nvarchar(max), CONSTRAINT "PK_63cffa5d8af90621882f0388359" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_pr" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_53c905e057325f1c1485affcb08" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7a208a8aca1af13a17d539f6122" DEFAULT 0, "companyId" varchar(255), "poId" uniqueidentifier, "prId" uniqueidentifier, CONSTRAINT "PK_53c905e057325f1c1485affcb08" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "template_lead_time" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ea4ba426655c0f173ddaf1153fa" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_caa28e7ad7419e8c94dee9ea053" DEFAULT 0, "companyId" varchar(255), "code" nvarchar(250), "headerLeadTimeId" uniqueidentifier, "numberOfDay" int, "fomular" nvarchar(max), "isSum" bit CONSTRAINT "DF_e7a235766e2e66e289cd6b2f6a9" DEFAULT 0, "sort" int CONSTRAINT "DF_a96cdba96e7990432438a21e41c" DEFAULT 0, CONSTRAINT "PK_ea4ba426655c0f173ddaf1153fa" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_lead_time" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0c6c781ce43587cf132bb34eda8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ae79bfe0b55ae0057b8d183a336" DEFAULT 0, "companyId" varchar(255), "code" nvarchar(250), "headerLeadTimeId" uniqueidentifier, "poId" uniqueidentifier, "numberOfDay" int, "sort" int CONSTRAINT "DF_88cbe93082d8978e8d08fefa8bd" DEFAULT 0, "fomular" nvarchar(max), "isSum" bit CONSTRAINT "DF_6ed2fb263c09f6ff62f4161eb0c" DEFAULT 0, "numberOfDaySupplier" int CONSTRAINT "DF_12349a9702817e848cf246e9fb0" DEFAULT 0, CONSTRAINT "PK_0c6c781ce43587cf132bb34eda8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "header_lead_time" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_bcb37c8947671638d31c13ec363" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f1c7292f3c394d08e6176bcc4e7" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(250), "code" nvarchar(50) NOT NULL, CONSTRAINT "PK_bcb37c8947671638d31c13ec363" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "pr_item_child" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_483038ac692cca2c511509260d9" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_7f02e0dca9cad9976814a6ce482" DEFAULT 0, "companyId" varchar(255), "prId" uniqueidentifier, "prItemId" uniqueidentifier, "quantity" int CONSTRAINT "DF_e997dd71e795c58f78b937027ab" DEFAULT 0, "shortText" varchar(500), "materialId" uniqueidentifier, "assetCode" nvarchar(max), "assetDesc" nvarchar(max), "io" nvarchar(max), "ioName" nvarchar(max), "iotype" nvarchar(max), "relstatus" nvarchar(max), "prItemNewId" varchar(255), "prItemOldId" varchar(255), CONSTRAINT "PK_483038ac692cca2c511509260d9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_product" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f1d4d48ce603faf6ffe241ca36f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c8e437ae16acd73eab48e8d7d28" DEFAULT 0, "companyId" varchar(255), "poId" uniqueidentifier, "materialId" uniqueidentifier, "note" varchar(250), "quantityDocuments" int, "quantityInput" int, "quantityPo" int, "quantityAcceptances" int, "price" int, "priceNew" int, "description" varchar(250), "itemCode" varchar(250), "serviceId" uniqueidentifier, "restQuantity" int CONSTRAINT "DF_1d79e2f182ac98479f14ee60b44" DEFAULT 0, "totalPrice" bigint, "shortText" varchar(250), "isConfirm" bit CONSTRAINT "DF_37eb6b29aaeab9cf3bb5d37164f" DEFAULT 0, "unitId" uniqueidentifier, "prId" varchar(255), "prItemId" varchar(255), "recommendedPurchaseId" varchar(255), "recommendedPurchasePrId" varchar(255), "poPrId" uniqueidentifier, "fund" nvarchar(max), "fp" nvarchar(max), "fpname" nvarchar(max), "fc" nvarchar(max), "fcname" nvarchar(max), "ci" nvarchar(max), "ciname" nvarchar(max), "budgetperiod" nvarchar(max), "budget" int CONSTRAINT "DF_40f0944dfad326b511ebac70b5e" DEFAULT 0, CONSTRAINT "PK_f1d4d48ce603faf6ffe241ca36f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po_history_status_execution" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_85ff67d6dc6c32b0b3b96695e89" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_4ceddd7fe3fc88ef2ed7ec757c8" DEFAULT 0, "companyId" varchar(255), "statusCurrent" varchar(150), "statusConvert" varchar(150), "poId" uniqueidentifier NOT NULL, "employeeId" uniqueidentifier, "supplierId" uniqueidentifier, "description" varchar(500), CONSTRAINT "PK_85ff67d6dc6c32b0b3b96695e89" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "po" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_23f862cda180f31f052b15f39e3" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f9e0a8f156093b268898ba16a42" DEFAULT 0, "companyId" uniqueidentifier, "code" varchar(250) NOT NULL, "title" varchar(250), "name" varchar(250), "supplierId" uniqueidentifier, "bidId" uniqueidentifier, "prIds" nvarchar(max), "prId" uniqueidentifier, "contractId" uniqueidentifier, "auctionId" uniqueidentifier, "offerId" uniqueidentifier, "contractPaymentPlanId" uniqueidentifier, "externalMaterialGroupId" uniqueidentifier, "incotermId" uniqueidentifier, "currencyId" uniqueidentifier, "status" varchar(50) NOT NULL, "budgetStatus" varchar(100), "orderStatus" varchar(50), "operator" varchar(250), "standard" varchar(250), "description" varchar(250), "paymentPlanType" varchar(250), "referenceSourceType" varchar(250), "referenceSourceNumbers" nvarchar(max), "reason" varchar(1000), "deliveryDate" datetime, "receivingDelivery" varchar(250), "isChild" bit CONSTRAINT "DF_278ba7fa4a595533574ad1ff691" DEFAULT 0, "parentId" uniqueidentifier, "lstPrId" varchar(4000), "recommendedPurchaseId" uniqueidentifier, CONSTRAINT "PK_23f862cda180f31f052b15f39e3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "payment_progress" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_7d684f89af5a8aaeed033d7073d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a9d1074f3c4317df036779638e4" DEFAULT 0, "companyId" varchar(255), "poId" uniqueidentifier, "contractId" uniqueidentifier, "name" varchar(250), "percent" int NOT NULL CONSTRAINT "DF_47910f953b541926202224d1466" DEFAULT 0, "time" datetime, "description" nvarchar(max), "money" float CONSTRAINT "DF_736dd1bafbe5a5305bdfb4488dd" DEFAULT 0, "suggestPaid" bigint NOT NULL CONSTRAINT "DF_585f6885dd7257a08ac8e3ea202" DEFAULT 0, "paymentStatus" varchar(50) CONSTRAINT "DF_879c998c1bb5907b0b94f94785b" DEFAULT 'UNPAIND', "historyNote" nvarchar(max), "paymentMethodId" uniqueidentifier, "requiredDocument" nvarchar(max), CONSTRAINT "PK_7d684f89af5a8aaeed033d7073d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract_document_handover" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_dc8447073a47c166395b1e28792" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_3354796c50428bd255d0c4e87a3" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(150) NOT NULL, "note" varchar(max), "supplierId" uniqueidentifier, "contractId" uniqueidentifier, CONSTRAINT "PK_dc8447073a47c166395b1e28792" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contract" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_17c3a89f58a2997276084e706e8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_292ab4e58f39b83de2d83994b26" DEFAULT 0, "companyId" varchar(255), "code" nvarchar(50) NOT NULL, "name" nvarchar(250), "nameSeller" nvarchar(250), "addressSeller" nvarchar(500), "emailSeller" nvarchar(250), "representativeSeller" nvarchar(250), "nameBuyer" nvarchar(250), "addressBuyer" nvarchar(500), "telBuyer" nvarchar(50), "supplierId" uniqueidentifier, "fileAttach" nvarchar(max), "effectiveDate" datetime, "expiredDate" datetime, "status" varchar(50), "description" nvarchar(max), "value" bigint CONSTRAINT "DF_102e2b2fb1e87167dcbbe893af5" DEFAULT 0, "totalAmount" int, "weighing" int, "quality" nvarchar(250), "manufacturer" nvarchar(250), "origin" nvarchar(250), "contractNumber" varchar(100), "deliveryDate" datetime, "contractDate" datetime, "referenceSource" varchar(100), "referenceSourceNumber" nvarchar(max), "fileOther" nvarchar(max), "eContract" varchar(100), "currencyId" uniqueidentifier, "externalMaterialGroupId" uniqueidentifier, "incotermId" uniqueidentifier, "lstPrId" varchar(4000), "prId" uniqueidentifier, "bidId" uniqueidentifier, "auctionId" uniqueidentifier, "offerId" uniqueidentifier, "recommendedPurchaseId" uniqueidentifier, CONSTRAINT "PK_17c3a89f58a2997276084e706e8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_pr" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_533403e11e3cda82aa5dc9411c4" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_cf96b191e7ab27a9aa266ee8359" DEFAULT 0, "companyId" varchar(255), "bidId" uniqueidentifier NOT NULL, "prId" uniqueidentifier, CONSTRAINT "PK_533403e11e3cda82aa5dc9411c4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "pr" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_537a62e85ab9aab677d7988a3e7" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_193091a0e7fc489859c94fafec7" DEFAULT 0, "companyId" varchar(255), "sourceType" varchar(50), "code" varchar(250) NOT NULL, "sapCode" varchar(100), "quantity" int, "status" varchar(50) NOT NULL, "ztCode" varchar(50), "zType" varchar(50), "zRelease" varchar(5), "prType" varchar(50), "budgetStatus" varchar(100), "createdTimeAt" datetime, "isAllowBid" bit NOT NULL CONSTRAINT "DF_506074fd506b062e2638734f92f" DEFAULT 1, "headerNote" nvarchar(max), "uses" nvarchar(max), "departmentRequired" nvarchar(max), "departmentId" uniqueidentifier, "prParentId" varchar(255), "requisitionerName" nvarchar(max), "requisitionerId" uniqueidentifier, "deletionIndicator" varchar(1), "budget" decimal(20,2) CONSTRAINT "DF_dd787d5f12c6be3a6561ee294a6" DEFAULT 0, "isParent" bit NOT NULL CONSTRAINT "DF_e52dcfdee8ccdf45a3510fae63e" DEFAULT 0, "plantId" uniqueidentifier, "isAlllowEdit" bit NOT NULL CONSTRAINT "DF_f82d7d7861e979c2ac4dc80b621" DEFAULT 0, "externalMaterialGroupId" uniqueidentifier, "lstPrChild" nvarchar(max), "isApprovedDone" bit CONSTRAINT "DF_b721ba5ba746f959714922df7b9" DEFAULT 0, "totalValue" bigint, "description" nvarchar(max), "isLockAllocation" bit CONSTRAINT "DF_818cc9b61165ec2656206ba8b0b" DEFAULT 0, "isSaveDraft" bit CONSTRAINT "DF_2643fe2593a7ab3784ebfbf7042" DEFAULT 0, "isAllocation" bit CONSTRAINT "DF_6a9a980b5eb4fa105dae84004ae" DEFAULT 0, "purchasingOrgId" uniqueidentifier, "purchasingGroupId" uniqueidentifier, "isCreateAllocation" bit NOT NULL CONSTRAINT "DF_eddb47857a764cf855781ce01f2" DEFAULT 0, "isReferenceSource" bit CONSTRAINT "DF_7e46c45fb360116a3a5350e9507" DEFAULT 0, "budgetMissing" decimal(20,2) CONSTRAINT "DF_6dc5bc9846f8e2c061a1a5b075e" DEFAULT 0, "lstExternalMaterialGroupId" nvarchar(max), "roundUpContId" uniqueidentifier, "lstPlantId" nvarchar(max), "lsPrType" nvarchar(max), "lstDepartmentId" nvarchar(max), "lstRequisitionerId" nvarchar(max), CONSTRAINT "PK_537a62e85ab9aab677d7988a3e7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IX_PR_COMPOSITE" ON "pr" ("prType", "status", "createdAt") `);
        await queryRunner.query(`CREATE TABLE "pr_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_6f5fcbd694aa613a9cf9439bde2" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e834a6a27b7d6dbc47f4eb8d1bf" DEFAULT 0, "companyId" varchar(255), "itemNo" varchar(255), "category" varchar(1), "materialId" uniqueidentifier, "shortText" varchar(1000), "quantity" int CONSTRAINT "DF_65d070e582fb92719ad53630496" DEFAULT 0, "plantId" uniqueidentifier, "plantCode" varchar(100), "unitCode" varchar(10), "unitId" uniqueidentifier, "ounCode" varchar(10), "ounId" uniqueidentifier, "materialGroupId" uniqueidentifier, "materialGroupCode" varchar(100), "purchasingGroupId" uniqueidentifier, "valuationType" varchar(10), "glAccountCode" varchar(50), "glAccountId" uniqueidentifier, "costCenterCode" varchar(50), "orderCode" varchar(50), "valuationPrice" bigint, "assetCode" nvarchar(max), "assetDesc" nvarchar(max), "subNoAset" varchar(3), "trackingNumber" varchar(10), "itemNote" nvarchar(255), "itemText" nvarchar(max), "deliveryText" nvarchar(max), "materialPoText" nvarchar(max), "batch" varchar(10), "receivedTime" datetime, "receivedDate" datetime, "deliveryDate" datetime, "itemClosed" varchar(1), "prId" uniqueidentifier, "serviceId" uniqueidentifier, "quantityBid" int CONSTRAINT "DF_92c3c36bcb79da6c06223c3fc98" DEFAULT 0, "status" varchar(50), "prStatus" varchar(50), "approveStatus" varchar(100), "blockStatus" varchar(100), "prParentId" varchar(255), "quantityItem" int CONSTRAINT "DF_b1bd7cb72e8c35af4206fae4f0f" DEFAULT 0, "budgetStatus" varchar(50), "budget" bigint, "minDeliveryDate" datetime, "maxDeliveryDate" datetime, "quantityAllocation" bigint CONSTRAINT "DF_6a5d843ba70d99797c87dd46f30" DEFAULT 0, "quantityRemaining" bigint CONSTRAINT "DF_57e44a1b044546dfbcae8d17142" DEFAULT 0, "fundCenter" varchar(1000), "fundProgram" varchar(1000), "isLockAllocation" bit CONSTRAINT "DF_1c76a52c1417f3c0453a4819333" DEFAULT 0, "externalMaterialGroupId" uniqueidentifier, "io" nvarchar(max), "ioName" nvarchar(max), "iotype" nvarchar(max), "relstatus" nvarchar(max), "requestDate" datetime, "priceUnit" bigint, "soItem" nvarchar(max), "purchasingGroupCode" varchar(255), "purchasingOrgId" uniqueidentifier, "purchasingOrgCode" varchar(255), "requistioner" nvarchar(max), "prItemType" nvarchar(max), "fp" nvarchar(max), "fc" nvarchar(max), "ci" nvarchar(max), "budgetPeriod" varchar(255), "total" bigint, CONSTRAINT "PK_6f5fcbd694aa613a9cf9439bde2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IX_PR_ITEMS_MAT" ON "pr_item" ("prId", "materialId") `);
        await queryRunner.query(`CREATE TABLE "media_file" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cac82b29eea888470cc40043b76" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_34d506b460c92d1ff3a9f2ee049" DEFAULT 0, "companyId" varchar(255), "fileUrl" varchar(250) NOT NULL, "fileName" varchar(250), "materialId" uniqueidentifier, "prItemId" uniqueidentifier, "shipmentId" uniqueidentifier, "businessPlanId" uniqueidentifier, "recommendedPurchaseId" uniqueidentifier, "contracyDocumentHandoverId" uniqueidentifier, "supplierUpgradeId" uniqueidentifier, CONSTRAINT "PK_cac82b29eea888470cc40043b76" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "material" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0343d0d577f3effc2054cbaca7f" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_38e58e1c93ae1bdbad635132215" DEFAULT 0, "companyId" varchar(255), "materialGroupId" uniqueidentifier, "name" varchar(1000) NOT NULL, "longText" nvarchar(max), "code" varchar(100), "oldCode" varchar(100), "materialTypeCode" varchar(100), "price" float, "leadTime" float, "description" nvarchar(max), "serviceId" varchar(255), "matGroup" varchar(1000), "sourcingCode" varchar(50), "plantId" uniqueidentifier, "materialTypeId" uniqueidentifier, "externalMaterialGroupCode" varchar(100), "externalMaterialGroupId" uniqueidentifier, "type" varchar(50), "purchasingSource" varchar(50), "unitCode" varchar(50), "unitId" uniqueidentifier, "valuationType" varchar(10), "isSync" bit CONSTRAINT "DF_0cbdf1f2d28eb568d872ffa3fa0" DEFAULT 0, "tolerance" decimal(20,4) CONSTRAINT "DF_c31fa1543644c8a26139ca1a55b" DEFAULT 0, "purchasingGroupId" uniqueidentifier, "numberMachinesBox" varchar(250), "lngth" decimal(20,3) CONSTRAINT "DF_e07eab3863bb442d5a5d3d12631" DEFAULT 0, "width" decimal(20,3) CONSTRAINT "DF_bc0b14d8617513aa34ffd8dc709" DEFAULT 0, "height" decimal(20,3) CONSTRAINT "DF_86b9c26d3c16be7931f9e5e8528" DEFAULT 0, "cmb" decimal(20,4) CONSTRAINT "DF_92ea8d0e7dd1179d12252a687ee" DEFAULT 0, "purchasingOrgId" uniqueidentifier, "purchaseTaxRate" float, "salesTaxRateInterpretation" float, "division" varchar(250), "divisionName" nvarchar(max), "standardPrice" decimal(20,2) CONSTRAINT "DF_e10ee8c5d1c5ab26422e363f878" DEFAULT 0, "valuationClass" varchar(250), "minSafetyStock" decimal(20,3) CONSTRAINT "DF_5c064070205e89d827da20bc2e9" DEFAULT 0, "safetyStock" decimal(20,3) CONSTRAINT "DF_3e3aed95cdbdeb91c00401949a6" DEFAULT 0, "deliveryTime" float, "grossWeight" decimal(20,3) CONSTRAINT "DF_3d84979f5bf5cf47d29cb5eefc6" DEFAULT 0, "netWeight" decimal(20,3) CONSTRAINT "DF_020d237d7d0a5b1e62b7b2b704d" DEFAULT 0, "transportationGroup" varchar(250), "englishName" nvarchar(max), "customsName" nvarchar(max), "unitOfMassCode" varchar(50), "unitOfMassId" uniqueidentifier, "unitOfVolumeCode" varchar(50), "unitOfVolumeId" uniqueidentifier, "size" varchar(50), "unitOfMeasurementCode" varchar(50), "unitOfMeasurementId" uniqueidentifier, CONSTRAINT "PK_0343d0d577f3effc2054cbaca7f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "material_plant" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_866fab908f58c573a97a55df624" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a901707da5316ad967953a61db9" DEFAULT 0, "companyId" varchar(255), "materialId" uniqueidentifier NOT NULL, "plantId" uniqueidentifier NOT NULL, "materialCode" varchar(250), "plantCode" varchar(250), "purchasingOrgId" uniqueidentifier, "purchasingGroupId" uniqueidentifier, "externalMaterialGroupId" uniqueidentifier, "price" float, "lastChange" datetime, "materialPriceId" uniqueidentifier, "lotSize" varchar(50), "mrpProfile" varchar(50), "mrpController" varchar(50), "minimumLotSize" decimal(20,3) CONSTRAINT "DF_02d8d601d957b90c3a48da5ac3c" DEFAULT 0, "mrpGroup" varchar(50), CONSTRAINT "PK_866fab908f58c573a97a55df624" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "plant" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_97e1eb0d045aadea59401ece5ba" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_80c77d7aa3345a38d0ea530a2b9" DEFAULT 0, "companyId" uniqueidentifier, "name" varchar(250) NOT NULL, "code" varchar(50) NOT NULL, "description" nvarchar(max), "companyCode" varchar(255), "purchasingOrganization" varchar(50), "address" varchar(1000), "district" varchar(500), "postCode" varchar(50), "country" varchar(250), "supplierSource" varchar(250), "isSap" bit NOT NULL CONSTRAINT "DF_2d7ceb21a7efbfe68450076da58" DEFAULT 0, "isAutoPo" bit NOT NULL CONSTRAINT "DF_09396e448b8c2a3f0930a899539" DEFAULT 0, CONSTRAINT "PK_97e1eb0d045aadea59401ece5ba" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "company" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_056f7854a7afdba7cbd6d45fc20" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_d481baf1fc95636e9828e9d4c1a" DEFAULT 0, "companyId" varchar(255), "code" varchar(50) NOT NULL, "name" varchar(50) NOT NULL, "shortName" varchar(100), "description" varchar(250), "address" varchar(1000), "district" varchar(500), "postCode" varchar(50), "country" varchar(250), "isHolding" bit NOT NULL CONSTRAINT "DF_1d5644405e2392f5f836ebdf1a9" DEFAULT 0, "level" varchar(50), "phone" varchar(20), "fax" varchar(20), "email" varchar(100), "website" varchar(250), "parentId" varchar(255), "purchasingOrgId" uniqueidentifier, CONSTRAINT "PK_056f7854a7afdba7cbd6d45fc20" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "flow_approve_base" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ac2205d4a77a47783557c2c5cbc" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_12fdde41d7f18a67cf52c6aa49f" DEFAULT 0, "companyId" varchar(255), "flowCode" varchar(50) NOT NULL, "type" varchar(50) NOT NULL CONSTRAINT "DF_c90e2b272e8ad9021ad47f7cccc" DEFAULT 'None', "departmentId" varchar(255), "blockId" varchar(255), "partId" varchar(255), "numberLevel" int, "applyCompany" bit CONSTRAINT "DF_ba8472768a64fb7dbacb4c3f2b9" DEFAULT 0, "applyBlock" bit CONSTRAINT "DF_6316ca468b51b670b4824ce2d46" DEFAULT 0, "applyAnother" bit CONSTRAINT "DF_fc0de25889447115698ef37b1cb" DEFAULT 0, "applyDepartment" bit CONSTRAINT "DF_455772bd11abbb03a88dad59438" DEFAULT 0, "applyPart" bit CONSTRAINT "DF_c67850f72a025038cd58eed75c2" DEFAULT 0, "level1ApproveAll" bit CONSTRAINT "DF_7201d38ef7fa45963001278d270" DEFAULT 0, "level2ApproveAll" bit CONSTRAINT "DF_3c352280821d3c6ab4b7f255059" DEFAULT 0, "level3ApproveAll" bit CONSTRAINT "DF_e30f9c149f0aa126f15669fb7d9" DEFAULT 0, "level4ApproveAll" bit CONSTRAINT "DF_a2277386695c12d6783552d28c9" DEFAULT 0, "level5ApproveAll" bit CONSTRAINT "DF_7c89c68a7f331c0b9847cf7166b" DEFAULT 0, "level6ApproveAll" bit CONSTRAINT "DF_a07306119bcc7a5f27f7613101a" DEFAULT 0, "status" varchar(255), CONSTRAINT "PK_ac2205d4a77a47783557c2c5cbc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "flow_approve_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_f8a20fb9ec06ef7a193051735ce" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a8f8a3788a9fd578253c81dabcb" DEFAULT 0, "companyId" varchar(255), "level" int NOT NULL, "possisonCode" varchar(50), "flowApproveId" uniqueidentifier, "flowBaseId" uniqueidentifier, "roleCompanyId" varchar(255), "departmentId" varchar(255), "blockId" varchar(255), "partId" varchar(255), "mustApproveAll" bit CONSTRAINT "DF_08554d34867bc956f1b51246dd5" DEFAULT 0, CONSTRAINT "PK_f8a20fb9ec06ef7a193051735ce" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "flow_approve" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a14489d1e345fa99f0efcf442fb" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_84add02b90bf5b99acb6dc8d69b" DEFAULT 0, "companyId" uniqueidentifier, "flowCode" varchar(50) NOT NULL, "type" varchar(50) NOT NULL CONSTRAINT "DF_bad090f364afe4f658f3ef46561" DEFAULT 'None', "departmentId" uniqueidentifier, CONSTRAINT "PK_a14489d1e345fa99f0efcf442fb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "department" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9a2213262c1593bffb581e382f5" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_9a5c86fd9cb505467b636adac95" DEFAULT 0, "companyId" uniqueidentifier, "name" varchar(50) NOT NULL, "code" varchar(50) NOT NULL, "description" varchar(250), "companyCode" varchar(50), "plantCode" varchar(50), "plantId" uniqueidentifier, "blockId" uniqueidentifier, CONSTRAINT "PK_9a2213262c1593bffb581e382f5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "employee_role" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_1c105b756816efbdeae09a9ab65" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2498045ef7f84462636ce2f3b57" DEFAULT 0, "companyId" varchar(255), "roleCode" varchar(50), "prType" varchar(250), "poType" varchar(250), "employeeId" uniqueidentifier, CONSTRAINT "PK_1c105b756816efbdeae09a9ab65" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "employee" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3c2bc72f03fd5abbbc5ac169498" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_6305b6e45a27e2563c05c35187e" DEFAULT 0, "companyId" uniqueidentifier, "code" varchar(50) NOT NULL, "name" varchar(50) NOT NULL, "email" varchar(50) NOT NULL, "position" varchar(50), "phone" varchar(50), "description" varchar(250), "kpi" float NOT NULL CONSTRAINT "DF_74a66834e51d9184098f8ddb83f" DEFAULT 0, "companyCode" varchar(50), "plantId" uniqueidentifier, "positionId" uniqueidentifier, "departmentId" uniqueidentifier, "blockId" uniqueidentifier, "partId" uniqueidentifier, "userId" uniqueidentifier NOT NULL, "purchasingGroupId" uniqueidentifier, "purchasingOrgId" uniqueidentifier, "orgCompanyId" varchar(255), "orgBlockId" varchar(255), "orgDepartmentId" varchar(255), "orgPartId" varchar(255), "orgPositionId" varchar(255), "orgTreeId" varchar(255), "isHolding" bit NOT NULL CONSTRAINT "DF_a15038b56be339cffa9d36c986e" DEFAULT 0, "permissionIndividualId" uniqueidentifier, CONSTRAINT "PK_3c2bc72f03fd5abbbc5ac169498" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_f4b0d329c4a3cf79ffe9d56504" ON "employee" ("userId") WHERE "userId" IS NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_374770032d4b1e34b5a44a1262" ON "employee" ("permissionIndividualId") WHERE "permissionIndividualId" IS NOT NULL`);
        await queryRunner.query(`CREATE TABLE "supplier_expertise" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5a1a8c0abbe2352fd4f2299eccf" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a0247628e7f31918528b48f6cbe" DEFAULT 0, "companyId" varchar(255), "comment" nvarchar(max), "commentCapacity" nvarchar(max), "note" nvarchar(max), "changeDate" datetime NOT NULL, "isCheckLaw" bit NOT NULL CONSTRAINT "DF_feb5b69882c14bd0b106bcee5ed" DEFAULT 0, "isCheckCapacity" bit NOT NULL CONSTRAINT "DF_9167cb138154428ae009d78fc4a" DEFAULT 0, "approvedLawId" uniqueidentifier, "status" varchar(50) NOT NULL, "statusLaw" varchar(50) NOT NULL, "statusCapacity" varchar(50) NOT NULL, "serviceId" uniqueidentifier NOT NULL, "supplierId" uniqueidentifier NOT NULL, "supplierServiceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_5a1a8c0abbe2352fd4f2299eccf" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_service" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_4e151e90cca90a38ba82c7bced5" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_e5eb1e6cf40b56b5735008a93a7" DEFAULT 0, "companyId" varchar(255), "score" float NOT NULL CONSTRAINT "DF_36099811dfbdca9142213ac1377" DEFAULT 0, "approveDate" datetime, "status" varchar(50) NOT NULL, "statusExpertise" varchar(50) NOT NULL CONSTRAINT "DF_c48018bc2ae0278447d81b2572a" DEFAULT 'ChuaThamDinh', "comment" nvarchar(max), "approverComment" nvarchar(max), "serviceId" uniqueidentifier, "supplierId" uniqueidentifier NOT NULL, "totalPrice" bigint, "supplierType" varchar(50), "oldScore" float CONSTRAINT "DF_e7b0d214c1401648618a5bf711d" DEFAULT 0, "isApproveActive" bit CONSTRAINT "DF_97a3f26fd94aea921c0fc26d49c" DEFAULT 0, "requestUpdateStatus" varchar(100), "reasonRejectCapacity" nvarchar(400), CONSTRAINT "PK_4e151e90cca90a38ba82c7bced5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier_capacity" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_fff5eb16a70f4b1522d4a9c6048" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ade9895fb02e19b23e191df0a6c" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_9a07d8f960887ba55a93b061861" DEFAULT 0, "name" varchar(250) NOT NULL, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_6aec5e618ce7d9f3f9a6a3eef18" DEFAULT 'string', "isRequired" bit NOT NULL CONSTRAINT "DF_17a3eab05128df44e1a89811e97" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_9cf7076d3ef487c21cffa9b1565" DEFAULT 1, "isChangeByYear" bit NOT NULL CONSTRAINT "DF_957b570b497d5ba51eb48960a4d" DEFAULT 0, "value" varchar(250), "percent" float, "percentRule" bigint, "percentDownRule" bigint, "description" varchar(250), "parentId" uniqueidentifier, "userScore" float NOT NULL CONSTRAINT "DF_71e6a78947414f08bd5944b5736" DEFAULT 0, "serviceId" uniqueidentifier NOT NULL, "serviceCapacityId" uniqueidentifier, "supplierId" uniqueidentifier NOT NULL, "supplierServiceId" uniqueidentifier NOT NULL, CONSTRAINT "PK_fff5eb16a70f4b1522d4a9c6048" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service_capacity" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_8f93a851d898bd3c70418732f4d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_9edfec4a2ff581c69c226998562" DEFAULT 0, "companyId" varchar(255), "sort" int NOT NULL CONSTRAINT "DF_45406d3f7501a0fe9f536fd5a48" DEFAULT 0, "name" varchar(250) NOT NULL, "isRequired" bit NOT NULL CONSTRAINT "DF_ffdcc45d812cf256763d5121774" DEFAULT 0, "isCalUp" bit NOT NULL CONSTRAINT "DF_835a2b9c3a01d38d1424458a83f" DEFAULT 1, "isChangeByYear" bit NOT NULL CONSTRAINT "DF_3bf737acaacc64371a3980e5043" DEFAULT 0, "type" nvarchar(255) NOT NULL CONSTRAINT "DF_49816337fa055d1de6a5a981ac8" DEFAULT 'string', "percent" float, "percentRule" bigint, "percentDownRule" bigint, "description" varchar(250), "parentId" uniqueidentifier, "serviceId" uniqueidentifier, "exMatGroupId" uniqueidentifier, CONSTRAINT "PK_8f93a851d898bd3c70418732f4d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_85a21558c006647cd76fdce044b" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2088dc728955b55ee628179ace8" DEFAULT 0, "companyId" varchar(255), "percentTech" int NOT NULL CONSTRAINT "DF_7a264faf9cebc48f66fb1e8e895" DEFAULT 0, "percentTrade" int NOT NULL CONSTRAINT "DF_b7e4980cff44e0514615d9fdbb1" DEFAULT 0, "percentPrice" int NOT NULL CONSTRAINT "DF_9dd97ee6ae95121dddde1d62035" DEFAULT 0, "name" varchar(50) NOT NULL, "code" varchar(50) NOT NULL, "level" int NOT NULL, "isLast" bit NOT NULL CONSTRAINT "DF_dd1e0b6bb3969ef151d0558a113" DEFAULT 0, "description" varchar(250), "parentId" uniqueidentifier, "approveById" uniqueidentifier, "fomular" nvarchar(max), "externalMaterialGroupId" varchar(255), "materialTypeId" varchar(255), "wayCalScorePrice" varchar(50) CONSTRAINT "DF_4b8a465acea4f98be081e0c17c6" DEFAULT 'SumScore', "statusCapacity" varchar(100) CONSTRAINT "DF_af24e72a7978e7b9f1123a0a165" DEFAULT 'ChuaDuyet', CONSTRAINT "PK_85a21558c006647cd76fdce044b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_ed405dda320051aca2dcb1a50bb" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_bb2373dfbabb24931613f89636e" DEFAULT 0, "companyId" varchar(255), "isAutoBid" bit CONSTRAINT "DF_2c02f4a679af3e92a9b13100773" DEFAULT 0, "isShowHomePage" bit CONSTRAINT "DF_88d9a390c207de38175475e9ef6" DEFAULT 0, "isCompleteAll" bit CONSTRAINT "DF_dff622ba82fc12c2ec50c236a64" DEFAULT 0, "isSendEmailInviteBid" bit CONSTRAINT "DF_8c2c6ad9d2a69e3908b7f6bd279" DEFAULT 1, "hasSendEmailInviteBid" bit CONSTRAINT "DF_7599873cdacffb775444ab85bde" DEFAULT 0, "name" varchar(250), "pmOrder" varchar(250), "refType" varchar(50), "businessPlantId" uniqueidentifier, "plantId" varchar(255), "code" varchar(50), "serviceInvite" varchar(250), "acceptEndDate" datetime, "submitEndDate" datetime, "addressSubmit" varchar(250), "companyInvite" varchar(250), "companyInviteId" varchar(250), "listAddress" varchar(250), "publicDate" datetime, "type" varchar(250), "biddingTypeCode" varchar(250), "bidTypeId" uniqueidentifier, "timeserving" float CONSTRAINT "DF_6515fbf7d7475d6995e0e720564" DEFAULT 0, "startBidDate" datetime, "bidOpenDate" datetime, "moneyGuarantee" bigint, "timeGuarantee" float, "masterBidGuaranteeId" uniqueidentifier, "timeTechDate" datetime, "timePriceDate" datetime, "timeCheckTechDate" datetime, "timeCheckPriceDate" datetime, "status" varchar(50), "statusTech" varchar(50), "statusTrade" varchar(50), "statusPrice" varchar(50), "statusChooseSupplier" varchar(50) CONSTRAINT "DF_9b42fa91bf0c25ac2967bde78ba" DEFAULT 'ChuaChon', "statusRateTech" varchar(50), "statusRateTrade" varchar(50), "statusRatePrice" varchar(50), "statusResetPrice" varchar(50) CONSTRAINT "DF_489c398b74476b993ca1a9249f2" DEFAULT 'ChuaTao', "resetPriceEndDate" datetime, "scoreDLC" int, "noteTech" nvarchar(max), "noteTrade" nvarchar(max), "notePrice" nvarchar(max), "noteTechLeader" nvarchar(max), "noteMPOLeader" nvarchar(max), "noteCloseBidMPO" nvarchar(max), "noteCloseBidMPOLeader" nvarchar(max), "serviceId" uniqueidentifier, "prId" uniqueidentifier, "exMatGroupId" uniqueidentifier, "prItemId" uniqueidentifier, "quantityItem" int CONSTRAINT "DF_f164c53b13cb1ef96ff6e6aaaec" DEFAULT 0, "isRequestDelete" bit CONSTRAINT "DF_3dcf6999e094a68fed1ea098b21" DEFAULT 0, "isLoadFromBusinessPlan" bit CONSTRAINT "DF_1d66773657c1982397c157cfa28" DEFAULT 0, "noteRequestDelete" nvarchar(max), "fomular" nvarchar(max), "wayCalScorePrice" varchar(50) CONSTRAINT "DF_14eeb32a4afc13fa768c305bea6" DEFAULT 'SumScore', "percentTech" int CONSTRAINT "DF_6873ee638b711272c30a575cf0a" DEFAULT 0, "percentTrade" int CONSTRAINT "DF_aebabb021cb55f38ddd412e3936" DEFAULT 0, "percentPrice" int CONSTRAINT "DF_3efdca65807634c189ffbaa7c60" DEFAULT 0, "fileDrawing" varchar(500), "fileJD" varchar(500), "fileKPI" varchar(500), "fileRule" varchar(500), "fileDocument" varchar(500), "fileAnother" varchar(500), "isRequireFilePriceDetail" bit CONSTRAINT "DF_6e29103f7d7a4240042b0388010" DEFAULT 0, "skipApprove" bit CONSTRAINT "DF_0be8d18d95ff934d18307d84dc1" DEFAULT 0, "isRequireFileTechDetail" bit CONSTRAINT "DF_e721484023f158ebe6ed014ae76" DEFAULT 0, "approveChooseSupplierWinDate" datetime, "noteFinishBidMPO" nvarchar(max), "isGetFromPr" bit CONSTRAINT "DF_0b1219ce95a5be62440d6084eab" DEFAULT 0, "isSurvey" bit CONSTRAINT "DF_703fdbecb8d55912b6a16897ad3" DEFAULT 0, "isLoadFromItem" bit CONSTRAINT "DF_b6abe5d9d7373332065fd0a78a3" DEFAULT 0, "fileScan" varchar(500), "bidCloseDate" datetime, "parentId" uniqueidentifier, "shipmentId" uniqueidentifier, "isMemberScore" bit NOT NULL CONSTRAINT "DF_4255e5d91088bad414a26e7335f" DEFAULT 0, "isOtherCouncil" bit NOT NULL CONSTRAINT "DF_398f2e1d185c76db8611193f71d" DEFAULT 0, "isPersonalScoreVisible" bit NOT NULL CONSTRAINT "DF_307d5090ddd6ef23fc09508fb38" DEFAULT 0, CONSTRAINT "PK_ed405dda320051aca2dcb1a50bb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_auction" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9e594e5a61c0f3cb25679f6ba8d" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a1a0108ff6d02b062bba2a52f37" DEFAULT 0, "companyId" varchar(255), "status" varchar(50) NOT NULL, "timePlus" bigint, "timePlusType" varchar(250), "dateStartPlus" datetime, "dateStart" datetime, "timeApprove" bigint, "step" bigint, "description" nvarchar(max), "fileUrl" varchar(250), "timeApproveType" varchar(250), "endDate" datetime NOT NULL, "bidId" uniqueidentifier NOT NULL, "parentId" uniqueidentifier, CONSTRAINT "PK_9e594e5a61c0f3cb25679f6ba8d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_auction_supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_1e67924888ad3a8291a3466348c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f39bcbf1e207ff61e2357983549" DEFAULT 0, "companyId" varchar(255), "score" float NOT NULL CONSTRAINT "DF_fdcb83f0dace53b4746f37e40b0" DEFAULT 0, "bidAuctionId" uniqueidentifier NOT NULL, "supplierId" uniqueidentifier NOT NULL, "status" varchar(50) NOT NULL, "submitDate" datetime, CONSTRAINT "PK_1e67924888ad3a8291a3466348c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_2bc0d2cab6276144d2ff98a2828" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c4995274dc4852068097e6cc998" DEFAULT 0, "companyId" varchar(255), "status" varchar(50) NOT NULL, "type" varchar(30), "description" nvarchar(max), "code" varchar(50) NOT NULL, "sapCode" varchar(50), "filterSapCode" int, "name" nvarchar(250) NOT NULL, "dealName" nvarchar(250), "abbreviation" nvarchar(250), "createYear" datetime, "isVietNam" bit CONSTRAINT "DF_87ba0f8eb0692c11fa0c2b6bb13" DEFAULT 1, "address" nvarchar(250) NOT NULL, "dealAddress" nvarchar(250), "dealAddress2" nvarchar(250), "dealAddress3" nvarchar(250), "dealAddress4" nvarchar(250), "dealAddress5" nvarchar(250), "represen" nvarchar(50), "represenPosition" nvarchar(50), "represenPhone" varchar(50), "represenFax" varchar(50), "represenEmail" varchar(50), "represenNote" nvarchar(max), "decider" nvarchar(50), "deciderPosition" nvarchar(50), "deciderPhone" varchar(50), "deciderFax" varchar(50), "deciderEmail" varchar(50), "deciderNote" nvarchar(max), "trader" nvarchar(50), "traderPosition" nvarchar(50), "traderPhone" varchar(50), "traderFax" varchar(50), "traderEmail" varchar(50), "traderNote" nvarchar(max), "chief" nvarchar(50), "contactName" nvarchar(250), "phone" varchar(50), "email" varchar(50), "fax" varchar(50), "website" varchar(500), "capital" bigint CONSTRAINT "DF_95303d8553bc526708fce214dfc" DEFAULT 0, "assets" bigint CONSTRAINT "DF_4c3a53d6967914f78bfd9a5fd5d" DEFAULT 0, "dateStart" datetime, "fileBill" varchar(4000), "fileInfoBill" varchar(4000), "fileMST" varchar(4000), "conditionalBusinessLicense" nvarchar(4000), "bankBrand" varchar(250), "userId" uniqueidentifier, "introducerId" varchar(255), "businessPartnerGroupId" uniqueidentifier, "businessPartnerGroupCode" varchar(250), "businessPartnerGroupType" varchar(250), "countryId" uniqueidentifier, "regionId" uniqueidentifier, "supplierType" varchar(250), "approveType" varchar(50), "legalStatus" varchar(50), "street" varchar(250), "postalCode" varchar(250), "currency" varchar(50), "purchasingHabit" varchar(250), "isApproveActive" bit CONSTRAINT "DF_40085299e3fe600eed99c0ef907" DEFAULT 0, "requestUpdateStatus" varchar(100), "urlType" varchar(300), "supplierNumberId" uniqueidentifier, "supplierSchemaId" uniqueidentifier, "businessTypeId" uniqueidentifier, "supplierSource" varchar(50), "isOfficial" bit NOT NULL CONSTRAINT "DF_17188d777614bbf63a84b0167df" DEFAULT 0, "reasonRejectLaw" nvarchar(400), "isInternal" bit NOT NULL CONSTRAINT "DF_62d4c5181ccf534a693f9b61aa8" DEFAULT 0, CONSTRAINT "PK_2bc0d2cab6276144d2ff98a2828" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_e8902c50550ff82dd0143913c0" ON "supplier" ("userId") WHERE "userId" IS NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_4df75df2edeff9fb748d216bc5" ON "supplier" ("businessPartnerGroupId") WHERE "businessPartnerGroupId" IS NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_21d5eab891f3a77c23b0dd6d0d" ON "supplier" ("supplierNumberId") WHERE "supplierNumberId" IS NOT NULL`);
        await queryRunner.query(`CREATE TABLE "user" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_cace4a159ff9f2512dd42373760" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_c95e384ff549a266b7dcba999db" DEFAULT 0, "companyId" varchar(255), "username" varchar(50) NOT NULL, "password" varchar(250) NOT NULL, "fcmToken" nvarchar(max), "deviceToken" nvarchar(max), "type" varchar(50) NOT NULL, "supplierId" uniqueidentifier, "baseUserId" varchar(255), "employeeId" uniqueidentifier, "roles" nvarchar(max), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_031cdc2c9c5eb56d48b5bdb4e5" ON "user" ("supplierId") WHERE "supplierId" IS NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_ab4a80281f1e8d524714e00f38" ON "user" ("employeeId") WHERE "employeeId" IS NOT NULL`);
        await queryRunner.query(`CREATE TABLE "user_external_material_group" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3afa7998994bd8b100db19647f1" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_26a3ed74d04d85ae372e62ad975" DEFAULT 0, "companyId" varchar(255), "userId" uniqueidentifier NOT NULL, "externalMaterialGroupId" uniqueidentifier, CONSTRAINT "PK_3afa7998994bd8b100db19647f1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "translation_entity" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_d1531816d11c886ac2877566289" DEFAULT NEWSEQUENTIALID(), "language" varchar(250) NOT NULL, "keyTable" varchar(250) NOT NULL, "keyCol" varchar(250) NOT NULL, "idMap" varchar(250) NOT NULL, "value" nvarchar(max), CONSTRAINT "PK_d1531816d11c886ac2877566289" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "synchronizing_log" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0f1c849bf3993ef289ab015a9ad" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_ab4be77536ce1861956451c17d1" DEFAULT 0, "companyId" varchar(255), "apiEndpoint" varchar(50), "apiStatus" varchar(50), "messageType" varchar(50), "message" varchar(255), "error" varchar(255), CONSTRAINT "PK_0f1c849bf3993ef289ab015a9ad" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "rating_configuration" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_d02b1aa66a9af562b40524fc45e" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_807be74d2b93dfc1ef9c4f96a02" DEFAULT 0, "companyId" varchar(255), "level" int NOT NULL CONSTRAINT "DF_c93c5c152de783402456315c02a" DEFAULT 0, "type" varchar(50) NOT NULL, "ratingType" varchar(50) NOT NULL, CONSTRAINT "PK_d02b1aa66a9af562b40524fc45e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "rate_data" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_9eba0f8d3ede313f500eeed0a31" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a18b8900db3481cb853fd71756f" DEFAULT 0, "companyId" varchar(255), "entityName" nvarchar(50) NOT NULL, "targetId" varchar(255), "bidId" varchar(255), "employeeId" varchar(255), "supplierId" varchar(255), "type" nvarchar(max), "lock" bit CONSTRAINT "DF_45600803b76b130fc5511a8829e" DEFAULT 0, "rateNumber" float CONSTRAINT "DF_e48c6ed03727103e72d3d5d0603" DEFAULT 0, CONSTRAINT "PK_9eba0f8d3ede313f500eeed0a31" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "organizational_position" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c50644cdb61c8efa1d493c8eba6" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_aef23a791a3c2272d0459e3c0cf" DEFAULT 0, "companyId" varchar(255), "organizationalPositionId" varchar(255), "roleStringify" nvarchar(max), "roleDataStringify" nvarchar(max), CONSTRAINT "PK_c50644cdb61c8efa1d493c8eba6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bid_supplier_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_55ae43730a45ec0e10ce849ca45" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_4dc59afd3901ded57ee08721118" DEFAULT 0, "companyId" varchar(255), "bidSupplierId" varchar(255) NOT NULL, "supplierId" varchar(255) NOT NULL, "bidId" varchar(255) NOT NULL, "bidPrId" varchar(255) NOT NULL, "materialId" varchar(255) NOT NULL, CONSTRAINT "PK_55ae43730a45ec0e10ce849ca45" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "service_capacity_list_detail" ADD CONSTRAINT "FK_ceba55fc1b9eb39b36c43a9644c" FOREIGN KEY ("serviceCapacityId") REFERENCES "service_capacity"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_year_detail" ADD CONSTRAINT "FK_b13f447aa60bdfc21ea9b307c3d" FOREIGN KEY ("supplierExpertiseDetailId") REFERENCES "supplier_expertise_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" ADD CONSTRAINT "FK_897a53586bd0c3ae35db7822c9d" FOREIGN KEY ("supplierCapacityId") REFERENCES "supplier_capacity"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" ADD CONSTRAINT "FK_4b70099f1d6be7f37b245384b0a" FOREIGN KEY ("supplierExpertiseId") REFERENCES "supplier_expertise"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item_tech_list_detail" ADD CONSTRAINT "FK_6d5e197e96ef20c03594d262ec2" FOREIGN KEY ("itemTechId") REFERENCES "item_tech"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item_tech" ADD CONSTRAINT "FK_48a9e3f00c9fd54618dcc23c5a6" FOREIGN KEY ("parentId") REFERENCES "item_tech"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "item_tech" ADD CONSTRAINT "FK_5964da7a8a0a76663eb696a6789" FOREIGN KEY ("prItemId") REFERENCES "pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_history" ADD CONSTRAINT "FK_b3e89605faa3387382f647936e5" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD CONSTRAINT "FK_fbfa3d8cf7a68c99ff0ec4896cd" FOREIGN KEY ("contractAppendixId") REFERENCES "contract_appendix"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" ADD CONSTRAINT "FK_875d0d2310ee6e12b3183ec3b6e" FOREIGN KEY ("contractAppendixId") REFERENCES "contract_appendix"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD CONSTRAINT "FK_eb779d50474b4b376465d77b3ea" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_history" ADD CONSTRAINT "FK_b4081c0bb5e5ad9440295155b7a" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_history" ADD CONSTRAINT "FK_5e938fef02a12dc97fe6bd31dd4" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_history" ADD CONSTRAINT "FK_64a00b17e837cf662894fef5e35" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_history" ADD CONSTRAINT "FK_00555eaae617d36cb019cc436ea" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_history" ADD CONSTRAINT "FK_18b2851fecdd41d92f1c1126c85" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_member" ADD CONSTRAINT "FK_345131b5513984074aef49c14ef" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_member" ADD CONSTRAINT "FK_f41f38ea6931f3fc5becb0d632c" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction_history" ADD CONSTRAINT "FK_a9b75b1dbd49d90df8afc7d2481" FOREIGN KEY ("auctionId") REFERENCES "auction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" ADD CONSTRAINT "FK_0f870976dd62fbe5ff8084fc567" FOREIGN KEY ("offerSupplierId") REFERENCES "offer_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" ADD CONSTRAINT "FK_a08481d93d35c70c12603e60014" FOREIGN KEY ("offerServiceId") REFERENCES "offer_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_tech_list_detail" ADD CONSTRAINT "FK_f70094a3975f2e3552b884d83d1" FOREIGN KEY ("bidTechId") REFERENCES "offer_tech"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_tech" ADD CONSTRAINT "FK_3c593faf2ddfd1a0c7161c83161" FOREIGN KEY ("parentId") REFERENCES "offer_tech"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_tech" ADD CONSTRAINT "FK_9182f39b9c62fd4bc4e6e013a31" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_tech" ADD CONSTRAINT "FK_da26ea1a2620a955851f69210ac" FOREIGN KEY ("offerServiceId") REFERENCES "offer_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" ADD CONSTRAINT "FK_81bd2a5c71e28586e2ba4e65176" FOREIGN KEY ("offerSupplierId") REFERENCES "offer_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" ADD CONSTRAINT "FK_c67bce06945fe1793a9e89e4aab" FOREIGN KEY ("offerTechId") REFERENCES "offer_tech"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_trade_list_detail" ADD CONSTRAINT "FK_70d1ba4d578cf661165e1412d5b" FOREIGN KEY ("offerTradeId") REFERENCES "offer_trade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_trade" ADD CONSTRAINT "FK_81aa09f465c800e6f82a739aaf6" FOREIGN KEY ("parentId") REFERENCES "offer_trade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_trade" ADD CONSTRAINT "FK_7f6124a13c3f3d94b43ddf85026" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_trade" ADD CONSTRAINT "FK_769211822e9a9130696244fd060" FOREIGN KEY ("offerServiceId") REFERENCES "offer_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" ADD CONSTRAINT "FK_0c88283a1c1d6b97ea830c17b90" FOREIGN KEY ("offerSupplierId") REFERENCES "offer_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" ADD CONSTRAINT "FK_d84e9c117fe73bf398cc38b8616" FOREIGN KEY ("offerTradeId") REFERENCES "offer_trade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" ADD CONSTRAINT "FK_6ddc272faa5c3750ae754e264d7" FOREIGN KEY ("offerSupplierId") REFERENCES "offer_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" ADD CONSTRAINT "FK_9c1553b44e928ae378ed2c107bc" FOREIGN KEY ("offerPriceId") REFERENCES "offer_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" ADD CONSTRAINT "FK_a6fd9457077b6e9f2a35cbb25ba" FOREIGN KEY ("offerPriceId") REFERENCES "offer_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" ADD CONSTRAINT "FK_1b467c5d4dc200dd94cfb9a7318" FOREIGN KEY ("offerPriceColId") REFERENCES "offer_price_col"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" ADD CONSTRAINT "FK_a4b6bcc6142788605753f739bb0" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" ADD CONSTRAINT "FK_930bd8534a1d0efdacefc41505a" FOREIGN KEY ("offerServiceId") REFERENCES "offer_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" ADD CONSTRAINT "FK_17d00a51f00b2c3f48fd25e0830" FOREIGN KEY ("offerSupplierId") REFERENCES "offer_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" ADD CONSTRAINT "FK_b3fa0454d450bd3566b67b11b4d" FOREIGN KEY ("offerPriceId") REFERENCES "offer_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" ADD CONSTRAINT "FK_c41fdd84d9524aeefa881e51fdd" FOREIGN KEY ("offerPriceColId") REFERENCES "offer_price_col"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_custom_price_value" ADD CONSTRAINT "FK_161b879aab20df18ceb15b86f93" FOREIGN KEY ("offerSupplierId") REFERENCES "offer_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage" ADD CONSTRAINT "FK_5c98f9d1318bb87998029e8ba82" FOREIGN KEY ("shipmentCostId") REFERENCES "shipment_cost"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" ADD CONSTRAINT "FK_b07484da07db3817414875e822d" FOREIGN KEY ("shipmentCostId") REFERENCES "shipment_cost"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" ADD CONSTRAINT "FK_686395bad7a9085049dd1f9219e" FOREIGN KEY ("shipmentCostStageId") REFERENCES "shipment_cost_stage"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" ADD CONSTRAINT "FK_68c5f86ef5a45aaf9c605cf1e74" FOREIGN KEY ("shipmentCostPriceId") REFERENCES "shipment_cost_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" ADD CONSTRAINT "FK_e6f59010cb7204b13fef1b1d2c7" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" ADD CONSTRAINT "FK_7037ad07e54ca0e48075b345f22" FOREIGN KEY ("shipmentPriceId") REFERENCES "shipment_cost_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" ADD CONSTRAINT "FK_30ab8ed0162ec34342d51068d5a" FOREIGN KEY ("shipmentCostId") REFERENCES "shipment_cost"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" ADD CONSTRAINT "FK_6c9750056dbad47e31b34c0580f" FOREIGN KEY ("shipmentCostStageCostId") REFERENCES "shipment_cost_stage_cost"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" ADD CONSTRAINT "FK_2126f0cb48c3e70f5cc8ece47ce" FOREIGN KEY ("offerSupplierId") REFERENCES "offer_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" ADD CONSTRAINT "FK_fba7b40cf7da451b567ca1f7ce0" FOREIGN KEY ("shipmentPriceId") REFERENCES "shipment_cost_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" ADD CONSTRAINT "FK_7a6badaaee477282083856dce3e" FOREIGN KEY ("shipmentId") REFERENCES "shipment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" ADD CONSTRAINT "FK_f8a80478d4f851ee9299295146d" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" ADD CONSTRAINT "FK_4f2947f3124e02456c3f9951848" FOREIGN KEY ("bidSupplierId") REFERENCES "bid_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" ADD CONSTRAINT "FK_56600efab6eb483eb902afa744b" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" ADD CONSTRAINT "FK_b7548e272aa8916db559dcd7364" FOREIGN KEY ("offerSupplierId") REFERENCES "offer_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" ADD CONSTRAINT "FK_a679f77c259d43a6b2d50292070" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" ADD CONSTRAINT "FK_ea06e85d35bc1cd664e29b4a97c" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" ADD CONSTRAINT "FK_d19b3ad6ea4d242e224e79d1a81" FOREIGN KEY ("parentId") REFERENCES "offer_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" ADD CONSTRAINT "FK_105573cee204cbcbb11562df909" FOREIGN KEY ("offerSupplierId") REFERENCES "offer_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" ADD CONSTRAINT "FK_be6b0ac54a29b8d5466cde600ab" FOREIGN KEY ("offerPriceId") REFERENCES "offer_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_price_list_detail" ADD CONSTRAINT "FK_ecd5c780d592dc89d69a4fa460c" FOREIGN KEY ("offerPriceId") REFERENCES "offer_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" ADD CONSTRAINT "FK_559e2900d2c0772ce9c75d6f496" FOREIGN KEY ("offerDealSupplierId") REFERENCES "offer_deal_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" ADD CONSTRAINT "FK_c1fbf776df2ef4524df71effd59" FOREIGN KEY ("offerPriceId") REFERENCES "offer_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" ADD CONSTRAINT "FK_a77f267009cea06dfcef3d048e2" FOREIGN KEY ("offerDealId") REFERENCES "offer_deal"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" ADD CONSTRAINT "FK_9cd5118d0138adb52a93fb0c465" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_deal" ADD CONSTRAINT "FK_e6626d6cb9ceef159383789e712" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_deal" ADD CONSTRAINT "FK_920b0e8da9bac1e3954ad903486" FOREIGN KEY ("parentId") REFERENCES "offer_deal"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" ADD CONSTRAINT "FK_3d4dc213d47aae6bd81b371d794" FOREIGN KEY ("offerDealId") REFERENCES "offer_deal"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" ADD CONSTRAINT "FK_555f706d11d79d5aaefb1566602" FOREIGN KEY ("offerPriceId") REFERENCES "offer_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_price" ADD CONSTRAINT "FK_bb4bacf4af6f704d19e6f4c4edb" FOREIGN KEY ("parentId") REFERENCES "offer_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_price" ADD CONSTRAINT "FK_4ff3c7ddf45b1a1c320904dc112" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_price" ADD CONSTRAINT "FK_5b6691af02ad6cfcdd127f9e37f" FOREIGN KEY ("offerServiceId") REFERENCES "offer_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_price" ADD CONSTRAINT "FK_f9fe2bd7a8fb496d41d4bb20282" FOREIGN KEY ("offerItemId") REFERENCES "offer_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_group" ADD CONSTRAINT "FK_2b32b07ecb77ac8eeaf509791db" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_scene_list_detail" ADD CONSTRAINT "FK_d37e997479bfdf69ae6c0cb1de7" FOREIGN KEY ("serviceSceneId") REFERENCES "service_scene"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_scene" ADD CONSTRAINT "FK_e86553ded4dda54f7a7c14840c9" FOREIGN KEY ("parentId") REFERENCES "service_scene"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_scene" ADD CONSTRAINT "FK_3c79f5c45621fa064473e282eb3" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_scene" ADD CONSTRAINT "FK_e95be4affefcf1ab6002459fb69" FOREIGN KEY ("exMatGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" ADD CONSTRAINT "FK_bf2be9fb6122e1c6333d2eced90" FOREIGN KEY ("parentId") REFERENCES "service_purchase_history"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" ADD CONSTRAINT "FK_4a77983cf66728f10f545e38367" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" ADD CONSTRAINT "FK_05ba8eaec87c4074d36b2b95b55" FOREIGN KEY ("exMatGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" ADD CONSTRAINT "FK_14fc1a21dab7b38d6e23058d079" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" ADD CONSTRAINT "FK_334264ed44b6305146a009dd587" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" ADD CONSTRAINT "FK_0c3875bbccefc454f60778a5be8" FOREIGN KEY ("roundUpContId") REFERENCES "round_up_cont"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" ADD CONSTRAINT "FK_65f4fa0adabc3a442073b435438" FOREIGN KEY ("roundUpContTemplateId") REFERENCES "round_up_cont_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" ADD CONSTRAINT "FK_02f96e8b73bc4786fec88ed3a15" FOREIGN KEY ("roundUpContTemplateColId") REFERENCES "round_up_cont_template_col"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" ADD CONSTRAINT "FK_2c7d7736cf544cb4180d31bac36" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" ADD CONSTRAINT "FK_e1ba8fa4e38ec536def890d3d34" FOREIGN KEY ("contractInspectionId") REFERENCES "contract_inspection"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" ADD CONSTRAINT "FK_a9e328db5a3eb7ef64ca6686c92" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" ADD CONSTRAINT "FK_fa9403ac1c39c5cfac4745419f0" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" ADD CONSTRAINT "FK_4fafd55c40d645bcc559752576d" FOREIGN KEY ("contractInspectionId") REFERENCES "contract_inspection"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" ADD CONSTRAINT "FK_89a43532c20392b4659cca1894f" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" ADD CONSTRAINT "FK_599cf2b972f05d8fa8185d8f7f6" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" ADD CONSTRAINT "FK_93123fc23fc6b69c14cf37f1c34" FOREIGN KEY ("paymentMethodId") REFERENCES "payment_method"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" ADD CONSTRAINT "FK_39ddf6775929f82b269bacf0dde" FOREIGN KEY ("contractInspectionId") REFERENCES "contract_inspection"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_765f50e4842172a1b7787f5bc39" FOREIGN KEY ("supplierNumberId") REFERENCES "supplier_number"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_5c9e2e27a2f9992d98f31cf4518" FOREIGN KEY ("titleId") REFERENCES "title"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_418e18593de4f3356b0b3513c7a" FOREIGN KEY ("stakeholderCategoryId") REFERENCES "stakeholder_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_bf01994068c2f28a228aab95172" FOREIGN KEY ("industryStandardId") REFERENCES "industry_standard"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_7814f80f7f44fe7c6b491900c1f" FOREIGN KEY ("businessPartnerGroupId") REFERENCES "business_partner_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_eda775f3a7f0eb2a46fdd023810" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_998441b96dcd0255f2695569d94" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_d2682514218f8972b6e51844f62" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_b5db1c68bd6e4394cd3b7fcb2c6" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_fd42d629edc79904b40ba0f4ca3" FOREIGN KEY ("incotermId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_5a790171cdffbf15cc6e230859b" FOREIGN KEY ("paymentTermId") REFERENCES "payment_term"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_5684b0cf010e7b31d1fd78c3e72" FOREIGN KEY ("paymentMethodId") REFERENCES "payment_method"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_2bae0c82513276899ff286b87e7" FOREIGN KEY ("planningGroupId") REFERENCES "planning_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" ADD CONSTRAINT "FK_d1e5111b9413ac6da66e324fe26" FOREIGN KEY ("glAccountId") REFERENCES "gl_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee_purchasing_group" ADD CONSTRAINT "FK_36c135cfb1a42c2881f431c32f5" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee_purchasing_group" ADD CONSTRAINT "FK_250e68fecb000d6c6388197fcaa" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "purchasing_group" ADD CONSTRAINT "FK_33dab669b380c9a0efbaa8adeb9" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "purchasing_group" ADD CONSTRAINT "FK_38910ed509406fa52c32dead2d6" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_department" ADD CONSTRAINT "FK_42637df66376135b4e4d245949e" FOREIGN KEY ("departmentId") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_department" ADD CONSTRAINT "FK_d2c52b293a9b5c9788f50aa5b83" FOREIGN KEY ("complaintId") REFERENCES "complaint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD CONSTRAINT "FK_f8716ddb7646ee7d4914a2da653" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD CONSTRAINT "FK_6f7ea66b6b04bedd6c7ea70a686" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD CONSTRAINT "FK_f885e2b5718e6608b27f2469a9c" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" ADD CONSTRAINT "FK_607cc3a203f6c6fb9d3b4a9b613" FOREIGN KEY ("poItemId") REFERENCES "po_product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" ADD CONSTRAINT "FK_b85930192a9f7f1b29b78f54f81" FOREIGN KEY ("contractItemId") REFERENCES "contract_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" ADD CONSTRAINT "FK_5bf396593fad751cdc004519746" FOREIGN KEY ("complaintId") REFERENCES "complaint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" ADD CONSTRAINT "FK_a9e62c10d280325f8a52e130b6f" FOREIGN KEY ("complaintId") REFERENCES "complaint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" ADD CONSTRAINT "FK_188784b2950c789a94d27111a7b" FOREIGN KEY ("complaintItemId") REFERENCES "complaint_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" ADD CONSTRAINT "FK_eea9690ca096d414369c9e9f667" FOREIGN KEY ("complaintId") REFERENCES "complaint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" ADD CONSTRAINT "FK_fe81416bff33ab13993dc321446" FOREIGN KEY ("complaintItemId") REFERENCES "complaint_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_item" ADD CONSTRAINT "FK_c58eb3e3d144addf4aa993a7880" FOREIGN KEY ("complaintId") REFERENCES "complaint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" ADD CONSTRAINT "FK_45566a3886b5ab972663ad9df07" FOREIGN KEY ("complaintId") REFERENCES "complaint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" ADD CONSTRAINT "FK_bcecb0e47289a9582cb73b37589" FOREIGN KEY ("responseDepartmentId") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" ADD CONSTRAINT "FK_aa7df4cd77a3a44a6c5048a648f" FOREIGN KEY ("receptEmployeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" ADD CONSTRAINT "FK_7d52f4c3cb76ff26da5f23ec3b4" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" ADD CONSTRAINT "FK_bc0e3960577ebf249f08ca89227" FOREIGN KEY ("complaintId") REFERENCES "complaint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_handling_plan" ADD CONSTRAINT "FK_62d5f286c893c574760f82809a8" FOREIGN KEY ("complaintId") REFERENCES "complaint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" ADD CONSTRAINT "FK_ac28f23f8454ab831799fd2874f" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" ADD CONSTRAINT "FK_a68606e0d5b2648761ca3062991" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" ADD CONSTRAINT "FK_b8e2ccd7f5193b5ba2e35e64607" FOREIGN KEY ("complaintId") REFERENCES "complaint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" ADD CONSTRAINT "FK_fa7b386046955f6a8fab9189bfa" FOREIGN KEY ("complaintChatId") REFERENCES "complaint_chat"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" ADD CONSTRAINT "FK_9c2934db56963e14328aae1f3ad" FOREIGN KEY ("senderId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" ADD CONSTRAINT "FK_891eda2c34fec7522f90198f36b" FOREIGN KEY ("receiverId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" ADD CONSTRAINT "FK_5322ff5a77cbdd03cca9041422b" FOREIGN KEY ("complaintId") REFERENCES "complaint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint" ADD CONSTRAINT "FK_163b8d410fd0877ee6da824bff1" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint" ADD CONSTRAINT "FK_58f2c71fa3be2b4329e6f27b447" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint" ADD CONSTRAINT "FK_3ccf9ff1b22399f3a967c395fc0" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint" ADD CONSTRAINT "FK_9e3eee9dfea3f22c0c499321333" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint" ADD CONSTRAINT "FK_7af461e25f1a5c918281e0d159c" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint" ADD CONSTRAINT "FK_06aff3d57e7fdd62467dc318657" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "complaint" ADD CONSTRAINT "FK_1ec5d290566bcb9b78ad4fa24e9" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" ADD CONSTRAINT "FK_e9292a5a26f1fff9137520a6f55" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" ADD CONSTRAINT "FK_deab8f46837df555b923140cd3e" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" ADD CONSTRAINT "FK_0456ebbd5a287c94401ae109900" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" ADD CONSTRAINT "FK_7c09fd4828b602ee9ac52203616" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" ADD CONSTRAINT "FK_c20c52b0f67b29edf9a9b241720" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_condition_type" ADD CONSTRAINT "FK_c137fbc838888f547569f6a9955" FOREIGN KEY ("procedureId") REFERENCES "procedure"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" ADD CONSTRAINT "FK_bb3d644d6777053ceea7c03eb77" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" ADD CONSTRAINT "FK_3c570242525b6a6011f63508a28" FOREIGN KEY ("procedureId") REFERENCES "procedure"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" ADD CONSTRAINT "FK_50c58f0442fe4dc00620e891c64" FOREIGN KEY ("poProductId") REFERENCES "po_product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" ADD CONSTRAINT "FK_3ba9d2082b44e9f629405370e6c" FOREIGN KEY ("poPriceListId") REFERENCES "po_price_list"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" ADD CONSTRAINT "FK_e4c1e340fb9fb6802d8b1f7dfe2" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_price_list" ADD CONSTRAINT "FK_28e424dc48824568a9f3e3040f7" FOREIGN KEY ("procedureId") REFERENCES "procedure"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_price_list" ADD CONSTRAINT "FK_42fe0cf7b3ec41e6a1bc366ba2a" FOREIGN KEY ("masterConditionTypeId") REFERENCES "master_condition_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_price_list" ADD CONSTRAINT "FK_431d0b0f1c6ebe0a2ce1ed597ed" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bill_history" ADD CONSTRAINT "FK_860ffb38bae97a975bf900337e1" FOREIGN KEY ("billId") REFERENCES "bill"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bill" ADD CONSTRAINT "FK_12cc7e32306c5b263f6fbcfd1e5" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bill" ADD CONSTRAINT "FK_42dbdce0becb29d047587aedb9b" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bill" ADD CONSTRAINT "FK_d5e2ad96d3fb0c0013b1a0176bb" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bill" ADD CONSTRAINT "FK_586340736f555e633b893bf191e" FOREIGN KEY ("billLookupId") REFERENCES "bill_lookup"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_bill" ADD CONSTRAINT "FK_0a987673166bbd293ee1113c503" FOREIGN KEY ("billId") REFERENCES "bill"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_bill" ADD CONSTRAINT "FK_54038a0e8db8407df9426fb8ccd" FOREIGN KEY ("paymentId") REFERENCES "payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_po" ADD CONSTRAINT "FK_3ad23ea16b4999989a500093da1" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_po" ADD CONSTRAINT "FK_548e1e23c4bd4160b0d4229b3b8" FOREIGN KEY ("paymentId") REFERENCES "payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_contract" ADD CONSTRAINT "FK_c7312ab6067307abca093192227" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_contract" ADD CONSTRAINT "FK_fd92000b542ff541c7e2001ad7d" FOREIGN KEY ("paymentId") REFERENCES "payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment" ADD CONSTRAINT "FK_a3772a91be111b4b6a8a496fffa" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment" ADD CONSTRAINT "FK_8bd02879aabfa095f531e9482f3" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_price" ADD CONSTRAINT "FK_ad4a0adb9e22ac71143040be5d2" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_price" ADD CONSTRAINT "FK_1423e57f6c13d2c6c7493ee072a" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_price" ADD CONSTRAINT "FK_187ce20abdd5890c63d5507dadc" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" ADD CONSTRAINT "FK_1357bc4c618d5bd899e0fac0805" FOREIGN KEY ("supplierSchemaId") REFERENCES "supplier_schema"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" ADD CONSTRAINT "FK_8613509f20081fcc76d02f4c56f" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" ADD CONSTRAINT "FK_0b142198eb6f2add8c922cb5031" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" ADD CONSTRAINT "FK_d8e9c8105a6e7afdcc07b4232b9" FOREIGN KEY ("purchasingOrgSchemaId") REFERENCES "purchasing_org_schema"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" ADD CONSTRAINT "FK_6713d7567399637fc589718da26" FOREIGN KEY ("supplierSchemaId") REFERENCES "supplier_schema"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" ADD CONSTRAINT "FK_b1f4880a317509e77bc120b5de0" FOREIGN KEY ("procedureId") REFERENCES "procedure"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "purchasing_org" ADD CONSTRAINT "FK_09f261633c748d640515c6b42f2" FOREIGN KEY ("purchasingOrgSchemaId") REFERENCES "purchasing_org_schema"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_supplier" ADD CONSTRAINT "FK_61528dcb2929731207983bf8c22" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_supplier" ADD CONSTRAINT "FK_cc3c1d35ee46341053a579caebe" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_supplier" ADD CONSTRAINT "FK_ed0dc3c247fdf754cd72a519d2d" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_supplier" ADD CONSTRAINT "FK_e104b8f1bba8ef15ac05c9703fa" FOREIGN KEY ("incotermId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_supplier" ADD CONSTRAINT "FK_c2ffeb50e8a7b17eafb1b5b5b15" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_supplier" ADD CONSTRAINT "FK_583285560f3ee962deeb2426156" FOREIGN KEY ("supplierPlantId") REFERENCES "supplier_plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" ADD CONSTRAINT "FK_7f96a57a3a3e11234a0999ebe09" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" ADD CONSTRAINT "FK_943585571daeb5970b45afde59f" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" ADD CONSTRAINT "FK_d6bbfa21cbc1eb57aced8803a9d" FOREIGN KEY ("glAccountId") REFERENCES "gl_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" ADD CONSTRAINT "FK_90161e44269091beab3c1a78a18" FOREIGN KEY ("paymentTermId") REFERENCES "payment_term"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" ADD CONSTRAINT "FK_988d26f4f517f0b9eaca1e799f3" FOREIGN KEY ("paymentMethodId") REFERENCES "payment_method"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" ADD CONSTRAINT "FK_ff0286b20f45e1acfa096cac8a8" FOREIGN KEY ("planningGroupId") REFERENCES "planning_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" ADD CONSTRAINT "FK_4ffc9fe34b4a2b3613bf97e741b" FOREIGN KEY ("supplierPlantId") REFERENCES "supplier_plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" ADD CONSTRAINT "FK_a765fa3fa241ab1feaed8947686" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" ADD CONSTRAINT "FK_fdd5c9c23a1d24aa4b2f12a6537" FOREIGN KEY ("recommendedPurchaseId") REFERENCES "recommended_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" ADD CONSTRAINT "FK_8d13360453dc4e89c923a9675ef" FOREIGN KEY ("recommendedPurchaseTemplateId") REFERENCES "recommended_purchase_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" ADD CONSTRAINT "FK_bc3d13277c8109589e05a56d4f5" FOREIGN KEY ("recommendedPurchaseTemplateColId") REFERENCES "recommended_purchase_template_col"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template_col" ADD CONSTRAINT "FK_65fe390786adf9eabfe77e4fbc1" FOREIGN KEY ("recommendedPurchaseTemplateId") REFERENCES "recommended_purchase_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" ADD CONSTRAINT "FK_56867f3dea6a7a251ff03f3d2bb" FOREIGN KEY ("recommendedPurchaseTemplateId") REFERENCES "recommended_purchase_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" ADD CONSTRAINT "FK_514e965f7d1450bc10ad2c0be4f" FOREIGN KEY ("recommendedPurchaseId") REFERENCES "recommended_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template" ADD CONSTRAINT "FK_629590a7a0b2a74b8da521bfe6b" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" ADD CONSTRAINT "FK_e9ac2edd097548f64822b1accbc" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" ADD CONSTRAINT "FK_97f5c9b79536805c9fc8c7b85f7" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" ADD CONSTRAINT "FK_d41ad35aead170a5b8e49696fc0" FOREIGN KEY ("businessPlanId") REFERENCES "business_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" ADD CONSTRAINT "FK_a1159cc8f0b652cc29daff454d6" FOREIGN KEY ("businessPlanTemplateId") REFERENCES "business_plan_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" ADD CONSTRAINT "FK_986dc8eee68507ab7b857cdc26d" FOREIGN KEY ("businessPlanTemplateColId") REFERENCES "business_plan_template_col"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" ADD CONSTRAINT "FK_2d0850037e7162fcbccc35fac08" FOREIGN KEY ("settingStringId") REFERENCES "setting_string"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" ADD CONSTRAINT "FK_6e4e4e3bd12d3a9f8d10e9c9205" FOREIGN KEY ("businessPlanId") REFERENCES "business_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" ADD CONSTRAINT "FK_f19549f2cb5f04564a957dce89a" FOREIGN KEY ("businessPlanTemplateColId") REFERENCES "business_plan_template_col"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_col" ADD CONSTRAINT "FK_ce9ceba53df65116ce1e52c7a4d" FOREIGN KEY ("businessPlanTemplateId") REFERENCES "business_plan_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" ADD CONSTRAINT "FK_b62e76c412972abcc69cd4a9542" FOREIGN KEY ("businessPlanTemplateId") REFERENCES "business_plan_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" ADD CONSTRAINT "FK_8cef4ff90faeed1db0e55ec12db" FOREIGN KEY ("businessPlanId") REFERENCES "business_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_template" ADD CONSTRAINT "FK_577edbb2040b81a30b65046c877" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "rfq_details" ADD CONSTRAINT "FK_e5e58eba9a4d61da3b33348ebe1" FOREIGN KEY ("rfqId") REFERENCES "rfq"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "rfq_details" ADD CONSTRAINT "FK_c1f1f558da24785770a4fc9eb98" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" ADD CONSTRAINT "FK_4797199c4668a0651e42148ba49" FOREIGN KEY ("recommendedPurchaseId") REFERENCES "recommended_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" ADD CONSTRAINT "FK_50d432d6a56e943df4cfc36ede6" FOREIGN KEY ("rfqId") REFERENCES "rfq"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD CONSTRAINT "FK_529714df12183872a093b083126" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "rfq" ADD CONSTRAINT "FK_3c9fad0f24deb8f94a7d52eb5d8" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" ADD CONSTRAINT "FK_da51824e171b4e0cdaf6863a41a" FOREIGN KEY ("businessPlanId") REFERENCES "business_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" ADD CONSTRAINT "FK_d3798bae2d52c720cb797dd6f12" FOREIGN KEY ("rfqId") REFERENCES "rfq"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD CONSTRAINT "FK_0ef5ca1c931f3bc1a8482b5c6c9" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD CONSTRAINT "FK_4f18c3c3f4eefb70911b281d031" FOREIGN KEY ("businessPlanTemplateId") REFERENCES "business_plan_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD CONSTRAINT "FK_8f0898ece0fdfe12e35e0a2e557" FOREIGN KEY ("currencyFromId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD CONSTRAINT "FK_38dde309d80ddc76e14b46a9333" FOREIGN KEY ("currencyToId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD CONSTRAINT "FK_61b1d2d07253d21134ffbb4a1b3" FOREIGN KEY ("paymentMethodId") REFERENCES "payment_method"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD CONSTRAINT "FK_15195e0c58e6bec6868cbc7ac7f" FOREIGN KEY ("incotermId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD CONSTRAINT "FK_0fe111e5ea041f9ec915ae719ce" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD CONSTRAINT "FK_ed0cb099f51901665a915427c97" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD CONSTRAINT "FK_789a2de21d2c42bdf786008889c" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business_plan" ADD CONSTRAINT "FK_b7ac1afdd371bd5ff418c785b11" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" ADD CONSTRAINT "FK_319df7ec61056f06f1dc1bff62d" FOREIGN KEY ("shipmentCostId") REFERENCES "shipment_cost"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" ADD CONSTRAINT "FK_b3f9f85d04e4c4bb357411c9a68" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" ADD CONSTRAINT "FK_2a5f0392c17a973d262d8b6670e" FOREIGN KEY ("shipmentId") REFERENCES "shipment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" ADD CONSTRAINT "FK_0a094aaead2900afb380c808fc0" FOREIGN KEY ("shipmentCostTypeId") REFERENCES "shipment_cost_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" ADD CONSTRAINT "FK_a0d4295e66e157c2d15fabbd144" FOREIGN KEY ("recommendedPurchaseId") REFERENCES "recommended_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" ADD CONSTRAINT "FK_3bbc6faff1a203112c13d56a920" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" ADD CONSTRAINT "FK_7fa40e952f17086bd80990543f9" FOREIGN KEY ("recommendedPurchaseId") REFERENCES "recommended_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" ADD CONSTRAINT "FK_6609dfdf64127bfef51a0312120" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" ADD CONSTRAINT "FK_8ed394ec430ad2516799a07594b" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" ADD CONSTRAINT "FK_df942b61b53dd32382f8254383b" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD CONSTRAINT "FK_b944d2043b4299a378dfac22ba9" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD CONSTRAINT "FK_f5c80d28d352d44564ee1b34949" FOREIGN KEY ("recommendedPurchaseTemplateId") REFERENCES "recommended_purchase_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD CONSTRAINT "FK_1b98ca07097fbc45dae59b5a3ef" FOREIGN KEY ("paymentMethodId") REFERENCES "payment_method"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD CONSTRAINT "FK_4507a0c178c4dced2e05a714432" FOREIGN KEY ("incotermId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD CONSTRAINT "FK_0c7398cc2a215c440022a055243" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD CONSTRAINT "FK_37b01f2bd0bbff1a6ff99559781" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD CONSTRAINT "FK_d3eb4d50f38d3e7b77eafd5fb25" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD CONSTRAINT "FK_f66bfae063912abc94dfdda6e17" FOREIGN KEY ("businessPlanId") REFERENCES "business_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD CONSTRAINT "FK_ea510ade4e4860e25a14aaf98af" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" ADD CONSTRAINT "FK_44532bb4ebe3c6d717e701042f6" FOREIGN KEY ("shipmentCostId") REFERENCES "shipment_cost"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" ADD CONSTRAINT "FK_4b030c35978d95b3fd38a3eff84" FOREIGN KEY ("settingStringId") REFERENCES "setting_string"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" ADD CONSTRAINT "FK_7887d763a1d0310fd0d45da2716" FOREIGN KEY ("recommendedPurchaseId") REFERENCES "recommended_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" ADD CONSTRAINT "FK_b2477ae65d965d98c38a08b0a06" FOREIGN KEY ("recommendedPurchaseTemplateColId") REFERENCES "recommended_purchase_template_col"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" ADD CONSTRAINT "FK_4b0de1dc73a3f2d1554f358c2ba" FOREIGN KEY ("settingStringId") REFERENCES "setting_string"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" ADD CONSTRAINT "FK_30be2849a3817c1dab6d7b7db8f" FOREIGN KEY ("roundUpContId") REFERENCES "round_up_cont"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" ADD CONSTRAINT "FK_dc631863f81821f7f36ef2e1df3" FOREIGN KEY ("roundUpContTemplateColId") REFERENCES "round_up_cont_template_col"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template_col" ADD CONSTRAINT "FK_761e4fc7f7886a5f1944e548a9c" FOREIGN KEY ("roundUpContTemplateId") REFERENCES "round_up_cont_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" ADD CONSTRAINT "FK_0956f1d610cc1f51d1ce0dba72d" FOREIGN KEY ("roundUpContId") REFERENCES "round_up_cont"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" ADD CONSTRAINT "FK_55b9a3b430e88def64dbfa75b3c" FOREIGN KEY ("roundUpContTemplateId") REFERENCES "round_up_cont_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template" ADD CONSTRAINT "FK_5b9ee39e5e04f174fd11852facd" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" ADD CONSTRAINT "FK_b1b4330833d136ccc5cf1fb1547" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" ADD CONSTRAINT "FK_c462535c6fb1d75ad8e6fc39b9a" FOREIGN KEY ("roundUpContId") REFERENCES "round_up_cont"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" ADD CONSTRAINT "FK_76e7cc946d3c515906f1d8fb470" FOREIGN KEY ("prItemId") REFERENCES "pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" ADD CONSTRAINT "FK_cafad8eff795afe6ec42f74a05a" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" ADD CONSTRAINT "FK_e696b97dd40a910d96803b8e3f4" FOREIGN KEY ("roundUpContPrId") REFERENCES "round_up_cont_pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" ADD CONSTRAINT "FK_3b3d36e7f92e4458dc23f00d336" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" ADD CONSTRAINT "FK_a8eba113909b088c3434b51359e" FOREIGN KEY ("roundUpContId") REFERENCES "round_up_cont"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" ADD CONSTRAINT "FK_cbfd799e57d0a85fa7aed1efd29" FOREIGN KEY ("budgetReceiptId") REFERENCES "budget_receipt"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" ADD CONSTRAINT "FK_0b4e6944cfbb325f547dc0da7e5" FOREIGN KEY ("prItemId") REFERENCES "pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_history" ADD CONSTRAINT "FK_10b41bd077e63546f970c636ac4" FOREIGN KEY ("budgetReceiptId") REFERENCES "budget_receipt"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" ADD CONSTRAINT "FK_19d00bde60e21dd0e8e4fc660da" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" ADD CONSTRAINT "FK_32ec5e375129c22fdcdfd79a02b" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" ADD CONSTRAINT "FK_b3727ce0a5fc2c6341567425800" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" ADD CONSTRAINT "FK_43a07ff6aea5dbc57f4db922f85" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" ADD CONSTRAINT "FK_e3e6cb562e73da197c87317a6d9" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" ADD CONSTRAINT "FK_30609fb5efc55dbe528b637a3b8" FOREIGN KEY ("employeeApprovedId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" ADD CONSTRAINT "FK_6dd002f4e8918bde5d2ed361646" FOREIGN KEY ("roundUpContId") REFERENCES "round_up_cont"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD CONSTRAINT "FK_c5cf2adfb9485a9a1be184db66c" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD CONSTRAINT "FK_5cff3aff3c166a088fb7fcaba33" FOREIGN KEY ("roundUpContTemplateId") REFERENCES "round_up_cont_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD CONSTRAINT "FK_21b36870d799272d9632aa94316" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD CONSTRAINT "FK_7296715b8f8437dfe044f650db9" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price_list_detail" ADD CONSTRAINT "FK_70b3d2a07ee305270b90ebf58dc" FOREIGN KEY ("bidPriceId") REFERENCES "bid_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" ADD CONSTRAINT "FK_34b418fb00850329693a941c154" FOREIGN KEY ("bidDealSupplierId") REFERENCES "bid_deal_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" ADD CONSTRAINT "FK_c95d5eae41bffd589aada849724" FOREIGN KEY ("bidPriceId") REFERENCES "bid_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" ADD CONSTRAINT "FK_291ace50643326687ee885f12e3" FOREIGN KEY ("bidDealId") REFERENCES "bid_deal"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" ADD CONSTRAINT "FK_14acd799af1581375e56c8e9ed6" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_deal" ADD CONSTRAINT "FK_94930fed93b5d08bd97b336a20a" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_deal" ADD CONSTRAINT "FK_0fa84e5f3dc0a7ba411603f50d3" FOREIGN KEY ("parentId") REFERENCES "bid_deal"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" ADD CONSTRAINT "FK_58e338315728b94e4d7dc64512b" FOREIGN KEY ("bidDealId") REFERENCES "bid_deal"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" ADD CONSTRAINT "FK_fa500f428373aa6dd5f183cc6fe" FOREIGN KEY ("bidPriceId") REFERENCES "bid_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_price_list_detail" ADD CONSTRAINT "FK_c3c43ede69d2f56a6c805b7ce23" FOREIGN KEY ("servicePriceId") REFERENCES "service_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_price_col" ADD CONSTRAINT "FK_b598b93058d3376e11f0a5d7ef0" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" ADD CONSTRAINT "FK_5f7d7b40cfb3d7ae2c8ee6555c5" FOREIGN KEY ("servicePriceId") REFERENCES "service_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" ADD CONSTRAINT "FK_1b5ccab6379d4d58b3fa85e7b41" FOREIGN KEY ("servicePriceColId") REFERENCES "service_price_col"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_price" ADD CONSTRAINT "FK_d4520a0074d2b259db1b40149cd" FOREIGN KEY ("parentId") REFERENCES "service_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_price" ADD CONSTRAINT "FK_4c807146960feb2faad0031f145" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" ADD CONSTRAINT "FK_2fe1c9d4d0e650eab1eb90e2191" FOREIGN KEY ("bidAuctionSupplierId") REFERENCES "bid_auction_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" ADD CONSTRAINT "FK_da27cb5423990fdcd1aa1a18a9d" FOREIGN KEY ("bidPriceId") REFERENCES "bid_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" ADD CONSTRAINT "FK_d4c542ae290d14e5afae2d9e33e" FOREIGN KEY ("bidAuctionId") REFERENCES "bid_auction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" ADD CONSTRAINT "FK_02a5453262e93dbdb2c88945407" FOREIGN KEY ("bidPriceId") REFERENCES "bid_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" ADD CONSTRAINT "FK_5736a119be595711e5d6abe52c5" FOREIGN KEY ("bidPriceId") REFERENCES "bid_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" ADD CONSTRAINT "FK_cec4a85e38a248f39dad6030c45" FOREIGN KEY ("bidPriceColId") REFERENCES "bid_price_col"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" ADD CONSTRAINT "FK_6dc4cb70fa85c3fda253011ede2" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" ADD CONSTRAINT "FK_12677bc2114915f63f5670cf496" FOREIGN KEY ("bidItemId") REFERENCES "bid_pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" ADD CONSTRAINT "FK_3071c61c2a5f338173be076e802" FOREIGN KEY ("bidExgroupId") REFERENCES "bid_exmatgroup"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" ADD CONSTRAINT "FK_037371a1b92304d1c0f2407e59e" FOREIGN KEY ("bidSupplierId") REFERENCES "bid_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" ADD CONSTRAINT "FK_656f153e808288f954929c26fd0" FOREIGN KEY ("bidPriceId") REFERENCES "bid_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" ADD CONSTRAINT "FK_a54de3eebd756601dade529646b" FOREIGN KEY ("bidPriceColId") REFERENCES "bid_price_col"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" ADD CONSTRAINT "FK_1a034db1ac5166a91766a3320ae" FOREIGN KEY ("bidSupplierId") REFERENCES "bid_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" ADD CONSTRAINT "FK_c2ca28dc7586fbdd2ca2704aa35" FOREIGN KEY ("bidPriceId") REFERENCES "bid_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price" ADD CONSTRAINT "FK_a440790cd408efa2af7dfc1a579" FOREIGN KEY ("parentId") REFERENCES "bid_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price" ADD CONSTRAINT "FK_867db5c177deae3fa9b9708dc2d" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price" ADD CONSTRAINT "FK_1e8f71f33bb081dc52ff951f1ea" FOREIGN KEY ("bidExgroupId") REFERENCES "bid_exmatgroup"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price" ADD CONSTRAINT "FK_4b18d8d606a19e22f05eda2aa9d" FOREIGN KEY ("servicePriceId") REFERENCES "service_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price" ADD CONSTRAINT "FK_9dba8c20a043fdc8d92971193f4" FOREIGN KEY ("bidItemId") REFERENCES "bid_pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_price" ADD CONSTRAINT "FK_06ef557e8c53a47cf4e9d24fe33" FOREIGN KEY ("baseItemId") REFERENCES "bid_pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" ADD CONSTRAINT "FK_16d1ee6e94f781d84aabd66c194" FOREIGN KEY ("bidSupplierId") REFERENCES "bid_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" ADD CONSTRAINT "FK_b3c0325be1f8f2b8e9ac36f940c" FOREIGN KEY ("bidPriceId") REFERENCES "bid_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_trade_list_detail" ADD CONSTRAINT "FK_1f7c17f8cf412189406f422713a" FOREIGN KEY ("bidTradeId") REFERENCES "bid_trade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_trade" ADD CONSTRAINT "FK_3b62791f9ea7005f5e904d03b1e" FOREIGN KEY ("parentId") REFERENCES "bid_trade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_trade" ADD CONSTRAINT "FK_be91681664c1ac732c62a9bf9af" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_trade" ADD CONSTRAINT "FK_4197c33978c1e13219ce2d5505b" FOREIGN KEY ("bidItemId") REFERENCES "bid_pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" ADD CONSTRAINT "FK_9aac8e343250eb68472ac29586d" FOREIGN KEY ("bidSupplierId") REFERENCES "bid_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" ADD CONSTRAINT "FK_cbeb3468d0b6a2e95cee6278ea6" FOREIGN KEY ("bidTradeId") REFERENCES "bid_trade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_custom_price_value" ADD CONSTRAINT "FK_4ac6991b9fc4be2e43b62066736" FOREIGN KEY ("bidSupplierId") REFERENCES "bid_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" ADD CONSTRAINT "FK_48178fa517c6aa634782f01f0a1" FOREIGN KEY ("bidSupplierId") REFERENCES "bid_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" ADD CONSTRAINT "FK_ab243f35d853ba3d61f70684435" FOREIGN KEY ("shipmentPriceId") REFERENCES "shipment_cost_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" ADD CONSTRAINT "FK_f0256ba2eb660b86de5838ccc76" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" ADD CONSTRAINT "FK_94afaf0e41c3cc6a62b05333f69" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" ADD CONSTRAINT "FK_00c3736269a1ba9dce75ed1eab1" FOREIGN KEY ("bidItemId") REFERENCES "bid_pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" ADD CONSTRAINT "FK_2a427d946d1e7c5e65df58021eb" FOREIGN KEY ("bidSupplierId") REFERENCES "bid_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" ADD CONSTRAINT "FK_47145f6233cbe2efec0ddfd400f" FOREIGN KEY ("bidTechId") REFERENCES "bid_tech"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_tech_list_detail" ADD CONSTRAINT "FK_4dd593368143571aceb5c285278" FOREIGN KEY ("bidTechId") REFERENCES "bid_tech"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_tech" ADD CONSTRAINT "FK_2fe4a2fbbde3f9c07132b807ad6" FOREIGN KEY ("parentId") REFERENCES "bid_tech"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_tech" ADD CONSTRAINT "FK_c7b436b32d81e53e7d9834ea324" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_tech" ADD CONSTRAINT "FK_146a75cc357e29e51dcfb4e29b2" FOREIGN KEY ("bidItemId") REFERENCES "bid_pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" ADD CONSTRAINT "FK_3640f4bfc140dc3cbc0f3135171" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" ADD CONSTRAINT "FK_4bccb43d8f408852456bd905f7d" FOREIGN KEY ("bidExgroupId") REFERENCES "bid_exmatgroup"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" ADD CONSTRAINT "FK_7c9fa4247e60e1ca6dcd0d005a8" FOREIGN KEY ("bidItemId") REFERENCES "bid_pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" ADD CONSTRAINT "FK_2ad88d1baaba82ed059ff9090dd" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" ADD CONSTRAINT "FK_8d6b936b57effc8e65a417476ae" FOREIGN KEY ("prItemId") REFERENCES "pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" ADD CONSTRAINT "FK_aa3e48bf90220f4695d81a9f1c2" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" ADD CONSTRAINT "FK_9b4eaab267adcec196f77d1f949" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" ADD CONSTRAINT "FK_c9c0e5048095c713a638f8c3b73" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" ADD CONSTRAINT "FK_2c6604e04024e064ff08e8b735e" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" ADD CONSTRAINT "FK_cf9e31e98cc93476ee3138a3d2d" FOREIGN KEY ("bidExgroupId") REFERENCES "bid_exmatgroup"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" ADD CONSTRAINT "FK_214dfe6e42461397dac93a6a66e" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" ADD CONSTRAINT "FK_2e2262f5998eb32bf2c3f5c1f65" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "external_material_group" ADD CONSTRAINT "FK_a061d3f0ae7cb6ae203a363dc68" FOREIGN KEY ("materialTypeId") REFERENCES "material_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" ADD CONSTRAINT "FK_fe1611c35570631d3312c462ade" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" ADD CONSTRAINT "FK_2e325531d7c12fd685a133439bc" FOREIGN KEY ("offerServiceId") REFERENCES "offer_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" ADD CONSTRAINT "FK_098c4adda530658b3e8615e80d5" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" ADD CONSTRAINT "FK_8c46fb2ccf3bc6c95241fd3be83" FOREIGN KEY ("shipmentPriceId") REFERENCES "shipment_cost_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer" ADD CONSTRAINT "FK_b47721a7fd707d5469426b6f107" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer" ADD CONSTRAINT "FK_62f7f0f9454992e9ad142b1123f" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer" ADD CONSTRAINT "FK_68382598864c2ea4de6b0d10a46" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer" ADD CONSTRAINT "FK_21fd2aeb1658ede130707b7c768" FOREIGN KEY ("businessPlantId") REFERENCES "business_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD CONSTRAINT "FK_95d1337b5304d0087dce61851cb" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD CONSTRAINT "FK_1e6072fb549a09805413bd2ee9d" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD CONSTRAINT "FK_c279dd6f1c5adb72dc39f11e529" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD CONSTRAINT "FK_266a893729922aae4f9772313c0" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD CONSTRAINT "FK_52f5f162637c6c426f2ab0609fb" FOREIGN KEY ("ounId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD CONSTRAINT "FK_5f2b9e18f5d3b99bb651fe755cf" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD CONSTRAINT "FK_1bb92b5a48afe1080abfad2447e" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD CONSTRAINT "FK_929cb4b9994dfaba179b6f8ae88" FOREIGN KEY ("prItemId") REFERENCES "pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offer_service" ADD CONSTRAINT "FK_4a954d26c27d94ddfb54d6a617e" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_uom" ADD CONSTRAINT "FK_98f3a2d6faf7da88cea55534c99" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_uom" ADD CONSTRAINT "FK_f854cfbd244e44105e538527286" FOREIGN KEY ("uomId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_uom" ADD CONSTRAINT "FK_c4ab0c425c3f8fea8d00bada1a5" FOREIGN KEY ("uomAlternativeId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD CONSTRAINT "FK_8381a3e58ee28d92ca599e716f9" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD CONSTRAINT "FK_b0097e6e8480c83a6a093d5096e" FOREIGN KEY ("departmentId") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" ADD CONSTRAINT "FK_a81c715097cfdadb7268a3c6f93" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" ADD CONSTRAINT "FK_de323c88d0bb034f889416bdd17" FOREIGN KEY ("reservationId") REFERENCES "reservation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" ADD CONSTRAINT "FK_82edfa9516bfe99e75573dff93e" FOREIGN KEY ("reservationItemId") REFERENCES "reservation_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" ADD CONSTRAINT "FK_d9675d88813083415663ab139dc" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD CONSTRAINT "FK_eb720657f1c94f439965a1e0924" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD CONSTRAINT "FK_c5366e6dff1e17a1c915501fc88" FOREIGN KEY ("requisitionerId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD CONSTRAINT "FK_99d98094e91f49f7bd28c27000b" FOREIGN KEY ("departmentId") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD CONSTRAINT "FK_542aff328c08cbb00f18265ee25" FOREIGN KEY ("warehouseReceivingId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation" ADD CONSTRAINT "FK_b99a51bb3d6e2fb009d8d2f7733" FOREIGN KEY ("warehouseIssueId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD CONSTRAINT "FK_95bea5815e6a171885dfaf52dad" FOREIGN KEY ("reservationId") REFERENCES "reservation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD CONSTRAINT "FK_65da55c09a185045547bf17f4dc" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD CONSTRAINT "FK_313ebecb912bdc209c41289a507" FOREIGN KEY ("uomId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reservation_item" ADD CONSTRAINT "FK_cf18fa972c4c56b8e5bdac4f7f8" FOREIGN KEY ("uomAlternativeId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "uom" ADD CONSTRAINT "FK_61d082272034b15d57b9d1ceb4a" FOREIGN KEY ("baseUnitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" ADD CONSTRAINT "FK_b52c2d03476b0389f02b55ae59b" FOREIGN KEY ("auctionSupplierId") REFERENCES "auction_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" ADD CONSTRAINT "FK_671378f570eecb7cee273187b7f" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" ADD CONSTRAINT "FK_a7e4ce508762e30a03cc63c0121" FOREIGN KEY ("prItemId") REFERENCES "pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" ADD CONSTRAINT "FK_fdbaf5040a851909cc661f9ed7b" FOREIGN KEY ("bidItemId") REFERENCES "bid_pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" ADD CONSTRAINT "FK_6cda22f6eee12c5fb930a91087e" FOREIGN KEY ("auctionId") REFERENCES "auction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" ADD CONSTRAINT "FK_f85d3c487b8b40397944e70462d" FOREIGN KEY ("exMatGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" ADD CONSTRAINT "FK_b2ee2caf4c7757c53bda116c932" FOREIGN KEY ("auctionId") REFERENCES "auction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" ADD CONSTRAINT "FK_08ab6c1ec05a27b032a5a59647c" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction" ADD CONSTRAINT "FK_ed52d5d5467372914c1d8ae0293" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction" ADD CONSTRAINT "FK_83ef4a2004de540c8b62cc7d819" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auction" ADD CONSTRAINT "FK_a35536b1e6e1c61dfac69d80dbf" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_history" ADD CONSTRAINT "FK_cedae6a0aa32c469442a9465bf5" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_history" ADD CONSTRAINT "FK_1add2777fbf78612fd65d33a930" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" ADD CONSTRAINT "FK_945717da2a116ba1049afa2176d" FOREIGN KEY ("employeeAccessId") REFERENCES "bid_employee_access"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" ADD CONSTRAINT "FK_5feaa34fe6080d172cc22472827" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" ADD CONSTRAINT "FK_e1ab4ee56faa6b39f43ee3e8d72" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" ADD CONSTRAINT "FK_3eb200381cf528ef3bb0328ddaf" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee_notify" ADD CONSTRAINT "FK_829b874115967ba418739c0020d" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee_warning" ADD CONSTRAINT "FK_f8729a94462abe07155640c0bc6" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "faq" ADD CONSTRAINT "FK_89ec0666829fad019cdf0dcac22" FOREIGN KEY ("categoryId") REFERENCES "faq_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_custom_price" ADD CONSTRAINT "FK_2092855ad0938615c9692bf66d5" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_tech_list_detail" ADD CONSTRAINT "FK_146154f9f04aa7b69ae91e183c2" FOREIGN KEY ("serviceTechId") REFERENCES "service_tech"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_tech" ADD CONSTRAINT "FK_3d46899c062ba4e9b5063c3992a" FOREIGN KEY ("parentId") REFERENCES "service_tech"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_tech" ADD CONSTRAINT "FK_662fc4b072225f31c77b1e80a85" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_trade_list_detail" ADD CONSTRAINT "FK_24bb7ed260a75bba7c4bbde494b" FOREIGN KEY ("serviceTradeId") REFERENCES "service_trade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_trade" ADD CONSTRAINT "FK_e47ba7f64cf736318a65b1c9c5d" FOREIGN KEY ("parentId") REFERENCES "service_trade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_trade" ADD CONSTRAINT "FK_82da0abb34d4309aab022fa6cb2" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" ADD CONSTRAINT "FK_25ab41887eb8ff9c33d22870a26" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" ADD CONSTRAINT "FK_14c6ca8022f36caccb2c0d08b57" FOREIGN KEY ("supplierExpertiseId") REFERENCES "supplier_expertise"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_law_detail" ADD CONSTRAINT "FK_61a4e2682fbbaf81ccad6ef2c1e" FOREIGN KEY ("supplierExpertiseId") REFERENCES "supplier_expertise"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_history" ADD CONSTRAINT "FK_0a04398f3acc707810153fdc5c6" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_list_detail" ADD CONSTRAINT "FK_6ddada23ada70eb762bd1a23629" FOREIGN KEY ("supplierCapacityId") REFERENCES "supplier_capacity"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_year_value" ADD CONSTRAINT "FK_91d2bd0882d25205d6caa30510c" FOREIGN KEY ("supplierCapacityId") REFERENCES "supplier_capacity"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_notify" ADD CONSTRAINT "FK_1c604e9c16b9d840aa0b73065db" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "setting_role" ADD CONSTRAINT "FK_ff016913fb6a03da4a2e474968e" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number" ADD CONSTRAINT "FK_90209110c6739b8ad68a20030db" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number" ADD CONSTRAINT "FK_f9011f5cd49dcab82a8faebb3b0" FOREIGN KEY ("supplierNumberRequestApproveId") REFERENCES "supplier_number_request_approve"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_number" ADD CONSTRAINT "FK_6883442a1e4c4469ae07d295ec2" FOREIGN KEY ("businessPartnerGroupId") REFERENCES "business_partner_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_po" ADD CONSTRAINT "FK_55727db416e5bcae91285e6d45d" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_po" ADD CONSTRAINT "FK_2cc3b270e1663c542d5561a2301" FOREIGN KEY ("shipmentId") REFERENCES "shipment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_document_handover" ADD CONSTRAINT "FK_96edb747773eb974dbafff0b621" FOREIGN KEY ("inboundId") REFERENCES "inbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_item" ADD CONSTRAINT "FK_e4b0906b4cfeb96bd72b6d1dfab" FOREIGN KEY ("shipmentId") REFERENCES "shipment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_item" ADD CONSTRAINT "FK_1ffd57cdd90a619e868f5216fb1" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_item" ADD CONSTRAINT "FK_727fe27762da6a6616ddc447a35" FOREIGN KEY ("inboundId") REFERENCES "inbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD CONSTRAINT "FK_23f36c98b6495d315bb829f684e" FOREIGN KEY ("employeeInchargeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD CONSTRAINT "FK_c728ef0f5f8d3bb3999240174f4" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound" ADD CONSTRAINT "FK_63e613d78ecc42f633f97a69e4f" FOREIGN KEY ("expectWarehouseId") REFERENCES "warehouse"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" ADD CONSTRAINT "FK_3ece30f79e56953c6895c88982b" FOREIGN KEY ("inboundId") REFERENCES "inbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" ADD CONSTRAINT "FK_56c5a8c813ff83ce4189d7567d4" FOREIGN KEY ("shipmentId") REFERENCES "shipment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment_container" ADD CONSTRAINT "FK_4d49ec47988e1cb6d474f99aecd" FOREIGN KEY ("shipmentId") REFERENCES "shipment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment" ADD CONSTRAINT "FK_9e154a0a8ab04ba7981356d9597" FOREIGN KEY ("shipmentRouteId") REFERENCES "shipment_route"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shipment" ADD CONSTRAINT "FK_53bb21f7164a3ee0095d6b32666" FOREIGN KEY ("shipmentCostTypeId") REFERENCES "shipment_cost_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_item" ADD CONSTRAINT "FK_0042af83254454c62fe5156e397" FOREIGN KEY ("inboundId") REFERENCES "inbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_item" ADD CONSTRAINT "FK_c30cf0f62e106d0c3b2adfcb036" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_item" ADD CONSTRAINT "FK_7a0543804dd8131978038775fdf" FOREIGN KEY ("poProductId") REFERENCES "po_product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inbound_container" ADD CONSTRAINT "FK_f23decf7082028bc724c552f689" FOREIGN KEY ("inboundId") REFERENCES "inbound"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_confirm_code" ADD CONSTRAINT "FK_1d3f2a4a0693ef7a44e57284ea9" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_access" ADD CONSTRAINT "FK_f5b81b0704c5b994467aa8120b3" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_access" ADD CONSTRAINT "FK_d70b3e485a2bb3b9df9d23520e1" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_member" ADD CONSTRAINT "FK_23604fe008a588fdb81f16e8db7" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" ADD CONSTRAINT "FK_409e23ca5ef234341acb1b1b61e" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" ADD CONSTRAINT "FK_f267d1620d405aecb0f9e8b2b3a" FOREIGN KEY ("bankBranchId") REFERENCES "bank_branch"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" ADD CONSTRAINT "FK_04d8fa067e5e953776515b0b467" FOREIGN KEY ("bankId") REFERENCES "bank"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" ADD CONSTRAINT "FK_8b763f7931bbc3caaef8cb377c9" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" ADD CONSTRAINT "FK_6f1d8a00b7ca9ebcfa1a486ab4e" FOREIGN KEY ("countryId") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ADD CONSTRAINT "FK_c28f181b6bc4d1d5c5d2ffd48d3" FOREIGN KEY ("countryId") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ADD CONSTRAINT "FK_63e9013142a18791a9c30b9d919" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bank_branch" ADD CONSTRAINT "FK_c2dcee7baa3689de70712030222" FOREIGN KEY ("bankId") REFERENCES "bank"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "region" ADD CONSTRAINT "FK_75ceb9efda6c228a50d88dcdfb8" FOREIGN KEY ("countryId") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "factory_supplier" ADD CONSTRAINT "FK_db22cf536eca5a1cf775a2ec114" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" ADD CONSTRAINT "FK_01b10d60210f802e05af7ae27dd" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" ADD CONSTRAINT "FK_fcfe5b3a6936c620cfbd072868c" FOREIGN KEY ("supplierServiceId") REFERENCES "supplier_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "site_assessment" ADD CONSTRAINT "FK_cbaa527ca33e12dcd6e6667e757" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "site_assessment" ADD CONSTRAINT "FK_bfa745fecb25ab5de1fa09049ee" FOREIGN KEY ("factorySupplierId") REFERENCES "factory_supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "site_assessment" ADD CONSTRAINT "FK_c74ce6419ee44d28fb24738ce2c" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "site_assessment" ADD CONSTRAINT "FK_e615bc382db5fbcd328fa0d7d2d" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "site_assessment" ADD CONSTRAINT "FK_0a3a2f5d3eee9ab91a6fa16c2a6" FOREIGN KEY ("plantSiteAssessmentId") REFERENCES "plan_site_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" ADD CONSTRAINT "FK_71c9f7ef0197019c21b302c7d94" FOREIGN KEY ("criteriaSiteAssessmentId") REFERENCES "criteria_site_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD CONSTRAINT "FK_5098f91ec52a61fdb326e2ac9e5" FOREIGN KEY ("parentId") REFERENCES "criteria_site_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" ADD CONSTRAINT "FK_6f52b7fc7657af04b3e748e0c16" FOREIGN KEY ("siteAssessmentId") REFERENCES "site_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade" ADD CONSTRAINT "FK_6dc3eb5c5f50a3164abfbb253f7" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" ADD CONSTRAINT "FK_17424dc39277092fd5b49cfa4a7" FOREIGN KEY ("evaluationHistoryPurchaseId") REFERENCES "evaluation_history_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" ADD CONSTRAINT "FK_bf2f38beb9d637ca530ef33c259" FOREIGN KEY ("supplierUpgradeId") REFERENCES "supplier_upgrade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" ADD CONSTRAINT "FK_18350f56ee46484cf04d85246a5" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" ADD CONSTRAINT "FK_5beddaa92916430024dd800fc65" FOREIGN KEY ("prItemId") REFERENCES "pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" ADD CONSTRAINT "FK_8e7161051f94bd3bdef552e70d6" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_list_detail" ADD CONSTRAINT "FK_4722f0d2ccf61a22efb684af696" FOREIGN KEY ("evaluationHistoryPurchaseId") REFERENCES "evaluation_history_purchase_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" ADD CONSTRAINT "FK_6fe7588e3f58faa3996cc5ffd6a" FOREIGN KEY ("evaluationHistoryPurchaseId") REFERENCES "evaluation_history_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" ADD CONSTRAINT "FK_983eec0b89f31767bf16514d0a1" FOREIGN KEY ("parentId") REFERENCES "evaluation_history_purchase_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" ADD CONSTRAINT "FK_5ef5127a079855988ca4bdf1c69" FOREIGN KEY ("evaluationHistoryPurchaseId") REFERENCES "evaluation_history_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" ADD CONSTRAINT "FK_fec6a681f2e02560c04813e31ab" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" ADD CONSTRAINT "FK_59e417fa710232418ae62af887e" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" ADD CONSTRAINT "FK_7e693a7b45f006c23c99e25b96e" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_company" ADD CONSTRAINT "FK_6b0274ea0b51809be3e668018b4" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_company" ADD CONSTRAINT "FK_58909d0a5cf1b73b4eb72da275c" FOREIGN KEY ("kpiId") REFERENCES "kpi"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_list_detail" ADD CONSTRAINT "FK_d6b3e6b43e92b2bcb63f43a75d0" FOREIGN KEY ("kpiDetailId") REFERENCES "kpi_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_position" ADD CONSTRAINT "FK_0335c46dbf129221b2baec68c2e" FOREIGN KEY ("positionId") REFERENCES "position"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_position" ADD CONSTRAINT "FK_af07677ea6e363186ce9002ce7d" FOREIGN KEY ("kpiId") REFERENCES "kpi"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "position" ADD CONSTRAINT "FK_3e108d1ddd5081c22fa6cde2929" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" ADD CONSTRAINT "FK_901869ca55c897c9a40ff8f24db" FOREIGN KEY ("positionId") REFERENCES "position"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" ADD CONSTRAINT "FK_2d565994cd9d4ed96c45a551908" FOREIGN KEY ("kpiId") REFERENCES "kpi"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" ADD CONSTRAINT "FK_87e7c18f48174c0afb66736c268" FOREIGN KEY ("kpiDetailId") REFERENCES "kpi_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" ADD CONSTRAINT "FK_4b29d3f63a0cb37472321af98e4" FOREIGN KEY ("parentId") REFERENCES "kpi_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" ADD CONSTRAINT "FK_b4aa7c402dfc9abfd5b21a4fb39" FOREIGN KEY ("kpiId") REFERENCES "kpi"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_scale" ADD CONSTRAINT "FK_4be103c46763a565561c5fb3d8a" FOREIGN KEY ("kpiId") REFERENCES "kpi"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" ADD CONSTRAINT "FK_21d04915e24ab81e30d745bca66" FOREIGN KEY ("parentId") REFERENCES "kpi_permission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" ADD CONSTRAINT "FK_8bb90496bc97dd27d03781176dd" FOREIGN KEY ("kpiId") REFERENCES "kpi"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "kpi" ADD CONSTRAINT "FK_d681a2d4568c32f0bf1f959d124" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_list_detail" ADD CONSTRAINT "FK_753ddc4a8712b090e8978615a3e" FOREIGN KEY ("ticketEvaluationKpiDetailId") REFERENCES "ticket_evaluation_kpi_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" ADD CONSTRAINT "FK_1a03b0ca72b0c3ca6d4022c3440" FOREIGN KEY ("parentId") REFERENCES "ticket_evaluation_kpi_detail"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" ADD CONSTRAINT "FK_4f75a6efb7f486da306108ff0f1" FOREIGN KEY ("ticketEvaluationKpiId") REFERENCES "ticket_evaluation_kpi"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi" ADD CONSTRAINT "FK_c57e79045293a991fe21f2b8616" FOREIGN KEY ("kpiId") REFERENCES "kpi"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" ADD CONSTRAINT "FK_9b7b7021050caa569bf4696bb27" FOREIGN KEY ("ticketEvaluationKpiId") REFERENCES "ticket_evaluation_kpi"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" ADD CONSTRAINT "FK_189aec52f7ad3f9c26bba3ca7fa" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" ADD CONSTRAINT "FK_6dbe2fa8007274e457ab84e8596" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" ADD CONSTRAINT "FK_60727ec7d7f9a21251595d1d708" FOREIGN KEY ("blockId") REFERENCES "block"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" ADD CONSTRAINT "FK_694d6561de7037de9b503fa1081" FOREIGN KEY ("departmentId") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" ADD CONSTRAINT "FK_42430478067ffe20b173d1e0b4d" FOREIGN KEY ("partId") REFERENCES "part"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" ADD CONSTRAINT "FK_fea404d0348bb49babc1933031e" FOREIGN KEY ("positionId") REFERENCES "position"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "block" ADD CONSTRAINT "FK_4d8ab272fa9ba5f4753427daf7b" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "block" ADD CONSTRAINT "FK_e96aee97dbe6375a95db3260c7b" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_service_history" ADD CONSTRAINT "FK_bdd65d9ecb07cddbeb1e314f22f" FOREIGN KEY ("supplierServiceId") REFERENCES "supplier_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" ADD CONSTRAINT "FK_472fbec079cd21902135300f65a" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" ADD CONSTRAINT "FK_7755f53936f46edab1e94a69b34" FOREIGN KEY ("poAcceptanceId") REFERENCES "po_acceptance"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_acceptance" ADD CONSTRAINT "FK_486b3dbea510ad34aafdd1459af" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "permission_employee" ADD CONSTRAINT "FK_799ca14e17fb8bbb87908b577fd" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "permission_approve" ADD CONSTRAINT "FK_f9688b6619160d29b70e6d8dc23" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "permission_individual" ADD CONSTRAINT "FK_47dbeb860b692c2984bd1ee5204" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_potential_upgrade" ADD CONSTRAINT "FK_96d404106e9aea66c337601f956" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD CONSTRAINT "FK_67d4c2ecf748c2b1e1a0d005452" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD CONSTRAINT "FK_667faf8aa18019a0be5969f00a3" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "template_lead_time" ADD CONSTRAINT "FK_a40faf13f4f1ef1e24e94644d1e" FOREIGN KEY ("headerLeadTimeId") REFERENCES "header_lead_time"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" ADD CONSTRAINT "FK_e69bb7f7956f48e5ea4e1d162d6" FOREIGN KEY ("headerLeadTimeId") REFERENCES "header_lead_time"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" ADD CONSTRAINT "FK_b7dea34b1fbaa3bca183e9089a7" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" ADD CONSTRAINT "FK_d3283beb2e1bb261de57765b1de" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" ADD CONSTRAINT "FK_5442dcc68f283ce0b00a08119db" FOREIGN KEY ("prItemId") REFERENCES "pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" ADD CONSTRAINT "FK_ac6ea51b4fe4c04bcdd1b47289f" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_product" ADD CONSTRAINT "FK_babde8a2029fbfc7e461716c732" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_product" ADD CONSTRAINT "FK_440105751648ea3d779e534cae2" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_product" ADD CONSTRAINT "FK_4988bdc3aeb03f2942dd547e8b7" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_product" ADD CONSTRAINT "FK_2b720950f95ada26703d43d0ade" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_product" ADD CONSTRAINT "FK_c52abf4abddeac5cf50574defc5" FOREIGN KEY ("poPrId") REFERENCES "po_pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" ADD CONSTRAINT "FK_ba2d6561c8edf521a953e60e26b" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" ADD CONSTRAINT "FK_f648a16e99b3ab2539ab82a1b01" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" ADD CONSTRAINT "FK_d3b785b0d918e399eb65987f7da" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_e5fb555ea198a0aec1530f5e46b" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_0b571b7679cf41929f5f3db2d89" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_e1e5f8634306efc40704a8a2628" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_9ae35e3316a98f3a9d740df26c0" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_717ae013a85f97e8e5685225037" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_20b22b68714456b5d4fcd03e1a5" FOREIGN KEY ("auctionId") REFERENCES "auction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_29298fefafdf17b73ddc57fb9b2" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_6a9bd6d1b0c829ba79e9df5269b" FOREIGN KEY ("contractPaymentPlanId") REFERENCES "payment_progress"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_2d2bc7cdf8143103d604f9900a3" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_089021c6354d574f686474375c4" FOREIGN KEY ("incotermId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_65e7018a5249abde4bc8984601c" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_fe4f20bbf025284450f3eb76dfb" FOREIGN KEY ("parentId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "po" ADD CONSTRAINT "FK_2b1fd6c325d0f941fdb4d3641bf" FOREIGN KEY ("recommendedPurchaseId") REFERENCES "recommended_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_progress" ADD CONSTRAINT "FK_ca21e5bc8d43455f8d543924951" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_progress" ADD CONSTRAINT "FK_327c5af92cf9846e831ed369853" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_progress" ADD CONSTRAINT "FK_64ef4ec3366d9912888f4726469" FOREIGN KEY ("paymentMethodId") REFERENCES "payment_method"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" ADD CONSTRAINT "FK_86c98c734f4a83226f818b9d84f" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" ADD CONSTRAINT "FK_9e6e482db5c7a0c06a0a946fcce" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_5f9aba86ffb85928d9bda6e9244" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_67fbad3a7aa24e0309aa820141b" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_e646a1a52f73e26ed0181e23954" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_d34578c47b89e5d6df2b6c26308" FOREIGN KEY ("incotermId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_06205a50255cd1483c099261c55" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_94641949661345c81577cdf4f11" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_8800bdc1430bda6326a79d70289" FOREIGN KEY ("auctionId") REFERENCES "auction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_1ceb00c0f3409976b5c203aef62" FOREIGN KEY ("offerId") REFERENCES "offer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_f61fc8649bdf23ed0554e37aac3" FOREIGN KEY ("recommendedPurchaseId") REFERENCES "recommended_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_pr" ADD CONSTRAINT "FK_5794fc22296992c81baa0e195a6" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_pr" ADD CONSTRAINT "FK_9f0a7da0ba899e9de495c4f8e3b" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_26832b04405eadd44f8c0c7d59b" FOREIGN KEY ("departmentId") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_5b0c36168da11741c39e798f7b8" FOREIGN KEY ("requisitionerId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_6066264aabfdcd542fc6a3c0c9d" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_df605ae982735f147e9c6474868" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_2b6813d4993bef855c6d78342c3" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_525a439839309d9d959323c20ff" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_4dd78943e2825467aded133ffe4" FOREIGN KEY ("roundUpContId") REFERENCES "round_up_cont"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_c3dd5d28af70015000aef46709c" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_e6cd71f78b524c85fb61a53a73b" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_b23c995c959b6188cb08a3f622c" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_2501011e9b19a1dbbdf4b1a253c" FOREIGN KEY ("ounId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_76d132894f2795b3fd554f71021" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_b077ac8e2f98be513b0a4adeb22" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_4f3dcad0f8b7a9de1a3586c75ee" FOREIGN KEY ("glAccountId") REFERENCES "gl_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_f46b4eaadb685dc4462f80a51e2" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_59e8a9cc2e2435ef62ae7402a1a" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_4693ef621ed8c42139f54828bab" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pr_item" ADD CONSTRAINT "FK_f1baae7bdff899bdf012d96ce7f" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "media_file" ADD CONSTRAINT "FK_55970c9780123468454379d06da" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "media_file" ADD CONSTRAINT "FK_1cc5cb77854e671446b446350ae" FOREIGN KEY ("prItemId") REFERENCES "pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "media_file" ADD CONSTRAINT "FK_219c6e4dc65911e58f80e98af45" FOREIGN KEY ("shipmentId") REFERENCES "shipment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "media_file" ADD CONSTRAINT "FK_76c105fac888a3fd39c0d7f5237" FOREIGN KEY ("businessPlanId") REFERENCES "business_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "media_file" ADD CONSTRAINT "FK_46097540235516368df3f9b4815" FOREIGN KEY ("recommendedPurchaseId") REFERENCES "recommended_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "media_file" ADD CONSTRAINT "FK_0b68aa07ebdddca98130b2bbc09" FOREIGN KEY ("contracyDocumentHandoverId") REFERENCES "contract_document_handover"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "media_file" ADD CONSTRAINT "FK_9ad5009fd5ee116750314b3112b" FOREIGN KEY ("supplierUpgradeId") REFERENCES "supplier_upgrade"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_230fdd10e2ec2fef8487373326d" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_96186eebb7d229c78816272197c" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_668784a765512e869dc2bdd8ce8" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_c357cc6b79ec0b39dbff21ca014" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_5d02cdb5c4ed1932f14ca3b4a3b" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_e88dd51ee57b5a2da314897478d" FOREIGN KEY ("materialTypeId") REFERENCES "material_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_05e8afb214220d7369fc8ce7121" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_93fa8f0c6478ea24e68ea6aa55d" FOREIGN KEY ("unitOfMassId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_21c59564d2cab04c8a2290d9340" FOREIGN KEY ("unitOfVolumeId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material" ADD CONSTRAINT "FK_79f678cfee42ed658578f1d65ad" FOREIGN KEY ("unitOfMeasurementId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD CONSTRAINT "FK_9f3786b8c6b3544cf1788c9578c" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD CONSTRAINT "FK_7d1711ee9ed8903e543e2cfe26d" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD CONSTRAINT "FK_5532f5738a7ac449fc235236f38" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD CONSTRAINT "FK_4a91d40f4cd48c67185aac810bd" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD CONSTRAINT "FK_ade03a7fb53dc16d53df8e1a272" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_plant" ADD CONSTRAINT "FK_9932501b854a513c65dc273c839" FOREIGN KEY ("materialPriceId") REFERENCES "material_price"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "plant" ADD CONSTRAINT "FK_e2f806458a1b9221895c16f732f" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "company" ADD CONSTRAINT "FK_88a7bb888e7b5497049ed3b7780" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" ADD CONSTRAINT "FK_972bb224dcfa0d09008cc54baf5" FOREIGN KEY ("flowApproveId") REFERENCES "flow_approve"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" ADD CONSTRAINT "FK_fa7233280ec0b0524feed660d20" FOREIGN KEY ("flowBaseId") REFERENCES "flow_approve_base"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "flow_approve" ADD CONSTRAINT "FK_2bbd46aeaac94543bb2a42c6dd5" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "flow_approve" ADD CONSTRAINT "FK_33745382d89ce3e3a5767e0429d" FOREIGN KEY ("departmentId") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "department" ADD CONSTRAINT "FK_1c9f0159b4ae69008bd356bb1ce" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "department" ADD CONSTRAINT "FK_3fa689e7846019b2f6ba63a5ec8" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "department" ADD CONSTRAINT "FK_8a58359bca9a68c8cbe9b36a249" FOREIGN KEY ("blockId") REFERENCES "block"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee_role" ADD CONSTRAINT "FK_607b4ff74f9698b5ceb556cac36" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_26c3d513e0832e5abbbdd3d2a88" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_3ecbbd14b56e4d4cba9805a6cd5" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_aff396af6e595420a64943f4c26" FOREIGN KEY ("positionId") REFERENCES "position"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_9ad20e4029f9458b6eed0b0c454" FOREIGN KEY ("departmentId") REFERENCES "department"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_c198bfebee9e60515b42d0c69f1" FOREIGN KEY ("blockId") REFERENCES "block"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_41f32656086e8795d0665a024ba" FOREIGN KEY ("partId") REFERENCES "part"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_f4b0d329c4a3cf79ffe9d565047" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_96f5c924a27ede4a8e042ff6474" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_0c2b01befa31dd8352bf0818cb9" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "employee" ADD CONSTRAINT "FK_374770032d4b1e34b5a44a12620" FOREIGN KEY ("permissionIndividualId") REFERENCES "permission_individual"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" ADD CONSTRAINT "FK_12f94c1dacf89108d7f46c63614" FOREIGN KEY ("approvedLawId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" ADD CONSTRAINT "FK_10cffdbce6d3a175451316e8028" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" ADD CONSTRAINT "FK_d096408a337c2f47410e8c9d1bb" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" ADD CONSTRAINT "FK_7a85deff050ee041cbdac9191a0" FOREIGN KEY ("supplierServiceId") REFERENCES "supplier_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD CONSTRAINT "FK_6b05b7e468e9a3017dc6d3f166d" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD CONSTRAINT "FK_f5c9f4bef5827bfb11d397889c5" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" ADD CONSTRAINT "FK_3c21723f51589880426ee09a38f" FOREIGN KEY ("parentId") REFERENCES "supplier_capacity"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" ADD CONSTRAINT "FK_f4c199649f05d2e0d4b6b1d1821" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" ADD CONSTRAINT "FK_a233c4a4427a6f87cc7fafc94e0" FOREIGN KEY ("serviceCapacityId") REFERENCES "service_capacity"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" ADD CONSTRAINT "FK_658a89d26d22b5889d2c8808343" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" ADD CONSTRAINT "FK_5224e0a2a8a3950a956ce919e92" FOREIGN KEY ("supplierServiceId") REFERENCES "supplier_service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_capacity" ADD CONSTRAINT "FK_dfaf935c8daef047d0dcf2bcde4" FOREIGN KEY ("parentId") REFERENCES "service_capacity"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_capacity" ADD CONSTRAINT "FK_e7b898103d3542a0127af5c146a" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_capacity" ADD CONSTRAINT "FK_2a527d9e453940590f4e611af67" FOREIGN KEY ("exMatGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service" ADD CONSTRAINT "FK_c5906ffd2cd6fa558710901e4d0" FOREIGN KEY ("parentId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service" ADD CONSTRAINT "FK_9228bad899a36c28a90bc34608a" FOREIGN KEY ("approveById") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid" ADD CONSTRAINT "FK_b7c6d96e04a08be91779ed34aa1" FOREIGN KEY ("businessPlantId") REFERENCES "business_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid" ADD CONSTRAINT "FK_97c59f5857f8e386c213640b046" FOREIGN KEY ("bidTypeId") REFERENCES "bid_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid" ADD CONSTRAINT "FK_e2aeadf35be1bbbbc7a86a828d5" FOREIGN KEY ("masterBidGuaranteeId") REFERENCES "setting_string"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid" ADD CONSTRAINT "FK_1854c79454cd8bbbf31cac76304" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid" ADD CONSTRAINT "FK_4b510836b0248b8ea62ce46723c" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid" ADD CONSTRAINT "FK_f8b5e1f8b78057b1b4046579841" FOREIGN KEY ("exMatGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid" ADD CONSTRAINT "FK_b63d03bb57eb5846ffcf79a036c" FOREIGN KEY ("prItemId") REFERENCES "pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid" ADD CONSTRAINT "FK_4967726b62bb9e095395d7cee50" FOREIGN KEY ("parentId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid" ADD CONSTRAINT "FK_009f84d1cdff98ad4ead3776d84" FOREIGN KEY ("shipmentId") REFERENCES "shipment_cost"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_auction" ADD CONSTRAINT "FK_98fb3b554a2cbb46e250fdf9321" FOREIGN KEY ("bidId") REFERENCES "bid"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_auction" ADD CONSTRAINT "FK_e702054a4806f40ed5a61982af2" FOREIGN KEY ("parentId") REFERENCES "bid_auction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" ADD CONSTRAINT "FK_0ffe9b806010eebbba16db32b77" FOREIGN KEY ("bidAuctionId") REFERENCES "bid_auction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" ADD CONSTRAINT "FK_4f8b58f91c29f00e1a7d285de54" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "FK_e8902c50550ff82dd0143913c0a" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "FK_4df75df2edeff9fb748d216bc50" FOREIGN KEY ("businessPartnerGroupId") REFERENCES "business_partner_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "FK_9b9e98aee5967cd71e0d773dd1b" FOREIGN KEY ("countryId") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "FK_791d9c9481ccda37ab36f5e1ead" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "FK_21d5eab891f3a77c23b0dd6d0de" FOREIGN KEY ("supplierNumberId") REFERENCES "supplier_number"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "FK_c86c2f41484d9b79fa8c4ca75ac" FOREIGN KEY ("supplierSchemaId") REFERENCES "supplier_schema"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "supplier" ADD CONSTRAINT "FK_ca444295a2048a2f14a94eb3d65" FOREIGN KEY ("businessTypeId") REFERENCES "business_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "FK_031cdc2c9c5eb56d48b5bdb4e54" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "FK_ab4a80281f1e8d524714e00f38f" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_external_material_group" ADD CONSTRAINT "FK_1c8cc4c312ab96d8bef13ad39ab" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_external_material_group" ADD CONSTRAINT "FK_14246f789ae24b0bdcb949b72db" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_external_material_group" DROP CONSTRAINT "FK_14246f789ae24b0bdcb949b72db"`);
        await queryRunner.query(`ALTER TABLE "user_external_material_group" DROP CONSTRAINT "FK_1c8cc4c312ab96d8bef13ad39ab"`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "FK_ab4a80281f1e8d524714e00f38f"`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "FK_031cdc2c9c5eb56d48b5bdb4e54"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_ca444295a2048a2f14a94eb3d65"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_c86c2f41484d9b79fa8c4ca75ac"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_21d5eab891f3a77c23b0dd6d0de"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_791d9c9481ccda37ab36f5e1ead"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_9b9e98aee5967cd71e0d773dd1b"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_4df75df2edeff9fb748d216bc50"`);
        await queryRunner.query(`ALTER TABLE "supplier" DROP CONSTRAINT "FK_e8902c50550ff82dd0143913c0a"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" DROP CONSTRAINT "FK_4f8b58f91c29f00e1a7d285de54"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier" DROP CONSTRAINT "FK_0ffe9b806010eebbba16db32b77"`);
        await queryRunner.query(`ALTER TABLE "bid_auction" DROP CONSTRAINT "FK_e702054a4806f40ed5a61982af2"`);
        await queryRunner.query(`ALTER TABLE "bid_auction" DROP CONSTRAINT "FK_98fb3b554a2cbb46e250fdf9321"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP CONSTRAINT "FK_009f84d1cdff98ad4ead3776d84"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP CONSTRAINT "FK_4967726b62bb9e095395d7cee50"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP CONSTRAINT "FK_b63d03bb57eb5846ffcf79a036c"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP CONSTRAINT "FK_f8b5e1f8b78057b1b4046579841"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP CONSTRAINT "FK_4b510836b0248b8ea62ce46723c"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP CONSTRAINT "FK_1854c79454cd8bbbf31cac76304"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP CONSTRAINT "FK_e2aeadf35be1bbbbc7a86a828d5"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP CONSTRAINT "FK_97c59f5857f8e386c213640b046"`);
        await queryRunner.query(`ALTER TABLE "bid" DROP CONSTRAINT "FK_b7c6d96e04a08be91779ed34aa1"`);
        await queryRunner.query(`ALTER TABLE "service" DROP CONSTRAINT "FK_9228bad899a36c28a90bc34608a"`);
        await queryRunner.query(`ALTER TABLE "service" DROP CONSTRAINT "FK_c5906ffd2cd6fa558710901e4d0"`);
        await queryRunner.query(`ALTER TABLE "service_capacity" DROP CONSTRAINT "FK_2a527d9e453940590f4e611af67"`);
        await queryRunner.query(`ALTER TABLE "service_capacity" DROP CONSTRAINT "FK_e7b898103d3542a0127af5c146a"`);
        await queryRunner.query(`ALTER TABLE "service_capacity" DROP CONSTRAINT "FK_dfaf935c8daef047d0dcf2bcde4"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" DROP CONSTRAINT "FK_5224e0a2a8a3950a956ce919e92"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" DROP CONSTRAINT "FK_658a89d26d22b5889d2c8808343"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" DROP CONSTRAINT "FK_a233c4a4427a6f87cc7fafc94e0"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" DROP CONSTRAINT "FK_f4c199649f05d2e0d4b6b1d1821"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity" DROP CONSTRAINT "FK_3c21723f51589880426ee09a38f"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP CONSTRAINT "FK_f5c9f4bef5827bfb11d397889c5"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP CONSTRAINT "FK_6b05b7e468e9a3017dc6d3f166d"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" DROP CONSTRAINT "FK_7a85deff050ee041cbdac9191a0"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" DROP CONSTRAINT "FK_d096408a337c2f47410e8c9d1bb"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" DROP CONSTRAINT "FK_10cffdbce6d3a175451316e8028"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise" DROP CONSTRAINT "FK_12f94c1dacf89108d7f46c63614"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_374770032d4b1e34b5a44a12620"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_0c2b01befa31dd8352bf0818cb9"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_96f5c924a27ede4a8e042ff6474"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_f4b0d329c4a3cf79ffe9d565047"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_41f32656086e8795d0665a024ba"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_c198bfebee9e60515b42d0c69f1"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_9ad20e4029f9458b6eed0b0c454"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_aff396af6e595420a64943f4c26"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_3ecbbd14b56e4d4cba9805a6cd5"`);
        await queryRunner.query(`ALTER TABLE "employee" DROP CONSTRAINT "FK_26c3d513e0832e5abbbdd3d2a88"`);
        await queryRunner.query(`ALTER TABLE "employee_role" DROP CONSTRAINT "FK_607b4ff74f9698b5ceb556cac36"`);
        await queryRunner.query(`ALTER TABLE "department" DROP CONSTRAINT "FK_8a58359bca9a68c8cbe9b36a249"`);
        await queryRunner.query(`ALTER TABLE "department" DROP CONSTRAINT "FK_3fa689e7846019b2f6ba63a5ec8"`);
        await queryRunner.query(`ALTER TABLE "department" DROP CONSTRAINT "FK_1c9f0159b4ae69008bd356bb1ce"`);
        await queryRunner.query(`ALTER TABLE "flow_approve" DROP CONSTRAINT "FK_33745382d89ce3e3a5767e0429d"`);
        await queryRunner.query(`ALTER TABLE "flow_approve" DROP CONSTRAINT "FK_2bbd46aeaac94543bb2a42c6dd5"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" DROP CONSTRAINT "FK_fa7233280ec0b0524feed660d20"`);
        await queryRunner.query(`ALTER TABLE "flow_approve_detail" DROP CONSTRAINT "FK_972bb224dcfa0d09008cc54baf5"`);
        await queryRunner.query(`ALTER TABLE "company" DROP CONSTRAINT "FK_88a7bb888e7b5497049ed3b7780"`);
        await queryRunner.query(`ALTER TABLE "plant" DROP CONSTRAINT "FK_e2f806458a1b9221895c16f732f"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP CONSTRAINT "FK_9932501b854a513c65dc273c839"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP CONSTRAINT "FK_ade03a7fb53dc16d53df8e1a272"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP CONSTRAINT "FK_4a91d40f4cd48c67185aac810bd"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP CONSTRAINT "FK_5532f5738a7ac449fc235236f38"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP CONSTRAINT "FK_7d1711ee9ed8903e543e2cfe26d"`);
        await queryRunner.query(`ALTER TABLE "material_plant" DROP CONSTRAINT "FK_9f3786b8c6b3544cf1788c9578c"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_79f678cfee42ed658578f1d65ad"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_21c59564d2cab04c8a2290d9340"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_93fa8f0c6478ea24e68ea6aa55d"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_05e8afb214220d7369fc8ce7121"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_e88dd51ee57b5a2da314897478d"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_5d02cdb5c4ed1932f14ca3b4a3b"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_c357cc6b79ec0b39dbff21ca014"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_668784a765512e869dc2bdd8ce8"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_96186eebb7d229c78816272197c"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "FK_230fdd10e2ec2fef8487373326d"`);
        await queryRunner.query(`ALTER TABLE "media_file" DROP CONSTRAINT "FK_9ad5009fd5ee116750314b3112b"`);
        await queryRunner.query(`ALTER TABLE "media_file" DROP CONSTRAINT "FK_0b68aa07ebdddca98130b2bbc09"`);
        await queryRunner.query(`ALTER TABLE "media_file" DROP CONSTRAINT "FK_46097540235516368df3f9b4815"`);
        await queryRunner.query(`ALTER TABLE "media_file" DROP CONSTRAINT "FK_76c105fac888a3fd39c0d7f5237"`);
        await queryRunner.query(`ALTER TABLE "media_file" DROP CONSTRAINT "FK_219c6e4dc65911e58f80e98af45"`);
        await queryRunner.query(`ALTER TABLE "media_file" DROP CONSTRAINT "FK_1cc5cb77854e671446b446350ae"`);
        await queryRunner.query(`ALTER TABLE "media_file" DROP CONSTRAINT "FK_55970c9780123468454379d06da"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_f1baae7bdff899bdf012d96ce7f"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_4693ef621ed8c42139f54828bab"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_59e8a9cc2e2435ef62ae7402a1a"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_f46b4eaadb685dc4462f80a51e2"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_4f3dcad0f8b7a9de1a3586c75ee"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_b077ac8e2f98be513b0a4adeb22"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_76d132894f2795b3fd554f71021"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_2501011e9b19a1dbbdf4b1a253c"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_b23c995c959b6188cb08a3f622c"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_e6cd71f78b524c85fb61a53a73b"`);
        await queryRunner.query(`ALTER TABLE "pr_item" DROP CONSTRAINT "FK_c3dd5d28af70015000aef46709c"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_4dd78943e2825467aded133ffe4"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_525a439839309d9d959323c20ff"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_2b6813d4993bef855c6d78342c3"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_df605ae982735f147e9c6474868"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_6066264aabfdcd542fc6a3c0c9d"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_5b0c36168da11741c39e798f7b8"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_26832b04405eadd44f8c0c7d59b"`);
        await queryRunner.query(`ALTER TABLE "bid_pr" DROP CONSTRAINT "FK_9f0a7da0ba899e9de495c4f8e3b"`);
        await queryRunner.query(`ALTER TABLE "bid_pr" DROP CONSTRAINT "FK_5794fc22296992c81baa0e195a6"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_f61fc8649bdf23ed0554e37aac3"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_1ceb00c0f3409976b5c203aef62"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_8800bdc1430bda6326a79d70289"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_94641949661345c81577cdf4f11"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_06205a50255cd1483c099261c55"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_d34578c47b89e5d6df2b6c26308"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_e646a1a52f73e26ed0181e23954"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_67fbad3a7aa24e0309aa820141b"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_5f9aba86ffb85928d9bda6e9244"`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" DROP CONSTRAINT "FK_9e6e482db5c7a0c06a0a946fcce"`);
        await queryRunner.query(`ALTER TABLE "contract_document_handover" DROP CONSTRAINT "FK_86c98c734f4a83226f818b9d84f"`);
        await queryRunner.query(`ALTER TABLE "payment_progress" DROP CONSTRAINT "FK_64ef4ec3366d9912888f4726469"`);
        await queryRunner.query(`ALTER TABLE "payment_progress" DROP CONSTRAINT "FK_327c5af92cf9846e831ed369853"`);
        await queryRunner.query(`ALTER TABLE "payment_progress" DROP CONSTRAINT "FK_ca21e5bc8d43455f8d543924951"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_2b1fd6c325d0f941fdb4d3641bf"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_fe4f20bbf025284450f3eb76dfb"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_65e7018a5249abde4bc8984601c"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_089021c6354d574f686474375c4"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_2d2bc7cdf8143103d604f9900a3"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_6a9bd6d1b0c829ba79e9df5269b"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_29298fefafdf17b73ddc57fb9b2"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_20b22b68714456b5d4fcd03e1a5"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_717ae013a85f97e8e5685225037"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_9ae35e3316a98f3a9d740df26c0"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_e1e5f8634306efc40704a8a2628"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_0b571b7679cf41929f5f3db2d89"`);
        await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_e5fb555ea198a0aec1530f5e46b"`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" DROP CONSTRAINT "FK_d3b785b0d918e399eb65987f7da"`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" DROP CONSTRAINT "FK_f648a16e99b3ab2539ab82a1b01"`);
        await queryRunner.query(`ALTER TABLE "po_history_status_execution" DROP CONSTRAINT "FK_ba2d6561c8edf521a953e60e26b"`);
        await queryRunner.query(`ALTER TABLE "po_product" DROP CONSTRAINT "FK_c52abf4abddeac5cf50574defc5"`);
        await queryRunner.query(`ALTER TABLE "po_product" DROP CONSTRAINT "FK_2b720950f95ada26703d43d0ade"`);
        await queryRunner.query(`ALTER TABLE "po_product" DROP CONSTRAINT "FK_4988bdc3aeb03f2942dd547e8b7"`);
        await queryRunner.query(`ALTER TABLE "po_product" DROP CONSTRAINT "FK_440105751648ea3d779e534cae2"`);
        await queryRunner.query(`ALTER TABLE "po_product" DROP CONSTRAINT "FK_babde8a2029fbfc7e461716c732"`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" DROP CONSTRAINT "FK_ac6ea51b4fe4c04bcdd1b47289f"`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" DROP CONSTRAINT "FK_5442dcc68f283ce0b00a08119db"`);
        await queryRunner.query(`ALTER TABLE "pr_item_child" DROP CONSTRAINT "FK_d3283beb2e1bb261de57765b1de"`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" DROP CONSTRAINT "FK_b7dea34b1fbaa3bca183e9089a7"`);
        await queryRunner.query(`ALTER TABLE "po_lead_time" DROP CONSTRAINT "FK_e69bb7f7956f48e5ea4e1d162d6"`);
        await queryRunner.query(`ALTER TABLE "template_lead_time" DROP CONSTRAINT "FK_a40faf13f4f1ef1e24e94644d1e"`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP CONSTRAINT "FK_667faf8aa18019a0be5969f00a3"`);
        await queryRunner.query(`ALTER TABLE "po_pr" DROP CONSTRAINT "FK_67d4c2ecf748c2b1e1a0d005452"`);
        await queryRunner.query(`ALTER TABLE "supplier_potential_upgrade" DROP CONSTRAINT "FK_96d404106e9aea66c337601f956"`);
        await queryRunner.query(`ALTER TABLE "permission_individual" DROP CONSTRAINT "FK_47dbeb860b692c2984bd1ee5204"`);
        await queryRunner.query(`ALTER TABLE "permission_approve" DROP CONSTRAINT "FK_f9688b6619160d29b70e6d8dc23"`);
        await queryRunner.query(`ALTER TABLE "permission_employee" DROP CONSTRAINT "FK_799ca14e17fb8bbb87908b577fd"`);
        await queryRunner.query(`ALTER TABLE "po_acceptance" DROP CONSTRAINT "FK_486b3dbea510ad34aafdd1459af"`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" DROP CONSTRAINT "FK_7755f53936f46edab1e94a69b34"`);
        await queryRunner.query(`ALTER TABLE "po_acceptance_employee" DROP CONSTRAINT "FK_472fbec079cd21902135300f65a"`);
        await queryRunner.query(`ALTER TABLE "supplier_service_history" DROP CONSTRAINT "FK_bdd65d9ecb07cddbeb1e314f22f"`);
        await queryRunner.query(`ALTER TABLE "block" DROP CONSTRAINT "FK_e96aee97dbe6375a95db3260c7b"`);
        await queryRunner.query(`ALTER TABLE "block" DROP CONSTRAINT "FK_4d8ab272fa9ba5f4753427daf7b"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" DROP CONSTRAINT "FK_fea404d0348bb49babc1933031e"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" DROP CONSTRAINT "FK_42430478067ffe20b173d1e0b4d"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" DROP CONSTRAINT "FK_694d6561de7037de9b503fa1081"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" DROP CONSTRAINT "FK_60727ec7d7f9a21251595d1d708"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" DROP CONSTRAINT "FK_6dbe2fa8007274e457ab84e8596"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" DROP CONSTRAINT "FK_189aec52f7ad3f9c26bba3ca7fa"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_employee" DROP CONSTRAINT "FK_9b7b7021050caa569bf4696bb27"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi" DROP CONSTRAINT "FK_c57e79045293a991fe21f2b8616"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" DROP CONSTRAINT "FK_4f75a6efb7f486da306108ff0f1"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_detail" DROP CONSTRAINT "FK_1a03b0ca72b0c3ca6d4022c3440"`);
        await queryRunner.query(`ALTER TABLE "ticket_evaluation_kpi_list_detail" DROP CONSTRAINT "FK_753ddc4a8712b090e8978615a3e"`);
        await queryRunner.query(`ALTER TABLE "kpi" DROP CONSTRAINT "FK_d681a2d4568c32f0bf1f959d124"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" DROP CONSTRAINT "FK_8bb90496bc97dd27d03781176dd"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission" DROP CONSTRAINT "FK_21d04915e24ab81e30d745bca66"`);
        await queryRunner.query(`ALTER TABLE "kpi_scale" DROP CONSTRAINT "FK_4be103c46763a565561c5fb3d8a"`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" DROP CONSTRAINT "FK_b4aa7c402dfc9abfd5b21a4fb39"`);
        await queryRunner.query(`ALTER TABLE "kpi_detail" DROP CONSTRAINT "FK_4b29d3f63a0cb37472321af98e4"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" DROP CONSTRAINT "FK_87e7c18f48174c0afb66736c268"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" DROP CONSTRAINT "FK_2d565994cd9d4ed96c45a551908"`);
        await queryRunner.query(`ALTER TABLE "kpi_permission_position" DROP CONSTRAINT "FK_901869ca55c897c9a40ff8f24db"`);
        await queryRunner.query(`ALTER TABLE "position" DROP CONSTRAINT "FK_3e108d1ddd5081c22fa6cde2929"`);
        await queryRunner.query(`ALTER TABLE "kpi_position" DROP CONSTRAINT "FK_af07677ea6e363186ce9002ce7d"`);
        await queryRunner.query(`ALTER TABLE "kpi_position" DROP CONSTRAINT "FK_0335c46dbf129221b2baec68c2e"`);
        await queryRunner.query(`ALTER TABLE "kpi_list_detail" DROP CONSTRAINT "FK_d6b3e6b43e92b2bcb63f43a75d0"`);
        await queryRunner.query(`ALTER TABLE "kpi_company" DROP CONSTRAINT "FK_58909d0a5cf1b73b4eb72da275c"`);
        await queryRunner.query(`ALTER TABLE "kpi_company" DROP CONSTRAINT "FK_6b0274ea0b51809be3e668018b4"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" DROP CONSTRAINT "FK_7e693a7b45f006c23c99e25b96e"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase" DROP CONSTRAINT "FK_59e417fa710232418ae62af887e"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" DROP CONSTRAINT "FK_fec6a681f2e02560c04813e31ab"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_employee" DROP CONSTRAINT "FK_5ef5127a079855988ca4bdf1c69"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" DROP CONSTRAINT "FK_983eec0b89f31767bf16514d0a1"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_detail" DROP CONSTRAINT "FK_6fe7588e3f58faa3996cc5ffd6a"`);
        await queryRunner.query(`ALTER TABLE "evaluation_history_purchase_list_detail" DROP CONSTRAINT "FK_4722f0d2ccf61a22efb684af696"`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" DROP CONSTRAINT "FK_8e7161051f94bd3bdef552e70d6"`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" DROP CONSTRAINT "FK_5beddaa92916430024dd800fc65"`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" DROP CONSTRAINT "FK_18350f56ee46484cf04d85246a5"`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" DROP CONSTRAINT "FK_bf2f38beb9d637ca530ef33c259"`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade_detail" DROP CONSTRAINT "FK_17424dc39277092fd5b49cfa4a7"`);
        await queryRunner.query(`ALTER TABLE "supplier_upgrade" DROP CONSTRAINT "FK_6dc3eb5c5f50a3164abfbb253f7"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP CONSTRAINT "FK_6f52b7fc7657af04b3e748e0c16"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP CONSTRAINT "FK_5098f91ec52a61fdb326e2ac9e5"`);
        await queryRunner.query(`ALTER TABLE "criteria_site_assessment_list_detail" DROP CONSTRAINT "FK_71c9f7ef0197019c21b302c7d94"`);
        await queryRunner.query(`ALTER TABLE "site_assessment" DROP CONSTRAINT "FK_0a3a2f5d3eee9ab91a6fa16c2a6"`);
        await queryRunner.query(`ALTER TABLE "site_assessment" DROP CONSTRAINT "FK_e615bc382db5fbcd328fa0d7d2d"`);
        await queryRunner.query(`ALTER TABLE "site_assessment" DROP CONSTRAINT "FK_c74ce6419ee44d28fb24738ce2c"`);
        await queryRunner.query(`ALTER TABLE "site_assessment" DROP CONSTRAINT "FK_bfa745fecb25ab5de1fa09049ee"`);
        await queryRunner.query(`ALTER TABLE "site_assessment" DROP CONSTRAINT "FK_cbaa527ca33e12dcd6e6667e757"`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" DROP CONSTRAINT "FK_fcfe5b3a6936c620cfbd072868c"`);
        await queryRunner.query(`ALTER TABLE "request_update_supplier" DROP CONSTRAINT "FK_01b10d60210f802e05af7ae27dd"`);
        await queryRunner.query(`ALTER TABLE "factory_supplier" DROP CONSTRAINT "FK_db22cf536eca5a1cf775a2ec114"`);
        await queryRunner.query(`ALTER TABLE "region" DROP CONSTRAINT "FK_75ceb9efda6c228a50d88dcdfb8"`);
        await queryRunner.query(`ALTER TABLE "bank_branch" DROP CONSTRAINT "FK_c2dcee7baa3689de70712030222"`);
        await queryRunner.query(`ALTER TABLE "bank_branch" DROP CONSTRAINT "FK_63e9013142a18791a9c30b9d919"`);
        await queryRunner.query(`ALTER TABLE "bank_branch" DROP CONSTRAINT "FK_c28f181b6bc4d1d5c5d2ffd48d3"`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" DROP CONSTRAINT "FK_6f1d8a00b7ca9ebcfa1a486ab4e"`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" DROP CONSTRAINT "FK_8b763f7931bbc3caaef8cb377c9"`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" DROP CONSTRAINT "FK_04d8fa067e5e953776515b0b467"`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" DROP CONSTRAINT "FK_f267d1620d405aecb0f9e8b2b3a"`);
        await queryRunner.query(`ALTER TABLE "supplier_bank" DROP CONSTRAINT "FK_409e23ca5ef234341acb1b1b61e"`);
        await queryRunner.query(`ALTER TABLE "contract_member" DROP CONSTRAINT "FK_23604fe008a588fdb81f16e8db7"`);
        await queryRunner.query(`ALTER TABLE "service_access" DROP CONSTRAINT "FK_d70b3e485a2bb3b9df9d23520e1"`);
        await queryRunner.query(`ALTER TABLE "service_access" DROP CONSTRAINT "FK_f5b81b0704c5b994467aa8120b3"`);
        await queryRunner.query(`ALTER TABLE "user_confirm_code" DROP CONSTRAINT "FK_1d3f2a4a0693ef7a44e57284ea9"`);
        await queryRunner.query(`ALTER TABLE "inbound_container" DROP CONSTRAINT "FK_f23decf7082028bc724c552f689"`);
        await queryRunner.query(`ALTER TABLE "inbound_item" DROP CONSTRAINT "FK_7a0543804dd8131978038775fdf"`);
        await queryRunner.query(`ALTER TABLE "inbound_item" DROP CONSTRAINT "FK_c30cf0f62e106d0c3b2adfcb036"`);
        await queryRunner.query(`ALTER TABLE "inbound_item" DROP CONSTRAINT "FK_0042af83254454c62fe5156e397"`);
        await queryRunner.query(`ALTER TABLE "shipment" DROP CONSTRAINT "FK_53bb21f7164a3ee0095d6b32666"`);
        await queryRunner.query(`ALTER TABLE "shipment" DROP CONSTRAINT "FK_9e154a0a8ab04ba7981356d9597"`);
        await queryRunner.query(`ALTER TABLE "shipment_container" DROP CONSTRAINT "FK_4d49ec47988e1cb6d474f99aecd"`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" DROP CONSTRAINT "FK_56c5a8c813ff83ce4189d7567d4"`);
        await queryRunner.query(`ALTER TABLE "shipment_inbound" DROP CONSTRAINT "FK_3ece30f79e56953c6895c88982b"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP CONSTRAINT "FK_63e613d78ecc42f633f97a69e4f"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP CONSTRAINT "FK_c728ef0f5f8d3bb3999240174f4"`);
        await queryRunner.query(`ALTER TABLE "inbound" DROP CONSTRAINT "FK_23f36c98b6495d315bb829f684e"`);
        await queryRunner.query(`ALTER TABLE "shipment_item" DROP CONSTRAINT "FK_727fe27762da6a6616ddc447a35"`);
        await queryRunner.query(`ALTER TABLE "shipment_item" DROP CONSTRAINT "FK_1ffd57cdd90a619e868f5216fb1"`);
        await queryRunner.query(`ALTER TABLE "shipment_item" DROP CONSTRAINT "FK_e4b0906b4cfeb96bd72b6d1dfab"`);
        await queryRunner.query(`ALTER TABLE "inbound_document_handover" DROP CONSTRAINT "FK_96edb747773eb974dbafff0b621"`);
        await queryRunner.query(`ALTER TABLE "shipment_po" DROP CONSTRAINT "FK_2cc3b270e1663c542d5561a2301"`);
        await queryRunner.query(`ALTER TABLE "shipment_po" DROP CONSTRAINT "FK_55727db416e5bcae91285e6d45d"`);
        await queryRunner.query(`ALTER TABLE "supplier_number" DROP CONSTRAINT "FK_6883442a1e4c4469ae07d295ec2"`);
        await queryRunner.query(`ALTER TABLE "supplier_number" DROP CONSTRAINT "FK_f9011f5cd49dcab82a8faebb3b0"`);
        await queryRunner.query(`ALTER TABLE "supplier_number" DROP CONSTRAINT "FK_90209110c6739b8ad68a20030db"`);
        await queryRunner.query(`ALTER TABLE "setting_role" DROP CONSTRAINT "FK_ff016913fb6a03da4a2e474968e"`);
        await queryRunner.query(`ALTER TABLE "supplier_notify" DROP CONSTRAINT "FK_1c604e9c16b9d840aa0b73065db"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_year_value" DROP CONSTRAINT "FK_91d2bd0882d25205d6caa30510c"`);
        await queryRunner.query(`ALTER TABLE "supplier_capacity_list_detail" DROP CONSTRAINT "FK_6ddada23ada70eb762bd1a23629"`);
        await queryRunner.query(`ALTER TABLE "supplier_history" DROP CONSTRAINT "FK_0a04398f3acc707810153fdc5c6"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_law_detail" DROP CONSTRAINT "FK_61a4e2682fbbaf81ccad6ef2c1e"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" DROP CONSTRAINT "FK_14c6ca8022f36caccb2c0d08b57"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_member" DROP CONSTRAINT "FK_25ab41887eb8ff9c33d22870a26"`);
        await queryRunner.query(`ALTER TABLE "service_trade" DROP CONSTRAINT "FK_82da0abb34d4309aab022fa6cb2"`);
        await queryRunner.query(`ALTER TABLE "service_trade" DROP CONSTRAINT "FK_e47ba7f64cf736318a65b1c9c5d"`);
        await queryRunner.query(`ALTER TABLE "service_trade_list_detail" DROP CONSTRAINT "FK_24bb7ed260a75bba7c4bbde494b"`);
        await queryRunner.query(`ALTER TABLE "service_tech" DROP CONSTRAINT "FK_662fc4b072225f31c77b1e80a85"`);
        await queryRunner.query(`ALTER TABLE "service_tech" DROP CONSTRAINT "FK_3d46899c062ba4e9b5063c3992a"`);
        await queryRunner.query(`ALTER TABLE "service_tech_list_detail" DROP CONSTRAINT "FK_146154f9f04aa7b69ae91e183c2"`);
        await queryRunner.query(`ALTER TABLE "service_custom_price" DROP CONSTRAINT "FK_2092855ad0938615c9692bf66d5"`);
        await queryRunner.query(`ALTER TABLE "faq" DROP CONSTRAINT "FK_89ec0666829fad019cdf0dcac22"`);
        await queryRunner.query(`ALTER TABLE "employee_warning" DROP CONSTRAINT "FK_f8729a94462abe07155640c0bc6"`);
        await queryRunner.query(`ALTER TABLE "employee_notify" DROP CONSTRAINT "FK_829b874115967ba418739c0020d"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" DROP CONSTRAINT "FK_3eb200381cf528ef3bb0328ddaf"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_access" DROP CONSTRAINT "FK_e1ab4ee56faa6b39f43ee3e8d72"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" DROP CONSTRAINT "FK_5feaa34fe6080d172cc22472827"`);
        await queryRunner.query(`ALTER TABLE "bid_employee_rate" DROP CONSTRAINT "FK_945717da2a116ba1049afa2176d"`);
        await queryRunner.query(`ALTER TABLE "bid_history" DROP CONSTRAINT "FK_1add2777fbf78612fd65d33a930"`);
        await queryRunner.query(`ALTER TABLE "bid_history" DROP CONSTRAINT "FK_cedae6a0aa32c469442a9465bf5"`);
        await queryRunner.query(`ALTER TABLE "auction" DROP CONSTRAINT "FK_a35536b1e6e1c61dfac69d80dbf"`);
        await queryRunner.query(`ALTER TABLE "auction" DROP CONSTRAINT "FK_83ef4a2004de540c8b62cc7d819"`);
        await queryRunner.query(`ALTER TABLE "auction" DROP CONSTRAINT "FK_ed52d5d5467372914c1d8ae0293"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" DROP CONSTRAINT "FK_08ab6c1ec05a27b032a5a59647c"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier" DROP CONSTRAINT "FK_b2ee2caf4c7757c53bda116c932"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" DROP CONSTRAINT "FK_f85d3c487b8b40397944e70462d"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" DROP CONSTRAINT "FK_6cda22f6eee12c5fb930a91087e"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" DROP CONSTRAINT "FK_fdbaf5040a851909cc661f9ed7b"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" DROP CONSTRAINT "FK_a7e4ce508762e30a03cc63c0121"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" DROP CONSTRAINT "FK_671378f570eecb7cee273187b7f"`);
        await queryRunner.query(`ALTER TABLE "auction_supplier_price" DROP CONSTRAINT "FK_b52c2d03476b0389f02b55ae59b"`);
        await queryRunner.query(`ALTER TABLE "uom" DROP CONSTRAINT "FK_61d082272034b15d57b9d1ceb4a"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP CONSTRAINT "FK_cf18fa972c4c56b8e5bdac4f7f8"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP CONSTRAINT "FK_313ebecb912bdc209c41289a507"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP CONSTRAINT "FK_65da55c09a185045547bf17f4dc"`);
        await queryRunner.query(`ALTER TABLE "reservation_item" DROP CONSTRAINT "FK_95bea5815e6a171885dfaf52dad"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP CONSTRAINT "FK_b99a51bb3d6e2fb009d8d2f7733"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP CONSTRAINT "FK_542aff328c08cbb00f18265ee25"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP CONSTRAINT "FK_99d98094e91f49f7bd28c27000b"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP CONSTRAINT "FK_c5366e6dff1e17a1c915501fc88"`);
        await queryRunner.query(`ALTER TABLE "reservation" DROP CONSTRAINT "FK_eb720657f1c94f439965a1e0924"`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" DROP CONSTRAINT "FK_d9675d88813083415663ab139dc"`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" DROP CONSTRAINT "FK_82edfa9516bfe99e75573dff93e"`);
        await queryRunner.query(`ALTER TABLE "reservation_item_child" DROP CONSTRAINT "FK_de323c88d0bb034f889416bdd17"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "FK_a81c715097cfdadb7268a3c6f93"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "FK_b0097e6e8480c83a6a093d5096e"`);
        await queryRunner.query(`ALTER TABLE "reservation_norm" DROP CONSTRAINT "FK_8381a3e58ee28d92ca599e716f9"`);
        await queryRunner.query(`ALTER TABLE "material_uom" DROP CONSTRAINT "FK_c4ab0c425c3f8fea8d00bada1a5"`);
        await queryRunner.query(`ALTER TABLE "material_uom" DROP CONSTRAINT "FK_f854cfbd244e44105e538527286"`);
        await queryRunner.query(`ALTER TABLE "material_uom" DROP CONSTRAINT "FK_98f3a2d6faf7da88cea55534c99"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP CONSTRAINT "FK_4a954d26c27d94ddfb54d6a617e"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP CONSTRAINT "FK_929cb4b9994dfaba179b6f8ae88"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP CONSTRAINT "FK_1bb92b5a48afe1080abfad2447e"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP CONSTRAINT "FK_5f2b9e18f5d3b99bb651fe755cf"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP CONSTRAINT "FK_52f5f162637c6c426f2ab0609fb"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP CONSTRAINT "FK_266a893729922aae4f9772313c0"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP CONSTRAINT "FK_c279dd6f1c5adb72dc39f11e529"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP CONSTRAINT "FK_1e6072fb549a09805413bd2ee9d"`);
        await queryRunner.query(`ALTER TABLE "offer_service" DROP CONSTRAINT "FK_95d1337b5304d0087dce61851cb"`);
        await queryRunner.query(`ALTER TABLE "offer" DROP CONSTRAINT "FK_21fd2aeb1658ede130707b7c768"`);
        await queryRunner.query(`ALTER TABLE "offer" DROP CONSTRAINT "FK_68382598864c2ea4de6b0d10a46"`);
        await queryRunner.query(`ALTER TABLE "offer" DROP CONSTRAINT "FK_62f7f0f9454992e9ad142b1123f"`);
        await queryRunner.query(`ALTER TABLE "offer" DROP CONSTRAINT "FK_b47721a7fd707d5469426b6f107"`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" DROP CONSTRAINT "FK_8c46fb2ccf3bc6c95241fd3be83"`);
        await queryRunner.query(`ALTER TABLE "offer_shipment_price" DROP CONSTRAINT "FK_098c4adda530658b3e8615e80d5"`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" DROP CONSTRAINT "FK_2e325531d7c12fd685a133439bc"`);
        await queryRunner.query(`ALTER TABLE "offer_custom_price" DROP CONSTRAINT "FK_fe1611c35570631d3312c462ade"`);
        await queryRunner.query(`ALTER TABLE "external_material_group" DROP CONSTRAINT "FK_a061d3f0ae7cb6ae203a363dc68"`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" DROP CONSTRAINT "FK_2e2262f5998eb32bf2c3f5c1f65"`);
        await queryRunner.query(`ALTER TABLE "bid_exmatgroup" DROP CONSTRAINT "FK_214dfe6e42461397dac93a6a66e"`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" DROP CONSTRAINT "FK_cf9e31e98cc93476ee3138a3d2d"`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" DROP CONSTRAINT "FK_2c6604e04024e064ff08e8b735e"`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" DROP CONSTRAINT "FK_c9c0e5048095c713a638f8c3b73"`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" DROP CONSTRAINT "FK_9b4eaab267adcec196f77d1f949"`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" DROP CONSTRAINT "FK_aa3e48bf90220f4695d81a9f1c2"`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" DROP CONSTRAINT "FK_8d6b936b57effc8e65a417476ae"`);
        await queryRunner.query(`ALTER TABLE "bid_pr_item" DROP CONSTRAINT "FK_2ad88d1baaba82ed059ff9090dd"`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" DROP CONSTRAINT "FK_7c9fa4247e60e1ca6dcd0d005a8"`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" DROP CONSTRAINT "FK_4bccb43d8f408852456bd905f7d"`);
        await queryRunner.query(`ALTER TABLE "bid_custom_price" DROP CONSTRAINT "FK_3640f4bfc140dc3cbc0f3135171"`);
        await queryRunner.query(`ALTER TABLE "bid_tech" DROP CONSTRAINT "FK_146a75cc357e29e51dcfb4e29b2"`);
        await queryRunner.query(`ALTER TABLE "bid_tech" DROP CONSTRAINT "FK_c7b436b32d81e53e7d9834ea324"`);
        await queryRunner.query(`ALTER TABLE "bid_tech" DROP CONSTRAINT "FK_2fe4a2fbbde3f9c07132b807ad6"`);
        await queryRunner.query(`ALTER TABLE "bid_tech_list_detail" DROP CONSTRAINT "FK_4dd593368143571aceb5c285278"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" DROP CONSTRAINT "FK_47145f6233cbe2efec0ddfd400f"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_tech_value" DROP CONSTRAINT "FK_2a427d946d1e7c5e65df58021eb"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" DROP CONSTRAINT "FK_00c3736269a1ba9dce75ed1eab1"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" DROP CONSTRAINT "FK_94afaf0e41c3cc6a62b05333f69"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier" DROP CONSTRAINT "FK_f0256ba2eb660b86de5838ccc76"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" DROP CONSTRAINT "FK_ab243f35d853ba3d61f70684435"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_shipment_value" DROP CONSTRAINT "FK_48178fa517c6aa634782f01f0a1"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_custom_price_value" DROP CONSTRAINT "FK_4ac6991b9fc4be2e43b62066736"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" DROP CONSTRAINT "FK_cbeb3468d0b6a2e95cee6278ea6"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_trade_value" DROP CONSTRAINT "FK_9aac8e343250eb68472ac29586d"`);
        await queryRunner.query(`ALTER TABLE "bid_trade" DROP CONSTRAINT "FK_4197c33978c1e13219ce2d5505b"`);
        await queryRunner.query(`ALTER TABLE "bid_trade" DROP CONSTRAINT "FK_be91681664c1ac732c62a9bf9af"`);
        await queryRunner.query(`ALTER TABLE "bid_trade" DROP CONSTRAINT "FK_3b62791f9ea7005f5e904d03b1e"`);
        await queryRunner.query(`ALTER TABLE "bid_trade_list_detail" DROP CONSTRAINT "FK_1f7c17f8cf412189406f422713a"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" DROP CONSTRAINT "FK_b3c0325be1f8f2b8e9ac36f940c"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_value" DROP CONSTRAINT "FK_16d1ee6e94f781d84aabd66c194"`);
        await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "FK_06ef557e8c53a47cf4e9d24fe33"`);
        await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "FK_9dba8c20a043fdc8d92971193f4"`);
        await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "FK_4b18d8d606a19e22f05eda2aa9d"`);
        await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "FK_1e8f71f33bb081dc52ff951f1ea"`);
        await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "FK_867db5c177deae3fa9b9708dc2d"`);
        await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "FK_a440790cd408efa2af7dfc1a579"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" DROP CONSTRAINT "FK_c2ca28dc7586fbdd2ca2704aa35"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price" DROP CONSTRAINT "FK_1a034db1ac5166a91766a3320ae"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" DROP CONSTRAINT "FK_a54de3eebd756601dade529646b"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" DROP CONSTRAINT "FK_656f153e808288f954929c26fd0"`);
        await queryRunner.query(`ALTER TABLE "bid_supplier_price_col_value" DROP CONSTRAINT "FK_037371a1b92304d1c0f2407e59e"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" DROP CONSTRAINT "FK_3071c61c2a5f338173be076e802"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" DROP CONSTRAINT "FK_12677bc2114915f63f5670cf496"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col" DROP CONSTRAINT "FK_6dc4cb70fa85c3fda253011ede2"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" DROP CONSTRAINT "FK_cec4a85e38a248f39dad6030c45"`);
        await queryRunner.query(`ALTER TABLE "bid_price_col_value" DROP CONSTRAINT "FK_5736a119be595711e5d6abe52c5"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" DROP CONSTRAINT "FK_02a5453262e93dbdb2c88945407"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_price" DROP CONSTRAINT "FK_d4c542ae290d14e5afae2d9e33e"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" DROP CONSTRAINT "FK_da27cb5423990fdcd1aa1a18a9d"`);
        await queryRunner.query(`ALTER TABLE "bid_auction_supplier_price_value" DROP CONSTRAINT "FK_2fe1c9d4d0e650eab1eb90e2191"`);
        await queryRunner.query(`ALTER TABLE "service_price" DROP CONSTRAINT "FK_4c807146960feb2faad0031f145"`);
        await queryRunner.query(`ALTER TABLE "service_price" DROP CONSTRAINT "FK_d4520a0074d2b259db1b40149cd"`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" DROP CONSTRAINT "FK_1b5ccab6379d4d58b3fa85e7b41"`);
        await queryRunner.query(`ALTER TABLE "service_price_col_value" DROP CONSTRAINT "FK_5f7d7b40cfb3d7ae2c8ee6555c5"`);
        await queryRunner.query(`ALTER TABLE "service_price_col" DROP CONSTRAINT "FK_b598b93058d3376e11f0a5d7ef0"`);
        await queryRunner.query(`ALTER TABLE "service_price_list_detail" DROP CONSTRAINT "FK_c3c43ede69d2f56a6c805b7ce23"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" DROP CONSTRAINT "FK_fa500f428373aa6dd5f183cc6fe"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_price" DROP CONSTRAINT "FK_58e338315728b94e4d7dc64512b"`);
        await queryRunner.query(`ALTER TABLE "bid_deal" DROP CONSTRAINT "FK_0fa84e5f3dc0a7ba411603f50d3"`);
        await queryRunner.query(`ALTER TABLE "bid_deal" DROP CONSTRAINT "FK_94930fed93b5d08bd97b336a20a"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" DROP CONSTRAINT "FK_14acd799af1581375e56c8e9ed6"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier" DROP CONSTRAINT "FK_291ace50643326687ee885f12e3"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" DROP CONSTRAINT "FK_c95d5eae41bffd589aada849724"`);
        await queryRunner.query(`ALTER TABLE "bid_deal_supplier_price_value" DROP CONSTRAINT "FK_34b418fb00850329693a941c154"`);
        await queryRunner.query(`ALTER TABLE "bid_price_list_detail" DROP CONSTRAINT "FK_70b3d2a07ee305270b90ebf58dc"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP CONSTRAINT "FK_7296715b8f8437dfe044f650db9"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP CONSTRAINT "FK_21b36870d799272d9632aa94316"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP CONSTRAINT "FK_5cff3aff3c166a088fb7fcaba33"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP CONSTRAINT "FK_c5cf2adfb9485a9a1be184db66c"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" DROP CONSTRAINT "FK_6dd002f4e8918bde5d2ed361646"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" DROP CONSTRAINT "FK_30609fb5efc55dbe528b637a3b8"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" DROP CONSTRAINT "FK_e3e6cb562e73da197c87317a6d9"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" DROP CONSTRAINT "FK_43a07ff6aea5dbc57f4db922f85"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" DROP CONSTRAINT "FK_b3727ce0a5fc2c6341567425800"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" DROP CONSTRAINT "FK_32ec5e375129c22fdcdfd79a02b"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt" DROP CONSTRAINT "FK_19d00bde60e21dd0e8e4fc660da"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_history" DROP CONSTRAINT "FK_10b41bd077e63546f970c636ac4"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" DROP CONSTRAINT "FK_0b4e6944cfbb325f547dc0da7e5"`);
        await queryRunner.query(`ALTER TABLE "budget_receipt_item" DROP CONSTRAINT "FK_cbfd799e57d0a85fa7aed1efd29"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" DROP CONSTRAINT "FK_a8eba113909b088c3434b51359e"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr" DROP CONSTRAINT "FK_3b3d36e7f92e4458dc23f00d336"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" DROP CONSTRAINT "FK_e696b97dd40a910d96803b8e3f4"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" DROP CONSTRAINT "FK_cafad8eff795afe6ec42f74a05a"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" DROP CONSTRAINT "FK_76e7cc946d3c515906f1d8fb470"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" DROP CONSTRAINT "FK_c462535c6fb1d75ad8e6fc39b9a"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" DROP CONSTRAINT "FK_b1b4330833d136ccc5cf1fb1547"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template" DROP CONSTRAINT "FK_5b9ee39e5e04f174fd11852facd"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" DROP CONSTRAINT "FK_55b9a3b430e88def64dbfa75b3c"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_history" DROP CONSTRAINT "FK_0956f1d610cc1f51d1ce0dba72d"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_template_col" DROP CONSTRAINT "FK_761e4fc7f7886a5f1944e548a9c"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" DROP CONSTRAINT "FK_dc631863f81821f7f36ef2e1df3"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" DROP CONSTRAINT "FK_30be2849a3817c1dab6d7b7db8f"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_setting_value" DROP CONSTRAINT "FK_4b0de1dc73a3f2d1554f358c2ba"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" DROP CONSTRAINT "FK_b2477ae65d965d98c38a08b0a06"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" DROP CONSTRAINT "FK_7887d763a1d0310fd0d45da2716"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_setting_value" DROP CONSTRAINT "FK_4b030c35978d95b3fd38a3eff84"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP CONSTRAINT "FK_44532bb4ebe3c6d717e701042f6"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP CONSTRAINT "FK_ea510ade4e4860e25a14aaf98af"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP CONSTRAINT "FK_f66bfae063912abc94dfdda6e17"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP CONSTRAINT "FK_d3eb4d50f38d3e7b77eafd5fb25"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP CONSTRAINT "FK_37b01f2bd0bbff1a6ff99559781"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP CONSTRAINT "FK_0c7398cc2a215c440022a055243"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP CONSTRAINT "FK_4507a0c178c4dced2e05a714432"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP CONSTRAINT "FK_1b98ca07097fbc45dae59b5a3ef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP CONSTRAINT "FK_f5c80d28d352d44564ee1b34949"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase" DROP CONSTRAINT "FK_b944d2043b4299a378dfac22ba9"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" DROP CONSTRAINT "FK_df942b61b53dd32382f8254383b"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" DROP CONSTRAINT "FK_8ed394ec430ad2516799a07594b"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" DROP CONSTRAINT "FK_6609dfdf64127bfef51a0312120"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_stage" DROP CONSTRAINT "FK_7fa40e952f17086bd80990543f9"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" DROP CONSTRAINT "FK_3bbc6faff1a203112c13d56a920"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_shipment_cost_price" DROP CONSTRAINT "FK_a0d4295e66e157c2d15fabbd144"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" DROP CONSTRAINT "FK_0a094aaead2900afb380c808fc0"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost" DROP CONSTRAINT "FK_2a5f0392c17a973d262d8b6670e"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" DROP CONSTRAINT "FK_b3f9f85d04e4c4bb357411c9a68"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_detail" DROP CONSTRAINT "FK_319df7ec61056f06f1dc1bff62d"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP CONSTRAINT "FK_b7ac1afdd371bd5ff418c785b11"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP CONSTRAINT "FK_789a2de21d2c42bdf786008889c"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP CONSTRAINT "FK_ed0cb099f51901665a915427c97"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP CONSTRAINT "FK_0fe111e5ea041f9ec915ae719ce"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP CONSTRAINT "FK_15195e0c58e6bec6868cbc7ac7f"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP CONSTRAINT "FK_61b1d2d07253d21134ffbb4a1b3"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP CONSTRAINT "FK_38dde309d80ddc76e14b46a9333"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP CONSTRAINT "FK_8f0898ece0fdfe12e35e0a2e557"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP CONSTRAINT "FK_4f18c3c3f4eefb70911b281d031"`);
        await queryRunner.query(`ALTER TABLE "business_plan" DROP CONSTRAINT "FK_0ef5ca1c931f3bc1a8482b5c6c9"`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" DROP CONSTRAINT "FK_d3798bae2d52c720cb797dd6f12"`);
        await queryRunner.query(`ALTER TABLE "business_plan_rfq" DROP CONSTRAINT "FK_da51824e171b4e0cdaf6863a41a"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "FK_3c9fad0f24deb8f94a7d52eb5d8"`);
        await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "FK_529714df12183872a093b083126"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" DROP CONSTRAINT "FK_50d432d6a56e943df4cfc36ede6"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_rfq" DROP CONSTRAINT "FK_4797199c4668a0651e42148ba49"`);
        await queryRunner.query(`ALTER TABLE "rfq_details" DROP CONSTRAINT "FK_c1f1f558da24785770a4fc9eb98"`);
        await queryRunner.query(`ALTER TABLE "rfq_details" DROP CONSTRAINT "FK_e5e58eba9a4d61da3b33348ebe1"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template" DROP CONSTRAINT "FK_577edbb2040b81a30b65046c877"`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" DROP CONSTRAINT "FK_8cef4ff90faeed1db0e55ec12db"`);
        await queryRunner.query(`ALTER TABLE "business_plan_history" DROP CONSTRAINT "FK_b62e76c412972abcc69cd4a9542"`);
        await queryRunner.query(`ALTER TABLE "business_plan_template_col" DROP CONSTRAINT "FK_ce9ceba53df65116ce1e52c7a4d"`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" DROP CONSTRAINT "FK_f19549f2cb5f04564a957dce89a"`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" DROP CONSTRAINT "FK_6e4e4e3bd12d3a9f8d10e9c9205"`);
        await queryRunner.query(`ALTER TABLE "business_plan_setting_value" DROP CONSTRAINT "FK_2d0850037e7162fcbccc35fac08"`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" DROP CONSTRAINT "FK_986dc8eee68507ab7b857cdc26d"`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" DROP CONSTRAINT "FK_a1159cc8f0b652cc29daff454d6"`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" DROP CONSTRAINT "FK_d41ad35aead170a5b8e49696fc0"`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" DROP CONSTRAINT "FK_97f5c9b79536805c9fc8c7b85f7"`);
        await queryRunner.query(`ALTER TABLE "business_plan_col_value" DROP CONSTRAINT "FK_e9ac2edd097548f64822b1accbc"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template" DROP CONSTRAINT "FK_629590a7a0b2a74b8da521bfe6b"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" DROP CONSTRAINT "FK_514e965f7d1450bc10ad2c0be4f"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_history" DROP CONSTRAINT "FK_56867f3dea6a7a251ff03f3d2bb"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_template_col" DROP CONSTRAINT "FK_65fe390786adf9eabfe77e4fbc1"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" DROP CONSTRAINT "FK_bc3d13277c8109589e05a56d4f5"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" DROP CONSTRAINT "FK_8d13360453dc4e89c923a9675ef"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" DROP CONSTRAINT "FK_fdd5c9c23a1d24aa4b2f12a6537"`);
        await queryRunner.query(`ALTER TABLE "recommended_purchase_col_value" DROP CONSTRAINT "FK_a765fa3fa241ab1feaed8947686"`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" DROP CONSTRAINT "FK_4ffc9fe34b4a2b3613bf97e741b"`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" DROP CONSTRAINT "FK_ff0286b20f45e1acfa096cac8a8"`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" DROP CONSTRAINT "FK_988d26f4f517f0b9eaca1e799f3"`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" DROP CONSTRAINT "FK_90161e44269091beab3c1a78a18"`);
        await queryRunner.query(`ALTER TABLE "role_fi_supplier" DROP CONSTRAINT "FK_d6bbfa21cbc1eb57aced8803a9d"`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" DROP CONSTRAINT "FK_943585571daeb5970b45afde59f"`);
        await queryRunner.query(`ALTER TABLE "supplier_plant" DROP CONSTRAINT "FK_7f96a57a3a3e11234a0999ebe09"`);
        await queryRunner.query(`ALTER TABLE "role_supplier" DROP CONSTRAINT "FK_583285560f3ee962deeb2426156"`);
        await queryRunner.query(`ALTER TABLE "role_supplier" DROP CONSTRAINT "FK_c2ffeb50e8a7b17eafb1b5b5b15"`);
        await queryRunner.query(`ALTER TABLE "role_supplier" DROP CONSTRAINT "FK_e104b8f1bba8ef15ac05c9703fa"`);
        await queryRunner.query(`ALTER TABLE "role_supplier" DROP CONSTRAINT "FK_ed0dc3c247fdf754cd72a519d2d"`);
        await queryRunner.query(`ALTER TABLE "role_supplier" DROP CONSTRAINT "FK_cc3c1d35ee46341053a579caebe"`);
        await queryRunner.query(`ALTER TABLE "role_supplier" DROP CONSTRAINT "FK_61528dcb2929731207983bf8c22"`);
        await queryRunner.query(`ALTER TABLE "purchasing_org" DROP CONSTRAINT "FK_09f261633c748d640515c6b42f2"`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" DROP CONSTRAINT "FK_b1f4880a317509e77bc120b5de0"`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" DROP CONSTRAINT "FK_6713d7567399637fc589718da26"`);
        await queryRunner.query(`ALTER TABLE "schemaConfig" DROP CONSTRAINT "FK_d8e9c8105a6e7afdcc07b4232b9"`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" DROP CONSTRAINT "FK_0b142198eb6f2add8c922cb5031"`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" DROP CONSTRAINT "FK_8613509f20081fcc76d02f4c56f"`);
        await queryRunner.query(`ALTER TABLE "supplier_list_price_po" DROP CONSTRAINT "FK_1357bc4c618d5bd899e0fac0805"`);
        await queryRunner.query(`ALTER TABLE "material_price" DROP CONSTRAINT "FK_187ce20abdd5890c63d5507dadc"`);
        await queryRunner.query(`ALTER TABLE "material_price" DROP CONSTRAINT "FK_1423e57f6c13d2c6c7493ee072a"`);
        await queryRunner.query(`ALTER TABLE "material_price" DROP CONSTRAINT "FK_ad4a0adb9e22ac71143040be5d2"`);
        await queryRunner.query(`ALTER TABLE "payment" DROP CONSTRAINT "FK_8bd02879aabfa095f531e9482f3"`);
        await queryRunner.query(`ALTER TABLE "payment" DROP CONSTRAINT "FK_a3772a91be111b4b6a8a496fffa"`);
        await queryRunner.query(`ALTER TABLE "payment_contract" DROP CONSTRAINT "FK_fd92000b542ff541c7e2001ad7d"`);
        await queryRunner.query(`ALTER TABLE "payment_contract" DROP CONSTRAINT "FK_c7312ab6067307abca093192227"`);
        await queryRunner.query(`ALTER TABLE "payment_po" DROP CONSTRAINT "FK_548e1e23c4bd4160b0d4229b3b8"`);
        await queryRunner.query(`ALTER TABLE "payment_po" DROP CONSTRAINT "FK_3ad23ea16b4999989a500093da1"`);
        await queryRunner.query(`ALTER TABLE "payment_bill" DROP CONSTRAINT "FK_54038a0e8db8407df9426fb8ccd"`);
        await queryRunner.query(`ALTER TABLE "payment_bill" DROP CONSTRAINT "FK_0a987673166bbd293ee1113c503"`);
        await queryRunner.query(`ALTER TABLE "bill" DROP CONSTRAINT "FK_586340736f555e633b893bf191e"`);
        await queryRunner.query(`ALTER TABLE "bill" DROP CONSTRAINT "FK_d5e2ad96d3fb0c0013b1a0176bb"`);
        await queryRunner.query(`ALTER TABLE "bill" DROP CONSTRAINT "FK_42dbdce0becb29d047587aedb9b"`);
        await queryRunner.query(`ALTER TABLE "bill" DROP CONSTRAINT "FK_12cc7e32306c5b263f6fbcfd1e5"`);
        await queryRunner.query(`ALTER TABLE "bill_history" DROP CONSTRAINT "FK_860ffb38bae97a975bf900337e1"`);
        await queryRunner.query(`ALTER TABLE "po_price_list" DROP CONSTRAINT "FK_431d0b0f1c6ebe0a2ce1ed597ed"`);
        await queryRunner.query(`ALTER TABLE "po_price_list" DROP CONSTRAINT "FK_42fe0cf7b3ec41e6a1bc366ba2a"`);
        await queryRunner.query(`ALTER TABLE "po_price_list" DROP CONSTRAINT "FK_28e424dc48824568a9f3e3040f7"`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" DROP CONSTRAINT "FK_e4c1e340fb9fb6802d8b1f7dfe2"`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" DROP CONSTRAINT "FK_3ba9d2082b44e9f629405370e6c"`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" DROP CONSTRAINT "FK_50c58f0442fe4dc00620e891c64"`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" DROP CONSTRAINT "FK_3c570242525b6a6011f63508a28"`);
        await queryRunner.query(`ALTER TABLE "po_product_price_list" DROP CONSTRAINT "FK_bb3d644d6777053ceea7c03eb77"`);
        await queryRunner.query(`ALTER TABLE "master_condition_type" DROP CONSTRAINT "FK_c137fbc838888f547569f6a9955"`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" DROP CONSTRAINT "FK_c20c52b0f67b29edf9a9b241720"`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" DROP CONSTRAINT "FK_7c09fd4828b602ee9ac52203616"`);
        await queryRunner.query(`ALTER TABLE "pr_keep_budget" DROP CONSTRAINT "FK_0456ebbd5a287c94401ae109900"`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" DROP CONSTRAINT "FK_deab8f46837df555b923140cd3e"`);
        await queryRunner.query(`ALTER TABLE "plant_purchasing_org" DROP CONSTRAINT "FK_e9292a5a26f1fff9137520a6f55"`);
        await queryRunner.query(`ALTER TABLE "complaint" DROP CONSTRAINT "FK_1ec5d290566bcb9b78ad4fa24e9"`);
        await queryRunner.query(`ALTER TABLE "complaint" DROP CONSTRAINT "FK_06aff3d57e7fdd62467dc318657"`);
        await queryRunner.query(`ALTER TABLE "complaint" DROP CONSTRAINT "FK_7af461e25f1a5c918281e0d159c"`);
        await queryRunner.query(`ALTER TABLE "complaint" DROP CONSTRAINT "FK_9e3eee9dfea3f22c0c499321333"`);
        await queryRunner.query(`ALTER TABLE "complaint" DROP CONSTRAINT "FK_3ccf9ff1b22399f3a967c395fc0"`);
        await queryRunner.query(`ALTER TABLE "complaint" DROP CONSTRAINT "FK_58f2c71fa3be2b4329e6f27b447"`);
        await queryRunner.query(`ALTER TABLE "complaint" DROP CONSTRAINT "FK_163b8d410fd0877ee6da824bff1"`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" DROP CONSTRAINT "FK_5322ff5a77cbdd03cca9041422b"`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" DROP CONSTRAINT "FK_891eda2c34fec7522f90198f36b"`);
        await queryRunner.query(`ALTER TABLE "complaint_chat" DROP CONSTRAINT "FK_9c2934db56963e14328aae1f3ad"`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" DROP CONSTRAINT "FK_fa7b386046955f6a8fab9189bfa"`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" DROP CONSTRAINT "FK_b8e2ccd7f5193b5ba2e35e64607"`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" DROP CONSTRAINT "FK_a68606e0d5b2648761ca3062991"`);
        await queryRunner.query(`ALTER TABLE "complaint_notify" DROP CONSTRAINT "FK_ac28f23f8454ab831799fd2874f"`);
        await queryRunner.query(`ALTER TABLE "complaint_handling_plan" DROP CONSTRAINT "FK_62d5f286c893c574760f82809a8"`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" DROP CONSTRAINT "FK_bc0e3960577ebf249f08ca89227"`);
        await queryRunner.query(`ALTER TABLE "complaint_employee" DROP CONSTRAINT "FK_7d52f4c3cb76ff26da5f23ec3b4"`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" DROP CONSTRAINT "FK_aa7df4cd77a3a44a6c5048a648f"`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" DROP CONSTRAINT "FK_bcecb0e47289a9582cb73b37589"`);
        await queryRunner.query(`ALTER TABLE "complaint_item_cargo" DROP CONSTRAINT "FK_45566a3886b5ab972663ad9df07"`);
        await queryRunner.query(`ALTER TABLE "complaint_item" DROP CONSTRAINT "FK_c58eb3e3d144addf4aa993a7880"`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" DROP CONSTRAINT "FK_fe81416bff33ab13993dc321446"`);
        await queryRunner.query(`ALTER TABLE "complaint_prevention" DROP CONSTRAINT "FK_eea9690ca096d414369c9e9f667"`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" DROP CONSTRAINT "FK_188784b2950c789a94d27111a7b"`);
        await queryRunner.query(`ALTER TABLE "complaint_fix" DROP CONSTRAINT "FK_a9e62c10d280325f8a52e130b6f"`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" DROP CONSTRAINT "FK_5bf396593fad751cdc004519746"`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" DROP CONSTRAINT "FK_b85930192a9f7f1b29b78f54f81"`);
        await queryRunner.query(`ALTER TABLE "complaint_line_item" DROP CONSTRAINT "FK_607cc3a203f6c6fb9d3b4a9b613"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP CONSTRAINT "FK_f885e2b5718e6608b27f2469a9c"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP CONSTRAINT "FK_6f7ea66b6b04bedd6c7ea70a686"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP CONSTRAINT "FK_f8716ddb7646ee7d4914a2da653"`);
        await queryRunner.query(`ALTER TABLE "complaint_department" DROP CONSTRAINT "FK_d2c52b293a9b5c9788f50aa5b83"`);
        await queryRunner.query(`ALTER TABLE "complaint_department" DROP CONSTRAINT "FK_42637df66376135b4e4d245949e"`);
        await queryRunner.query(`ALTER TABLE "purchasing_group" DROP CONSTRAINT "FK_38910ed509406fa52c32dead2d6"`);
        await queryRunner.query(`ALTER TABLE "purchasing_group" DROP CONSTRAINT "FK_33dab669b380c9a0efbaa8adeb9"`);
        await queryRunner.query(`ALTER TABLE "employee_purchasing_group" DROP CONSTRAINT "FK_250e68fecb000d6c6388197fcaa"`);
        await queryRunner.query(`ALTER TABLE "employee_purchasing_group" DROP CONSTRAINT "FK_36c135cfb1a42c2881f431c32f5"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_d1e5111b9413ac6da66e324fe26"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_2bae0c82513276899ff286b87e7"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_5684b0cf010e7b31d1fd78c3e72"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_5a790171cdffbf15cc6e230859b"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_fd42d629edc79904b40ba0f4ca3"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_b5db1c68bd6e4394cd3b7fcb2c6"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_d2682514218f8972b6e51844f62"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_998441b96dcd0255f2695569d94"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_eda775f3a7f0eb2a46fdd023810"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_7814f80f7f44fe7c6b491900c1f"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_bf01994068c2f28a228aab95172"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_418e18593de4f3356b0b3513c7a"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_5c9e2e27a2f9992d98f31cf4518"`);
        await queryRunner.query(`ALTER TABLE "supplier_number_request_approve" DROP CONSTRAINT "FK_765f50e4842172a1b7787f5bc39"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" DROP CONSTRAINT "FK_39ddf6775929f82b269bacf0dde"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_payment_progress" DROP CONSTRAINT "FK_93123fc23fc6b69c14cf37f1c34"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" DROP CONSTRAINT "FK_599cf2b972f05d8fa8185d8f7f6"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection" DROP CONSTRAINT "FK_89a43532c20392b4659cca1894f"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" DROP CONSTRAINT "FK_4fafd55c40d645bcc559752576d"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" DROP CONSTRAINT "FK_fa9403ac1c39c5cfac4745419f0"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_item" DROP CONSTRAINT "FK_a9e328db5a3eb7ef64ca6686c92"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" DROP CONSTRAINT "FK_e1ba8fa4e38ec536def890d3d34"`);
        await queryRunner.query(`ALTER TABLE "contract_inspection_employee" DROP CONSTRAINT "FK_2c7d7736cf544cb4180d31bac36"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" DROP CONSTRAINT "FK_02f96e8b73bc4786fec88ed3a15"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" DROP CONSTRAINT "FK_65f4fa0adabc3a442073b435438"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" DROP CONSTRAINT "FK_0c3875bbccefc454f60778a5be8"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" DROP CONSTRAINT "FK_334264ed44b6305146a009dd587"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_col_value" DROP CONSTRAINT "FK_14fc1a21dab7b38d6e23058d079"`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" DROP CONSTRAINT "FK_05ba8eaec87c4074d36b2b95b55"`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" DROP CONSTRAINT "FK_4a77983cf66728f10f545e38367"`);
        await queryRunner.query(`ALTER TABLE "service_purchase_history" DROP CONSTRAINT "FK_bf2be9fb6122e1c6333d2eced90"`);
        await queryRunner.query(`ALTER TABLE "service_scene" DROP CONSTRAINT "FK_e95be4affefcf1ab6002459fb69"`);
        await queryRunner.query(`ALTER TABLE "service_scene" DROP CONSTRAINT "FK_3c79f5c45621fa064473e282eb3"`);
        await queryRunner.query(`ALTER TABLE "service_scene" DROP CONSTRAINT "FK_e86553ded4dda54f7a7c14840c9"`);
        await queryRunner.query(`ALTER TABLE "service_scene_list_detail" DROP CONSTRAINT "FK_d37e997479bfdf69ae6c0cb1de7"`);
        await queryRunner.query(`ALTER TABLE "material_group" DROP CONSTRAINT "FK_2b32b07ecb77ac8eeaf509791db"`);
        await queryRunner.query(`ALTER TABLE "offer_price" DROP CONSTRAINT "FK_f9fe2bd7a8fb496d41d4bb20282"`);
        await queryRunner.query(`ALTER TABLE "offer_price" DROP CONSTRAINT "FK_5b6691af02ad6cfcdd127f9e37f"`);
        await queryRunner.query(`ALTER TABLE "offer_price" DROP CONSTRAINT "FK_4ff3c7ddf45b1a1c320904dc112"`);
        await queryRunner.query(`ALTER TABLE "offer_price" DROP CONSTRAINT "FK_bb4bacf4af6f704d19e6f4c4edb"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" DROP CONSTRAINT "FK_555f706d11d79d5aaefb1566602"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_price" DROP CONSTRAINT "FK_3d4dc213d47aae6bd81b371d794"`);
        await queryRunner.query(`ALTER TABLE "offer_deal" DROP CONSTRAINT "FK_920b0e8da9bac1e3954ad903486"`);
        await queryRunner.query(`ALTER TABLE "offer_deal" DROP CONSTRAINT "FK_e6626d6cb9ceef159383789e712"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" DROP CONSTRAINT "FK_9cd5118d0138adb52a93fb0c465"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier" DROP CONSTRAINT "FK_a77f267009cea06dfcef3d048e2"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" DROP CONSTRAINT "FK_c1fbf776df2ef4524df71effd59"`);
        await queryRunner.query(`ALTER TABLE "offer_deal_supplier_price_value" DROP CONSTRAINT "FK_559e2900d2c0772ce9c75d6f496"`);
        await queryRunner.query(`ALTER TABLE "offer_price_list_detail" DROP CONSTRAINT "FK_ecd5c780d592dc89d69a4fa460c"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" DROP CONSTRAINT "FK_be6b0ac54a29b8d5466cde600ab"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price" DROP CONSTRAINT "FK_105573cee204cbcbb11562df909"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" DROP CONSTRAINT "FK_d19b3ad6ea4d242e224e79d1a81"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" DROP CONSTRAINT "FK_ea06e85d35bc1cd664e29b4a97c"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier" DROP CONSTRAINT "FK_a679f77c259d43a6b2d50292070"`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" DROP CONSTRAINT "FK_b7548e272aa8916db559dcd7364"`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" DROP CONSTRAINT "FK_56600efab6eb483eb902afa744b"`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" DROP CONSTRAINT "FK_4f2947f3124e02456c3f9951848"`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" DROP CONSTRAINT "FK_f8a80478d4f851ee9299295146d"`);
        await queryRunner.query(`ALTER TABLE "shipment_stage" DROP CONSTRAINT "FK_7a6badaaee477282083856dce3e"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" DROP CONSTRAINT "FK_fba7b40cf7da451b567ca1f7ce0"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_shipment_value" DROP CONSTRAINT "FK_2126f0cb48c3e70f5cc8ece47ce"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" DROP CONSTRAINT "FK_6c9750056dbad47e31b34c0580f"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_price" DROP CONSTRAINT "FK_30ab8ed0162ec34342d51068d5a"`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" DROP CONSTRAINT "FK_7037ad07e54ca0e48075b345f22"`);
        await queryRunner.query(`ALTER TABLE "bid_shipment_price" DROP CONSTRAINT "FK_e6f59010cb7204b13fef1b1d2c7"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" DROP CONSTRAINT "FK_68c5f86ef5a45aaf9c605cf1e74"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" DROP CONSTRAINT "FK_686395bad7a9085049dd1f9219e"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage_cost" DROP CONSTRAINT "FK_b07484da07db3817414875e822d"`);
        await queryRunner.query(`ALTER TABLE "shipment_cost_stage" DROP CONSTRAINT "FK_5c98f9d1318bb87998029e8ba82"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_custom_price_value" DROP CONSTRAINT "FK_161b879aab20df18ceb15b86f93"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" DROP CONSTRAINT "FK_c41fdd84d9524aeefa881e51fdd"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" DROP CONSTRAINT "FK_b3fa0454d450bd3566b67b11b4d"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_col_value" DROP CONSTRAINT "FK_17d00a51f00b2c3f48fd25e0830"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" DROP CONSTRAINT "FK_930bd8534a1d0efdacefc41505a"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col" DROP CONSTRAINT "FK_a4b6bcc6142788605753f739bb0"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" DROP CONSTRAINT "FK_1b467c5d4dc200dd94cfb9a7318"`);
        await queryRunner.query(`ALTER TABLE "offer_price_col_value" DROP CONSTRAINT "FK_a6fd9457077b6e9f2a35cbb25ba"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" DROP CONSTRAINT "FK_9c1553b44e928ae378ed2c107bc"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_price_value" DROP CONSTRAINT "FK_6ddc272faa5c3750ae754e264d7"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" DROP CONSTRAINT "FK_d84e9c117fe73bf398cc38b8616"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_trade_value" DROP CONSTRAINT "FK_0c88283a1c1d6b97ea830c17b90"`);
        await queryRunner.query(`ALTER TABLE "offer_trade" DROP CONSTRAINT "FK_769211822e9a9130696244fd060"`);
        await queryRunner.query(`ALTER TABLE "offer_trade" DROP CONSTRAINT "FK_7f6124a13c3f3d94b43ddf85026"`);
        await queryRunner.query(`ALTER TABLE "offer_trade" DROP CONSTRAINT "FK_81aa09f465c800e6f82a739aaf6"`);
        await queryRunner.query(`ALTER TABLE "offer_trade_list_detail" DROP CONSTRAINT "FK_70d1ba4d578cf661165e1412d5b"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" DROP CONSTRAINT "FK_c67bce06945fe1793a9e89e4aab"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_tech_value" DROP CONSTRAINT "FK_81bd2a5c71e28586e2ba4e65176"`);
        await queryRunner.query(`ALTER TABLE "offer_tech" DROP CONSTRAINT "FK_da26ea1a2620a955851f69210ac"`);
        await queryRunner.query(`ALTER TABLE "offer_tech" DROP CONSTRAINT "FK_9182f39b9c62fd4bc4e6e013a31"`);
        await queryRunner.query(`ALTER TABLE "offer_tech" DROP CONSTRAINT "FK_3c593faf2ddfd1a0c7161c83161"`);
        await queryRunner.query(`ALTER TABLE "offer_tech_list_detail" DROP CONSTRAINT "FK_f70094a3975f2e3552b884d83d1"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" DROP CONSTRAINT "FK_a08481d93d35c70c12603e60014"`);
        await queryRunner.query(`ALTER TABLE "offer_supplier_service" DROP CONSTRAINT "FK_0f870976dd62fbe5ff8084fc567"`);
        await queryRunner.query(`ALTER TABLE "auction_history" DROP CONSTRAINT "FK_a9b75b1dbd49d90df8afc7d2481"`);
        await queryRunner.query(`ALTER TABLE "po_member" DROP CONSTRAINT "FK_f41f38ea6931f3fc5becb0d632c"`);
        await queryRunner.query(`ALTER TABLE "po_member" DROP CONSTRAINT "FK_345131b5513984074aef49c14ef"`);
        await queryRunner.query(`ALTER TABLE "po_history" DROP CONSTRAINT "FK_18b2851fecdd41d92f1c1126c85"`);
        await queryRunner.query(`ALTER TABLE "po_history" DROP CONSTRAINT "FK_00555eaae617d36cb019cc436ea"`);
        await queryRunner.query(`ALTER TABLE "po_history" DROP CONSTRAINT "FK_64a00b17e837cf662894fef5e35"`);
        await queryRunner.query(`ALTER TABLE "contract_history" DROP CONSTRAINT "FK_5e938fef02a12dc97fe6bd31dd4"`);
        await queryRunner.query(`ALTER TABLE "contract_history" DROP CONSTRAINT "FK_b4081c0bb5e5ad9440295155b7a"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "FK_eb779d50474b4b376465d77b3ea"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_payment_progress" DROP CONSTRAINT "FK_875d0d2310ee6e12b3183ec3b6e"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP CONSTRAINT "FK_fbfa3d8cf7a68c99ff0ec4896cd"`);
        await queryRunner.query(`ALTER TABLE "pr_history" DROP CONSTRAINT "FK_b3e89605faa3387382f647936e5"`);
        await queryRunner.query(`ALTER TABLE "item_tech" DROP CONSTRAINT "FK_5964da7a8a0a76663eb696a6789"`);
        await queryRunner.query(`ALTER TABLE "item_tech" DROP CONSTRAINT "FK_48a9e3f00c9fd54618dcc23c5a6"`);
        await queryRunner.query(`ALTER TABLE "item_tech_list_detail" DROP CONSTRAINT "FK_6d5e197e96ef20c03594d262ec2"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" DROP CONSTRAINT "FK_4b70099f1d6be7f37b245384b0a"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_detail" DROP CONSTRAINT "FK_897a53586bd0c3ae35db7822c9d"`);
        await queryRunner.query(`ALTER TABLE "supplier_expertise_year_detail" DROP CONSTRAINT "FK_b13f447aa60bdfc21ea9b307c3d"`);
        await queryRunner.query(`ALTER TABLE "service_capacity_list_detail" DROP CONSTRAINT "FK_ceba55fc1b9eb39b36c43a9644c"`);
        await queryRunner.query(`DROP TABLE "bid_supplier_item"`);
        await queryRunner.query(`DROP TABLE "organizational_position"`);
        await queryRunner.query(`DROP TABLE "rate_data"`);
        await queryRunner.query(`DROP TABLE "rating_configuration"`);
        await queryRunner.query(`DROP TABLE "synchronizing_log"`);
        await queryRunner.query(`DROP TABLE "translation_entity"`);
        await queryRunner.query(`DROP TABLE "user_external_material_group"`);
        await queryRunner.query(`DROP INDEX "REL_ab4a80281f1e8d524714e00f38" ON "user"`);
        await queryRunner.query(`DROP INDEX "REL_031cdc2c9c5eb56d48b5bdb4e5" ON "user"`);
        await queryRunner.query(`DROP TABLE "user"`);
        await queryRunner.query(`DROP INDEX "REL_21d5eab891f3a77c23b0dd6d0d" ON "supplier"`);
        await queryRunner.query(`DROP INDEX "REL_4df75df2edeff9fb748d216bc5" ON "supplier"`);
        await queryRunner.query(`DROP INDEX "REL_e8902c50550ff82dd0143913c0" ON "supplier"`);
        await queryRunner.query(`DROP TABLE "supplier"`);
        await queryRunner.query(`DROP TABLE "bid_auction_supplier"`);
        await queryRunner.query(`DROP TABLE "bid_auction"`);
        await queryRunner.query(`DROP TABLE "bid"`);
        await queryRunner.query(`DROP TABLE "service"`);
        await queryRunner.query(`DROP TABLE "service_capacity"`);
        await queryRunner.query(`DROP TABLE "supplier_capacity"`);
        await queryRunner.query(`DROP TABLE "supplier_service"`);
        await queryRunner.query(`DROP TABLE "supplier_expertise"`);
        await queryRunner.query(`DROP INDEX "REL_374770032d4b1e34b5a44a1262" ON "employee"`);
        await queryRunner.query(`DROP INDEX "REL_f4b0d329c4a3cf79ffe9d56504" ON "employee"`);
        await queryRunner.query(`DROP TABLE "employee"`);
        await queryRunner.query(`DROP TABLE "employee_role"`);
        await queryRunner.query(`DROP TABLE "department"`);
        await queryRunner.query(`DROP TABLE "flow_approve"`);
        await queryRunner.query(`DROP TABLE "flow_approve_detail"`);
        await queryRunner.query(`DROP TABLE "flow_approve_base"`);
        await queryRunner.query(`DROP TABLE "company"`);
        await queryRunner.query(`DROP TABLE "plant"`);
        await queryRunner.query(`DROP TABLE "material_plant"`);
        await queryRunner.query(`DROP TABLE "material"`);
        await queryRunner.query(`DROP TABLE "media_file"`);
        await queryRunner.query(`DROP INDEX "IX_PR_ITEMS_MAT" ON "pr_item"`);
        await queryRunner.query(`DROP TABLE "pr_item"`);
        await queryRunner.query(`DROP INDEX "IX_PR_COMPOSITE" ON "pr"`);
        await queryRunner.query(`DROP TABLE "pr"`);
        await queryRunner.query(`DROP TABLE "bid_pr"`);
        await queryRunner.query(`DROP TABLE "contract"`);
        await queryRunner.query(`DROP TABLE "contract_document_handover"`);
        await queryRunner.query(`DROP TABLE "payment_progress"`);
        await queryRunner.query(`DROP TABLE "po"`);
        await queryRunner.query(`DROP TABLE "po_history_status_execution"`);
        await queryRunner.query(`DROP TABLE "po_product"`);
        await queryRunner.query(`DROP TABLE "pr_item_child"`);
        await queryRunner.query(`DROP TABLE "header_lead_time"`);
        await queryRunner.query(`DROP TABLE "po_lead_time"`);
        await queryRunner.query(`DROP TABLE "template_lead_time"`);
        await queryRunner.query(`DROP TABLE "po_pr"`);
        await queryRunner.query(`DROP TABLE "action_log"`);
        await queryRunner.query(`DROP TABLE "supplier_potential_upgrade"`);
        await queryRunner.query(`DROP TABLE "business_type"`);
        await queryRunner.query(`DROP INDEX "REL_47dbeb860b692c2984bd1ee520" ON "permission_individual"`);
        await queryRunner.query(`DROP TABLE "permission_individual"`);
        await queryRunner.query(`DROP TABLE "organizational_tree"`);
        await queryRunner.query(`DROP TABLE "permission_approve"`);
        await queryRunner.query(`DROP INDEX "REL_799ca14e17fb8bbb87908b577f" ON "permission_employee"`);
        await queryRunner.query(`DROP TABLE "permission_employee"`);
        await queryRunner.query(`DROP TABLE "permission"`);
        await queryRunner.query(`DROP TABLE "po_acceptance"`);
        await queryRunner.query(`DROP TABLE "po_acceptance_employee"`);
        await queryRunner.query(`DROP TABLE "supplier_service_history"`);
        await queryRunner.query(`DROP TABLE "block"`);
        await queryRunner.query(`DROP TABLE "ticket_evaluation_kpi_employee"`);
        await queryRunner.query(`DROP TABLE "part"`);
        await queryRunner.query(`DROP TABLE "ticket_evaluation_kpi"`);
        await queryRunner.query(`DROP TABLE "ticket_evaluation_kpi_detail"`);
        await queryRunner.query(`DROP TABLE "ticket_evaluation_kpi_list_detail"`);
        await queryRunner.query(`DROP TABLE "kpi"`);
        await queryRunner.query(`DROP TABLE "kpi_permission"`);
        await queryRunner.query(`DROP TABLE "kpi_scale"`);
        await queryRunner.query(`DROP TABLE "kpi_detail"`);
        await queryRunner.query(`DROP TABLE "kpi_permission_position"`);
        await queryRunner.query(`DROP TABLE "position"`);
        await queryRunner.query(`DROP TABLE "kpi_position"`);
        await queryRunner.query(`DROP TABLE "kpi_list_detail"`);
        await queryRunner.query(`DROP TABLE "kpi_company"`);
        await queryRunner.query(`DROP TABLE "evaluation_history_purchase"`);
        await queryRunner.query(`DROP TABLE "evaluation_history_purchase_employee"`);
        await queryRunner.query(`DROP TABLE "evaluation_history_purchase_detail"`);
        await queryRunner.query(`DROP TABLE "evaluation_history_purchase_list_detail"`);
        await queryRunner.query(`DROP TABLE "pr_item_compoment"`);
        await queryRunner.query(`DROP TABLE "supplier_upgrade_detail"`);
        await queryRunner.query(`DROP TABLE "supplier_upgrade"`);
        await queryRunner.query(`DROP TABLE "criteria_site_assessment"`);
        await queryRunner.query(`DROP TABLE "criteria_site_assessment_list_detail"`);
        await queryRunner.query(`DROP TABLE "site_assessment"`);
        await queryRunner.query(`DROP TABLE "plan_site_assessment"`);
        await queryRunner.query(`DROP TABLE "request_update_supplier"`);
        await queryRunner.query(`DROP TABLE "factory_supplier"`);
        await queryRunner.query(`DROP TABLE "purchasing_area"`);
        await queryRunner.query(`DROP TABLE "business_partner_group"`);
        await queryRunner.query(`DROP TABLE "region"`);
        await queryRunner.query(`DROP TABLE "country"`);
        await queryRunner.query(`DROP TABLE "bank_branch"`);
        await queryRunner.query(`DROP TABLE "bank"`);
        await queryRunner.query(`DROP TABLE "supplier_bank"`);
        await queryRunner.query(`DROP TABLE "condition_type"`);
        await queryRunner.query(`DROP TABLE "language_key"`);
        await queryRunner.query(`DROP TABLE "language"`);
        await queryRunner.query(`DROP TABLE "contract_member"`);
        await queryRunner.query(`DROP TABLE "data_history"`);
        await queryRunner.query(`DROP TABLE "service_access"`);
        await queryRunner.query(`DROP TABLE "user_confirm_code"`);
        await queryRunner.query(`DROP TABLE "inbound_container"`);
        await queryRunner.query(`DROP TABLE "inbound_item"`);
        await queryRunner.query(`DROP TABLE "shipment_cost_type"`);
        await queryRunner.query(`DROP INDEX "IDX_282584715bfdf3cb328b29fbe8" ON "shipment_route"`);
        await queryRunner.query(`DROP TABLE "shipment_route"`);
        await queryRunner.query(`DROP INDEX "IDX_49797bb4bc2ff5063b0b222849" ON "shipment_type"`);
        await queryRunner.query(`DROP TABLE "shipment_type"`);
        await queryRunner.query(`DROP INDEX "IDX_0557c7d7bfcedb7e91dc6dd890" ON "shipment"`);
        await queryRunner.query(`DROP TABLE "shipment"`);
        await queryRunner.query(`DROP TABLE "shipment_container"`);
        await queryRunner.query(`DROP TABLE "shipment_inbound"`);
        await queryRunner.query(`DROP INDEX "IDX_08cefbb982da97567edebfa78e" ON "inbound"`);
        await queryRunner.query(`DROP TABLE "inbound"`);
        await queryRunner.query(`DROP TABLE "shipment_item"`);
        await queryRunner.query(`DROP TABLE "inbound_document_handover"`);
        await queryRunner.query(`DROP TABLE "shipment_po"`);
        await queryRunner.query(`DROP INDEX "REL_f9011f5cd49dcab82a8faebb3b" ON "supplier_number"`);
        await queryRunner.query(`DROP INDEX "REL_90209110c6739b8ad68a20030d" ON "supplier_number"`);
        await queryRunner.query(`DROP TABLE "supplier_number"`);
        await queryRunner.query(`DROP TABLE "setting_role"`);
        await queryRunner.query(`DROP TABLE "supplier_notify"`);
        await queryRunner.query(`DROP TABLE "supplier_capacity_year_value"`);
        await queryRunner.query(`DROP TABLE "supplier_capacity_list_detail"`);
        await queryRunner.query(`DROP TABLE "supplier_history"`);
        await queryRunner.query(`DROP TABLE "supplier_expertise_law_detail"`);
        await queryRunner.query(`DROP TABLE "supplier_expertise_member"`);
        await queryRunner.query(`DROP TABLE "setting_string_client"`);
        await queryRunner.query(`DROP TABLE "service_trade"`);
        await queryRunner.query(`DROP TABLE "service_trade_list_detail"`);
        await queryRunner.query(`DROP TABLE "service_tech"`);
        await queryRunner.query(`DROP TABLE "service_tech_list_detail"`);
        await queryRunner.query(`DROP TABLE "service_custom_price"`);
        await queryRunner.query(`DROP TABLE "email_template"`);
        await queryRunner.query(`DROP TABLE "email_history"`);
        await queryRunner.query(`DROP TABLE "link_client"`);
        await queryRunner.query(`DROP TABLE "faq"`);
        await queryRunner.query(`DROP TABLE "faq_category"`);
        await queryRunner.query(`DROP TABLE "employee_warning"`);
        await queryRunner.query(`DROP TABLE "employee_notify"`);
        await queryRunner.query(`DROP TABLE "bid_type"`);
        await queryRunner.query(`DROP TABLE "bid_employee_access"`);
        await queryRunner.query(`DROP TABLE "bid_employee_rate"`);
        await queryRunner.query(`DROP TABLE "bid_history"`);
        await queryRunner.query(`DROP TABLE "banner_client"`);
        await queryRunner.query(`DROP TABLE "auction"`);
        await queryRunner.query(`DROP TABLE "auction_supplier"`);
        await queryRunner.query(`DROP TABLE "auction_supplier_price"`);
        await queryRunner.query(`DROP TABLE "uom"`);
        await queryRunner.query(`DROP TABLE "reservation_item"`);
        await queryRunner.query(`DROP TABLE "reservation"`);
        await queryRunner.query(`DROP TABLE "reservation_item_child"`);
        await queryRunner.query(`DROP TABLE "warehouse"`);
        await queryRunner.query(`DROP TABLE "reservation_norm"`);
        await queryRunner.query(`DROP TABLE "material_uom"`);
        await queryRunner.query(`DROP TABLE "offer_service"`);
        await queryRunner.query(`DROP TABLE "offer"`);
        await queryRunner.query(`DROP TABLE "offer_shipment_price"`);
        await queryRunner.query(`DROP TABLE "offer_custom_price"`);
        await queryRunner.query(`DROP TABLE "external_material_group"`);
        await queryRunner.query(`DROP TABLE "bid_exmatgroup"`);
        await queryRunner.query(`DROP TABLE "bid_pr_item"`);
        await queryRunner.query(`DROP TABLE "bid_custom_price"`);
        await queryRunner.query(`DROP TABLE "bid_tech"`);
        await queryRunner.query(`DROP TABLE "bid_tech_list_detail"`);
        await queryRunner.query(`DROP TABLE "bid_supplier_tech_value"`);
        await queryRunner.query(`DROP TABLE "bid_supplier"`);
        await queryRunner.query(`DROP TABLE "bid_supplier_shipment_value"`);
        await queryRunner.query(`DROP TABLE "bid_supplier_custom_price_value"`);
        await queryRunner.query(`DROP TABLE "bid_supplier_trade_value"`);
        await queryRunner.query(`DROP TABLE "bid_trade"`);
        await queryRunner.query(`DROP TABLE "bid_trade_list_detail"`);
        await queryRunner.query(`DROP TABLE "bid_supplier_price_value"`);
        await queryRunner.query(`DROP TABLE "bid_price"`);
        await queryRunner.query(`DROP TABLE "bid_supplier_price"`);
        await queryRunner.query(`DROP TABLE "bid_supplier_price_col_value"`);
        await queryRunner.query(`DROP TABLE "bid_price_col"`);
        await queryRunner.query(`DROP TABLE "bid_price_col_value"`);
        await queryRunner.query(`DROP TABLE "bid_auction_price"`);
        await queryRunner.query(`DROP TABLE "bid_auction_supplier_price_value"`);
        await queryRunner.query(`DROP TABLE "service_price"`);
        await queryRunner.query(`DROP TABLE "service_price_col_value"`);
        await queryRunner.query(`DROP TABLE "service_price_col"`);
        await queryRunner.query(`DROP TABLE "service_price_list_detail"`);
        await queryRunner.query(`DROP TABLE "bid_deal_price"`);
        await queryRunner.query(`DROP TABLE "bid_deal"`);
        await queryRunner.query(`DROP TABLE "bid_deal_supplier"`);
        await queryRunner.query(`DROP TABLE "bid_deal_supplier_price_value"`);
        await queryRunner.query(`DROP TABLE "bid_price_list_detail"`);
        await queryRunner.query(`DROP TABLE "material_type"`);
        await queryRunner.query(`DROP TABLE "round_up_cont"`);
        await queryRunner.query(`DROP TABLE "budget_receipt"`);
        await queryRunner.query(`DROP TABLE "budget_receipt_history"`);
        await queryRunner.query(`DROP TABLE "budget_receipt_item"`);
        await queryRunner.query(`DROP TABLE "round_up_cont_pr"`);
        await queryRunner.query(`DROP TABLE "round_up_cont_pr_item"`);
        await queryRunner.query(`DROP TABLE "round_up_cont_template"`);
        await queryRunner.query(`DROP TABLE "round_up_cont_history"`);
        await queryRunner.query(`DROP TABLE "round_up_cont_template_col"`);
        await queryRunner.query(`DROP TABLE "round_up_cont_setting_value"`);
        await queryRunner.query(`DROP TABLE "setting_string"`);
        await queryRunner.query(`DROP TABLE "recommended_purchase_setting_value"`);
        await queryRunner.query(`DROP TABLE "recommended_purchase"`);
        await queryRunner.query(`DROP TABLE "recommended_purchase_shipment_stage"`);
        await queryRunner.query(`DROP TABLE "recommended_purchase_shipment_cost_price"`);
        await queryRunner.query(`DROP INDEX "IDX_8ab33728a839e919b07186dc63" ON "shipment_cost"`);
        await queryRunner.query(`DROP TABLE "shipment_cost"`);
        await queryRunner.query(`DROP TABLE "shipment_cost_detail"`);
        await queryRunner.query(`DROP TABLE "business_plan"`);
        await queryRunner.query(`DROP TABLE "business_plan_rfq"`);
        await queryRunner.query(`DROP TABLE "rfq"`);
        await queryRunner.query(`DROP TABLE "recommended_purchase_rfq"`);
        await queryRunner.query(`DROP TABLE "rfq_details"`);
        await queryRunner.query(`DROP TABLE "business_plan_template"`);
        await queryRunner.query(`DROP TABLE "business_plan_history"`);
        await queryRunner.query(`DROP TABLE "business_plan_template_col"`);
        await queryRunner.query(`DROP TABLE "business_plan_setting_value"`);
        await queryRunner.query(`DROP TABLE "business_plan_col_value"`);
        await queryRunner.query(`DROP TABLE "recommended_purchase_template"`);
        await queryRunner.query(`DROP TABLE "recommended_purchase_history"`);
        await queryRunner.query(`DROP TABLE "recommended_purchase_template_col"`);
        await queryRunner.query(`DROP TABLE "recommended_purchase_col_value"`);
        await queryRunner.query(`DROP TABLE "payment_method"`);
        await queryRunner.query(`DROP TABLE "role_fi_supplier"`);
        await queryRunner.query(`DROP TABLE "planning_group"`);
        await queryRunner.query(`DROP TABLE "payment_term"`);
        await queryRunner.query(`DROP TABLE "supplier_plant"`);
        await queryRunner.query(`DROP TABLE "role_supplier"`);
        await queryRunner.query(`DROP TABLE "incoterm"`);
        await queryRunner.query(`DROP TABLE "purchasing_org"`);
        await queryRunner.query(`DROP TABLE "purchasing_org_schema"`);
        await queryRunner.query(`DROP TABLE "schemaConfig"`);
        await queryRunner.query(`DROP TABLE "supplier_schema"`);
        await queryRunner.query(`DROP TABLE "supplier_list_price_po"`);
        await queryRunner.query(`DROP TABLE "currency"`);
        await queryRunner.query(`DROP TABLE "material_price"`);
        await queryRunner.query(`DROP TABLE "payment"`);
        await queryRunner.query(`DROP TABLE "payment_contract"`);
        await queryRunner.query(`DROP TABLE "payment_po"`);
        await queryRunner.query(`DROP TABLE "payment_bill"`);
        await queryRunner.query(`DROP TABLE "bill"`);
        await queryRunner.query(`DROP TABLE "bill_lookup"`);
        await queryRunner.query(`DROP TABLE "bill_history"`);
        await queryRunner.query(`DROP TABLE "po_price_list"`);
        await queryRunner.query(`DROP TABLE "procedure"`);
        await queryRunner.query(`DROP TABLE "po_product_price_list"`);
        await queryRunner.query(`DROP TABLE "master_condition_type"`);
        await queryRunner.query(`DROP TABLE "pr_keep_budget"`);
        await queryRunner.query(`DROP TABLE "plant_purchasing_org"`);
        await queryRunner.query(`DROP TABLE "complaint"`);
        await queryRunner.query(`DROP TABLE "complaint_chat"`);
        await queryRunner.query(`DROP TABLE "complaint_notify"`);
        await queryRunner.query(`DROP TABLE "complaint_handling_plan"`);
        await queryRunner.query(`DROP TABLE "complaint_employee"`);
        await queryRunner.query(`DROP TABLE "complaint_item_cargo"`);
        await queryRunner.query(`DROP TABLE "complaint_item"`);
        await queryRunner.query(`DROP TABLE "complaint_prevention"`);
        await queryRunner.query(`DROP TABLE "complaint_fix"`);
        await queryRunner.query(`DROP TABLE "complaint_line_item"`);
        await queryRunner.query(`DROP TABLE "contract_item"`);
        await queryRunner.query(`DROP TABLE "complaint_department"`);
        await queryRunner.query(`DROP TABLE "purchasing_group"`);
        await queryRunner.query(`DROP TABLE "employee_purchasing_group"`);
        await queryRunner.query(`DROP INDEX "REL_765f50e4842172a1b7787f5bc3" ON "supplier_number_request_approve"`);
        await queryRunner.query(`DROP TABLE "supplier_number_request_approve"`);
        await queryRunner.query(`DROP TABLE "gl_account"`);
        await queryRunner.query(`DROP TABLE "industry_standard"`);
        await queryRunner.query(`DROP TABLE "stakeholder_category"`);
        await queryRunner.query(`DROP TABLE "title"`);
        await queryRunner.query(`DROP TABLE "contract_inspection_payment_progress"`);
        await queryRunner.query(`DROP TABLE "contract_inspection"`);
        await queryRunner.query(`DROP TABLE "contract_inspection_item"`);
        await queryRunner.query(`DROP TABLE "contract_inspection_employee"`);
        await queryRunner.query(`DROP TABLE "round_up_cont_col_value"`);
        await queryRunner.query(`DROP TABLE "service_purchase_history"`);
        await queryRunner.query(`DROP TABLE "service_scene"`);
        await queryRunner.query(`DROP TABLE "service_scene_list_detail"`);
        await queryRunner.query(`DROP TABLE "material_group"`);
        await queryRunner.query(`DROP TABLE "offer_price"`);
        await queryRunner.query(`DROP TABLE "offer_deal_price"`);
        await queryRunner.query(`DROP TABLE "offer_deal"`);
        await queryRunner.query(`DROP TABLE "offer_deal_supplier"`);
        await queryRunner.query(`DROP TABLE "offer_deal_supplier_price_value"`);
        await queryRunner.query(`DROP TABLE "offer_price_list_detail"`);
        await queryRunner.query(`DROP TABLE "offer_supplier_price"`);
        await queryRunner.query(`DROP TABLE "offer_supplier"`);
        await queryRunner.query(`DROP TABLE "shipment_stage"`);
        await queryRunner.query(`DROP TABLE "offer_supplier_shipment_value"`);
        await queryRunner.query(`DROP INDEX "REL_6c9750056dbad47e31b34c0580" ON "shipment_cost_price"`);
        await queryRunner.query(`DROP TABLE "shipment_cost_price"`);
        await queryRunner.query(`DROP TABLE "bid_shipment_price"`);
        await queryRunner.query(`DROP INDEX "REL_68c5f86ef5a45aaf9c605cf1e7" ON "shipment_cost_stage_cost"`);
        await queryRunner.query(`DROP TABLE "shipment_cost_stage_cost"`);
        await queryRunner.query(`DROP TABLE "shipment_cost_stage"`);
        await queryRunner.query(`DROP TABLE "offer_supplier_custom_price_value"`);
        await queryRunner.query(`DROP TABLE "offer_supplier_price_col_value"`);
        await queryRunner.query(`DROP TABLE "offer_price_col"`);
        await queryRunner.query(`DROP TABLE "offer_price_col_value"`);
        await queryRunner.query(`DROP TABLE "offer_supplier_price_value"`);
        await queryRunner.query(`DROP TABLE "offer_supplier_trade_value"`);
        await queryRunner.query(`DROP TABLE "offer_trade"`);
        await queryRunner.query(`DROP TABLE "offer_trade_list_detail"`);
        await queryRunner.query(`DROP TABLE "offer_supplier_tech_value"`);
        await queryRunner.query(`DROP TABLE "offer_tech"`);
        await queryRunner.query(`DROP TABLE "offer_tech_list_detail"`);
        await queryRunner.query(`DROP TABLE "offer_supplier_service"`);
        await queryRunner.query(`DROP TABLE "auction_history"`);
        await queryRunner.query(`DROP TABLE "po_member"`);
        await queryRunner.query(`DROP TABLE "po_history"`);
        await queryRunner.query(`DROP TABLE "contract_history"`);
        await queryRunner.query(`DROP TABLE "contract_appendix"`);
        await queryRunner.query(`DROP TABLE "contract_appendix_payment_progress"`);
        await queryRunner.query(`DROP TABLE "contract_appendix_item"`);
        await queryRunner.query(`DROP TABLE "pr_history"`);
        await queryRunner.query(`DROP TABLE "item_tech"`);
        await queryRunner.query(`DROP TABLE "item_tech_list_detail"`);
        await queryRunner.query(`DROP TABLE "supplier_expertise_detail"`);
        await queryRunner.query(`DROP TABLE "supplier_expertise_year_detail"`);
        await queryRunner.query(`DROP TABLE "service_capacity_list_detail"`);
    }

}
