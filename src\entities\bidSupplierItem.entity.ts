import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

@Entity('bid_supplier_item')
export class BidSupplierItemEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidSupplierId: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidId: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidPrId: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  materialId: string
}
