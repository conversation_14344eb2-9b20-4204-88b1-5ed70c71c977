import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnCont1750753328369 implements MigrationInterface {
  name = 'AddColumnCont1750753328369'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "templateExcelId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "prType" varchar(150)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "prType"`)
    await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "templateExcelId"`)
  }
}
