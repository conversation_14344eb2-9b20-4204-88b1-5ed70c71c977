import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

/** Ngôn ngữ */
@Entity('language')
export class LanguageEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  key: string

  @Column({
    type: 'varchar',
    length: 1000,
    nullable: false,
  })
  value: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  languageType: string
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  path: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  createdBy: string
}
