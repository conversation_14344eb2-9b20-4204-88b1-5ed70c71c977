import { MigrationInterface, QueryRunner } from "typeorm";

export class GenSyncTable1748624928107 implements MigrationInterface {
    name = 'GenSyncTable1748624928107'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "dataJson" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "datId" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "entityName" varchar(250)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "urlSync" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "urlSend" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "message"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "message" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "error"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "error" nvarchar(max)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "error"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "error" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "message"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" ADD "message" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "urlSend"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "urlSync"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "entityName"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "datId"`);
        await queryRunner.query(`ALTER TABLE "synchronizing_log" DROP COLUMN "dataJson"`);
    }

}
