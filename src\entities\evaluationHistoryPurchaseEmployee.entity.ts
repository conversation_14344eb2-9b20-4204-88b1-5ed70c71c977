import { Column, <PERSON>tity, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { EvaluationHistoryPurchaseEntity } from './evaluationHistoryPurchase.entity'

/** <PERSON>h sách nhân viên đánh giá lịch sử mua hàng */

@Entity('evaluation_history_purchase_employee')
export class EvaluationHistoryPurchaseEmployeeEntity extends BaseEntity {
  /** Chủ hàng */
  @Column({ type: 'varchar', nullable: false })
  evaluationHistoryPurchaseId: string
  @ManyToOne(() => EvaluationHistoryPurchaseEntity, (p) => p.evaluationPurchaseEmployees)
  @JoinColumn({ name: 'evaluationHistoryPurchaseId', referencedColumnName: 'id' })
  evaluationPurchase: Promise<EvaluationHistoryPurchaseEntity>

  /** Nhân viên đánh giá */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.evaluationPurchaseEmployees)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>
}
