import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateSapCode1749263786466 implements MigrationInterface {
    name = 'CreateSapCode1749263786466'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation" ADD "sapCode" varchar(250)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "sapCode"`);
    }

}
