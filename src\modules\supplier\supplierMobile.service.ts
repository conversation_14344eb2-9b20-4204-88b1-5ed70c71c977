import { Injectable, NotAcceptableException, NotFoundException } from '@nestjs/common'
import * as moment from 'moment'
import { Between, LessThanOrEqual, Like, MoreThanOrEqual } from 'typeorm'
import {
  enumData,
  ERROR_YOU_DO_NOT_HAVE_PERMISSION
} from '../../constants'
import { RoleDataPermission } from '../../constants/permission'
import { PaginationDto, UserDto } from '../../dto'
import { coreHelper } from '../../helpers'
import {
  CompanyRepository,
  EmployeeRepository,
  SupplierRepository,
  SupplierServiceRepository,
  TemplateEvaluationPotentialRepository
} from '../../repositories'

@Injectable()
export class SupplierMobileService {
  constructor(
    private readonly repo: SupplierRepository,
    private readonly supplierServiceRepo: SupplierServiceRepository,
    private readonly employeeRepo: EmployeeRepository,
    private readonly companyRepo: CompanyRepository,
    private readonly templateEvaluationPotentialRepo: TemplateEvaluationPotentialRepository,
  ) { }

  /** Phân trang Danh sách NCC mobile */
  async pagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data) throw new NotFoundException()

    const whereCon: any = { isDeleted: false }
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.status) whereCon.status = data.where.status
    if (data.where.supplierSource) whereCon.supplierSource = data.where.supplierSource
    if (data.where.type) whereCon.type = data.where.type

    if (data.where.businessTypeId) whereCon.businessTypeId = data.where.businessTypeId
    if (data.where.companyId) whereCon.companyId = data.where.companyId

    if (data.where.score) whereCon.score = Like(`%${data.where.score}%`)
    if (data.where.sapCode) whereCon.sapCode = data.where.sapCode
    if (data.where.companyCode) {
      whereCon.company = {}
      whereCon.company.code = Like(`%${data.where.companyCode}%`)
    }

    if (data.where?.dateStart && data.where.dateEnd) {
      whereCon.createdAt = Between(
        moment(data.where.dateStart).format('YYYY-MM-DD 00:00:00'),
        moment(data.where.dateEnd).format('YYYY-MM-DD 23:59:59'),
      )
    } else if (data.where?.dateStart) {
      whereCon.createdAt = MoreThanOrEqual(moment(data.where.dateStart).format('YYYY-MM-DD 00:00:00'))
    } else if (data.where?.dateEnd) {
      whereCon.createdAt = LessThanOrEqual(moment(data.where.dateEnd).format('YYYY-MM-DD 23:59:59'))
    }

    if (data.where.supplierCode) whereCon.code = Like(`%${data.where.supplierCode}%`)

    // nếu lọc service
    if (data.where.serviceId) {
      let where2: any = { serviceId: data.where.serviceId, companyId: user.companyId, isDeleted: false }
      if (data.where.supplierType) {
        where2.supplierType = data.where.supplierType
      }
      const lstSupplierService = await this.supplierServiceRepo.find({ where: where2, select: { id: true, supplierId: true } })

      if (lstSupplierService.length == 0) return [[], 0]
    }

    // lọc ra danh sách template
    const listEmp = await this.employeeRepo.find({ where: { isDeleted: false } })
    const listTemplate = await this.templateEvaluationPotentialRepo.find({ where: { isDeleted: false } })

    const dictTemplate: Record<string, { employeeLawId: string; employeeCapacityId: string; employeeLawName: string; employeeCapacityName: string }> =
      {}

    const dictEmp: Record<string, { name: string }> = {}

    listEmp.forEach((item) => {
      dictEmp[item.id] = {
        name: item.name,
      }
    })

    listTemplate.forEach((item) => {
      dictTemplate[item.companyId] = {
        employeeLawId: item.employeeLawId,
        employeeLawName: item.employeeLawId ? dictEmp[item.employeeLawId]?.name : '',
        employeeCapacityId: item?.employeeCapacityId,
        employeeCapacityName: item.employeeCapacityId ? dictEmp[item.employeeCapacityId]?.name : '',
      }
    })

    const res: any = await this.repo.findAndCount(
      {
        where: whereCon,
        skip: data.skip,
        take: data.take,
        relations: { user: true, supplierServices: { service: true }, businessType: true },
        order: { createdAt: 'DESC' },
      },
      user,
      RoleDataPermission.ManagementSupSupplier.code,
    )
    const dicStatus: any = {}
    const dicStatusColor: any = {}
    const dicStatusBgColor: any = {}
    const dicStatusBorderColor: any = {}

    {
      const lstPOStatus = coreHelper.convertObjToArray(enumData.SupplierStatus)
      lstPOStatus.forEach((c) => (dicStatus[c.code] = c.name))
      lstPOStatus.forEach((c) => (dicStatusColor[c.code] = c.color))
      lstPOStatus.forEach((c) => (dicStatusBgColor[c.code] = c.bgColor))
      lstPOStatus.forEach((c) => (dicStatusBorderColor[c.code] = c.borderColor))
    }

    const dicCompany: any = {}

    const listCompany = await this.companyRepo.find({
      where: {
        isDeleted: false,
      },
    })
    {
      listCompany.forEach((c) => (dicCompany[c.id] = c))
    }
    for (const item of res[0]) {
      item.statusName = dicStatus[item.status]
      item.permission = await this.repo.getRowRole(user, item, RoleDataPermission.ManagementSupSupplier.code)

      item.businessTypeName = item?.__businessType__?.name
      item.typeName = enumData.supplierType[item.type]?.name
      item.companyCode = dicCompany[item.companyId]?.code

      // Thêm phần màu cho trạng thái khóa/mở khóa nhà cung cấp
      item.requestUpdateStatusColor = enumData.RequestUpdateStatusSupplier[item.requestUpdateStatus]?.color
      item.requestUpdateStatusBgColor = enumData.RequestUpdateStatusSupplier[item.requestUpdateStatus]?.bgColor
      item.requestUpdateStatusBorderColor = enumData.RequestUpdateStatusSupplier[item.requestUpdateStatus]?.borderColor
      item.requestUpdateStatusName = enumData.RequestUpdateStatusSupplier[item.requestUpdateStatus]?.name

      item.supplierName = item.name
      item.supplierCode = item.code

      item.statusName = dicStatus[item.status]
      item.statusColor = dicStatusColor[item.status]
      item.statusBgColor = dicStatusBgColor[item.status]
      item.statusBorderColor = dicStatusBorderColor[item.status]

      delete item.__user__

      item.listSupplierService = []
      const lstSupplierService = item.__supplierServices__ || []
      item.totalSupplierService = lstSupplierService?.length
      delete item.__supplierServices__

      if (item.status === enumData.SupplierStatus.LegalReview.code || item.status === enumData.SupplierStatus.ReCheck.code) {
        item.currentEmpEvaluateName = dictTemplate[item.companyId].employeeLawName
      }

      if (item.status === enumData.SupplierStatus.CapacityReview.code) {
        item.currentEmpEvaluateName = dictTemplate[item.companyId].employeeCapacityName
      }

      for (const supplierService of lstSupplierService) {
        const itemName = supplierService.__service__.code + ' - ' + supplierService.__service__.name
        delete supplierService.__service__

        item.listSupplierService.push({
          ...supplierService,
          itemName,
        })
      }
    }

    return res
  }

  /** Lấy chi tiết NCC cho mobile */
  async findDetail(user: UserDto, data: { id: string; supplierServiceId: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: {
        supplierServices: { service: true },
        businessType: true,
        country: true,
        region: true,
      },
    })
    if (!res) throw new NotFoundException('Không tìm thấy nhà cung cấp')

    res.listSupplierService = []
    const lstSupplierService = res.__supplierServices__ || []
    for (const supplierService of lstSupplierService) {
      const itemName = supplierService.__service__.code + ' - ' + supplierService.__service__.name
      res.listSupplierService.push(itemName)
    }
    res.businessTypeName = res.__businessType__?.name || ''
    res.countryName = res.__country__?.name || ''
    res.regionName = res.__region__?.name || ''
    return res
  }
}
