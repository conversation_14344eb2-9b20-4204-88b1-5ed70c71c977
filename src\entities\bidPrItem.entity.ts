import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { PrItemEntity } from './prItem.entity'
import { ServiceEntity } from './service.entity'
import { BidTechEntity } from './bidTech.entity'
import { BidTradeEntity } from './bidTrade.entity'
import { BidPriceEntity } from './bidPrice.entity'
import { BidPriceColEntity } from './bidPriceCol.entity'
import { BidCustomPriceEntity } from './bidCustomPrice.entity'
import { BidSupplierEntity } from './bidSupplier.entity'
import { AuctionSupplierPriceEntity } from './auctionSupPrice.entity'
import { MaterialEntity } from './material.entity'
import { UomEntity } from './uom.entity'
import { MaterialGroupEntity } from './materialGroup.entity'
import { BidExMatGroupEntity } from './bidExgroup.entity'

@Entity('bid_pr_item')
export class BidPrItemEntity extends BaseEntity {
  /** Mã hệ thống tự sinh */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  currency: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.bidPrItem)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prItemId?: string
  /** Pr */
  @ManyToOne(() => PrItemEntity, (p) => p.bidPrItem)
  @JoinColumn({ name: 'prItemId', referencedColumnName: 'id' })
  prItem: Promise<PrItemEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPriceId?: string

  /** Lĩnh vực mời thầu */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId?: string
  /** Lĩnh vực mời thầu */
  @ManyToOne(() => ServiceEntity, (p) => p.bidPrItem)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  /** Số lượng của Item trong gói thầu chi tiết */
  @Column({
    nullable: true,
    default: 0,
  })
  quantityItem: number

  /** Số lượng của Item trong gói thầu chi tiết */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  /** Tỉ trọng % kỹ thuật */
  @Column({
    nullable: true,
    default: 0,
  })
  percentTech: number

  /** Tỉ trọng % DKTM */
  @Column({
    nullable: true,
    default: 0,
  })
  percentTrade: number

  /** Tỉ trọng % giá */
  @Column({
    nullable: true,
    default: 0,
  })
  percentPrice: number

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Category */
  @Column({
    type: 'varchar',
    nullable: true,
    length: 1,
  })
  category: string

  /** tên hàng */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  shortText: string

  /** Category */
  @Column({
    type: 'varchar',
    nullable: true,
    length: 50,
  })
  categoryName: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.bidItems)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @Column({
    type: 'varchar',
    nullable: true,
    length: 50,
  })
  materialCode: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  unitCode: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  unitName: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.bidItems)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string

  @ManyToOne(() => MaterialGroupEntity, (p) => p.prItems)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>

  /** Nhóm vật tư */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  materialGroupName: string

  /** Nhóm vật tư */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  materialGroupCode: string

  /** 1 dịch vụ sẽ có nhiều cấu hình kỹ thuật */
  @OneToMany(() => BidTechEntity, (p) => p.bidItem)
  techs: Promise<BidTechEntity[]>

  /** 1 dịch vụ sẽ có nhiều cấu hình thương mại */
  @OneToMany(() => BidTradeEntity, (p) => p.bidItem)
  trades: Promise<BidTradeEntity[]>

  @OneToMany(() => BidPriceEntity, (p) => p.bidItem)
  prices: Promise<BidPriceEntity[]>

  @OneToMany(() => BidPriceEntity, (p) => p.baseItem)
  basePrice: Promise<BidPriceEntity[]>

  @OneToMany(() => BidPriceColEntity, (p) => p.bidItem)
  bidPriceCols: Promise<BidPriceColEntity[]>

  @OneToMany(() => BidCustomPriceEntity, (p) => p.bidItem)
  customPrices: Promise<BidCustomPriceEntity[]>

  /** Danh sách nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierEntity, (p) => p.bidItem)
  bidSuppliers: Promise<BidSupplierEntity[]>

  /** Công thức tính cột đơn giá */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fomular: string

  /** Có tự động đấu thầu không? */
  @Column({
    nullable: true,
    default: false,
  })
  isExmatgroup: boolean

  @OneToMany(() => AuctionSupplierPriceEntity, (p) => p.bidItem)
  auctionSupplierPrice: Promise<AuctionSupplierPriceEntity[]>

  /* này cho shipment */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  conditionType: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @Column({
    nullable: true,
  })
  amount: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  crcy: string

  @Column({
    nullable: true,
  })
  per: number

  @Column({
    nullable: true,
  })
  conditionValue: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  curr: string

  @Column({
    nullable: true,
  })
  cConDe: number

  @Column({
    nullable: true,
  })
  numCCo: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidExgroupId: string
  @ManyToOne(() => BidExMatGroupEntity, (p) => p.bidPrItems)
  @JoinColumn({ name: 'bidExgroupId', referencedColumnName: 'id' })
  bidExgroup: Promise<BidExMatGroupEntity>
}
