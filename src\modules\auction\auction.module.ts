import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { AuctionHistoryRepository, AuctionRepository, AuctionSupplierPriceRepository, AuctionSupplierRepository } from '../../repositories'
import { AuctionService } from './auction.service'
import { AuctionController } from './auction.controller'
import { EmailModule } from '../email/email.module'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'
import { FlowApproveModule } from '../flowApprove/flowApprove.module'

@Module({
  imports: [
    EmailModule,
    TypeOrmExModule.forCustomRepository([AuctionRepository, AuctionSupplierRepository, AuctionHistoryRepository, AuctionSupplierPriceRepository]),
    OrganizationalPositionModule,
    FlowApproveModule,
  ],
  controllers: [AuctionController],
  providers: [AuctionService],
  exports: [AuctionService],
})
export class AuctionModule {}
