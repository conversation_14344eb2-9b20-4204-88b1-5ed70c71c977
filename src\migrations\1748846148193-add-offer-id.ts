import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddOfferId1748846148193 implements MigrationInterface {
  name = 'AddOfferId1748846148193'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "offer" ADD "offerId" varchar(250)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "offer" DROP COLUMN "offerId"`)
  }
}
