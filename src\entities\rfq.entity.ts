import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { MaterialEntity } from './material.entity'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { RfqDetailsEntity } from './rfqDetails.entity'
import { BusinessPlanRfqEntity } from './businessPlanRfq.entity'
import { RecommendedPurchaseRfqEntity } from './recommendedPurchaseRfq.entity'
import { CompanyEntity } from './company.entity'
import { RequestQuoteEntity } from './requestQuote.entity'
import { CurrencyEntity } from './currency.entity'
import { IncotermEntity } from './incoterm.entity'
import { PaymentTermEntity } from './paymentTerm.entity'
import { PurchasingOrgEntity } from './purchasingOrg.entity'
import { PurchasingGroupEntity } from './purchasingGroup.entity'
import { PlantEntity } from './plant.entity'
import { RfqApprovedEntity } from './rfqApproved.entity'

@Entity('rfq')
export class RfqEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  type: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.rfqs)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** số lượng cần đặt hàng */
  @Column({
    nullable: true,
    default: 0,
  })
  rfqQuantity: number

  /** Thời gian giao hàng */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  /** Thời gian giao hàng */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  deadLine: Date

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.rfqs)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** Giá khởi điểm */
  @Column({ type: 'bigint', nullable: true })
  netPrice: number

  /* Id nguồn tạo */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  targetId: string

  /* entity tạo */
  @Column({
    length: 50,
    nullable: true,
  })
  entityName: string

  /* id của lần đàm phán giá nào */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  dealId: string

  @Column({
    nullable: true,
    default: false,
  })
  isSynchronizing: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  sapCode: string

  @OneToMany(() => RfqDetailsEntity, (p) => p.rfq)
  rfqDetails: Promise<RfqDetailsEntity[]>

  @OneToMany(() => RecommendedPurchaseRfqEntity, (p) => p.rfq)
  recommendedPurchaseRfq: Promise<RecommendedPurchaseRfqEntity[]>

  @OneToMany(() => BusinessPlanRfqEntity, (p) => p.rfq)
  businessPlanRfq: Promise<BusinessPlanRfqEntity[]>

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  code: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string

  @ManyToOne(() => CompanyEntity, (p) => p.rfqs)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  requestQuoteId: string
  @ManyToOne(() => RequestQuoteEntity, (p) => p.rfqs)
  @JoinColumn({ name: 'requestQuoteId', referencedColumnName: 'id' })
  requestQuote: Promise<RequestQuoteEntity>

  // Số hoặc mã để quản lý nhóm RFQ
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  collNo: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  currencyId: string

  @ManyToOne(() => CurrencyEntity, (p) => p.rfqs)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermId: string

  @ManyToOne(() => IncotermEntity, (p) => p.rfqs)
  @JoinColumn({ name: 'incotermId', referencedColumnName: 'id' })
  incoterm: Promise<IncotermEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentTermId: string

  @ManyToOne(() => PaymentTermEntity, (p) => p.rfqs)
  @JoinColumn({ name: 'paymentTermId', referencedColumnName: 'id' })
  paymentTerm: Promise<PaymentTermEntity>

  // Người liên lạc bên NCC your_ref
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  yourRef: string

  // Người bán sales_per
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  salesPer: string

  // Người liên lạc bên mua. (our_ref)
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  ourRef: string

  // Số điện thoại. (Telephone)
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  telephone: string

  // Ghi chú (text1)
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  headerText: string

  // Header note (text2)
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  headerNote: string

  // pricingTypes (text3)
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  pricingTypes: string

  // Deadlines (text4)
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  deadlines: string

  // Terms of delivery (text5)
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  termsDelivery: string

  // Shipping instructions (text6)
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  shippingInstructions: string

  // Terms of payment (text7)
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  termsPayment: string

  // Inco. Location1
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  incoLoc1: string

  // Inco. Location2
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  incoLoc2: string

  // inco_ver
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  incoVer: string

  // Targ. Val.
  @Column({
    type: 'nvarchar',
    length: 1000,
    nullable: true,
  })
  targVal: string

  // Ngày bảo hành
  @Column({
    nullable: true,
    type: 'datetime',
  })
  warrantyDate: Date

  // Ngày bảo hành bindg_date
  @Column({
    nullable: true,
    type: 'datetime',
  })
  bindgDate: Date

  // apply_date
  @Column({
    nullable: true,
    type: 'datetime',
  })
  applyDate: Date

  // val_start
  @Column({
    nullable: true,
    type: 'datetime',
  })
  valStart: Date

  // valEnd
  @Column({
    nullable: true,
    type: 'datetime',
  })
  valEnd: Date

  // submit_date
  @Column({
    nullable: true,
    type: 'datetime',
  })
  submitDate: Date
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string

  @ManyToOne(() => PurchasingOrgEntity, (p) => p.rfqs)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string

  @ManyToOne(() => PurchasingGroupEntity, (p) => p.rfqs)
  @JoinColumn({ name: 'purchasingGroupId', referencedColumnName: 'id' })
  purchasingGroup: Promise<PurchasingGroupEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @ManyToOne(() => PlantEntity, (p) => p.rfqs)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /** quodate */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  quoteDate: Date

  /* shipment conditionTypeid compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactId: string[]

  /* shipment conditionType code compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactCode: string[]

  /* shipment conditionType price compact */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  shipmentFeeConditionTypeCompactValue: string[]

  @Column({
    nullable: true,
    default: false,
  })
  isChoose: boolean

  @Column({
    nullable: true,
    default: false,
  })
  isWiner: boolean

  // điểm hệ thống
  @Column({
    type: 'float',
    nullable: true,
  })
  scoreSystem: number

  // điểm nhà cung cấp
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  // ranking
  @Column({
    type: 'float',
    nullable: true,
  })
  ranking: number

  // ranking
  @Column({
    type: 'float',
    nullable: true,
  })
  rank: number

  // tổng tiền
  @Column({
    type: 'float',
    nullable: true,
  })
  totalPrice: number

  /** exchange */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  exchange: number

  /** ngày lấy thông tin */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  dateExchange: Date
}
