import { <PERSON>ti<PERSON>, Column, <PERSON>To<PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierServiceEntity } from './supplierService.entity'

/** <PERSON><PERSON><PERSON><PERSON> chứng nhận iso */
@Entity('supplier_certificate')
export class SupplierCertificateEntity extends BaseEntity {
  /**  Tên chứng nhận */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** file chứng nhận */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  fileAttachment: string

  /** <PERSON>ĩnh vực kinh doanh NCC  */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierServiceId: string
  @ManyToOne(() => SupplierServiceEntity, (p) => p.supplierCertificates)
  @JoinColumn({ name: 'supplierServiceId', referencedColumnName: 'id' })
  supplierService: Promise<SupplierServiceEntity>
}
