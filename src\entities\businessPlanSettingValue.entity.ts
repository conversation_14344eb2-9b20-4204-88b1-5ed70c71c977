import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { SettingStringEntity } from './settingString.entity'
import { BusinessPlanEntity } from './businessPlan.entity'
import { BusinessPlanTemplateColEntity } from './businessPlanTemplateCol.entity'

@Entity('business_plan_setting_value')
export class BusinessPlanSettingValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  settingStringId: string
  @ManyToOne(() => SettingStringEntity, (p) => p.businessPlanSettingValue)
  @JoinColumn({ name: 'settingStringId', referencedColumnName: 'id' })
  settingString: Promise<SettingStringEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanId: string
  @ManyToOne(() => BusinessPlanEntity, (p) => p.businessPlanSettingValue)
  @JoinColumn({ name: 'businessPlanId', referencedColumnName: 'id' })
  businessPlan: Promise<BusinessPlanEntity>

  /** value */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  value: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  settingCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanTemplateColId: string
  @ManyToOne(() => BusinessPlanTemplateColEntity, (p) => p.businessPlanSettingValue)
  @JoinColumn({ name: 'businessPlanTemplateColId', referencedColumnName: 'id' })
  businessPlanTemplateCol: Promise<BusinessPlanTemplateColEntity>
}
