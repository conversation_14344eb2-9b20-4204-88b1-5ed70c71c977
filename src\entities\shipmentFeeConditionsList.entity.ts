import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ShipmentFeeConditionsToListEntity } from './shipmentFeeConditionsToList.entity'
import { RequestQuoteFeeEntity } from './requestQuoteFee.entity'

@Entity('shipment_fee_conditions_list')
export class ShipmentFeeConditionsListEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  description: string
  /* relation đến shipmentFeeConditions */

  @OneToMany(() => ShipmentFeeConditionsToListEntity, (p) => p.shipmentFeeConditionsList)
  shipmentFeeConditionsToList: Promise<ShipmentFeeConditionsToListEntity[]>

  @OneToMany(() => RequestQuoteFeeEntity, (p) => p.shipmentFeeConditionsList)
  requestQuoteFees: Promise<RequestQuoteFeeEntity[]>
}
