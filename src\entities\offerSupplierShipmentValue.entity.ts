import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { OfferSupplierEntity } from './offerSupplier.entity'
import { OfferTechEntity } from './offerTech.entity'
import { ShipmentCostPriceEntity } from './shipmentCostPrice.entity'

@Entity('offer_supplier_shipment_value')
export class OfferSupplierShipmentValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  offerSupplierId: string
  @ManyToOne(() => OfferSupplierEntity, (p) => p.offerSupplierShipmentTechValue)
  @JoinColumn({ name: 'offerSupplierId', referencedColumnName: 'id' })
  offerSupplier: Promise<OfferSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPriceId: string
  @ManyToOne(() => ShipmentCostPriceEntity, (p) => p.offerSupplierShipmentTechValue)
  @JoinColumn({ name: 'shipmentPriceId', referencedColumnName: 'id' })
  shipmentPrice: Promise<ShipmentCostPriceEntity>
}
