import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { ServiceTradeEntity } from './serviceTrade.entity'

@Entity('service_trade_list_detail')
export class ServiceTradeListDetailEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  serviceTradeId: string
  @ManyToOne(() => ServiceTradeEntity, (p) => p.serviceTradeListDetails)
  @JoinColumn({ name: 'serviceTradeId', referencedColumnName: 'id' })
  serviceTrade: Promise<ServiceTradeEntity>
}
