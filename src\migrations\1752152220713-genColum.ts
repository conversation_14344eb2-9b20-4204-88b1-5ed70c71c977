import { MigrationInterface, QueryRunner } from 'typeorm'

export class GenColum1752152220713 implements MigrationInterface {
  name = 'GenColum1752152220713'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "itemNo" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "tracking" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "remind1" int`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "remind2" int`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "remind3" int`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "remindNo" int`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "priceUnit" int`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "unitId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "quotComment" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "infoUpdate" nvarchar(10)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "tax" nvarchar(500)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "itemText" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "materialPoText" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "additionalText" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "stockInformationText" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "code" nvarchar(250)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "requestQuoteId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "collNo" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "currencyId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "incotermId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "paymentTermId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "yourRef" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "salesPer" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "ourRef" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "telephone" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "headerText" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "headerNote" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "pricingTypes" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "deadlines" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "termsDelivery" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "shippingInstructions" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "termsPayment" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "incoLoc1" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "incoLoc2" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "incoVer" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "targVal" nvarchar(1000)`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "warrantyDate" datetime`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "bindgDate" datetime`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "applyDate" datetime`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "valStart" datetime`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "valEnd" datetime`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "submitDate" datetime`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "plantId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "quoteDate" datetime`)
    await queryRunner.query(`ALTER TABLE "request_quote" ADD "plantId" uniqueidentifier`)

    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "netPrice"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "netPrice" decimal(20,3) CONSTRAINT "DF_0ecda2359612578c3ecd070a585" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "companyId"`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "companyId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "purchasingOrgId"`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "purchasingOrgId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "purchasingGroupId"`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "purchasingGroupId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "rfq_details" ADD CONSTRAINT "FK_ac72c79d9497b30e90c7cb54018" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "rfq" ADD CONSTRAINT "FK_620877d63a936fa2f81bc2105d3" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "rfq" ADD CONSTRAINT "FK_dad59ff4a053738591d4e0eeb2c" FOREIGN KEY ("requestQuoteId") REFERENCES "request_quote"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "rfq" ADD CONSTRAINT "FK_9e9aced7261fdb819663a673d27" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "rfq" ADD CONSTRAINT "FK_003b5de5b6dbd8352769efd1d99" FOREIGN KEY ("incotermId") REFERENCES "incoterm"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "rfq" ADD CONSTRAINT "FK_ee908966be77b369db1887fe7aa" FOREIGN KEY ("paymentTermId") REFERENCES "payment_term"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "rfq" ADD CONSTRAINT "FK_c7465ed965a2f6a979f558a9bdf" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "rfq" ADD CONSTRAINT "FK_0d63bf89826f7f15159624c3384" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "rfq" ADD CONSTRAINT "FK_91a54c140fc93098d6e0816bfd6" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote" ADD CONSTRAINT "FK_2baeb00d5973929115a15b8770b" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "request_quote" DROP CONSTRAINT "FK_2baeb00d5973929115a15b8770b"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "FK_91a54c140fc93098d6e0816bfd6"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "FK_0d63bf89826f7f15159624c3384"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "FK_c7465ed965a2f6a979f558a9bdf"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "FK_ee908966be77b369db1887fe7aa"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "FK_003b5de5b6dbd8352769efd1d99"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "FK_9e9aced7261fdb819663a673d27"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "FK_dad59ff4a053738591d4e0eeb2c"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP CONSTRAINT "FK_620877d63a936fa2f81bc2105d3"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP CONSTRAINT "FK_ac72c79d9497b30e90c7cb54018"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "purchasingGroupId"`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "purchasingGroupId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "purchasingOrgId"`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "purchasingOrgId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "companyId"`)
    await queryRunner.query(`ALTER TABLE "rfq" ADD "companyId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP CONSTRAINT "DF_0ecda2359612578c3ecd070a585"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "netPrice"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" ADD "netPrice" bigint`)

    await queryRunner.query(`ALTER TABLE "request_quote" DROP COLUMN "plantId"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "quoteDate"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "plantId"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "submitDate"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "valEnd"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "valStart"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "applyDate"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "bindgDate"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "warrantyDate"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "targVal"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "incoVer"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "incoLoc2"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "incoLoc1"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "termsPayment"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "shippingInstructions"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "termsDelivery"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "deadlines"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "pricingTypes"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "headerNote"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "headerText"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "telephone"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "ourRef"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "salesPer"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "yourRef"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "paymentTermId"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "incotermId"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "currencyId"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "collNo"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "requestQuoteId"`)
    await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "code"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "stockInformationText"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "additionalText"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "materialPoText"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "itemText"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "tax"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "infoUpdate"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "quotComment"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "unitId"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "priceUnit"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "remindNo"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "remind3"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "remind2"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "remind1"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "tracking"`)
    await queryRunner.query(`ALTER TABLE "rfq_details" DROP COLUMN "itemNo"`)
  }
}
