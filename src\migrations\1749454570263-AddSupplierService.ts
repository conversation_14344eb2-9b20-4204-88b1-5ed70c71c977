import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSupplierService1749454570263 implements MigrationInterface {
    name = 'AddSupplierService1749454570263'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD "supplierCompanyId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "supplier_service" ADD CONSTRAINT "FK_a29f74d275c2ac21e20e74d6cc3" FOREIGN KEY ("supplierCompanyId") REFERENCES "supplier_company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP CONSTRAINT "FK_a29f74d275c2ac21e20e74d6cc3"`);
        await queryRunner.query(`ALTER TABLE "supplier_service" DROP COLUMN "supplierCompanyId"`);
    }

}
