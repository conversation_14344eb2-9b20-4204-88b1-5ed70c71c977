import { BaseEntity } from './base.entity'
import { Entity, Column, OneToOne, JoinColumn, ManyToOne } from 'typeorm'
import { SupplierEntity } from './supplier.entity'
import { BusinessPartnerGroupEntity, SupplierNumberRequestApproveEntity } from '.'

/** Thông tin mã số SAP NCC */
@Entity('supplier_number')
export class SupplierNumberEntity extends BaseEntity {
  /**Trạng thái đồng bộ */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  syncStatus: string

  /**Trạng thái duyệt */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  approvalStatus: string

  /** Mã số doanh nghiệp */
  @Column({
    type: 'int',
    nullable: true,
  })
  code: string

  /** Loại hình doanh nghiệp*/
  @Column({
    type: 'varchar',
    length: 30,
    nullable: true,
  })
  businessPartnerGroupType: string

  /** <PERSON><PERSON><PERSON> liên hệ với NCC */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string

  @OneToOne(() => SupplierEntity, (p) => p.supplierNumber)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Mối liên hệ với thông tin yêu cầu duyệt */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierNumberRequestApproveId: string

  @OneToOne(() => SupplierNumberRequestApproveEntity, (p) => p.supplierNumber)
  @JoinColumn({ name: 'supplierNumberRequestApproveId', referencedColumnName: 'id' })
  supplierNumberRequestApprove: Promise<SupplierNumberRequestApproveEntity>

  /** Mối liên hệ với đối tác kinh doanh: để tạo mã sap */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPartnerGroupId: string

  @ManyToOne(() => BusinessPartnerGroupEntity, (p) => p.supplierNumbers)
  @JoinColumn({ name: 'businessPartnerGroupId', referencedColumnName: 'id' })
  businessPartnerGroup: Promise<BusinessPartnerGroupEntity>

  /**Biến kiểm tra đã đủ dữ liệu cho 3 role chưa, nếu đủ => được phép gửi duyệt */
  @Column({
    nullable: true,
    default: false,
  })
  isRequestApprove: boolean
}
