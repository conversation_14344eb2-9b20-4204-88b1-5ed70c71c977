import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { ServiceEntity } from './service.entity'
import { SupplierEntity } from './supplier.entity'
import { EvaluationHistoryPurchaseDetailEntity } from './evaluationHistoryPurchasedetail.entity'
import { SupplierUpgradeDetailEntity } from '.'
import { EvaluationHistoryPurchaseEmployeeEntity } from './evaluationHistoryPurchaseEmployee.entity'

/** Phiếu đánh giá lịch sử mua hàng */
@Entity('evaluation_history_purchase')
export class EvaluationHistoryPurchaseEntity extends BaseEntity {
  /** Mã */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** <PERSON><PERSON><PERSON> đ<PERSON> giá */
  @Column({
    type: 'datetime',
    nullable: false,
  })
  evaluationDate: Date

  /**  Lý do điều chỉnh */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  reasonUpdate: string

  /** Lĩnh vực mua hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId?: string
  @ManyToOne(() => ServiceEntity, (p) => p.evaluationHistoryPurchases)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  /** Nhà cung cấp */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.evaluationHistoryPurchases)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Kì bắt đầu đánh giá */
  @Column({
    type: 'datetime',
    nullable: false,
  })
  evaluationStartMonth: Date

  /** Kì kết thúc đánh giá */
  @Column({
    type: 'datetime',
    nullable: false,
  })
  evaluationEndMonth: Date

  /** Ghi chú */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  comment: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  suggestion: string

  /** Tổng điểm */
  @Column({
    type: 'float',
    nullable: true,
    default: 0,
  })
  totalScore: number

  /** Xếp loại */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  rating: string

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => EvaluationHistoryPurchaseDetailEntity, (p) => p.evaluationHistoryPurchase)
  evaluationHistoryPurchaseDetails: Promise<EvaluationHistoryPurchaseDetailEntity[]>

  /** DS chi tiết */
  @OneToMany(() => SupplierUpgradeDetailEntity, (p) => p.evaluationHistoryPurchase)
  supplierUpgradeDetails: Promise<SupplierUpgradeDetailEntity[]>

  /** DS nhân viên */
  @OneToMany(() => EvaluationHistoryPurchaseEmployeeEntity, (p) => p.evaluationPurchase)
  evaluationPurchaseEmployees: Promise<EvaluationHistoryPurchaseEmployeeEntity[]>
}
