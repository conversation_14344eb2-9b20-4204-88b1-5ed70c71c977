import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne, OneToMany } from 'typeorm'
import { MaterialEntity } from './material.entity'
import { UomEntity } from './uom.entity'
import { BaseEntity } from './base.entity'
import { ReservationEntity } from './reservation.entity'
import { ReservationItemChildEntity } from './reservationItemChild.entity'
@Entity('reservation_item')
export class ReservationItemEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  reservationId: string
  @ManyToOne(() => ReservationEntity, (p) => p.reservationItems)
  @JoinColumn({ name: 'reservationId', referencedColumnName: 'id' })
  reservation: Promise<ReservationEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.reservationItems)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** tên hàng */
  @Column({
    type: 'varchar',
    length: 2000,
    nullable: true,
  })
  shortText: string

  /** số lượng */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  uomCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  uomId: string
  @ManyToOne(() => UomEntity, (p) => p.reservationItems)
  @JoinColumn({ name: 'uomId', referencedColumnName: 'id' })
  uom: Promise<UomEntity>

  /** số lượng quy đổi */
  @Column({ nullable: true, default: 0, type: 'decimal', scale: 4, precision: 20 })
  quantityAlternative: number

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  uomAlternativeCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  uomAlternativeId: string
  @ManyToOne(() => UomEntity, (p) => p.alternativeReservationItems)
  @JoinColumn({ name: 'uomAlternativeId', referencedColumnName: 'id' })
  uomAlternative: Promise<UomEntity>

  /** số lượng thực nhận */
  @Column({ nullable: true, default: 0, type: 'decimal', scale: 4, precision: 20 })
  actualQuantity: number

  /** Batch */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  batch: string

  @Column({
    nullable: true,
    type: 'datetime',
  })
  expiryDate: Date

  @Column({
    nullable: true,
    type: 'datetime',
  })
  time: Date

  /** Tài liệu kèm theo  */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  referenceNo: string

  /** Ghi chú */
  @Column({
    length: 'max',
    nullable: true,
  })
  description: string

  /** asset */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetCode: string

  /** asset Desc */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetDesc: string

  /** io */
  @Column({
    length: 'max',
    nullable: true,
  })
  io: string

  /** io Name */
  @Column({
    length: 'max',
    nullable: true,
  })
  ioName: string

  /** iotype */
  @Column({
    length: 'max',
    nullable: true,
  })
  iotype: string

  /** io Name */
  @Column({
    length: 'max',
    nullable: true,
  })
  relstatus: string

  /** số lượng */
  @Column({ nullable: true, default: 0, type: 'decimal', scale: 4, precision: 20 })
  norm: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  reservationParentId: string

  @Column({
    length: 'max',
    nullable: true,
  })
  budgetPeriod: string

  @Column({
    length: 'max',
    nullable: true,
  })
  fp: string

  @Column({
    length: 'max',
    nullable: true,
  })
  fc: string

  @Column({
    length: 'max',
    nullable: true,
  })
  ci: string

  /** Ngân sách */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 4, default: 0 })
  budget: number

  /** fundCenter */
  @Column({ type: 'varchar', length: 1000, nullable: true })
  fundCenter: string

  /** fund program */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  fundProgram: string

  @OneToMany(() => ReservationItemChildEntity, (p) => p.reservationItem)
  reservationItemChilds: Promise<ReservationItemChildEntity[]>

  /** Item Line */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  itemNo: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  warehouseIssueSloc: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  reservationUomNormId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  baseUnitId: string
  @ManyToOne(() => UomEntity, (p) => p.reservationItemBaseUnit)
  @JoinColumn({ name: 'baseUnitId', referencedColumnName: 'id' })
  baseUnit: Promise<UomEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  reservationUomNormDetailId: string

  /** item_delete */
  @Column({
    type: 'varchar',
    nullable: true,
    length: 1,
  })
  itemDelete: string

  /** item_complete */
  @Column({
    type: 'varchar',
    nullable: true,
    length: 1,
  })
  itemComplete: string
}
