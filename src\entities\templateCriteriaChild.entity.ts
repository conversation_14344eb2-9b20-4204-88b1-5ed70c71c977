import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { TemplateCriterialEntity } from './templateCriteria.entity'

@Entity('template_criterial_child')
export class TemplateCriterialChildEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  templateCriteriaId: string
  @ManyToOne(() => TemplateCriterialEntity, (p) => p.templateCriterialChilds)
  @JoinColumn({ name: 'templateCriteriaId', referencedColumnName: 'id' })
  templateCriterial: Promise<TemplateCriterialEntity>
}
