import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierNumberRequestApproveEntity } from '.'

/** cá nhân/ tổ chức
 */
@Entity('title')
export class TitleEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.title)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>
}
