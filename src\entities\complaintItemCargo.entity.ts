import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ComplaintEntity } from './complaint.entity'
import { DepartmentEntity } from './department.entity'
import { EmployeeEntity } from './employee.entity'

/** <PERSON>hiếu nại - xử lý hàng hóa */
@Entity('complaint_item_cargo')
export class ComplaintItemCargoEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  complaintId: string
  @ManyToOne(() => ComplaintEntity, (p) => p.complaintItemCargos)
  @JoinColumn({ name: 'complaintId', referencedColumnName: 'id' })
  complaint: Promise<ComplaintEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  itemCode: string

  /**Short text */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  shortText: string

  /**Đơ<PERSON> vị tính*/
  @Column({ type: 'varchar', length: 30, nullable: true })
  unitName: string

  /** Số lượng*/
  @Column({
    nullable: true,
  })
  quantity: number

  /** Số lượng lỗi(Int)*/
  @Column({
    nullable: true,
  })
  quantityErrorInt: number

  /** Số lượng lỗi(Ext)*/
  @Column({
    nullable: true,
  })
  quantityErrorExt: number

  /** Mức độ thay đổi %*/
  @Column({
    nullable: true,
  })
  degreeChange: number

  /** Số lượng trả lại*/
  @Column({
    nullable: true,
  })
  numberReturns: number

  /**Mức độ ưu tiên enumData */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  priorityLevel: string

  /**Mã hàng NCC */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  supplierProductCode: string

  /**Số lô */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  lotNumber: string

  /**Số lô NCC */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  supplierLotNumber: string

  /**Số seri */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  serialNumber: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  responseDepartmentId: string
  @ManyToOne(() => DepartmentEntity, (p) => p.complaintItemCargos)
  @JoinColumn({ name: 'responseDepartmentId', referencedColumnName: 'id' })
  department: Promise<DepartmentEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  receptEmployeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.complaintItemCargos)
  @JoinColumn({ name: 'receptEmployeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>
}
