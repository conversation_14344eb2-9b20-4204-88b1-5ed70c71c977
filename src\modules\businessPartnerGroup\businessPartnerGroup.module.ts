import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { BusinessPartnerGroupController } from './businessPartnerGroup.controller'
import { BusinessPartnerGroupService } from './businessPartnerGroup.service'
import { BusinessPartnerGroupRepository } from '../../repositories'
import { OrganizationalPositionModule } from '../organizationalPosition/organizationalPosition.module'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([BusinessPartnerGroupRepository]), OrganizationalPositionModule],
  controllers: [BusinessPartnerGroupController],
  providers: [BusinessPartnerGroupService],
})
export class BusinessPartnerGroupModule {}
