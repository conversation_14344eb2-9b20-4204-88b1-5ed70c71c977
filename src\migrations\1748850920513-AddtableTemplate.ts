import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddtableTemplate1748850920513 implements MigrationInterface {
  name = 'AddtableTemplate1748850920513'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "template_criterial_child" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_0350f3e247f54f0aee02f6a5810" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a7f33ec2c389acad3d3507b1982" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(250), "templateCriteriaId" uniqueidentifier, CONSTRAINT "PK_0350f3e247f54f0aee02f6a5810" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "template_criterial" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5871fb503d578a7706ecb403924" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_25cc9d4dbda11584e7964f3dc2f" DEFAULT 0, "companyId" varchar(255), "name" nvarchar(250), "maxScore" float, "failingGrade" float, "percent" float, "criteriaType" nvarchar(100), "templateEvaluationPotentialId" uniqueidentifier, CONSTRAINT "PK_5871fb503d578a7706ecb403924" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "template_evaluation_potential" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_5e12a82465433950d38edc23d3c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_526df80cfd9d261fac1b36c0a55" DEFAULT 0, "companyId" varchar(255), "employeeLawId" uniqueidentifier, "employeeCapacityId" uniqueidentifier, "passingScore" float, CONSTRAINT "PK_5e12a82465433950d38edc23d3c" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "template_criterial_child" ADD CONSTRAINT "FK_2af468d15dbd62c9303c51da232" FOREIGN KEY ("templateCriteriaId") REFERENCES "template_criterial"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "template_criterial" ADD CONSTRAINT "FK_92af6141f1a62a97405f68d345b" FOREIGN KEY ("templateEvaluationPotentialId") REFERENCES "template_evaluation_potential"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "template_evaluation_potential" ADD CONSTRAINT "FK_744ad1cd43844091866f00facf8" FOREIGN KEY ("employeeLawId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "template_evaluation_potential" ADD CONSTRAINT "FK_7ac49e09431de3f4c43e659d99e" FOREIGN KEY ("employeeCapacityId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "template_evaluation_potential" DROP CONSTRAINT "FK_7ac49e09431de3f4c43e659d99e"`)
    await queryRunner.query(`ALTER TABLE "template_evaluation_potential" DROP CONSTRAINT "FK_744ad1cd43844091866f00facf8"`)
    await queryRunner.query(`ALTER TABLE "template_criterial" DROP CONSTRAINT "FK_92af6141f1a62a97405f68d345b"`)
    await queryRunner.query(`ALTER TABLE "template_criterial_child" DROP CONSTRAINT "FK_2af468d15dbd62c9303c51da232"`)
    await queryRunner.query(`DROP TABLE "template_evaluation_potential"`)
    await queryRunner.query(`DROP TABLE "template_criterial"`)
    await queryRunner.query(`DROP TABLE "template_criterial_child"`)
  }
}
