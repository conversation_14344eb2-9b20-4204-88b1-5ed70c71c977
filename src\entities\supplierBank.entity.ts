import { Entity, Column, <PERSON>To<PERSON><PERSON>, Join<PERSON><PERSON>umn } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BankBranchEntity } from './bankBranch.entity'
import { SupplierEntity } from './supplier.entity'
import { BankEntity } from './bank.entity'
import { CountryEntity } from './country.entity'
import { RegionEntity } from './region.entity'

/** Tài khoản ngân hàng NCC */
@Entity('supplier_bank')
export class SupplierBankEntity extends BaseEntity {
  /** Số tài khoản ngân hàng */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  bankNumber: string

  /** Nhậ<PERSON> bổ sung STK */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  accountNumber: string

  /** Chủ thẻ */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  bankUsername: string

  /** Swift Code */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  swiftCode: string

  /** IBAN */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  iban: string

  /** File thông báo mở tài khoản/mẫu 08 -->URL */
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  fileAccount: string

  /** Nhà cung cấp  */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.banks)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Chi nhánh/PGD Ngân hàng  */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  bankBranchId: string
  @ManyToOne(() => BankBranchEntity, (p) => p.supplierBanks)
  @JoinColumn({ name: 'bankBranchId', referencedColumnName: 'id' })
  bankBranch: Promise<BankBranchEntity>

  /** Ngân hàng  */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  bankId: string
  @ManyToOne(() => BankEntity, (p) => p.supplierBanks)
  @JoinColumn({ name: 'bankId', referencedColumnName: 'id' })
  bank: Promise<BankEntity>

  /** Tỉnh/Thành phố */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  regionId: string
  @ManyToOne(() => RegionEntity, (e) => e.supplierBanks)
  @JoinColumn({ name: 'regionId', referencedColumnName: 'id' })
  region: Promise<RegionEntity>

  /** Quốc gia */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  countryId: string
  @ManyToOne(() => CountryEntity, (e) => e.supplierBanks)
  @JoinColumn({ name: 'countryId', referencedColumnName: 'id' })
  country: Promise<CountryEntity>
}
