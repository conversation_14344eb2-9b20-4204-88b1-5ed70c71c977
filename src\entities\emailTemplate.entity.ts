import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'

@Entity('email_template')
export class EmailTemplateEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 150,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string
}
