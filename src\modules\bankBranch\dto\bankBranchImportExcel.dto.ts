import { ApiProperty, PartialType } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { BankBranchCreateDto } from './bankBranchCreate.dto'

/** Interface tạo mới một Chi nhánh/Phòng giao dịch Ngân hàng. */
export class BankBranchImportExcelDto {
  @ApiProperty({ description: 'Mã Chi nhánh/Phòng giao dịch Ngân hàng.' })
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty({ description: 'Tên Chi nhánh/Phòng giao dịch Ngân hàng.' })
  @IsString()
  name: string

  @ApiProperty({ description: 'Mã công ty' })
  @IsNotEmpty()
  bankCode: string

  @ApiProperty({ description: 'Mã quốc gia' })
  @IsNotEmpty()
  countryCode: string

  @ApiProperty({ description: 'Id thành phố.' })
  @IsNotEmpty()
  @IsString()
  regionCode: string

  @ApiProperty({ description: 'SWIFT/BIC.' })
  swift: string

  @ApiProperty({ description: 'Số tài khoản ngân hàng.' })
  accountNumber: string

  @ApiProperty({ description: 'Bank branch.' })
  @IsNotEmpty()
  @IsString()
  bankBranch: string

  @ApiProperty({ description: 'Nhóm ngân hàng enumData: BankGroupType.' })
  @IsNotEmpty()
  @IsString()
  bankGroupType: string

  @ApiProperty({ description: 'Postbank acct' })
  @IsOptional()
  postbank: boolean

  @ApiProperty({ description: 'Địa chỉ.' })
  address?: string
}
