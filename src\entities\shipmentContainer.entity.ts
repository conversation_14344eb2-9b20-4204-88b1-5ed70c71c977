import { BaseEntity } from './base.entity'
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm'
import { ShipmentEntity } from '.'

/** DS Container */
@Entity('shipment_container')
export class ShipmentContainerEntity extends BaseEntity {
  /** Shipment */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentId: string
  @ManyToOne(() => ShipmentEntity, (p) => p.shipmentContainers)
  @JoinColumn({ name: 'shipmentId', referencedColumnName: 'id' })
  shipment: Promise<ShipmentEntity>

  /** Số container number */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  containerNumber: string

  /** Số seal number hãng tàu */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  sealNumber: string

  /** Số seal number */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  shipSealNumber: string

  /** Loại container */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  containerType: string

  /** <PERSON><PERSON> lượng gói (kiện hàng) */
  @Column({
    nullable: true,
    type: 'int',
  })
  packageQuantity: number
}
