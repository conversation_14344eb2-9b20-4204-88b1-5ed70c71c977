import { AfterLoad, Column, Entity, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PermissionToGroupEntity } from './permissionToGroup.entity'

@Entity('permission_view')
export class PermissionViewEntity extends BaseEntity {
  /** Tên ngân hàng */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** Mã ngân hàng */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Mã ngân hàng */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  path: string

  @Column({
    nullable: false,
    default: false,
  })
  view: boolean

  @Column({
    nullable: false,
    default: false,
  })
  delete: boolean

  @Column({
    nullable: false,
    default: false,
  })
  edit: boolean

  @Column({
    nullable: false,
    default: false,
  })
  add: boolean

  @Column({
    nullable: false,
    default: false,
  })
  watchAnother: boolean

  @Column({
    nullable: false,
    default: false,
  })
  editAnother: boolean

  @OneToMany(() => PermissionToGroupEntity, (p) => p.permissionView)
  permissionToGroups: Promise<PermissionToGroupEntity[]>

  permissionGroupIds: any[]

  @AfterLoad()
  async getRelationData() {
    const resultData: any = this
    if (resultData.__permissionToGroups__) {
      resultData.permissionGroupIds = resultData.__permissionToGroups__.map((item: PermissionToGroupEntity) => item.groupPermissionId)
      delete resultData.__permissionToGroups__
    }
  }
}
