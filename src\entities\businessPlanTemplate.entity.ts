import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { EmployeeEntity } from './employee.entity'
import { BusinessPlanTemplateColEntity } from './businessPlanTemplateCol.entity'
import { BusinessPlanHistoryEntity } from './businessPlanHistory.entity'
import { BusinessPlanEntity } from './businessPlan.entity'
import { BusinessPlanColValueEntity } from './businessPlanColValue.entity'

@Entity('business_plan_template')
export class BusinessPlanTemplateEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: false,
  })
  name: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /** Layout làm tròn cont (RoundUpContTemplateType) */
  @Column({
    length: 20,
    nullable: true,
  })
  type: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        return JSON.stringify(value)
      },
      from(value) {
        return value ? JSON.parse(value) : {}
      },
    },
  })
  shipmentPlanNumberConfigTable: any

  /** nv tạo */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.businessPlanTemplates)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @OneToMany(() => BusinessPlanTemplateColEntity, (p) => p.businessPlanTemplate)
  businessPlanTemplateCols: Promise<BusinessPlanTemplateColEntity[]>

  @OneToMany(() => BusinessPlanHistoryEntity, (p) => p.businessPlanTemplate)
  histories: Promise<BusinessPlanHistoryEntity[]>

  @OneToMany(() => BusinessPlanEntity, (p) => p.businessPlanTemplate)
  businessPlans: Promise<BusinessPlanEntity[]>

  @OneToMany(() => BusinessPlanColValueEntity, (p) => p.businessPlanTemplate)
  businessPlanColValues: Promise<BusinessPlanColValueEntity[]>
}
