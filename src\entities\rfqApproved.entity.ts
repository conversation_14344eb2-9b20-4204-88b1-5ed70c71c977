import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

@Entity('rfq_approved')
export class RfqApprovedEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  lstSupplierId: string[]

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  lstRfqId: string[]

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  requestQuoteId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string

  @Column({
    type: 'nvarchar',
    length: 20,
    nullable: true,
  })
  status: string
}
