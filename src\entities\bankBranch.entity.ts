import { Entity, Column, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BankEntity } from './bank.entity'
import { CountryEntity } from './country.entity'
import { SupplierBankEntity } from './supplierBank.entity'
import { RegionEntity } from './region.entity'

/** Chi nhánh/Phòng giao dịch Ngân hàng */
@Entity('bank_branch')
export class BankBranchEntity extends BaseEntity {
  /** Tên Chi nhánh/Phòng giao dịch Ngân hàng  */
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: true,
  })
  name: string

  /** Mã <PERSON> nhánh/Phòng giao dịch Ngân hàng */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Quốc gia */
  @Column({
    type: 'varchar',
    nullable: false,
  })
  countryId: string
  @ManyToOne(() => CountryEntity, (e) => e.bankBranchs)
  @JoinColumn({ name: 'countryId', referencedColumnName: 'id' })
  country: Promise<CountryEntity>

  /** Tỉnh/Thành phố */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  regionId: string
  @ManyToOne(() => RegionEntity, (e) => e.bankBranchs)
  @JoinColumn({ name: 'regionId', referencedColumnName: 'id' })
  region: Promise<RegionEntity>

  /** Địa chỉ */
  @Column({
    type: 'nvarchar',
    length: 4000,
    nullable: true,
  })
  address: string

  /** Mã vùng */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  areaCode: string

  /** Bank branch */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  bankBranch: string

  /** SWIFT/BIC */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  swift: string

  /** Nhóm ngân hàng enumData: BankGroupType */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  bankGroupType: string

  /** Số tài khoản ngân hàng */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  accountNumber: string

  /** Postbank acct */
  @Column({
    nullable: true,
    default: false,
  })
  postbank: boolean

  /** Mô tả */
  @Column({
    type: 'nvarchar',
    length: 4000,
    nullable: true,
  })
  description: string

  // Ngân hàng
  @Column({
    type: 'varchar',
    nullable: false,
  })
  bankId: string
  @ManyToOne(() => BankEntity, (e) => e.bankBranchs)
  @JoinColumn({ name: 'bankId', referencedColumnName: 'id' })
  bank: Promise<BankEntity>

  // Tài khoản NH NCC
  @OneToMany(() => SupplierBankEntity, (e) => e.bankBranch)
  supplierBanks: Promise<SupplierBankEntity[]>
}
