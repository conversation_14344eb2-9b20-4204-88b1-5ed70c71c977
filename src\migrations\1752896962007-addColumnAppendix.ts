import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnAppendix1752896962007 implements MigrationInterface {
    name = 'AddColumnAppendix1752896962007'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "totalPrice"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "totalPrice" float`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "totalPriceVND"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "totalPriceVND" float`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "totalPriceAfterTax"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "totalPriceAfterTax" float`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "totalPriceAfterTax"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "totalPriceAfterTax" bigint`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "totalPriceVND"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "totalPriceVND" bigint`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "totalPrice"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "totalPrice" bigint`);
    }

}
