import { <PERSON><PERSON><PERSON>, Column, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { PlantEntity } from './plant.entity'
import { RoleSupplierEntity } from './roleSupplier.entity'
import { RoleFiSupplierEntity } from './roleFiSupplier.entity'

@Entity('supplier_plant')
export class SupplierPlantEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.supplierPlants)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.supplierPlants)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @OneToMany(() => RoleSupplierEntity, (p) => p.supplierPlant)
  roleSuppliers: Promise<RoleSupplierEntity[]>

  @OneToMany(() => RoleFiSupplierEntity, (p) => p.supplierPlant)
  roleFiSuppliers: Promise<RoleFiSupplierEntity[]>
}
