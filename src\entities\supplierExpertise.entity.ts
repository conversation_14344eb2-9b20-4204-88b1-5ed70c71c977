import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyToOne, Join<PERSON><PERSON>um<PERSON>, OneToMany } from 'typeorm'
import { ServiceEntity } from './service.entity'
import { SupplierEntity } from './supplier.entity'
import { SupplierExpertiseDetailEntity } from './supplierExpertiseDetail.entity'
import { SupplierServiceEntity } from './supplierService.entity'
import { EmployeeEntity } from './employee.entity'
import { SupplierExpertiseLawDetailEntity } from './supplierExpertiseLawDetail.entity'
import { SupplierExpertiseMemberEntity } from './supplierExpertiseMember.entity'

/** Lần thẩm định của Item NCC */
@Entity('supplier_expertise')
export class SupplierExpertiseEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  comment: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  commentCapacity: string

  /** <PERSON>ý do */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  note: string

  /** <PERSON><PERSON><PERSON> thẩm định */
  @Column({
    nullable: false,
    type: 'datetime',
  })
  changeDate: Date

  /** Option có check Law không */
  @Column({
    nullable: false,
    default: false,
  })
  isCheckLaw: boolean

  /** Option có check năng lực không */
  @Column({
    nullable: false,
    default: false,
  })
  isCheckCapacity: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  approvedLawId: string
  // Nhân viên duyệt pháp lý
  @ManyToOne(() => EmployeeEntity, (p) => p.supplierExpertiseLaw)
  @JoinColumn({ name: 'approvedLawId', referencedColumnName: 'id' })
  approvedLaw: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusLaw: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusCapacity: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.supplierExpertise)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.supplierExpertise)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierServiceId: string
  @ManyToOne(() => SupplierServiceEntity, (p) => p.supplierExpertise)
  @JoinColumn({ name: 'supplierServiceId', referencedColumnName: 'id' })
  supplierService: Promise<SupplierServiceEntity>

  @OneToMany(() => SupplierExpertiseDetailEntity, (p) => p.supplierExpertise)
  supplierExpertiseDetails: Promise<SupplierExpertiseDetailEntity[]>

  @OneToMany(() => SupplierExpertiseLawDetailEntity, (p) => p.supplierExpertise)
  supplierExpertiseLawDetails: Promise<SupplierExpertiseLawDetailEntity[]>

  @OneToMany(() => SupplierExpertiseMemberEntity, (p) => p.supplierExpertise)
  members: Promise<SupplierExpertiseMemberEntity[]>
}
