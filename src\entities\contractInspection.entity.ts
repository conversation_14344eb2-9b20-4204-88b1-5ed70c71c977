import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { SupplierEntity } from './supplier.entity'
import { ContractEntity } from './contract.entity'
import { ContractInspectionEmployeeEntity } from './contractInspectionEmployeeEntity.entity'
import { ContractInspectionPaymentProgressEntity } from './contractInspectionPaymentProgress.entity'
import { ContractInspectionItemEntity } from './contractInspectionItem.entity'

/** Nghiệm thu hợp đồng */
@Entity({ name: 'contract_inspection' })
export class ContractInspectionEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 300,
    nullable: true,
  })
  name: string

  /**Tên BBNT tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 300,
    nullable: true,
  })
  nameEN: string

  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  code: string

  /** <PERSON>ợp đồng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.contractInspections)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  /** nhà cung cấp */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.contractInspections)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Địa điểm bàn giao */
  @Column({
    type: 'varchar',
    length: 300,
    nullable: true,
  })
  address: string

  /** Thời gian bàn giao */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  time: Date

  /**Thành phần tham gia nghiệm thu */
  @OneToMany(() => ContractInspectionEmployeeEntity, (p) => p.contractInspection)
  contractInspectionEmps: Promise<ContractInspectionEmployeeEntity[]>

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  createdBy: string

  /** Ghi chú  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  note: string

  /** Đối tượng nghiệm thu  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  object: string

  /** kết quả nghiệm thu */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  resultInspection: string
  /** File đính kèm */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  fileAttach: string

  /** Loại nghiệm thu */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  inspectionType: string

  @Column({ type: 'nvarchar', nullable: true })
  fileUrl: string

  /** Tiến độ thanh toán trong nghiệm thu hợp đồng */
  @OneToMany(() => ContractInspectionPaymentProgressEntity, (p) => p.contractInspection)
  paymentPlans: Promise<ContractInspectionPaymentProgressEntity[]>

  /** Item trong nghiệm thu hợp đồng */
  @OneToMany(() => ContractInspectionItemEntity, (p) => p.contractInspection)
  contractInspectionItems: Promise<ContractInspectionItemEntity[]>
}
