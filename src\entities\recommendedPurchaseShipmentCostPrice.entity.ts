import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { SupplierEntity } from './supplier.entity'

/** Đề nghị mua hàng shipment cost */
@Entity('recommended_purchase_shipment_cost_price')
export class RecommendedPurchaseShipmentCostPriceEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  conditionType: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @Column({
    nullable: true,
  })
  amount: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  crcy: string

  @Column({
    nullable: true,
  })
  per: number

  @Column({
    nullable: true,
  })
  conditionValue: number

  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  curr: string

  @Column({
    nullable: true,
  })
  cConDe: number

  @Column({
    nullable: true,
  })
  numCCo: number

  /**Nguồn tham chiếu */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  referenceSource: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseId: string
  @ManyToOne(() => RecommendedPurchaseEntity, (p) => p.recommendedPurchaseShipmentCostPrices)
  @JoinColumn({ name: 'recommendedPurchaseId', referencedColumnName: 'id' })
  recommendedPurchase: Promise<RecommendedPurchaseEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.recommendedPurchaseShipmentCostPrices)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Chi phí dự kiến */
  @Column({
    nullable: true,
    type: 'bigint',
  })
  price: number

  /** Mối liên hệ với shipment cost stage cost*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseShipmentStageId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentCostId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPriceId: string
}
