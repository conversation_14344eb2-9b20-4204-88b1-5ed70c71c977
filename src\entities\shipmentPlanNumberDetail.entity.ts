import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ShipmentPlanNumberEntity } from './shipmentPlanNumber.entity'

@Entity('shipment_plan_number_detail')
export class ShipmentPlanNumberDetailEntity extends BaseEntity {
  /* id của shipment plan number */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentPlanNumberId: string

  @ManyToOne(() => ShipmentPlanNumberEntity, (p) => p.shipmentPlanNumberDetails)
  @JoinColumn({ name: 'shipmentPlanNumberId', referencedColumnName: 'id' })
  shipmentPlanNumber: Promise<ShipmentPlanNumberEntity>

  // Id của shipmentFeeCondictions
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentFeeConditionId: string

  // Id của shipmentFeeCondictions
  @Column({
    type: 'varchar',
    nullable: true,
  })
  value: string

  // Id của shipmentFeeCondictions
  @Column({
    type: 'varchar',
    nullable: true,
  })
  idValue: string
}
