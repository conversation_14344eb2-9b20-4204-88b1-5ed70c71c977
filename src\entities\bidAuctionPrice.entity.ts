import { Entity, Column, <PERSON>T<PERSON><PERSON>ne, Join<PERSON>olumn } from 'typeorm'
import { BidAuctionEntity } from './bidAuction.entity'
import { BidPriceEntity } from './bidPrice.entity'
import { BaseEntity } from './base.entity'

@Entity('bid_auction_price')
export class BidAuctionPriceEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  maxPrice: number

  /* Tên */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  name: string

  /* Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unit: string

  /* <PERSON>ố lượng */
  @Column({
    type: 'int',
    nullable: true,
  })
  number: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidAuctionId: string
  @ManyToOne(() => BidAuctionEntity, (p) => p.bidAuctionPrice)
  @JoinColumn({ name: 'bidAuctionId', referencedColumnName: 'id' })
  bidAuction: Promise<BidAuctionEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidAuctionPrices)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>
}
