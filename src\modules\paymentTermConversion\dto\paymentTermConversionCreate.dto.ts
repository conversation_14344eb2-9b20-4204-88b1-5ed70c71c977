import { IsOptional, IsString, IsNumber, IsUUID } from 'class-validator'

export class PaymentTermConversionCreateDto {
  @IsOptional()
  @IsUUID()
  paymentTermId?: string

  @IsOptional()
  @IsString()
  code?: string

  @IsOptional()
  @IsNumber()
  percentBank?: number

  finalDto: FinalDto
}

export class FinalDto {
  @IsOptional()
  @IsNumber()
  dayOfRecommendedPurchase?: number

  @IsOptional()
  @IsNumber()
  percentOfRecommendedPurchase?: number

  @IsOptional()
  @IsString()
  recommendedPurchaseCodeLeadTime?: string

  @IsOptional()
  @IsNumber()
  dayOfContract?: number

  @IsOptional()
  @IsNumber()
  percentOfContract?: number

  @IsOptional()
  @IsString()
  contractCodeLeadTime?: string

  @IsOptional()
  @IsNumber()
  dayOfSupplierPrepare?: number

  @IsOptional()
  @IsNumber()
  percentSupplierPrepare?: number

  @IsOptional()
  @IsString()
  supplierPrepareCodeLeadTime?: string

  @IsOptional()
  @IsNumber()
  dayOfSupplierProduction?: number

  @IsOptional()
  @IsNumber()
  percentSupplierProduction?: number

  @IsOptional()
  @IsString()
  supplierProductionCodeLeadTime?: string

  @IsOptional()
  @IsNumber()
  dayOfSupplierProductionToPort?: number

  @IsOptional()
  @IsNumber()
  percentSupplierProductionToPort?: number

  @IsOptional()
  @IsString()
  supplierProductionToPortCodeLeadTime?: string

  @IsOptional()
  @IsNumber()
  dayOfTransportSupplierToVietNam?: number

  @IsOptional()
  @IsNumber()
  percentTransportSupplierToVietNam?: number

  @IsOptional()
  @IsString()
  transportSupplierToVietNamCodeLeadTime?: string

  @IsOptional()
  @IsNumber()
  dayOfTransportVietNamToWarehouse?: number

  @IsOptional()
  @IsNumber()
  percentTransportVietNamToWarehouse?: number

  @IsOptional()
  @IsString()
  transportVietNamToWarehouseCodeLeadTime?: string

  @IsOptional()
  @IsNumber()
  dayOfQualityCheckAndReceiving?: number

  @IsOptional()
  @IsNumber()
  percentQualityCheckAndReceiving?: number

  @IsOptional()
  @IsString()
  qualityCheckAndReceivingCodeLeadTime?: string

  @IsOptional()
  @IsNumber()
  dayOfLeadtimePurchase?: number

  @IsOptional()
  @IsNumber()
  percentLeadtimePurchase?: number

  @IsOptional()
  @IsString()
  leadtimePurchaseCodeLeadTime?: string

  @IsOptional()
  @IsNumber()
  dayOfLeadtimeDelivery?: number

  @IsOptional()
  @IsNumber()
  percentLeadtimeDelivery?: number

  @IsOptional()
  @IsString()
  leadtimeDeliveryCodeLeadTime?: string
}
