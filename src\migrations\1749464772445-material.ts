import { MigrationInterface, QueryRunner } from 'typeorm'

export class Material1749464772445 implements MigrationInterface {
  name = 'Material1749464772445'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "material" ADD "fixedLotSize" decimal(20,3) CONSTRAINT "DF_80a3568e8979fb0e242a0f333b0" DEFAULT 0`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_80a3568e8979fb0e242a0f333b0"`)
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "fixedLotSize"`)
  }
}
