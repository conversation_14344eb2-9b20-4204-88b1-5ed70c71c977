import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifyMaterial1749733311255 implements MigrationInterface {
    name = 'ModifyMaterial1749733311255'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material" ADD "costingLotSize" decimal(20,3) CONSTRAINT "DF_1d5f98bcfa0cb0f773eec24ad95" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "material" ADD "reorderPoint" decimal(20,3) CONSTRAINT "DF_5cd5decc504e2228f6d7bf557f3" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "material" ADD "scientificName" nvarchar(200)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "representativeName" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p9Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p9Packing" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p8Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p8BaoBi" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p7Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p7CoLyTinh" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p6Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p6DecorColor" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p5Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p5Model" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p20Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p20NhomMau" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p19Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p19LoaiVan" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p18Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p18GiayBalance" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p17Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p17GiayOverlay" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p16Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p16PhamCapSX" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "batch" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p15Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "link" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p15GocVatCapPhatTai" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "hsCode" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p14Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p14HoaVanAndKhuon" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p13Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p13SpTheoQc" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p12Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p12KhuonHemKhoa" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p11Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p11QuyCach2" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p10Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p10QuyCach" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "referenceCode" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "flagForDetete" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "loadingGroup" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "orderUnitDescription" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "variablePurchaseOrderUnitActive" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "unitCode2" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "mRPProfileDescription" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "indivColl" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "mRPType" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "coProduct" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "schedMarginKey" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "gRProcessingTime" nvarchar(10)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "gRProcessingHour" float`);
        await queryRunner.query(`ALTER TABLE "material" ADD "qMMaterialAuth" float`);
        await queryRunner.query(`ALTER TABLE "material" ADD "qMMaterialAuthorizationGroup" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "priceControlIndicator" nvarchar(50)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "materialPriceDetermination" float`);
        await queryRunner.query(`ALTER TABLE "material" ADD "priceUnit" float`);
        await queryRunner.query(`ALTER TABLE "material" ADD "orderUnit" nvarchar(50)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "plannedPrice1" decimal(20,3) CONSTRAINT "DF_3ebbfbe51ddc3e88072ec1e7843" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "material" ADD "plannedPriceDate" datetime`);
        await queryRunner.query(`ALTER TABLE "material" ADD "minimumRemainingShelfLife" nvarchar(50)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "totalShelfLife" float`);
        await queryRunner.query(`ALTER TABLE "material" ADD "varianceKey" nvarchar(50)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "xPlantMaterialStatus" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "flagForDeteteAtPlantLevel" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "purchaseOrderText" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "unitOfIssue" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "customerCode" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "profitCenter" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "profitCenterDescription" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "criticalPart" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "specialProcurementKey" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "planningTimeFence" float`);
        await queryRunner.query(`ALTER TABLE "material" ADD "inHouseProduction" float`);
        await queryRunner.query(`ALTER TABLE "material" ADD "underdelyTol" decimal(20,4) CONSTRAINT "DF_2ec8363b9c2c94672604f063c60" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "material" ADD "overdelyTol" decimal(20,4) CONSTRAINT "DF_ef24b26693cb867793a732ee68f" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "material" ADD "productionUnit" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p21Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p21NhomMauM2" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p22Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "p22KhuonHieuUng" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "ph3Code" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "ph3Name" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "fundProgram" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "fundProgramDescr" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "fundCenter" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "fundCenterDescr" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "commitment" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "commitmentDescr" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "createBy" nvarchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" ADD "createOn" datetime`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "storageLocation"`);
        await queryRunner.query(`ALTER TABLE "material" ADD "storageLocation" varchar(200)`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "salesTaxRateInterpretation"`);
        await queryRunner.query(`ALTER TABLE "material" ADD "salesTaxRateInterpretation" nvarchar(250)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "salesTaxRateInterpretation"`);
        await queryRunner.query(`ALTER TABLE "material" ADD "salesTaxRateInterpretation" float`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "storageLocation"`);
        await queryRunner.query(`ALTER TABLE "material" ADD "storageLocation" varchar(250)`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "createOn"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "createBy"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "commitmentDescr"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "commitment"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "fundCenterDescr"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "fundCenter"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "fundProgramDescr"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "fundProgram"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "ph3Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "ph3Code"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p22KhuonHieuUng"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p22Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p21NhomMauM2"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p21Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "productionUnit"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_ef24b26693cb867793a732ee68f"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "overdelyTol"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_2ec8363b9c2c94672604f063c60"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "underdelyTol"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "inHouseProduction"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "planningTimeFence"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "specialProcurementKey"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "criticalPart"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "profitCenterDescription"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "profitCenter"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "customerCode"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "unitOfIssue"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "purchaseOrderText"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "flagForDeteteAtPlantLevel"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "xPlantMaterialStatus"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "varianceKey"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "totalShelfLife"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "minimumRemainingShelfLife"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "plannedPriceDate"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_3ebbfbe51ddc3e88072ec1e7843"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "plannedPrice1"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "orderUnit"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "priceUnit"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "materialPriceDetermination"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "priceControlIndicator"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "qMMaterialAuthorizationGroup"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "qMMaterialAuth"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "gRProcessingHour"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "gRProcessingTime"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "schedMarginKey"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "coProduct"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "mRPType"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "indivColl"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "mRPProfileDescription"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "unitCode2"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "variablePurchaseOrderUnitActive"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "orderUnitDescription"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "loadingGroup"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "flagForDetete"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "referenceCode"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p10QuyCach"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p10Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p11QuyCach2"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p11Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p12KhuonHemKhoa"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p12Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p13SpTheoQc"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p13Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p14HoaVanAndKhuon"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p14Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "hsCode"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p15GocVatCapPhatTai"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "link"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p15Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "batch"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p16PhamCapSX"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p16Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p17GiayOverlay"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p17Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p18GiayBalance"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p18Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p19LoaiVan"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p19Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p20NhomMau"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p20Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p5Model"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p5Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p6DecorColor"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p6Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p7CoLyTinh"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p7Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p8BaoBi"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p8Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p9Packing"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "p9Name"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "representativeName"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "scientificName"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_5cd5decc504e2228f6d7bf557f3"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "reorderPoint"`);
        await queryRunner.query(`ALTER TABLE "material" DROP CONSTRAINT "DF_1d5f98bcfa0cb0f773eec24ad95"`);
        await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "costingLotSize"`);
    }

}
