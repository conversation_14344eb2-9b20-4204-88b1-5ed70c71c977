import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateGroupPermissionView1749527483051 implements MigrationInterface {
    name = 'UpdateGroupPermissionView1749527483051'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "group_view_permission" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_2662d9779aa2d45e23c95cfb342" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_0f4b80a6c7ec87e49c581e5f7da" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "name" nvarchar(500), "viewPermissionCode" nvarchar(max), CONSTRAINT "PK_2662d9779aa2d45e23c95cfb342" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "group_view_permission"`);
    }

}
