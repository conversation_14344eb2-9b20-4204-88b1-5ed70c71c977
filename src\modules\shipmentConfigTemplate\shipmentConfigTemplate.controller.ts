import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { JwtAuthGuard } from '../common/guards'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ShipmentConfigTemplateService } from './shipmentConfigTemplate.service'
import { ShipmentConfigTemplateCreateDto, ShipmentConfigTemplateUpdateDto } from './dto/shipmentConfigTemplateCreate.dto'

@ApiBearerAuth()
@ApiTags('Shipment-Config-Template')
@UseGuards(JwtAuthGuard)
@Controller('shipment_config_template')
export class ShipmentConfigTemplateController {
  constructor(private readonly service: ShipmentConfigTemplateService) {}

  @Post('pagination')
  async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Hàm tạo' })
  @Post('create_data')
  async create(@CurrentUser() user: UserDto, @Body() data: ShipmentConfigTemplateCreateDto) {
    return this.service.create(user, data)
  }

  @ApiOperation({ summary: 'Hàm update' })
  @Post('update')
  async update(@CurrentUser() user: UserDto, @Body() data: ShipmentConfigTemplateUpdateDto) {
    return this.service.update(user, data)
  }

  @Post('detail')
  async detailTemplate(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return this.service.detailTemplate(user, data)
  }

  @Post('delete')
  async delete(@CurrentUser() user: UserDto, @Body() data: { id: string; idType: string }) {
    return this.service.deleteTempDetail(user, data)
  }

  @Post('delete_temp')
  async deleteTemp(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return this.service.deleteTempConfig(user, data)
  }

  @Post('change_status')
  async changStatus(@CurrentUser() user: UserDto, @Body() data: { id: string; status: string }) {
    return this.service.changStatus(user, data)
  }
}
