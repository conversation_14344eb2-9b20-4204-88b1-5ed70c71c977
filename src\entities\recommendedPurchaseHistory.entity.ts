import { Entity, Column, Join<PERSON>olumn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { RecommendedPurchaseTemplateEntity } from './recommendedPurchaseTemplate.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'

@Entity('recommended_purchase_history')
export class RecommendedPurchaseHistoryEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  createdByName: string

  /** Trạng thái hiện tại */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: true,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseTemplateId: string
  @ManyToOne(() => RecommendedPurchaseTemplateEntity, (p) => p.histories)
  @JoinColumn({ name: 'recommendedPurchaseTemplateId', referencedColumnName: 'id' })
  recommendedPurchaseTemplate: Promise<RecommendedPurchaseTemplateEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseId: string
  @ManyToOne(() => RecommendedPurchaseEntity, (p) => p.histories)
  @JoinColumn({ name: 'recommendedPurchaseId', referencedColumnName: 'id' })
  recommendedPurchase: Promise<RecommendedPurchaseEntity>
}
