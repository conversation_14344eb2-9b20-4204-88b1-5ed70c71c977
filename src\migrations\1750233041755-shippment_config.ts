import { MigrationInterface, QueryRunner } from 'typeorm'

export class ShippmentConfig1750233041755 implements MigrationInterface {
  name = 'ShippmentConfig1750233041755'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "conditionValueIdCompact" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "conditionValueIdCompact"`)
  }
}
