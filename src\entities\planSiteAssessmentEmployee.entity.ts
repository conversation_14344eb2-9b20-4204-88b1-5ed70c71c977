import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { PlanSiteAssessmentEntity } from './planSiteAssessment.entity'
/** Thành viên đánh giá hiện trường */
@Entity('plan_site_assessment_employee')
export class PlanSiteAssessmentEmployeeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  employeeId: string

  @ManyToOne(() => EmployeeEntity)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  planSiteAssessmentId: string
  @ManyToOne(() => PlanSiteAssessmentEntity, (p) => p.employees)
  @JoinColumn({ name: 'planSiteAssessmentId', referencedColumnName: 'id' })
  planSiteAssessment: Promise<PlanSiteAssessmentEntity>

  /** id khối */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgBlockId: string

  /** id phòng ban */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgDepartmentId: string

  /** id bộ phận */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgPartId: string

  /** id vị trí */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  orgPositionId: string

  @Column({
    nullable: true,
    default: false,
  })
  isLeader: boolean
}
