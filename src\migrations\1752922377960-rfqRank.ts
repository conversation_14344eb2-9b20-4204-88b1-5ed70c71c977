import { MigrationInterface, QueryRunner } from "typeorm";

export class RfqRank1752922377960 implements MigrationInterface {
    name = 'RfqRank1752922377960'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "rfq" ADD "rank" float`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "rfq" DROP COLUMN "rank"`);
    }

}
