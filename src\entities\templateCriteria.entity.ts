import { <PERSON><PERSON><PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneTo<PERSON>any } from 'typeorm'
import { BaseEntity } from './base.entity'
import { TemplateEvaluationPotentialEntity } from './templateEvaluationPotential.entity'
import { TemplateCriterialChildEntity } from './templateCriteriaChild.entity'

@Entity('template_criterial')
export class TemplateCriterialEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  name: string

  /**Điểm tối đa */
  @Column({
    type: 'float',
    nullable: true,
  })
  maxScore: number

  /**Điểm liệt */
  @Column({
    type: 'float',
    nullable: true,
  })
  failingGrade: number

  /**Trọng số */
  @Column({
    type: 'float',
    nullable: true,
  })
  percent: number

  /** Loại tiêu chí */
  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: true,
  })
  criteriaType: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  templateEvaluationPotentialId: string
  @ManyToOne(() => TemplateEvaluationPotentialEntity, (p) => p.templateCriterions)
  @JoinColumn({ name: 'templateEvaluationPotentialId', referencedColumnName: 'id' })
  templateEvaluationPotential: Promise<TemplateEvaluationPotentialEntity>

  @OneToMany(() => TemplateCriterialChildEntity, (p) => p.templateCriterial)
  templateCriterialChilds: Promise<TemplateCriterialChildEntity[]>
}
