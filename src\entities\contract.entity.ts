import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import {
  AuctionEntity,
  BusinessPlanEntity,
  CompanyEntity,
  OfferEntity,
  PaymentTermEntity,
  PurchasingGroupEntity,
  PurchasingOrgEntity,
  RecommendedPurchaseEntity,
} from '.'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { BillEntity } from './bill.entity'
import { ComplaintEntity } from './complaint.entity'
import { ContractAppendixEntity } from './contractAppendix.entity'
import { ContractDocumentHandoverEntity } from './contractDocumentHandover.entity'
import { ContractHistoryEntity } from './contractHistory.entity'
import { ContractInspectionEntity } from './contractInspection.entity'
import { ContractItemEntity } from './contractItem.entity'
import { CurrencyEntity } from './currency.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { IncotermEntity } from './incoterm.entity'
import { PaymentContractEntity } from './paymentContract.entity'
import { PaymentProgressEntity } from './paymentProgress.entity'
import { POEntity } from './po.entity'
import { POContractEntity } from './poContract.entity'
import { PrEntity } from './pr.entity'
import { SupplierEntity } from './supplier.entity'

/** Bảng hợp đồng */
@Entity({ name: 'contract' })
export class ContractEntity extends BaseEntity {
  /**Số hợp đồng */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: false,
  })
  code: string

  /**Số hợp đồng ngoại thương */
  @Column({
    type: 'nvarchar',
    length: 150,
    nullable: true,
  })
  foreignContractCode: string

  /**Tên hợp đồng */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  name: string

  /**Tên hợp đồng tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  nameEN: string

  /** Ngày kí hợp đồng */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  contractDate: Date

  /**Loại hợp đồng */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  contractType: string

  /** Ngày hiệu lực */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  effectiveDate: Date

  /** Ngày hết hạn */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  expiredDate: Date

  /**Địa chỉ bên mua */
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: true,
  })
  addressBuyer: string

  /**Số điện thoại bên mua */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  telBuyer: string

  /**Số fax bên mua */
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  faxBuyer: string

  /**Tên bên mua */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  nameBuyer: string

  /**Bên bán */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  nameSeller: string

  /** nhà cung cấp */
  @Column({
    type: 'nvarchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.contracts)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /**Địa chỉ bên bán */
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: true,
  })
  addressSeller: string

  /**Email bên bán */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  emailSeller: string

  /**Số điện thoại bên  bán*/
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  telSeller: string

  /**
   * Đại diện bởi
   */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  representativeSeller: string

  /** Chứng từ khác */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  fileAttach: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  createdBy: string

  /** Trị giá hợp đồng trước thuế */
  @Column({
    type: 'float',
    default: 0,
    nullable: true,
  })
  contractAmountBeforeTax: number

  /** Trị giá hợp đồng trước thuế bằng chữ*/
  @Column({
    type: 'nvarchar',
    length: 400,
    nullable: true,
  })
  contractAmountBeforeTaxText: string

  /** Trị giá hợp đồng sau thuế*/
  @Column({
    type: 'float',
    default: 0,
    nullable: true,
  })
  contractValueAfterTax: number

  /**Trị giá hợp đồng sau thuế bằng chữ*/
  @Column({
    type: 'nvarchar',
    length: 400,
    nullable: true,
  })
  contractAmountAfterTaxText: string

  /** Tỷ giá */
  @Column({
    nullable: true,
    type: 'float',
  })
  exchangeRate: number

  /** Giá trị hợp đồng quy đổi sang VNĐ*/
  @Column({
    type: 'float',
    default: 0,
    nullable: true,
  })
  contractValueBeforeTaxInVND: number

  /** Giá trị hợp đồng sau thuế quy đổi sang VNĐ*/
  @Column({
    type: 'float',
    default: 0,
    nullable: true,
  })
  contractValueAfterTaxInVND: number

  /** Giá trị HĐ */
  @Column({
    type: 'bigint',
    default: 0,
    nullable: true,
  })
  value: number

  /** Tổng số lượng*/
  @Column({
    nullable: true,
  })
  totalAmount: number

  /** Trọng lượng*/
  @Column({
    nullable: true,
  })
  weighing: number

  /**Chất lượng */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  quality: string

  /**Nhà máy */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  manufacturer: string

  /**Xuất xứ */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  origin: string
  /**Phụ lục hợp đồng */
  @OneToMany(() => ContractAppendixEntity, (p) => p.contract)
  appendixs: Promise<ContractAppendixEntity[]>

  @OneToMany(() => ContractHistoryEntity, (p) => p.contract)
  contractHistorys: Promise<ContractHistoryEntity[]>

  /** Tiến độ thanh toán */
  @OneToMany(() => PaymentProgressEntity, (p) => p.contract)
  paymentPlan: Promise<PaymentProgressEntity[]>

  /** Số hợp đồng */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  contractNumber: string

  /** Thời gian giao hàng */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  /** Nguồn tham chiếu ENUM: REFERENCE_SOURCE */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  referenceSource: string

  /** Số chứng từ tham chiếu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  referenceSourceNumber: string

  /** Chứng từ khác */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  fileOther: string

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  eContract: string

  /**Đơn vị tiền tệ */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currencyId: string
  @ManyToOne(() => CurrencyEntity, (currency) => currency.contracts)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })

  /**external matGroup */
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (emg) => emg.contracts)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  /**Điều kiện thương mại */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermId: string
  @ManyToOne(() => IncotermEntity, (incoterm) => incoterm.contracts)
  @JoinColumn({ name: 'incotermId', referencedColumnName: 'id' })
  incoterm: Promise<IncotermEntity>

  @OneToMany(() => ContractItemEntity, (p) => p.contract)
  contractItems: Promise<ContractItemEntity[]>

  @OneToMany(() => ContractInspectionEntity, (p) => p.contract)
  contractInspections: Promise<ContractInspectionEntity[]>

  /** Hóa đơn */
  @OneToMany(() => BillEntity, (p) => p.contract)
  bills: Promise<BillEntity[]>

  /** DS khiếu nại */
  @OneToMany(() => ComplaintEntity, (p) => p.contract)
  complaints: Promise<ComplaintEntity[]>

  @OneToMany(() => PaymentContractEntity, (p) => p.contract)
  paymentContracts: Promise<PaymentContractEntity[]>

  @OneToMany(() => BusinessPlanEntity, (p) => p.contract)
  businessPlan: Promise<BusinessPlanEntity[]>

  @OneToMany(() => ContractDocumentHandoverEntity, (p) => p.contract)
  contractDocumentHandovers: Promise<ContractDocumentHandoverEntity[]>

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  lstPrId: string

  /**#region nguồn tham chiếu pr, thầu, đấu giá, báo giá, đề nghị mua hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.contracts)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.contracts)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  auctionId: string
  @ManyToOne(() => AuctionEntity, (p) => p.contracts)
  @JoinColumn({ name: 'auctionId', referencedColumnName: 'id' })
  auction: Promise<AuctionEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string
  @ManyToOne(() => OfferEntity, (p) => p.contracts)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  // Đề nghị mua hàng
  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseId: string
  @ManyToOne(() => RecommendedPurchaseEntity, (p) => p.contract)
  @JoinColumn({ name: 'recommendedPurchaseId', referencedColumnName: 'id' })
  recommendedPurchase: Promise<RecommendedPurchaseEntity>

  // Tổ chức mua hàng
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string
  @ManyToOne(() => PurchasingOrgEntity, (p) => p.contracts)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  // Nhóm mua hàng
  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string
  @ManyToOne(() => PurchasingGroupEntity, (p) => p.contracts)
  @JoinColumn({ name: 'purchasingGroupId', referencedColumnName: 'id' })
  purchasingGroup: Promise<PurchasingGroupEntity>

  /** Công ty */
  @ManyToOne(() => CompanyEntity, (e) => e.contracts)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  // Thời hạn thanh toán
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentTermId: string
  @ManyToOne(() => PaymentTermEntity, (p) => p.contracts)
  @JoinColumn({ name: 'paymentTermId', referencedColumnName: 'id' })
  paymentTerm: Promise<PaymentTermEntity>

  /** Phiên bản Incoterm */
  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: true,
  })
  incotermVersion: string

  /** Địa điểm áp dụng Incoterm */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  incotermLocation: string

  //#region thông tin in riêng
  /** Địa điểm áp dụng Incoterm tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  incotermLocationEn: string

  /**Chất lượng tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  qualityEn: string

  /**Đóng gói */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  packaging: string

  /**Đóng gói tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  packagEn: string

  /**Ký hiệu*/
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  marking: string

  /**Ký hiệu tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  markingEn: string

  /**Cảng xếp*/
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  portOfLoading: string

  /**Cảng dở tiếng anh*/
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  portOfDischargeEn: string

  /**Giao hàng từng phần */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  partialDelivery: string

  /**Giao hàng từng phần tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  partialDeliveryEn: string

  /**Chuyển tải */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  transshipment: string

  /**Chuyển tải tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  transshipmentEn: string

  /**Số ngày gửi chứng từ */
  @Column({
    nullable: true,
  })
  documentSendingDays: number

  /**Vận đơn */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  billOfLading: string

  /**Vận đơn tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  billOfLadingEn: string

  /**Theo lệnh của */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  onBehalfOf: string

  /**Theo lệnh của tiếng anh */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  onBehalfOfEn: string

  /**Hóa đơn thương mại đã ký */
  @Column({
    nullable: true,
  })
  signedInvoiceCount: number

  /**Chi tiết danh sách đóng gói */
  @Column({
    nullable: true,
  })
  packingListDetailCount: number

  /**Giấy chứng nhận kiểm tra chất lượng */
  @Column({
    nullable: true,
  })
  qualityCertificateCount: number

  /**Thể hiện số Lot*/
  @Column({
    nullable: true,
  })
  isShowLot: boolean

  /**Thể hiện số Lot tiếng anh*/
  @Column({
    nullable: true,
  })
  isShowLotEn: boolean

  /**Giấy chứng nhận xuất xứ */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  certificateOfOrigin: string

  /**Giấy chứng nhận xuất xứ (Tiếng Anh) */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  certificateOfOriginEn: string

  /**Nơi có thẩm quyền (Tiếng Anh)*/
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  authorizedLocation: string

  /**Số lượng bản gốc */
  @Column({
    nullable: true,
  })
  originalCopyCount: number

  /**Số lượng bản copy */
  @Column({
    nullable: true,
  })
  copyCount: number

  /**Đại lý bảo hiểm */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  insuranceAgent: string

  /**Thời gian gửi chứng từ đến bên mua */
  @Column({
    nullable: true,
  })
  documentDeliveryTime: number

  /**Thời gian lập văn bản Khiếu nại */
  @Column({
    nullable: true,
  })
  claimFilingTime: number

  /**Thời gian người bán phản hồi khiếu nại*/
  @Column({
    nullable: true,
  })
  sellerClaimResponseTime: number

  /**Thời gian người bán phản hồi khiếu nại số lượng*/
  @Column({
    nullable: true,
  })
  quantityClaimTime: number

  /**Điều khoản bảo hành */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  warrantyTerms: string

  /**Điều khoản bảo hành (Tiếng Anh) */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  warrantyTermsEn: string

  /**Thời gian thông báo tính huống bất khả kháng*/
  @Column({
    nullable: true,
  })
  forceMajeureNoticeTime: number

  /**Nơi giải quyết phát sinh ngoài hợp đồng */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  disputeResolutionPlace: string

  /**Địa điểm giải quyết phát sinh ngoài hợp đồng (Tiếng Anh) */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  disputeResolutionLocationEn: string

  /** Giá trị phạt */
  @Column({
    type: 'bigint',
    default: 0,
    nullable: true,
  })
  penaltyValue: number

  /**Thời gian thanh toán khoản phạt*/
  @Column({
    nullable: true,
  })
  penaltyPaymentTime: number

  /**Điều khoản khác */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  otherTerms: string

  /**Điều khoản khác (Tiếng Anh) */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  otherTermsEn: string

  /**Điểm giao hàng */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  deliveryPoint: string

  /**Điểm giao hàng (Tiếng Anh)*/
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  deliveryPointEn: string

  /**Đích đến */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  destination: string

  /**Đích đến (Tiếng Anh) */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  destinationEn: string

  /**Cước phí*/
  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: true,
  })
  freightPaymentTerm: string

  /**Freight */
  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: true,
  })
  freightPaymentTermEn: string

  /**Sổ tài khoản */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  bankNumber: string

  /**Chủ tài khoản */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  bankUsername: string

  /**Ngân hàng */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  bankName: string

  /**Chi nhánh */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  bankBranchName: string

  /**Swift Code */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  swiftCode: string

  /**IBAN */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  iban: string

  @Column({ type: 'nvarchar', nullable: true })
  fileUrl: string

  /**Lý do yêu cầu kiểm tra lại */
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  reason: string
  //#region

  /**#end region */

  @OneToMany(() => POContractEntity, (p) => p.contract)
  poContract: Promise<POContractEntity[]>

  /** Danh sách PO theo HĐ */
  @OneToMany(() => POEntity, (p) => p.contract)
  pos: Promise<POEntity[]>
}
