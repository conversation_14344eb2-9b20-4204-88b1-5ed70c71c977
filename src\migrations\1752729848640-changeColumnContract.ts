import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeColumnContract1752729848640 implements MigrationInterface {
    name = 'ChangeColumnContract1752729848640'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_eaf2c2037ad7b443951391ea9a4"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "isShowLotNumber"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_3357159f36e8fac2dc4cc5803d8"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "showLotNumberEn"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "isShowLot" bit`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "isShowLotEn" bit`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "totalPrice"`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "totalPrice" float`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "totalPriceVND"`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "totalPriceVND" float`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "totalPriceAfterTax"`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "totalPriceAfterTax" float`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_6ac98f4816e080139bf62485578"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractAmountBeforeTax"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "contractAmountBeforeTax" float CONSTRAINT "DF_6ac98f4816e080139bf62485578" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_f463ba6bb67cc057ef421ead61e"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractValueAfterTax"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "contractValueAfterTax" float CONSTRAINT "DF_f463ba6bb67cc057ef421ead61e" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_f9b5ecaed2c2380f766ed260762"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractValueBeforeTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "contractValueBeforeTaxInVND" float CONSTRAINT "DF_f9b5ecaed2c2380f766ed260762" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_b202bf15b8b01321f52395f16f3"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractValueAfterTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "contractValueAfterTaxInVND" float CONSTRAINT "DF_b202bf15b8b01321f52395f16f3" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_02c0c8206d9394d427078f4e2f2"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractAmountBeforeTax"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "contractAmountBeforeTax" float CONSTRAINT "DF_02c0c8206d9394d427078f4e2f2" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_7bfefed0d5604051a5f97151698"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractValueAfterTax"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "contractValueAfterTax" float CONSTRAINT "DF_7bfefed0d5604051a5f97151698" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_bd5ea893d3d160e68942f122222"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractValueBeforeTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "contractValueBeforeTaxInVND" float CONSTRAINT "DF_bd5ea893d3d160e68942f122222" DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_24d5b0461c905b71195e43482dd"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractValueAfterTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "contractValueAfterTaxInVND" float CONSTRAINT "DF_24d5b0461c905b71195e43482dd" DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_24d5b0461c905b71195e43482dd"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractValueAfterTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "contractValueAfterTaxInVND" bigint`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "DF_24d5b0461c905b71195e43482dd" DEFAULT 0 FOR "contractValueAfterTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_bd5ea893d3d160e68942f122222"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractValueBeforeTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "contractValueBeforeTaxInVND" bigint`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "DF_bd5ea893d3d160e68942f122222" DEFAULT 0 FOR "contractValueBeforeTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_7bfefed0d5604051a5f97151698"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractValueAfterTax"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "contractValueAfterTax" bigint`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "DF_7bfefed0d5604051a5f97151698" DEFAULT 0 FOR "contractValueAfterTax"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "DF_02c0c8206d9394d427078f4e2f2"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contractAmountBeforeTax"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "contractAmountBeforeTax" bigint`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "DF_02c0c8206d9394d427078f4e2f2" DEFAULT 0 FOR "contractAmountBeforeTax"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_b202bf15b8b01321f52395f16f3"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractValueAfterTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "contractValueAfterTaxInVND" bigint`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD CONSTRAINT "DF_b202bf15b8b01321f52395f16f3" DEFAULT 0 FOR "contractValueAfterTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_f9b5ecaed2c2380f766ed260762"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractValueBeforeTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "contractValueBeforeTaxInVND" bigint`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD CONSTRAINT "DF_f9b5ecaed2c2380f766ed260762" DEFAULT 0 FOR "contractValueBeforeTaxInVND"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_f463ba6bb67cc057ef421ead61e"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractValueAfterTax"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "contractValueAfterTax" bigint`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD CONSTRAINT "DF_f463ba6bb67cc057ef421ead61e" DEFAULT 0 FOR "contractValueAfterTax"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP CONSTRAINT "DF_6ac98f4816e080139bf62485578"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" DROP COLUMN "contractAmountBeforeTax"`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD "contractAmountBeforeTax" bigint`);
        await queryRunner.query(`ALTER TABLE "contract_appendix" ADD CONSTRAINT "DF_6ac98f4816e080139bf62485578" DEFAULT 0 FOR "contractAmountBeforeTax"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "totalPriceAfterTax"`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "totalPriceAfterTax" bigint`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "totalPriceVND"`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "totalPriceVND" bigint`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "totalPrice"`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "totalPrice" bigint`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "isShowLotEn"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "isShowLot"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "showLotNumberEn" bit`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "DF_3357159f36e8fac2dc4cc5803d8" DEFAULT 0 FOR "showLotNumberEn"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "isShowLotNumber" bit`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "DF_eaf2c2037ad7b443951391ea9a4" DEFAULT 0 FOR "isShowLotNumber"`);
    }

}
