import { Controller, UseGuards, Post, Body } from '@nestjs/common'
import { JwtAuthGuard } from '../common/guards'
import { PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger'
import { IncotermConversionCreateDto, IncotermConversionUpdateDto } from './dto'
import { FilterOneDto } from '../../dto/filterOne.dto'
import { IncotermConversionService } from './incotermConversion.service'

@ApiBearerAuth()
@ApiTags('IncotermConversion')
@UseGuards(JwtAuthGuard)
@Controller('incoterm_conversion')
export class IncotermConversionController {
  constructor(private readonly service: IncotermConversionService) {}

  @ApiOperation({ summary: '<PERSON><PERSON>y danh sách incoterm' })
  @Post('find')
  public async find() {
    return await this.service.find()
  }

  @ApiOperation({ summary: 'Danh sách incoterm phân trang' })
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới một incoterm với dữ liệu được cung cấp.' })
  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: IncotermConversionCreateDto[]) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái kích hoạt của incoterm.' })
  @Post('update_active')
  async updateActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Load data select' })
  @Post('load_data_select')
  async loadDataSelect(@CurrentUser() user: UserDto) {
    return await this.service.loadDataSelect(user)
  }
}
