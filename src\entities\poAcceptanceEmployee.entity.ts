import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { PoAcceptanceEntity } from './poAcceptance.entity'

/**<PERSON><PERSON><PERSON> viên nghiệm thu PO*/
@Entity({ name: 'po_acceptance_employee' })
export class PoAcceptanceEmployeeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  employeeId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.poAcceptanceEmployees)
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'id' })
  employee: Promise<EmployeeEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  poAcceptanceId: string
  @ManyToOne(() => PoAcceptanceEntity, (p) => p.poAcceptanceEmployees)
  @JoinColumn({ name: 'poAcceptanceId', referencedColumnName: 'id' })
  poAcceptance: Promise<PoAcceptanceEntity>
}
