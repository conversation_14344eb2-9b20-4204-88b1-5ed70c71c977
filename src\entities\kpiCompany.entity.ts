import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CompanyEntity } from './company.entity'
import { KpiEntity } from './kpi.entity'

/**  kpi mua hàng công ty*/
@Entity('kpi_company')
export class KpiCompanyEntity extends BaseEntity {
  /** id công ty */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string
  @ManyToOne(() => CompanyEntity, (p) => p.kpiCompanys)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  /** id kpi mua hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  kpiId: string
  @ManyToOne(() => KpiEntity, (p) => p.kpiCompanys)
  @JoinColumn({ name: 'kpiId', referencedColumnName: 'id' })
  kpi: Promise<KpiEntity>
}
