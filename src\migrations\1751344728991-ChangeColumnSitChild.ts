import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeColumnSitChild1751344728991 implements MigrationInterface {
  name = 'ChangeColumnSitChild1751344728991'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "criteria_site_assessment_child" ALTER COLUMN "supplierReply" nvarchar(max)
        `)
    await queryRunner.query(`
            ALTER TABLE "criteria_site_assessment_child" ALTER COLUMN "supplierReplyNumber" bigint
        `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "criteria_site_assessment_child" ALTER COLUMN "supplierReplyNumber" int
        `)
    await queryRunner.query(`
            ALTER TABLE "criteria_site_assessment_child" ALTER COLUMN "supplierReply" varchar(400) `)
  }
}
