import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyTo<PERSON>ne, JoinColum<PERSON> } from 'typeorm'
import { TemplateSiteAssessmentItemChildEntity } from './templateSiteAssessmentItemChild.entity'

@Entity('template_site_assessment_list_detail')
export class TemplateSiteAssessmentListDetailEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  value: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  templateSiteAssessmentItemChildId: string
  @ManyToOne(() => TemplateSiteAssessmentItemChildEntity, (p) => p.templateSiteAssessmentListDetails)
  @JoinColumn({ name: 'templateSiteAssessmentItemChildId', referencedColumnName: 'id' })
  templateSiteAssessmentItemChild: Promise<TemplateSiteAssessmentItemChildEntity>
}
