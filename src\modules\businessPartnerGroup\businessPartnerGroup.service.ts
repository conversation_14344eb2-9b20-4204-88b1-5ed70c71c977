import { Injectable } from '@nestjs/common'
import { In, Like } from 'typeorm'
import { PaginationDto, UserDto } from '../../dto'
import { CREATE_SUCCESS, enumData, ERROR_CODE_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { BusinessPartnerGroupEntity } from '../../entities'
import { coreHelper } from '../../helpers'
import { BusinessPartnerGroupRepository } from '../../repositories'
import { BusinessPartnerGroupCreateDto, BusinessPartnerGroupUpdateDto, BusinessPartnerGroupCreateByExcelDto } from './dto'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'

@Injectable()
export class BusinessPartnerGroupService {
  constructor(private repo: BusinessPartnerGroupRepository, private organizationalPositionService: OrganizationalPositionService) {}

  /** Lấy ds */
  public async find() {
    return await this.repo.find({ where: { isDeleted: false }, order: { code: 'ASC' } })
  }

  /** Lấy ds có phân trang */
  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = {}
    const dataRs = await this.organizationalPositionService.getListRoleTest(user, enumData.RoleEnum.BusinessPartnerGroupr.code)
    if (dataRs.type === enumData.RoleData.All.code) {
      /* do nothing */
    }
    if (dataRs.type === enumData.RoleData.AllCompany.code) {
      whereCon[dataRs.properties] = dataRs.where
    }
    if (dataRs.type === enumData.RoleData.Child.code) {
      whereCon[dataRs.properties] = In(dataRs.where)
    }
    if (data.where.name) {
      whereCon.name = Like(`%${data.where.name}%`)
    }
    if (data.where.code) {
      whereCon.code = Like(`%${data.where.code}%`)
    }
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    const res = await this.repo.findAndCount({
      where: whereCon,
      order: { code: 'DESC', createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })

    return res
  }

  /** Tạo mới một với dữ liệu được cung cấp. */
  async createData(user: UserDto, data: BusinessPartnerGroupCreateDto) {
    const isCodeExist = await this.repo.findOne({ where: { code: data.code }, select: { id: true } })
    if (isCodeExist) throw new Error(ERROR_CODE_TAKEN)

    const entityNew = new BusinessPartnerGroupEntity()
    entityNew.code = data.code
    entityNew.name = data.name
    entityNew.description = data.description
    entityNew.startCode = data.startCode
    entityNew.endCode = data.endCode

    entityNew.createdAt = new Date()
    entityNew.createdBy = user.id
    await this.repo.insert(entityNew)

    return { message: CREATE_SUCCESS }
  }

  /** Cập nhật thông tin của với dữ liệu được cung cấp. */
  async updateData(user: UserDto, data: BusinessPartnerGroupUpdateDto) {
    const region = await this.repo.findOne({ where: { id: data.id } })
    if (!region) throw new Error(ERROR_NOT_FOUND_DATA)
    region.code = data.code
    region.name = data.name
    region.description = data.description
    region.startCode = data.startCode
    region.endCode = data.endCode

    region.updatedBy = user.id
    region.updatedAt = new Date()

    await this.repo.update(region.id, region)

    return { message: UPDATE_SUCCESS }
  }

  /** Cập nhật trạng thái kích hoạt của. */
  async updateActive(user: UserDto, id: string) {
    const entity = await this.repo.findOne({
      where: { id: id },
      select: { id: true, isDeleted: true },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const newIsDeleted = !entity.isDeleted
    await this.repo.update(id, {
      isDeleted: newIsDeleted,
      updatedBy: user.id,
      updatedAt: new Date(),
    })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /**Hàm import excel */
  public async importData(user: UserDto, data: BusinessPartnerGroupCreateByExcelDto[]) {
    //check xem data có trùng nhau hoặc không có không
    if (data.length < 1) throw new Error('Vui lòng điền ít nhất 1 dòng dữ liệu!')
    {
      const dublicateArayCode = coreHelper.findDuplicates(data, 'code')
      if (dublicateArayCode.length > 0) throw new Error(`Danh sách mã trùng nhau ${dublicateArayCode.toString()}`)
    }

    //lọc lấy danh sách countryKey của
    const countryKeys = data.map(function (obj) {
      return obj.code
    })

    // tìm ra những mã bị trùng dưới data base
    const dupCount = await this.repo.find({ where: { code: In(countryKeys) } })
    if (dupCount.length > 0) {
      const dupCode = data.map(function (obj) {
        return obj.code
      })
      throw new Error(`Danh sách mã trùng nhau [${dupCode.toString()}]`)
    }
    await this.repo.manager.transaction('READ UNCOMMITTED', async (trans) => {
      const lstTask = []
      const repo = trans.getRepository(BusinessPartnerGroupEntity)
      const dicRegion: any = {}

      const set = new Set()
      for (const item of data) {
        item.code = item.code.toString()
        if (dicRegion[item.code]) continue
        if (set.has(item.code)) continue
        set.add(item.code)

        const entity = new BusinessPartnerGroupEntity()
        entity.createdBy = user.id
        entity.createdAt = new Date()
        entity.code = item.code
        entity.name = item.name
        entity.description = item.description
        entity.startCode = item.startCode
        entity.endCode = item.endCode

        lstTask.push(entity)
      }
      const lstChild = coreHelper.splitArrayByParameters(lstTask, 2000)
      for (const chunk of lstChild) {
        await trans.insert(BusinessPartnerGroupEntity, chunk)
      }
    })
    return { message: CREATE_SUCCESS }
  }

  async loadDataSelect(user: UserDto) {
    return await this.repo.find({
      where: { isDeleted: false },
      select: { id: true, code: true, name: true },
    })
  }
}
