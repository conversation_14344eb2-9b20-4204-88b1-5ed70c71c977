import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeTemplate1750503402327 implements MigrationInterface {
  name = 'ChangeTemplate1750503402327'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceYearId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceYear" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "status" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "year" int`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter1" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter2" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter3" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter4" bigint`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter4"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter3"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter2"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter1"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "year"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "status"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceYear"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceYearId"`)
  }
}
