import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { SupplierEntity } from './supplier.entity'

/**<PERSON><PERSON><PERSON> nâng cấp nhà cung cấp tiềm năng*/
@Entity('supplier_potential_upgrade')
export class SupplierPotentialUpgradeEntity extends BaseEntity {
  /** Mã */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** trạng thái */
  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  status: string

  /** nhà cung cấp */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.supplierPotentialUpgrades)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /**Thông tin pháp lý nhà cung cấp */
  @Column({
    length: 'max',
    nullable: true,
  })
  jsonSupplier: string
}
