import { Inject, Injectable, OnModuleInit } from '@nestjs/common'
import { Between, DataSource, EntityManager, ILike, Not } from 'typeorm'
import { TemplateCreateDto, UpdateTemplateDto } from './dto'
import { TemplateRepository } from '../../repositories/template.repository'
import { PaginationDto, UserDto } from '../../dto'
import { TemplateEntity } from '../../entities'
import moment from 'moment'
import { AllTemplates } from '../../constants/enumTemplate'
import { InjectEntityManager } from '@nestjs/typeorm'
import { DATA_SOURCE, ERROR_CODE_TAKEN, ERROR_NAME_TAKEN, ERROR_NOT_FOUND_DATA, UPDATE_SUCCESS } from '../../constants'
import axios from 'axios'
import PizZip from 'pizzip'
import Docxtemplater from 'docxtemplater'
import * as htmlToDocx from 'html-to-docx'
import * as mammoth from 'mammoth'

import { UploadFileService } from '../uploadFile/uploadFile.service'
import { ContractAppendixRepository, ContractInspectionRepository, ContractRepository, PORepository } from '../../repositories'
import { coreHelper } from '../../helpers'

@Injectable()
export class TemplateService {
  private readonly entityManager: EntityManager
  constructor(
    @Inject(DATA_SOURCE) private dataSource: DataSource,
    private readonly templateRepo: TemplateRepository,
    private readonly poRepo: PORepository,
    private readonly contractRepo: ContractRepository,
    private readonly contractAppendixRepo: ContractAppendixRepository,
    private readonly contractInspectionRepo: ContractInspectionRepository,
    private readonly uploadService: UploadFileService,
  ) {
    this.entityManager = this.dataSource.manager
  }

  // Hàm tạo template
  public async create(user: UserDto, data: TemplateCreateDto) {
    let temp = new TemplateEntity()
    temp.isDeleted = false
    temp.type = data.type
    temp.name = data.name
    temp.link = data.link
    temp.createdAt = new Date()
    //temp.createdBy = user.employeeId
    return await this.templateRepo.save(temp)
  }

  public async loadOptionType() {
    const data = Object.keys(AllTemplates).map((key) => ({
      label: AllTemplates[key],
      value: key,
    }))

    return { data }
  }

  async updateData(user: UserDto, data: UpdateTemplateDto) {
    const existEntity = await this.templateRepo.findOne({ where: { id: data.id } })
    if (!existEntity) throw new Error(ERROR_NOT_FOUND_DATA)

    const isNameExist = await this.templateRepo.exists({ where: { name: data.name, id: Not(data.id) } })
    if (isNameExist) throw new Error(ERROR_NAME_TAKEN)

    existEntity.name = data.name
    existEntity.link = data.link
    existEntity.type = data.type
    existEntity.updatedBy = user.id

    await this.templateRepo.update(existEntity.id, existEntity)

    return { message: UPDATE_SUCCESS }
  }

  // Hàm phân trang lấy danh sách template
  public async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { isDeleted: false }
    if (data.where.name) whereCon.name = ILike(`%${data.where.name}%`)
    if (data.where.createdAt)
      whereCon.createdAt = Between(
        moment(data.where.createdAt).format('YYYY-MM-DD 00:00:00'),
        moment(data.where.createdAt).format('YYYY-MM-DD 23:59:59'),
      )
    const res: any[] = await this.templateRepo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })
    return res
  }

  public async findTemplate(typeTemplate: string) {
    const res = await this.templateRepo.find({
      where: { type: typeTemplate, isDeleted: false },
    })
    return res
  }

  // Hàm xóa Template
  public async updateStatus(id: string) {
    const temp = await this.templateRepo.findOne({ where: { id: id } })
    if (temp) {
      return await this.templateRepo.update({ id: id }, { isDeleted: !temp.isDeleted })
    }
  }

  // Hàm logic lấy data
  public async getData(col: string, table: string, id: string) {
    const sql = `SELECT ${col} FROM ${table} WHERE id = '${id}'`
    return await this.entityManager.query(sql, [])
  }
  async downloadTemplateContractAppendix(
    templateId: string,
    contractId: string,
    contractAppendixId: string,
  ): Promise<{ buffer: Buffer; fileUrl: string }> {
    const temp = await this.templateRepo.findOne({ where: { id: templateId } })
    if (!temp) throw new Error('Template not found')

    const res = await axios.get(temp.link, { responseType: 'arraybuffer' })
    const zip = new PizZip(res.data)

    const templateConfig = AllTemplates[temp.type]
    const keys = Object.keys(templateConfig)
    const tableKeys = ['PAYMENT_SCHEDULE', 'ITEM_INFORMATION']
    const mapOfTableBody: Record<string, string> = {}

    const results = await Promise.all(
      keys.map(async (key) => {
        const rawSql = templateConfig[key]
        let finalSql = ''

        if (key === 'CONTRACT_APPENDIX') {
          finalSql = rawSql.replace('?', `'${contractAppendixId}'`)
        } else {
          finalSql = rawSql.replace('?', `'${contractId}'`)
        }

        const res = await this.entityManager.query(finalSql)
        return { key, res }
      }),
    )

    const dataToReplace: Record<string, any> = {}

    for (const { key, res } of results) {
      if (tableKeys.includes(key)) {
        const headers = Object.keys(res[0] || {})
        const rows = res.map((r) => headers.map((h) => r[h]))

        const tableHtml = `
          <table border="1" style="border-collapse: collapse; font-family: 'Times New Roman'; font-size: 10pt; width: 100%; table-layout: fixed;">
            <thead>
              <tr>
                ${headers
                  .map(
                    (h) =>
                      `<th style="padding: 3px; border: 1px solid black; text-align: center; white-space: normal; word-break: break-word;">${h}</th>`,
                  )
                  .join('')}
              </tr>
            </thead>
            <tbody>
              ${rows
                .map(
                  (row) =>
                    `<tr>${row
                      .map(
                        (cell) =>
                          `<td style="padding: 3px; border: 1px solid black; text-align: left; white-space: normal; word-break: break-word;">${
                            cell ?? ''
                          }</td>`,
                      )
                      .join('')}</tr>`,
                )
                .join('')}
            </tbody>
          </table>
        `

        const tableDocxBuffer = await htmlToDocx(tableHtml)
        const tableZip = new PizZip(tableDocxBuffer)
        const tableXml = tableZip.file('word/document.xml')?.asText()
        let tableBody = tableXml?.match(/<w:body[^>]*>([\s\S]*?)<\/w:body>/)?.[1] || ''
        tableBody = tableBody.replace(/<w:sectPr[\s\S]*?<\/w:sectPr>/g, '')

        let count = 0
        tableBody = tableBody.replace(/<w:tblGrid[\s\S]*?<\/w:tblGrid>/g, (match) => {
          return count++ === 0 ? match : ''
        })

        dataToReplace[key] = `<?${key}?>`
        mapOfTableBody[key] = tableBody
      } else {
        const row = Array.isArray(res) && res.length > 0 ? res[0] : {}
        Object.entries(row).forEach(([k, v]) => (dataToReplace[k] = v))
      }
    }

    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
      delimiters: { start: '<?', end: '?>' },
    })

    doc.setData(dataToReplace)
    doc.render()

    const renderedBuffer = doc.getZip().generate({ type: 'nodebuffer' })
    const zip2 = new PizZip(renderedBuffer)
    let documentXml = zip2.file('word/document.xml')?.asText()
    if (!documentXml) throw new Error('Không đọc được document.xml')

    const decodeXml = (s: string) => s.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&')
    documentXml = decodeXml(documentXml)

    for (const key of tableKeys) {
      let searchText = ''
      if (key === 'PAYMENT_SCHEDULE') searchText = 'Thời hạn thanh toán:'
      else if (key === 'ITEM_INFORMATION') searchText = 'Thông tin hàng hóa / dịch vụ:'
      else continue

      const paraMatch = this.findParagraphContaining(searchText, documentXml)
      if (!paraMatch) {
        console.warn(`Không tìm thấy đoạn <w:p> chứa "${searchText}", bỏ qua chèn bảng "${key}"`)
        continue
      }

      const insertion = paraMatch + mapOfTableBody[key]
      documentXml = documentXml.replace(paraMatch, insertion)
    }

    zip2.file('word/document.xml', documentXml)
    const docxBuffer = zip2.generate({ type: 'nodebuffer' })

    const uploadResult = await this.uploadService.uploadSingleWord({
      originalname: 'Phụ lục hợp đồng.docx',
      buffer: docxBuffer,
    })

    const fileUrl = uploadResult[0]
    console.log('fileUrl', fileUrl)
    await this.contractAppendixRepo.update({ id: contractAppendixId }, { fileUrl })

    return { buffer: docxBuffer, fileUrl: fileUrl }
  }

  async downloadTemplateContract(templateId: string, contractId: string): Promise<{ buffer: Buffer; fileUrl: string }> {
    const temp = await this.templateRepo.findOne({ where: { id: templateId } })
    if (!temp) throw new Error('Template not found')

    const res = await axios.get(temp.link, { responseType: 'arraybuffer' })
    const zip = new PizZip(res.data)

    const templateConfig = AllTemplates[temp.type]
    const keys = Object.keys(templateConfig)
    const tableKeys = ['PAYMENT_SCHEDULE', 'ITEM_INFORMATION', 'LATEST_DELIVERY_TIME']
    const mapOfTableBody: Record<string, string> = {}

    const results = await Promise.all(
      keys.map(async (key) => {
        const sql = templateConfig[key].replace('?', `'${contractId}'`)
        const res = await this.entityManager.query(sql)
        return { key, res }
      }),
    )

    const dataToReplace: Record<string, any> = {}

    for (const { key, res } of results) {
      if (tableKeys.includes(key)) {
        const headers = Object.keys(res[0] || {})
        const rows = res.map((r) => headers.map((h) => r[h]))

        const tableHtml = `
          <table border="1" style="border-collapse: collapse; font-family: 'Times New Roman'; font-size: 10pt; width: 100%; table-layout: fixed;">
            <thead>
              <tr>
                ${headers
                  .map(
                    (h) =>
                      `<th style="padding: 3px; border: 1px solid black; text-align: center; white-space: normal; word-break: break-word;">${h}</th>`,
                  )
                  .join('')}
              </tr>
            </thead>
            <tbody>
              ${rows
                .map(
                  (row) =>
                    `<tr>${row
                      .map(
                        (cell) =>
                          `<td style="padding: 3px; border: 1px solid black; text-align: left; white-space: normal; word-break: break-word;">${
                            cell ?? ''
                          }</td>`,
                      )
                      .join('')}</tr>`,
                )
                .join('')}
            </tbody>
          </table>
        `

        const tableDocxBuffer = await htmlToDocx(tableHtml)
        const tableZip = new PizZip(tableDocxBuffer)
        const tableXml = tableZip.file('word/document.xml')?.asText()
        let tableBody = tableXml?.match(/<w:body[^>]*>([\s\S]*?)<\/w:body>/)?.[1] || ''
        tableBody = tableBody.replace(/<w:sectPr[\s\S]*?<\/w:sectPr>/g, '')

        let count = 0
        tableBody = tableBody.replace(/<w:tblGrid[\s\S]*?<\/w:tblGrid>/g, (match) => {
          return count++ === 0 ? match : ''
        })

        dataToReplace[key] = `<?${key}?>`
        mapOfTableBody[key] = tableBody
      } else {
        const row = Array.isArray(res) && res.length > 0 ? res[0] : {}
        Object.entries(row).forEach(([k, v]) => {
          if (v instanceof Date || (!isNaN(Date.parse(v)) && typeof v === 'string')) {
            dataToReplace[k] = coreHelper.dateToString(v);
          } else {
            dataToReplace[k] = v;
          }
        })
        
      }
    }

    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
      delimiters: { start: '<?', end: '?>' },
    })

    doc.setData(dataToReplace)
    doc.render()

    const renderedBuffer = doc.getZip().generate({ type: 'nodebuffer' })
    const zip2 = new PizZip(renderedBuffer)
    let documentXml = zip2.file('word/document.xml')?.asText()
    if (!documentXml) throw new Error('Không đọc được document.xml')

    const decodeXml = (s: string) => s.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&')
    documentXml = decodeXml(documentXml)

    for (const key of tableKeys) {
      let searchText = ''
      if (key === 'PAYMENT_SCHEDULE') searchText = 'Thời hạn thanh toán:'
      else if (key === 'ITEM_INFORMATION') searchText = 'Thông tin hàng hóa / dịch vụ:'
      else if (key === 'LATEST_DELIVERY_TIME') searchText = 'Thời gian giao hàng trễ:'
      else continue

      const paraMatch = this.findParagraphContaining(searchText, documentXml)
      if (!paraMatch) {
        console.warn(`Không tìm thấy đoạn <w:p> chứa "${searchText}", bỏ qua chèn bảng "${key}"`)
        continue
      }

      const insertion = paraMatch + mapOfTableBody[key]
      documentXml = documentXml.replace(paraMatch, insertion)
    }

    zip2.file('word/document.xml', documentXml)
    const docxBuffer = zip2.generate({ type: 'nodebuffer' })

    const uploadResult = await this.uploadService.uploadSingleWord({
      originalname: 'Hợp đồng.docx',
      buffer: docxBuffer,
    })

    const fileUrl = uploadResult[0]
    console.log('fileUrl', fileUrl)
    await this.contractRepo.update({ id: contractId }, { fileUrl })

    return { buffer: docxBuffer, fileUrl: fileUrl }
  }

  async downloadTemplateContractInspection(templateId: string, contractInspectionId: string): Promise<{ buffer: Buffer; fileUrl: string }> {
    const temp = await this.templateRepo.findOne({ where: { id: templateId } })
    if (!temp) throw new Error('Template not found')

    const res = await axios.get(temp.link, { responseType: 'arraybuffer' })
    const zip = new PizZip(res.data)

    const templateConfig = AllTemplates[temp.type]
    const keys = Object.keys(templateConfig)
    const tableKeys = ['PAYMENT_SCHEDULE', 'ITEM_INFORMATION', 'LATEST_DELIVERY_TIME']
    const mapOfTableBody: Record<string, string> = {}

    const results = await Promise.all(
      keys.map(async (key) => {
        const sql = templateConfig[key].replace('?', `'${contractInspectionId}'`)
        const res = await this.entityManager.query(sql)
        return { key, res }
      }),
    )

    const dataToReplace: Record<string, any> = {}

    for (const { key, res } of results) {
      if (tableKeys.includes(key)) {
        const headers = Object.keys(res[0] || {})
        const rows = res.map((r) => headers.map((h) => r[h]))

        const tableHtml = `
          <table border="1" style="border-collapse: collapse; font-family: 'Times New Roman'; font-size: 10pt; width: 100%; table-layout: fixed;">
            <thead>
              <tr>
                ${headers
                  .map(
                    (h) =>
                      `<th style="padding: 3px; border: 1px solid black; text-align: center; white-space: normal; word-break: break-word;">${h}</th>`,
                  )
                  .join('')}
              </tr>
            </thead>
            <tbody>
              ${rows
                .map(
                  (row) =>
                    `<tr>${row
                      .map(
                        (cell) =>
                          `<td style="padding: 3px; border: 1px solid black; text-align: left; white-space: normal; word-break: break-word;">${
                            cell ?? ''
                          }</td>`,
                      )
                      .join('')}</tr>`,
                )
                .join('')}
            </tbody>
          </table>
        `

        const tableDocxBuffer = await htmlToDocx(tableHtml)
        const tableZip = new PizZip(tableDocxBuffer)
        const tableXml = tableZip.file('word/document.xml')?.asText()
        let tableBody = tableXml?.match(/<w:body[^>]*>([\s\S]*?)<\/w:body>/)?.[1] || ''
        tableBody = tableBody.replace(/<w:sectPr[\s\S]*?<\/w:sectPr>/g, '')

        let count = 0
        tableBody = tableBody.replace(/<w:tblGrid[\s\S]*?<\/w:tblGrid>/g, (match) => {
          return count++ === 0 ? match : ''
        })

        dataToReplace[key] = `<?${key}?>`
        mapOfTableBody[key] = tableBody
      } else {
        const row = Array.isArray(res) && res.length > 0 ? res[0] : {}
        Object.entries(row).forEach(([k, v]) => (dataToReplace[k] = v))
      }
    }

    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
      delimiters: { start: '<?', end: '?>' },
    })

    doc.setData(dataToReplace)
    doc.render()

    const renderedBuffer = doc.getZip().generate({ type: 'nodebuffer' })
    const zip2 = new PizZip(renderedBuffer)
    let documentXml = zip2.file('word/document.xml')?.asText()
    if (!documentXml) throw new Error('Không đọc được document.xml')

    const decodeXml = (s: string) => s.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&')
    documentXml = decodeXml(documentXml)

    // for (const key of tableKeys) {
    //   let searchText = ''
    //   if (key === 'PAYMENT_SCHEDULE') searchText = 'Tiến độ thanh toán:'
    //   else if (key === 'ITEM_INFORMATION') searchText = 'Thông tin hàng hóa / dịch vụ:'
    //   else if (key === 'LATEST_DELIVERY_TIME') searchText = 'Thời gian giao hàng trễ nhất:'

    //   const paraMatch = this.findParagraphContaining(searchText, documentXml)
    //   if (!paraMatch) throw new Error(`Không tìm thấy đoạn <w:p> chứa "${searchText}"`)

    //   const insertion = paraMatch + mapOfTableBody[key]
    //   documentXml = documentXml.replace(paraMatch, insertion)
    // }

    zip2.file('word/document.xml', documentXml)
    const docxBuffer = zip2.generate({ type: 'nodebuffer' })

    const uploadResult = await this.uploadService.uploadSingleWord({
      originalname: 'contract-template.docx',
      buffer: docxBuffer,
    })

    const fileUrl = uploadResult[0]
    console.log('fileUrl', fileUrl)
    await this.contractInspectionRepo.update({ id: contractInspectionId }, { fileUrl })

    return { buffer: docxBuffer, fileUrl: fileUrl }
  }
  async downloadAllTemplate(
    templateId: string,
    referenceId: string,
    type: 'contract' | 'appendix' | 'inspection',
    extraIds?: { contractId?: string; contractAppendixId?: string },
  ): Promise<{ buffer: Buffer; fileUrl: string }> {
    const temp = await this.templateRepo.findOne({ where: { id: templateId } })
    if (!temp) throw new Error('Template not found')

    const res = await axios.get(temp.link, { responseType: 'arraybuffer' })
    const zip = new PizZip(res.data)

    const templateConfig = AllTemplates[temp.type]
    const keys = Object.keys(templateConfig)
    const mapOfTableBody: Record<string, string> = {}
    const dataToReplace: Record<string, any> = {}

    // Bản các key cần gắn bảng và văn bản tìm trong docx
    const tableKeysMap: Record<string, string[]> = {
      contract: ['PAYMENT_SCHEDULE', 'ITEM_INFORMATION', 'LATEST_DELIVERY_TIME'],
      appendix: ['PAYMENT_SCHEDULE', 'ITEM_INFORMATION'],
      inspection: ['PAYMENT_SCHEDULE', 'ITEM_INFORMATION', 'LATEST_DELIVERY_TIME'],
    }

    const searchTextMap: Record<string, Record<string, string>> = {
      contract: {
        PAYMENT_SCHEDULE: 'Thời hạn thanh toán:',
        ITEM_INFORMATION: 'Thông tin hàng hóa / dịch vụ:',
        LATEST_DELIVERY_TIME: 'Thời gian giao hàng trễ:',
      },
      appendix: {
        PAYMENT_SCHEDULE: 'Thời hạn thanh toán:',
        ITEM_INFORMATION: 'Thông tin hàng hóa / dịch vụ:',
      },
      inspection: {
        PAYMENT_SCHEDULE: 'Tiến độ thanh toán:',
        ITEM_INFORMATION: 'Thông tin hàng hóa / dịch vụ:',
        LATEST_DELIVERY_TIME: 'Thời gian giao hàng trễ nhất:',
      },
    }

    const tableKeys = tableKeysMap[type] || []

    const results = await Promise.all(
      keys.map(async (key) => {
        let sql = templateConfig[key]

        if (type === 'appendix') {
          // Trường hợp phụ lục hợp đồng
          if (key === 'CONTRACT_APPENDIX') {
            sql = sql.replace('?', `'${extraIds?.contractAppendixId}'`)
          } else {
            sql = sql.replace('?', `'${extraIds?.contractId}'`)
          }
          // Trường hợp còn lại
        } else {
          sql = sql.replace('?', `'${referenceId}'`)
        }

        const res = await this.entityManager.query(sql)
        return { key, res }
      }),
    )

    for (const { key, res } of results) {
      if (tableKeys.includes(key)) {
        const headers = Object.keys(res[0] || {})
        const rows = res.map((r) => headers.map((h) => r[h]))

        const tableHtml = `
          <table border="1" style="border-collapse: collapse; font-family: 'Times New Roman'; font-size: 10pt; width: 100%; table-layout: fixed;">
            <thead>
              <tr>
                ${headers
                  .map(
                    (h) =>
                      `<th style="padding: 3px; border: 1px solid black; text-align: center; white-space: normal; word-break: break-word;">${h}</th>`,
                  )
                  .join('')}
              </tr>
            </thead>
            <tbody>
              ${rows
                .map(
                  (row) =>
                    `<tr>${row
                      .map(
                        (cell) =>
                          `<td style="padding: 3px; border: 1px solid black; text-align: left; white-space: normal; word-break: break-word;">${
                            cell ?? ''
                          }</td>`,
                      )
                      .join('')}</tr>`,
                )
                .join('')}
            </tbody>
          </table>
        `

        const tableDocxBuffer = await htmlToDocx(tableHtml)
        const tableZip = new PizZip(tableDocxBuffer)
        const tableXml = tableZip.file('word/document.xml')?.asText()
        let tableBody = tableXml?.match(/<w:body[^>]*>([\s\S]*?)<\/w:body>/)?.[1] || ''
        tableBody = tableBody.replace(/<w:sectPr[\s\S]*?<\/w:sectPr>/g, '')

        let count = 0
        tableBody = tableBody.replace(/<w:tblGrid[\s\S]*?<\/w:tblGrid>/g, (match) => {
          return count++ === 0 ? match : ''
        })

        dataToReplace[key] = `<?${key}?>`
        mapOfTableBody[key] = tableBody
      } else {
        const row = Array.isArray(res) && res.length > 0 ? res[0] : {}
        Object.entries(row).forEach(([k, v]) => (dataToReplace[k] = v))
      }
    }

    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
      delimiters: { start: '<?', end: '?>' },
    })

    doc.setData(dataToReplace)
    doc.render()

    const renderedBuffer = doc.getZip().generate({ type: 'nodebuffer' })
    const zip2 = new PizZip(renderedBuffer)
    let documentXml = zip2.file('word/document.xml')?.asText()
    if (!documentXml) throw new Error('Không đọc được document.xml')

    const decodeXml = (s: string) => s.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&')
    documentXml = decodeXml(documentXml)

    for (const key of tableKeys) {
      const searchText = searchTextMap[type]?.[key]
      if (!searchText) continue
      const paraMatch = this.findParagraphContaining(searchText, documentXml)
      if (!paraMatch) throw new Error(`Không tìm thấy đoạn <w:p> chứa "${searchText}"`)
      const insertion = paraMatch + mapOfTableBody[key]
      documentXml = documentXml.replace(paraMatch, insertion)
    }

    zip2.file('word/document.xml', documentXml)
    const docxBuffer = zip2.generate({ type: 'nodebuffer' })

    const fileNameMap = {
      contract: 'Hợp đồng.docx',
      appendix: 'Phụ lục hợp đồng.docx',
      inspection: 'Biên bản nghiệm thu.docx',
    }

    const uploadResult = await this.uploadService.uploadSingleWord({
      originalname: fileNameMap[type],
      buffer: docxBuffer,
    })

    const fileUrl = uploadResult[0]
    console.log('fileUrl', fileUrl)

    // Cập nhật DB theo loại
    if (type === 'contract') {
      await this.contractRepo.update({ id: referenceId }, { fileUrl })
    } else if (type === 'appendix') {
      await this.contractAppendixRepo.update({ id: referenceId }, { fileUrl })
    } else if (type === 'inspection') {
      await this.contractInspectionRepo.update({ id: referenceId }, { fileUrl })
    }

    return { buffer: docxBuffer, fileUrl }
  }

  private findParagraphContaining(text: string, documentXml: string): string | null {
    const paraRegex = /<w:p[\s\S]*?<\/w:p>/g
    const allParas = documentXml.match(paraRegex) || []

    for (const para of allParas) {
      const innerText = para.replace(/<[^>]+>/g, '')
      if (innerText.includes(text)) {
        return para
      }
    }

    return null
  }
  private cleanCell(cell: any): string {
    return this.smartSpacing(String(cell ?? ''))
      .replace(/\n|\r|\t/g, ' ')
      .replace(/\s{2,}/g, ' ')
      .trim()
  }
  private smartSpacing(text: string): string {
    return text
      .replace(/([a-zA-Z])(\d)/g, '$1 $2')
      .replace(/(\d)([a-zA-Z])/g, '$1 $2')
      .replace(/([^\s])([-\/])([^\s])/g, '$1 $2 $3')
  }

  async downloadTemplatePO(templateId: string, poId: string): Promise<{ buffer: Buffer; fileUrl: string }> {
    const temp = await this.templateRepo.findOne({ where: { id: templateId } })
    if (!temp) throw new Error('Template not found')

    const res = await axios.get(temp.link, { responseType: 'arraybuffer' })
    const zip = new PizZip(res.data)

    const templateConfig = AllTemplates[temp.type]
    const keys = Object.keys(templateConfig)
    const tableKeys = ['PO_PR_LIST', 'PO_CONTRACT_LIST']
    const mapOfTableBody: Record<string, string> = {}

    const results = await Promise.all(
      keys.map(async (key) => {
        const sql = templateConfig[key].replace('?', `'${poId}'`)
        const res = await this.entityManager.query(sql)
        return { key, res }
      }),
    )

    const dataToReplace: Record<string, any> = {}

    for (const { key, res } of results) {
      if (tableKeys.includes(key)) {
        const headers = Object.keys(res[0] || {})
        const rows = res.map((r) => headers.map((h) => r[h]))

        const tableHtml = `
          <table border="1" style="border-collapse: collapse; font-family: 'Times New Roman'; font-size: 10pt; width: 100%; table-layout: fixed;">
            <thead>
              <tr>
                ${headers
                  .map(
                    (h) =>
                      `<th style="padding: 3px; border: 1px solid black; text-align: center; white-space: normal; word-break: break-word;">${h}</th>`,
                  )
                  .join('')}
              </tr>
            </thead>
            <tbody>
              ${rows
                .map(
                  (row) =>
                    `<tr>${row
                      .map(
                        (cell) =>
                          `<td style="padding: 3px; border: 1px solid black; text-align: left; white-space: normal; word-break: break-word;">${
                            cell ?? ''
                          }</td>`,
                      )
                      .join('')}</tr>`,
                )
                .join('')}
            </tbody>
          </table>
        `

        const tableDocxBuffer = await htmlToDocx(tableHtml)
        const tableZip = new PizZip(tableDocxBuffer)
        const tableXml = tableZip.file('word/document.xml')?.asText()
        let tableBody = tableXml?.match(/<w:body[^>]*>([\s\S]*?)<\/w:body>/)?.[1] || ''
        tableBody = tableBody.replace(/<w:sectPr[\s\S]*?<\/w:sectPr>/g, '')

        let count = 0
        tableBody = tableBody.replace(/<w:tblGrid[\s\S]*?<\/w:tblGrid>/g, (match) => {
          return count++ === 0 ? match : ''
        })

        dataToReplace[key] = `<?${key}?>`
        mapOfTableBody[key] = tableBody
      } else {
        const row = Array.isArray(res) && res.length > 0 ? res[0] : {}
        Object.entries(row).forEach(([k, v]) => (dataToReplace[k] = v))
      }
    }

    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
      delimiters: { start: '<?', end: '?>' },
    })

    doc.setData(dataToReplace)
    doc.render()

    const renderedBuffer = doc.getZip().generate({ type: 'nodebuffer' })
    const zip2 = new PizZip(renderedBuffer)
    let documentXml = zip2.file('word/document.xml')?.asText()
    if (!documentXml) throw new Error('Không đọc được document.xml')

    const decodeXml = (s: string) => s.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&')
    documentXml = decodeXml(documentXml)

    for (const key of tableKeys) {
      let searchText = ''
      if (key === 'PO_PR_LIST') searchText = 'Thời hạn thanh toán:'
      else if (key === 'PO_PR_LIST') searchText = 'Thông tin hàng hóa / dịch vụ:'

      const paraMatch = this.findParagraphContaining(searchText, documentXml)
      if (!paraMatch) throw new Error(`Không tìm thấy đoạn <w:p> chứa "${searchText}"`)

      const insertion = paraMatch + mapOfTableBody[key]
      documentXml = documentXml.replace(paraMatch, insertion)
    }

    zip2.file('word/document.xml', documentXml)
    const docxBuffer = zip2.generate({ type: 'nodebuffer' })

    const uploadResult = await this.uploadService.uploadSingleWord({
      originalname: 'Danh sách PO.docx',
      buffer: docxBuffer,
    })

    const fileUrl = uploadResult[0]
    console.log('fileUrl', fileUrl)
    await this.contractRepo.update({ id: poId }, { fileUrl })

    return { buffer: docxBuffer, fileUrl: fileUrl }
  }

  async loadKeysFromTemplateUrl(templateId: string): Promise<string[]> {
    const temp = await this.templateRepo.findOne({ where: { id: templateId } })
    if (!temp || !temp.link) throw new Error('Template not found or link is missing')

    const res = await axios.get(temp.link, { responseType: 'arraybuffer' })

    const buffer = Buffer.from(res.data)

    const result = await mammoth.extractRawText({ buffer })

    const matches = result.value.match(/<\?([A-Z0-9_]+)\?>/g) || []

    const keys = matches.map((m) => m.replace(/[<??>]/g, ''))

    return Array.from(new Set(keys))
  }
}
