import { <PERSON>ti<PERSON>, Column, Jo<PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CompanyEntity } from './company.entity'
import { FlowApproveEntity } from './flowApprove.entity'
import { DepartmentEntity } from './department.entity'
import { FlowApproveBaseEntity } from './flowApproveBase.entity'
import { BlockEntity } from './block.entity'
import { PartEntity } from './part.entity'

@Entity({ name: 'flow_approve_detail' })
export class FlowApproveDetailEntity extends BaseEntity {
  // Cấp bậc  (1-2-3)
  @Column({ nullable: false })
  level: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  possisonCode: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  employeePositionId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  flowApproveId: string
  @ManyToOne(() => FlowApproveEntity, (p) => p.flowApprovieDetails)
  @JoinColumn({ name: 'flowApproveId', referencedColumnName: 'id' })
  flowApprove: Promise<FlowApproveEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  flowBaseId: string
  @ManyToOne(() => FlowApproveBaseEntity, (p) => p.flowApprovieDetails)
  @JoinColumn({ name: 'flowBaseId', referencedColumnName: 'id' })
  flowBase: Promise<FlowApproveBaseEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  roleCompanyId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  departmentId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  blockId: string

  /** Bộ phận */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  partId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purGrId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purOrgId: string

  @Column({
    nullable: true,
    default: false,
  })
  mustApproveAll: boolean
}
