import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTableSupplierCompany1749124981605 implements MigrationInterface {
  name = 'AddTableSupplierCompany1749124981605'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "supplier_company" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_3e3ed41fe5a4f5ea801e2e55dad" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_4257099d371c22f2c9ca92b798c" DEFAULT 0, "companyId" uniqueidentifier, "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "score" int CONSTRAINT "DF_dbacf0a59f2fd019373aa70fc27" DEFAULT 0, "status" varchar(50) NOT NULL, "supplierId" uniqueidentifier NOT NULL, CONSTRAINT "PK_3e3ed41fe5a4f5ea801e2e55dad" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `ALTER TABLE "supplier_company" ADD CONSTRAINT "FK_e538041468907b0e1e36c01ef99" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "supplier_company" ADD CONSTRAINT "FK_a0e7dafb871a85e78b155e56e4e" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier_company" DROP CONSTRAINT "FK_a0e7dafb871a85e78b155e56e4e"`)
    await queryRunner.query(`ALTER TABLE "supplier_company" DROP CONSTRAINT "FK_e538041468907b0e1e36c01ef99"`)
    await queryRunner.query(`DROP TABLE "supplier_company"`)
  }
}
