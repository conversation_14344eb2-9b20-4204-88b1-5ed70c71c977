import { MigrationInterface, QueryRunner } from 'typeorm'

export class Guide1750930662211 implements MigrationInterface {
  name = 'Guide1750930662211'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "guide" ADD "guideType" nvarchar(max) NOT NULL`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "guide" DROP COLUMN "guideType"`)
  }
}
