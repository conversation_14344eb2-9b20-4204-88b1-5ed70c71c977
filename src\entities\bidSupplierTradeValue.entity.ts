import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { BidSupplierEntity } from './bidSupplier.entity'
import { BidTradeEntity } from './bidTrade.entity'

@Entity('bid_supplier_trade_value')
export class BidSupplierTradeValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidSupplierId: string
  @ManyToOne(() => BidSupplierEntity, (p) => p.bidSupplierTradeValue)
  @JoinColumn({ name: 'bidSupplierId', referencedColumnName: 'id' })
  bidSupplier: Promise<BidSupplierEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidTradeId: string
  @ManyToOne(() => BidTradeEntity, (p) => p.bidSupplierTradeValue)
  @JoinColumn({ name: 'bidTradeId', referencedColumnName: 'id' })
  bidTrade: Promise<BidTradeEntity>
}
