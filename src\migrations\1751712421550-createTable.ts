import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateTable1751712421550 implements MigrationInterface {
  name = 'CreateTable1751712421550'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "request_quote_detail" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_140aeeba6c2796226c46a96a539" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_f790471b86a0c0decfc87292086" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "itemNo" varchar(255), "requestQuoteId" uniqueidentifier, "prItemId" uniqueidentifier, "category" varchar(1), "materialId" uniqueidentifier, "materialGroupId" uniqueidentifier, "orderCode" varchar(50), "orderName" varchar(50), "assetCode" nvarchar(max), "assetDesc" nvarchar(max), "plantId" uniqueidentifier, "sloc" nvarchar(max), "quantity" float CONSTRAINT "DF_6a04878f0cb8db5b609710b2b63" DEFAULT 0, "deliveryDate" datetime, "shortText" varchar(1000), CONSTRAINT "PK_140aeeba6c2796226c46a96a539" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "request_quote_fee" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_a4d1855a943c1d054b430a2998c" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_cd82795af9fc4e81f5057138e22" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "requestQuoteId" uniqueidentifier, "shipmentFeeConditionsId" uniqueidentifier NOT NULL, "shipmentFeeConditionsListId" uniqueidentifier NOT NULL, "shipmentFeeConditionsToListId" uniqueidentifier NOT NULL, CONSTRAINT "PK_a4d1855a943c1d054b430a2998c" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "request_quote_supplier" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_edb2db9fd7ac77104befd465290" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_a6bff559b5ef8a17e3193f06704" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "requestQuoteId" uniqueidentifier, "supplierId" uniqueidentifier, CONSTRAINT "PK_edb2db9fd7ac77104befd465290" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE TABLE "request_quote" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_c88873e9368e068e3220d2e69be" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_2bf9993bc1fe2e9fbcfcdc509d7" DEFAULT 0, "companyId" uniqueidentifier, "purchasingOrgId" uniqueidentifier, "purchasingGroupId" uniqueidentifier, "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "name" nvarchar(1000), "employeeId" uniqueidentifier, "purpose" nvarchar(10), "typeQuote" nvarchar(10), "quotationPeriod" nvarchar(10), "timeStartReceiving" datetime, "timeEndReceiving" datetime, "quantity" int CONSTRAINT "DF_1b949cf89ac79932b3da4d4b12f" DEFAULT 0, "timeConfirmParticipationQuotation" datetime, "description" nvarchar(max), "referenceQuote" nvarchar(10), "prId" uniqueidentifier, "shipmentPlanId" uniqueidentifier, "lstIncotermId" varchar(255), "lstPaymentTermId" varchar(255), "quotationForm" nvarchar(10), "status" nvarchar(50), "quotationValidity" int, CONSTRAINT "PK_c88873e9368e068e3220d2e69be" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(`ALTER TABLE "media_file" ADD "requestQuoteId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "request_quote_detail" ADD CONSTRAINT "FK_6f813eca62746509370f45402e6" FOREIGN KEY ("requestQuoteId") REFERENCES "request_quote"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_detail" ADD CONSTRAINT "FK_ab5e2f23b44a5747caffddab6de" FOREIGN KEY ("prItemId") REFERENCES "pr_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_detail" ADD CONSTRAINT "FK_87e8e085d101e3291f760720c3c" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_detail" ADD CONSTRAINT "FK_3d18a373d6ec2c114c9176ea007" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_detail" ADD CONSTRAINT "FK_f6e064ea376aa685e500e481188" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_fee" ADD CONSTRAINT "FK_1ccc2083e9b189a4cc21d4ce8c2" FOREIGN KEY ("requestQuoteId") REFERENCES "request_quote"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_fee" ADD CONSTRAINT "FK_1d3a1063b2a6c456a09f9de80e8" FOREIGN KEY ("shipmentFeeConditionsId") REFERENCES "shipment_fee_conditions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_fee" ADD CONSTRAINT "FK_4135ac52c6cb4b535b0e0245500" FOREIGN KEY ("shipmentFeeConditionsListId") REFERENCES "shipment_fee_conditions_list"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_fee" ADD CONSTRAINT "FK_d18a3e9ab08af307fbb7c619750" FOREIGN KEY ("shipmentFeeConditionsToListId") REFERENCES "shipment_fee_conditions_to_list"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_supplier" ADD CONSTRAINT "FK_14c692ddbd4710b5b3705f75eb5" FOREIGN KEY ("requestQuoteId") REFERENCES "request_quote"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote_supplier" ADD CONSTRAINT "FK_f3a968dfd854dfcecdc748aa731" FOREIGN KEY ("supplierId") REFERENCES "supplier"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote" ADD CONSTRAINT "FK_0f03de8bf398ebee75d568bbf5b" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote" ADD CONSTRAINT "FK_7382972dcd3cde6535c4ae83938" FOREIGN KEY ("purchasingOrgId") REFERENCES "purchasing_org"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote" ADD CONSTRAINT "FK_5e0e77dd918deffdb3e7fc2a946" FOREIGN KEY ("purchasingGroupId") REFERENCES "purchasing_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote" ADD CONSTRAINT "FK_c1970cce0640c0b071dfb6c603e" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote" ADD CONSTRAINT "FK_8afcf8c07e5f028784b4494d034" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "request_quote" ADD CONSTRAINT "FK_0a527e4adb161cf82fc521617c3" FOREIGN KEY ("shipmentPlanId") REFERENCES "shipment_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "media_file" ADD CONSTRAINT "FK_1674c103d7834550f98ec9d2ce6" FOREIGN KEY ("requestQuoteId") REFERENCES "request_quote"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "media_file" DROP CONSTRAINT "FK_1674c103d7834550f98ec9d2ce6"`)
    await queryRunner.query(`ALTER TABLE "request_quote" DROP CONSTRAINT "FK_0a527e4adb161cf82fc521617c3"`)
    await queryRunner.query(`ALTER TABLE "request_quote" DROP CONSTRAINT "FK_8afcf8c07e5f028784b4494d034"`)
    await queryRunner.query(`ALTER TABLE "request_quote" DROP CONSTRAINT "FK_c1970cce0640c0b071dfb6c603e"`)
    await queryRunner.query(`ALTER TABLE "request_quote" DROP CONSTRAINT "FK_5e0e77dd918deffdb3e7fc2a946"`)
    await queryRunner.query(`ALTER TABLE "request_quote" DROP CONSTRAINT "FK_7382972dcd3cde6535c4ae83938"`)
    await queryRunner.query(`ALTER TABLE "request_quote" DROP CONSTRAINT "FK_0f03de8bf398ebee75d568bbf5b"`)
    await queryRunner.query(`ALTER TABLE "request_quote_supplier" DROP CONSTRAINT "FK_f3a968dfd854dfcecdc748aa731"`)
    await queryRunner.query(`ALTER TABLE "request_quote_supplier" DROP CONSTRAINT "FK_14c692ddbd4710b5b3705f75eb5"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "FK_d18a3e9ab08af307fbb7c619750"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "FK_4135ac52c6cb4b535b0e0245500"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "FK_1d3a1063b2a6c456a09f9de80e8"`)
    await queryRunner.query(`ALTER TABLE "request_quote_fee" DROP CONSTRAINT "FK_1ccc2083e9b189a4cc21d4ce8c2"`)
    await queryRunner.query(`ALTER TABLE "request_quote_detail" DROP CONSTRAINT "FK_f6e064ea376aa685e500e481188"`)
    await queryRunner.query(`ALTER TABLE "request_quote_detail" DROP CONSTRAINT "FK_3d18a373d6ec2c114c9176ea007"`)
    await queryRunner.query(`ALTER TABLE "request_quote_detail" DROP CONSTRAINT "FK_87e8e085d101e3291f760720c3c"`)
    await queryRunner.query(`ALTER TABLE "request_quote_detail" DROP CONSTRAINT "FK_ab5e2f23b44a5747caffddab6de"`)
    await queryRunner.query(`ALTER TABLE "request_quote_detail" DROP CONSTRAINT "FK_6f813eca62746509370f45402e6"`)
    await queryRunner.query(`ALTER TABLE "media_file" DROP COLUMN "requestQuoteId"`)
    await queryRunner.query(`DROP TABLE "request_quote"`)
    await queryRunner.query(`DROP TABLE "request_quote_supplier"`)
    await queryRunner.query(`DROP TABLE "request_quote_fee"`)
    await queryRunner.query(`DROP TABLE "request_quote_detail"`)
  }
}
