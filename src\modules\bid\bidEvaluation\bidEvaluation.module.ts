import { Module } from '@nestjs/common'
import { BidEvaluationController } from './bidEvaluation.controller'
import { BidEvaluationService } from './bidEvaluation.service'
import { EmailModule } from '../../email/email.module'
import {
  BidRepository,
  BidEmployeeAccessRepository,
  BidSupplierRepository,
  BidPrItemRepository,
  BidSupplierItemRepository,
} from '../../../repositories'
import { TypeOrmExModule } from '../../../typeorm'
import { FlowApproveModule } from '../../flowApprove/flowApprove.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BidRepository,
      BidEmployeeAccessRepository,
      BidSupplierRepository,
      BidPrItemRepository,
      BidSupplierItemRepository,
    ]),
    EmailModule,
    FlowApproveModule,
  ],
  controllers: [BidEvaluationController],
  providers: [BidEvaluationService],
  exports: [BidEvaluationService],
})
export class BidEvaluationModule {}
