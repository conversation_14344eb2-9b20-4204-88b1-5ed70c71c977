import { BaseEntity } from './base.entity'
import { Entity, Column } from 'typeorm'

@Entity('purchasing_plan_scenario')
export class PurchasingPlanScenarioEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string
}
