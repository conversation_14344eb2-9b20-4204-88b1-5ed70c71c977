import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { BlockCreateDto } from './blockCreate.dto'

export class BlockUpdateDto extends BlockCreateDto {
  @ApiProperty({ description: 'Id' })
  @IsNotEmpty()
  @IsString()
  id: string
}

export class AddDepartmentBlockDto {
  @ApiProperty({ description: 'Id công ty' })
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiProperty({ description: 'Id phòng ban' })
  @IsOptional()
  departmentIds: string[]
}
