import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { BankBranchService } from './bankBranch.service'
import { JwtAuthGuard } from '../common/guards'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { BankBranchCreateDto, BankBranchImportExcelDto, BankBranchUpdateDto } from './dto'

@ApiBearerAuth()
@ApiTags('Bank Branch')
@Controller('bank_branch')
export class BankBranchController {
  constructor(private readonly service: BankBranchService) {}

  @ApiOperation({ summary: '<PERSON><PERSON>y danh sách Chi nhánh/Phòng giao dịch Ngân hàng' })
  @UseGuards(JwtAuthGuard)
  @Post('find')
  public async find() {
    return await this.service.find()
  }

  @ApiOperation({ summary: 'Danh sách Chi nhánh/Phòng giao dịch Ngân hàng phân trang' })
  @UseGuards(JwtAuthGuard)
  @Post('pagination')
  public async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới một Chi nhánh/Phòng giao dịch Ngân hàng với dữ liệu được cung cấp.' })
  @UseGuards(JwtAuthGuard)
  @Post('create_data')
  async createData(@CurrentUser() user: UserDto, @Body() data: BankBranchCreateDto) {
    return await this.service.createData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật thông tin của Chi nhánh/Phòng giao dịch Ngân hàng với dữ liệu được cung cấp.' })
  @UseGuards(JwtAuthGuard)
  @Post('update_data')
  async updateData(@CurrentUser() user: UserDto, @Body() data: BankBranchUpdateDto) {
    return await this.service.updateData(user, data)
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái kích hoạt của Chi nhánh/Phòng giao dịch Ngân hàng.' })
  @UseGuards(JwtAuthGuard)
  @Post('update_active')
  async updateActive(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateActive(user, data.id)
  }

  @ApiOperation({ summary: 'Tạo mới một Chi nhánh/Phòng giao dịch Ngân hàng với danh sách dữ liệu danh được cung cấp.' })
  @UseGuards(JwtAuthGuard)
  @Post('import_data')
  async importData(@CurrentUser() user: UserDto, @Body() data: BankBranchImportExcelDto[]) {
    return await this.service.importData(user, data)
  }

  @ApiOperation({ summary: 'Load data select' })
  @Post('load_data_select')
  async loadDataSelect(@CurrentUser() user: UserDto, @Body() data: { countryId: string; regionId: string; bankId: string }) {
    return await this.service.loadDataSelect(user, data)
  }

  @ApiOperation({ summary: 'Hàm tìm kiếm chi tiết ' })
  @UseGuards(JwtAuthGuard)
  @Post('find_detail')
  async findDetail(@Body() data: FilterOneDto) {
    return this.service.findDetail(data)
  }
}
