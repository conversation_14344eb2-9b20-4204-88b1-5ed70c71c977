import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { MasterConditionTypeEntity } from './masterConditionType.entity'
import { SchemaConfigEntity } from './schemaConfig.entity'
import { PoPriceListEntity } from './poPriceList.entity'
import { PoProductPriceListEntity } from './poProductPriceList.entity'
/**Tiền tệ */
@Entity('procedure')
export class ProcedureEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @OneToMany(() => MasterConditionTypeEntity, (p) => p.procedure)
  masterConditionType: Promise<MasterConditionTypeEntity[]>

  @OneToMany(() => SchemaConfigEntity, (p) => p.procedure)
  schemaConfig: Promise<SchemaConfigEntity[]>

  @OneToMany(() => PoPriceListEntity, (p) => p.procedure)
  poPriceList: Promise<PoPriceListEntity[]>

  @OneToMany(() => PoProductPriceListEntity, (p) => p.procedure)
  poProductPriceList: Promise<PoProductPriceListEntity[]>
}
