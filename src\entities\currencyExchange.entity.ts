import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { CurrencyEntity } from './currency.entity'

@Entity('currency_exchange')
export class CurrencyExchangeEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  currencyId: string
  @ManyToOne(() => CurrencyEntity, (p) => p.currencyExchange)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  /** exchange */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 3, default: 0 })
  exchange: number

  /** Nguồn lấy thông tin */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  source: string

  /** ngày lấy thông tin */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  date: Date
}
