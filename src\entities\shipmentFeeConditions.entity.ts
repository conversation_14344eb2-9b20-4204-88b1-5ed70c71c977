import { Column, Entity, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ShipmentFeeConditionsToListEntity } from './shipmentFeeConditionsToList.entity'
import { RequestQuoteFeeEntity } from './requestQuoteFee.entity'

@Entity('shipment_fee_conditions')
export class ShipmentFeeConditionsEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string
  /* name condition type */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  name: string
  /* description condition type */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string
  /* có liên quan đến NCC boolean */

  @Column({
    nullable: false,
    default: false,
  })
  isRelatedToSupplier: boolean

  @OneToMany(() => ShipmentFeeConditionsToListEntity, (p) => p.shipmentFeeConditions)
  shipmentFeeConditionsToList: Promise<ShipmentFeeConditionsToListEntity[]>

  @OneToMany(() => RequestQuoteFeeEntity, (p) => p.shipmentFeeConditions)
  requestQuoteFees: Promise<RequestQuoteFeeEntity[]>
}
