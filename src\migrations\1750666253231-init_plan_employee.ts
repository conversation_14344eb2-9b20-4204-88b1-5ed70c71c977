import { MigrationInterface, QueryRunner } from 'typeorm'

export class InitPlanEmployee1750666253231 implements MigrationInterface {
  name = 'InitPlanEmployee1750666253231'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "plan_site_assessment_employee" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_95ab73396298f071991ec5569be" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_1868ca812cae36d458f60e16498" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "employeeId" uniqueidentifier NOT NULL, "planSiteAssessmentId" uniqueidentifier NOT NULL, "orgBlockId" varchar(255), "orgDepartmentId" varchar(255), "orgPartId" varchar(255), "orgPositionId" varchar(255), "isLeader" bit NOT NULL CONSTRAINT "DF_535324e8d4a390ee519332c98e9" DEFAULT 0, CONSTRAINT "PK_95ab73396298f071991ec5569be" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment_child" ADD "supplierScoreEvaluation" int`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment_child" ADD "employeeEvaluationId" varchar(255)`)
    await queryRunner.query(
      `ALTER TABLE "criteria_site_assessment" ADD "weight" float NOT NULL CONSTRAINT "DF_ffd71b98cfecba266371b40d259" DEFAULT 0`,
    )
    await queryRunner.query(
      `ALTER TABLE "template_site_assessment_item" ADD "weight" float NOT NULL CONSTRAINT "DF_d0b8f7b7a38948ca78241bd0e26" DEFAULT 0`,
    )
    await queryRunner.query(`ALTER TABLE "media_file" ADD "criteriaSiteAssessmentChildId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "plan_site_assessment_employee" ADD CONSTRAINT "FK_c9e499d67ef0b81f31ffffc1b0d" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "plan_site_assessment_employee" ADD CONSTRAINT "FK_54d1f6b545e45c32adb5d085e0c" FOREIGN KEY ("planSiteAssessmentId") REFERENCES "plan_site_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "media_file" ADD CONSTRAINT "FK_30a2c51145cc83406a89492f544" FOREIGN KEY ("criteriaSiteAssessmentChildId") REFERENCES "criteria_site_assessment_child"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "media_file" DROP CONSTRAINT "FK_30a2c51145cc83406a89492f544"`)
    await queryRunner.query(`ALTER TABLE "plan_site_assessment_employee" DROP CONSTRAINT "FK_54d1f6b545e45c32adb5d085e0c"`)
    await queryRunner.query(`ALTER TABLE "plan_site_assessment_employee" DROP CONSTRAINT "FK_c9e499d67ef0b81f31ffffc1b0d"`)
    await queryRunner.query(`ALTER TABLE "media_file" DROP COLUMN "criteriaSiteAssessmentChildId"`)
    await queryRunner.query(`ALTER TABLE "template_site_assessment_item" DROP CONSTRAINT "DF_d0b8f7b7a38948ca78241bd0e26"`)
    await queryRunner.query(`ALTER TABLE "template_site_assessment_item" DROP COLUMN "weight"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP CONSTRAINT "DF_ffd71b98cfecba266371b40d259"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment" DROP COLUMN "weight"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment_child" DROP COLUMN "employeeEvaluationId"`)
    await queryRunner.query(`ALTER TABLE "criteria_site_assessment_child" DROP COLUMN "supplierScoreEvaluation"`)
    await queryRunner.query(`DROP TABLE "plan_site_assessment_employee"`)
  }
}
