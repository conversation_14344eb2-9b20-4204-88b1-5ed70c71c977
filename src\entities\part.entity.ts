import { Entity, Column, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { TicketEvaluationKpiEmployeeEntity } from './ticketEvaluationKpiEmployee.entity'
import { PlanSiteAssessmentEmployeeEntity } from './planSiteAssessmentEmployee.entity'

/** <PERSON><PERSON>u hình bộ phận */
@Entity('part')
export class PartEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** <PERSON>ô tả khối */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => EmployeeEntity, (p) => p.part)
  employee: Promise<EmployeeEntity[]>

  /** ds phiếu đánh giá <PERSON> và NV */
  @OneToMany(() => TicketEvaluationKpiEmployeeEntity, (p) => p.part)
  ticketEvaluationKpiEmployees: Promise<TicketEvaluationKpiEmployeeEntity[]>
}
