import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { KpiCompanyEntity } from './kpiCompany.entity'
import { KpiDetailEntity } from './kpiDetail.entity'
import { KpiPositionEntity } from './kpiPosition.entity'
import { TicketEvaluationKpiEntity } from './ticketEvaluationKpi.entity'
import { CompanyEntity } from './company.entity'
import { KpiScaleEntity } from './kpiScale.entity'
import { KpiPermissionEntity } from './kpiPermission.entity'
import { KpiPermissionPositionEntity } from './kpiPermissionPosition.entity'

/**  kpi mua hàng */
@Entity('kpi')
export class KpiEntity extends BaseEntity {
  /** Mã  kpi */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Tên  kpi */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string

  /** loại  kpi enumData: PurchaseKpi*/
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  /** trạng thái  enumData: KpiStatus*/
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Thời gian ấp dụng từ */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  dateFrom: Date

  /** Mô tả  Order */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /** id công ty */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string
  @ManyToOne(() => CompanyEntity, (p) => p.kpis)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  /** ds kpi công ty */
  @OneToMany(() => KpiCompanyEntity, (p) => p.kpi)
  kpiCompanys: Promise<KpiCompanyEntity[]>

  /** ds kpi template */
  @OneToMany(() => KpiDetailEntity, (p) => p.kpi)
  kpiDetails: Promise<KpiDetailEntity[]>

  /** ds kpi vị trí */
  @OneToMany(() => KpiPositionEntity, (p) => p.kpi)
  kpiPoitions: Promise<KpiPositionEntity[]>

  /** ds phiếu đánh giá kpi */
  @OneToMany(() => TicketEvaluationKpiEntity, (p) => p.kpi)
  ticketEvaluationKpis: Promise<TicketEvaluationKpiEntity[]>

  /** ds thang điểm */
  @OneToMany(() => KpiScaleEntity, (p) => p.kpi)
  kpiScales: Promise<KpiScaleEntity[]>

  /** ds phân quyền */
  @OneToMany(() => KpiPermissionEntity, (p) => p.kpi)
  kpiPermissions: Promise<KpiPermissionEntity[]>

  /** ds phân quyền vị trí */
  @OneToMany(() => KpiPermissionPositionEntity, (p) => p.kpi)
  kpiPermissionPoitions: Promise<KpiPermissionPositionEntity[]>
}
