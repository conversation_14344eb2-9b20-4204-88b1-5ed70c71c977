import { MigrationInterface, QueryRunner } from "typeorm";

export class Batch1750228342559 implements MigrationInterface {
    name = 'Batch1750228342559'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" ADD "batch" varchar(500)`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" ADD "unitId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" ADD CONSTRAINT "FK_12e84a763f8dee544e5c2aaa9e5" FOREIGN KEY ("unitId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" DROP CONSTRAINT "FK_12e84a763f8dee544e5c2aaa9e5"`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" DROP COLUMN "unitId"`);
        await queryRunner.query(`ALTER TABLE "pr_item_compoment" DROP COLUMN "batch"`);
    }

}
