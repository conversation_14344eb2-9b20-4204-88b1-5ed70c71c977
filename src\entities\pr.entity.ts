import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { AuctionEntity } from './auction.entity'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { BidPrEntity } from './bidPr.entity'
import { BudgetReceiptEntity } from './budgetReceipt.entity'
import { BusinessPlanColValueEntity } from './businessPlanColValue.entity'
import { ContractEntity } from './contract.entity'
import { DepartmentEntity } from './department.entity'
import { EmployeeEntity } from './employee.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { MediaFileEntity } from './mediaFile.entity'
import { OfferEntity } from './offer.entity'
import { PlantEntity } from './plant.entity'
import { PoPrEntity } from './poPr.entity'
import { PrHistoryEntity } from './prHistory.entity'
import { PrItemEntity } from './prItem.entity'
import { PrItemChildEntity } from './prItemChild.entity'
import { PrItemCompomentEntity } from './prItemComponent.entity'
import { PurchasingGroupEntity } from './purchasingGroup.entity'
import { PurchasingOrgEntity } from './purchasingOrg.entity'
import { RequestQuoteEntity } from './requestQuote.entity'
import { RoundUpContEntity } from './roundUpCont.entity'

/** YCMH */

@Index('IX_PR_COMPOSITE', ['prType', 'status', 'createdAt'])
@Entity('pr')
export class PrEntity extends BaseEntity {
  /** Nguồn tạo enum PRSouceType */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  sourceType: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string

  /** Mã SAP Code */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  sapCode: string

  /** số lượng của pr */
  @Column({
    nullable: true,
  })
  quantity: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  /** Loại giao dịch */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  ztCode: string

  /** Loại chứng từ */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  zType: string

  /** Cấp duyệt PR */
  @Column({
    type: 'varchar',
    length: 5,
    nullable: true,
  })
  zRelease: string

  /** Type để phân biệt các loại PR */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  prType: string

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  budgetStatus: string

  @Column({
    nullable: true,
    type: 'datetime',
  })
  createdTimeAt: Date

  /** Cho phép tạo gói thầu, cập nhật false, nếu các PrItem đã tạo thầu hết */
  @Column({
    nullable: false,
    default: true,
  })
  isAllowBid: boolean

  /** Header note */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  headerNote: string

  /** mục đích sử dụng */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  uses: string

  /** Bộ phận yêu cầu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  departmentRequired: string

  /** Bộ phận yêu cầu */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  departmentId: string
  @ManyToOne(() => DepartmentEntity, (p) => p.pr)
  @JoinColumn({ name: 'departmentId', referencedColumnName: 'id' })
  department: Promise<DepartmentEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prParentId: string

  /** Người yêu cầu */
  @Column({
    length: 'max',
    nullable: true,
  })
  requisitionerName: string

  /** Id Của plant */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  requisitionerId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.pr)
  @JoinColumn({ name: 'requisitionerId', referencedColumnName: 'id' })
  requisitioner: Promise<EmployeeEntity>

  /** Deletion indicator */
  @Column({
    type: 'varchar',
    length: 1,
    nullable: true,
  })
  deletionIndicator: string

  /** Ngân sách */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  budget: number

  /** Là PR tổng hợp */
  @Column({
    nullable: false,
    default: false,
  })
  isParent: boolean

  @OneToMany(() => PrItemEntity, (p) => p.pr)
  prItems: Promise<PrItemEntity[]>

  @OneToMany(() => PrHistoryEntity, (p) => p.pr)
  histories: Promise<PrHistoryEntity[]>

  /** Các gói thầu tổng và gói thầu chi tiết của Pr */
  @OneToMany(() => BidEntity, (p) => p.pr)
  bids: Promise<BidEntity[]>

  /** Danh sách nhà cung cấp đấu thầu */
  @OneToMany(() => BidPrEntity, (p) => p.pr)
  bidPr: Promise<BidPrEntity[]>

  @OneToMany(() => AuctionEntity, (p) => p.pr)
  auctions: Promise<AuctionEntity[]>

  @OneToMany(() => OfferEntity, (p) => p.pr)
  offer: Promise<OfferEntity[]>

  /** Id Của plant */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @ManyToOne(() => PlantEntity, (p) => p.pr)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /** Chỉ khi duyệt thì biến này mới == true , thì người tạo không được chỉnh */
  @Column({
    nullable: false,
    default: false,
  })
  isAlllowEdit: boolean

  @OneToMany(() => PrItemCompomentEntity, (p) => p.pr)
  prItemCompoments: Promise<PrItemCompomentEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.prs)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  /** Danh sách PR thành phần của PR tổng */
  @Column({
    length: 'max',
    nullable: true,
  })
  lstPrChild: string

  /** Duyệt hết các cấp thì biến này = true */
  @Column({
    nullable: true,
    default: false,
  })
  isApprovedDone: boolean

  /** Giá trị*/
  @Column({ type: 'bigint', nullable: true })
  totalValue: number

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /** Lock PR không chỉnh sửa thì biến này = true */
  @Column({
    nullable: true,
    default: false,
  })
  isLockAllocation: boolean

  /** Lock PR không chỉnh sửa thì biến này = true */
  @Column({
    nullable: true,
    default: false,
  })
  isSaveDraft: boolean

  @OneToMany(() => ContractEntity, (p) => p.pr)
  contracts: Promise<ContractEntity[]>

  /** PR này là PR sinh ra từ phân bổ PR */
  @Column({
    nullable: true,
    default: false,
  })
  isAllocation: boolean

  /** Danh sách phiếu điều chỉnh ngân sách */
  @OneToMany(() => BudgetReceiptEntity, (p) => p.pr)
  budgetReceipts: Promise<BudgetReceiptEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string
  @ManyToOne(() => PurchasingOrgEntity, (p) => p.prs)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string
  @ManyToOne(() => PurchasingGroupEntity, (p) => p.prs)
  @JoinColumn({ name: 'purchasingGroupId', referencedColumnName: 'id' })
  purchasingGroup: Promise<PurchasingGroupEntity>

  @OneToMany(() => BusinessPlanColValueEntity, (p) => p.pr)
  businessPlanColValues: Promise<BusinessPlanColValueEntity[]>

  /** Là PR sinh ra từ phân bổ */
  @Column({
    nullable: false,
    default: false,
  })
  isCreateAllocation: boolean

  /** Nguồn tham chiếu  */
  @Column({
    nullable: true,
    default: false,
  })
  isReferenceSource: boolean

  /** Ngân sách thiếu */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 2, default: 0 })
  budgetMissing: number

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstExternalMaterialGroupId: string

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstPlantId: string

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lsPrType: string

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstDepartmentId: string

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstRequisitionerId: string

  @OneToMany(() => PrItemChildEntity, (p) => p.pr)
  prItemChilds: Promise<PrItemChildEntity[]>

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstFund: string

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstFundGroup: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  legalEntity: string

  @OneToMany(() => MediaFileEntity, (p) => p.pr)
  mediaFiles: Promise<MediaFileEntity[]>

  @OneToMany(() => RoundUpContEntity, (p) => p.pr)
  roundUpCont: Promise<RoundUpContEntity[]>

  @OneToMany(() => RequestQuoteEntity, (p) => p.pr)
  requestQuotes: Promise<RequestQuoteEntity[]>

  @OneToMany(() => PoPrEntity, (p) => p.pr)
  poPr: Promise<PoPrEntity[]>
}
