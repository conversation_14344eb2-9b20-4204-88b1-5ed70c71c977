import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnContractItem1752225830982 implements MigrationInterface {
  name = 'AddColumnContractItem1752225830982'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_item" ADD "materialStorageLocationId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "contract_item" ADD CONSTRAINT "FK_6b18a29f7a58abf1d37e6f7016f" FOREIGN KEY ("materialStorageLocationId") REFERENCES "material_storage_location"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_item" DROP CONSTRAINT "FK_6b18a29f7a58abf1d37e6f7016f"`)
    await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "materialStorageLocationId"`)
  }
}
