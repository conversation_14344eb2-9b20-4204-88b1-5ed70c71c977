import { Entity, Column } from 'typeorm'
import { BaseEntity } from './base.entity'

@Entity('action_log')
export class ActionLogEntity extends BaseEntity {
  /**Người tạo */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: false,
  })
  createdByName: string

  /** Trạng thái hiện tại */
  @Column({
    type: 'nvarchar',
    length: 150,
    nullable: true,
  })
  status: string

  /**<PERSON><PERSON> tả hành động đã thực hiện */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({ type: 'varchar', length: 36, nullable: true })
  planSiteAssessmentId?: string

  @Column({ type: 'varchar', length: 36, nullable: true })
  siteAssessmentId?: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  targetId: string

  @Column({
    length: 50,
    nullable: true,
  })
  entityName: string

  /**<PERSON><PERSON><PERSON> phân quyền */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  jsonOld: string

  /**JSO<PERSON> phân quyền dữ liệu*/
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  jsonNew: string
}
