import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeColum1750611602300 implements MigrationInterface {
    name = 'ChangeColum1750611602300'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" DROP CONSTRAINT "FK_e696b97dd40a910d96803b8e3f4"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP CONSTRAINT "FK_4dd78943e2825467aded133ffe4"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" DROP COLUMN "roundUpContPrId"`);
        await queryRunner.query(`ALTER TABLE "pr" DROP COLUMN "roundUpContId"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD "prId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" ADD CONSTRAINT "FK_f70d50a89439a8f048193b59571" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP CONSTRAINT "FK_f70d50a89439a8f048193b59571"`);
        await queryRunner.query(`ALTER TABLE "round_up_cont" DROP COLUMN "prId"`);
        await queryRunner.query(`ALTER TABLE "pr" ADD "roundUpContId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" ADD "roundUpContPrId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "pr" ADD CONSTRAINT "FK_4dd78943e2825467aded133ffe4" FOREIGN KEY ("roundUpContId") REFERENCES "round_up_cont"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "round_up_cont_pr_item" ADD CONSTRAINT "FK_e696b97dd40a910d96803b8e3f4" FOREIGN KEY ("roundUpContPrId") REFERENCES "round_up_cont_pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
