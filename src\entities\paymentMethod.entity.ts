import { Column, Entity, OneToMany } from 'typeorm'
import {
  BusinessPlanEntity,
  ContractAppendixPaymentProgressEntity,
  PaymentProgressEntity,
  POEntity,
  SupplierNumberRequestApproveEntity,
  SupplierServiceEntity,
} from '.'
import { BaseEntity } from './base.entity'
import { ContractInspectionPaymentProgressEntity } from './contractInspectionPaymentProgress.entity'
import { RecommendedPurchaseEntity } from './recommendedPurchase.entity'
import { RoleFiSupplierEntity } from './roleFiSupplier.entity'

@Entity('payment_method')
export class PaymentMethodEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.paymentMethod)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>

  @OneToMany(() => PaymentProgressEntity, (p) => p.paymentMethod)
  paymentPlans: Promise<PaymentProgressEntity[]>

  @OneToMany(() => ContractInspectionPaymentProgressEntity, (p) => p.paymentMethod)
  contractInspectionPaymentProgresses: Promise<ContractInspectionPaymentProgressEntity[]>

  @OneToMany(() => BusinessPlanEntity, (p) => p.paymentMethod)
  businessPlan: Promise<BusinessPlanEntity[]>

  @OneToMany(() => RecommendedPurchaseEntity, (p) => p.paymentMethod)
  recommendedPurchase: Promise<RecommendedPurchaseEntity[]>

  @OneToMany(() => RoleFiSupplierEntity, (p) => p.paymentMethod)
  roleFiSuppliers: Promise<RoleFiSupplierEntity[]>

  @OneToMany(() => SupplierServiceEntity, (p) => p.paymentMethod)
  supplierServices: Promise<SupplierServiceEntity[]>

  @OneToMany(() => ContractAppendixPaymentProgressEntity, (p) => p.paymentMethod)
  contractAppendixPaymentProgressItems: Promise<ContractAppendixPaymentProgressEntity[]>

  @OneToMany(() => POEntity, (p) => p.paymentMethod)
  pos: Promise<POEntity[]>
}
