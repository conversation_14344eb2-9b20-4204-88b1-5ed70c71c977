import { BaseEntity } from './base.entity'
import { Column, Entity } from 'typeorm'

/**<PERSON><PERSON><PERSON> phân quyền vị trí của cây */
@Entity('organizational_position')
export class OrganizationalPositionEntity extends BaseEntity {
  /** ID vị trí trong cauay sơ đồ tổ chức(<PERSON><PERSON><PERSON> lấy relation)*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  organizationalPositionId: string

  /**JSO<PERSON> phân quyền */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  roleStringify: string

  /**JSO<PERSON> phân quyền dữ liệu*/
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  roleDataStringify: string
}
