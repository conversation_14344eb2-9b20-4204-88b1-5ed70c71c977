import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { JwtModule, JwtModuleOptions } from '@nestjs/jwt'
import { AuthService } from './auth.service'
import { AuthController } from './auth.controller'
import {
  EmployeeRepository,
  OrganizationalTreeRepository,
  PermissionRepository,
  SettingRoleRepository,
  SupplierRepository,
  UserRepository,
} from '../../repositories'
import { PassportModule } from '@nestjs/passport'
import { TypeOrmExModule } from '../../typeorm'
import { EmailModule } from '../email/email.module'
import { JwtStrategy } from './jwt.strategy'
import { OrganizationalTreeModule } from '../organizationalTree/organizationalTree.module'
import { OrganizationalPositionRepository } from '../../repositories/organizationalPosition.repository'
import { PermissionGroupRepository } from '../../repositories/pemissionGroup.repository'
import { PermissionViewModule } from '../perrmissionView/permissionView.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      UserRepository,
      SupplierRepository,
      EmployeeRepository,
      SettingRoleRepository,
      PermissionGroupRepository,
      PermissionRepository,
      OrganizationalTreeRepository,
    ]),
    PassportModule.register({ session: true }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService): Promise<JwtModuleOptions> => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: configService.get<string>('JWT_EXPIRY') },
      }),
    }),
    EmailModule,
    PermissionViewModule,
    OrganizationalTreeModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule {}
