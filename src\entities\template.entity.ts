import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'

@Entity('template')
export class TemplateEntity extends BaseEntity {
  @Column({
    nullable: false,
  })
  name: string

  @Column({
    nullable: false,
  })
  link: string

  @Column({
    nullable: false,
  })
  type: string
}
