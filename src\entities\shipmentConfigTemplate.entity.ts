import { Column, Entity, OneToMany } from 'typeorm'
import { ShipmentConfigTemplateDetailEntity } from './shipmentConfigTemplateDetail.entity'
import { BaseEntity } from './base.entity'

@Entity('shipment_config_template')
export class ShipmentConfigTemplateEntity extends BaseEntity {
  /* Tên mẫu cấu hình */
  @Column({
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  shipmentFeeConditionTypeCompactId: string
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  shipmentFeeConditionTypeCompactCode: string
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  shipmentFeeConditionTypeCompactValue: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
    transformer: {
      to(value) {
        return JSON.stringify(value)
      },
      from(value) {
        return value ? JSON.parse(value) : {}
      },
    },
  })
  configTable: any

  /* Mô tả mẫu cấu hình */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  /* Trạng thái của mẫu cấu hình */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @OneToMany(() => ShipmentConfigTemplateDetailEntity, (p) => p.shipmentConfigTemplate)
  shipmentConfigTemplateDetails: Promise<ShipmentConfigTemplateDetailEntity[]>
}
