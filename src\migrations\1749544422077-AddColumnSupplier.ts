import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnSupplier1749544422077 implements MigrationInterface {
  name = 'AddColumnSupplier1749544422077'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier" ADD "reasonRequestReCheck" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "reasonRequestReCheck"`)
  }
}
