import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdatePermission1749806395881 implements MigrationInterface {
  name = 'UpdatePermission1749806395881'

  public async up(queryRunner: QueryRunner): Promise<void> {
    const columnExists = await queryRunner.hasColumn('site_assessment', 'count')

    if (!columnExists) {
      await queryRunner.query('ALTER TABLE "site_assessment" ADD "count" bigint')
    }
    await queryRunner.query(`ALTER TABLE "group_permission" ADD "purchasingOrgIdTemp" varchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "group_permission" DROP COLUMN "purchasingOrgIdTemp"`)
    await queryRunner.query('ALTER TABLE "site_assessment" DROP COLUMN "count"')
  }
}
