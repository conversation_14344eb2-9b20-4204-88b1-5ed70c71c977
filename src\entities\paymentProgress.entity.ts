import { <PERSON>umn, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { enumData } from '../constants'
import { BaseEntity } from './base.entity'
import { ContractEntity } from './contract.entity'
import { POEntity } from './po.entity'
import { PaymentMethodEntity } from './paymentMethod.entity'

/** Tiến độ thanh toán HĐ hoặc PO không theo HĐ */
@Entity({ name: 'payment_progress' })
export class PaymentProgressEntity extends BaseEntity {
  /** PO không theo HĐ */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.paymentPlan)
  @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  po: Promise<POEntity>

  /** HĐ */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.paymentPlan)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  /** Tên tiến độ */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** % tiến độ */
  @Column({
    default: 0,
  })
  percent: number

  /** Thời gian */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  time: Date

  /** Ghi chú */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  description: string

  /** Số tiền cần thanh toán của tiến độ */
  @Column({
    nullable: true,
    type: 'float',
    default: 0,
  })
  money: number

  /** Số tiền đã ĐNTT của tiến độ */
  @Column({
    type: 'bigint',
    default: 0,
  })
  suggestPaid: number

  /** Trạng thái ĐNTT, enum PaymentProgressStatus */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    default: enumData.PaymentProgressStatus.Unpaid.code,
  })
  paymentStatus: string

  /** Lịch sử ghi nhận */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  historyNote: string

  /** Danh sách PO của Tiến độ thanh toán theo HĐ */
  @OneToMany(() => POEntity, (p) => p.paymentPlan)
  pos: Promise<POEntity[]>

  /** Phương thức thanh toán */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentMethodId: string
  @ManyToOne(() => PaymentMethodEntity, (p) => p.paymentPlans)
  @JoinColumn({ name: 'paymentMethodId', referencedColumnName: 'id' })
  paymentMethod: Promise<PaymentMethodEntity>

  /** Chứng từ yêu cầu */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  requiredDocument: string
}
