import { ShipmentCostEntity, ShipmentEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'

/** Thông tin shipment cost type*/
@Entity('shipment_cost_type')
export class ShipmentCostTypeEntity extends BaseEntity {
  /** Mã */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Tên */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @OneToMany(() => ShipmentCostEntity, (p) => p.shipmentCostType)
  shipmentCosts: Promise<ShipmentCostEntity[]>

  @OneToMany(() => ShipmentEntity, (p) => p.shipmentCostType)
  shipments: Promise<ShipmentEntity[]>
}
