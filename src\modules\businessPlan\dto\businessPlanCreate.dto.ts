import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class BussinessPlanCreateDto {
  name: string

  @ApiProperty()
  description: string

  @ApiProperty({ description: 'businessPlanTemplateId Template phương án kinh doanh' })
  @IsNotEmpty()
  @IsString()
  businessPlanTemplateId: string

  @ApiProperty({ description: 'companyId' })
  @IsNotEmpty()
  @IsString()
  companyId: string

  @ApiProperty({ description: 'plantId' })
  plantId: string

  @ApiProperty({ description: 'currencyFromId ' })
  currencyFromId: string

  @ApiProperty({ description: 'currencyToId ' })
  currencyToId: string

  @ApiProperty({ description: '' })
  exchangeRate: number

  @ApiProperty({ description: 'Phương thức thanh toá ' })
  paymentMethodId: string

  @ApiProperty({ description: 'Điều kiện giao hàng ' })
  incotermId: string

  @ApiProperty({ description: 'Số lượng tròn cont' })
  numberCont: number

  @ApiProperty({ description: 'địa điểm giao hàng' })
  deliveryAddress: string

  @ApiProperty({ description: 'Thời gian dự kiến về kho' })
  estimatedTime: Date

  @ApiProperty({ description: 'Nguồn mua' })
  purchasingSource: string

  @ApiProperty({ description: 'Nguồn bán' })
  sellingSource: string

  @ApiProperty({ description: 'Id hợp đồng' })
  contractId: string

  @ApiProperty({ description: '  Danh sách PR ID' })
  lstPr: string[]

  lstColValue: BussinessPlanColValueCreateDto[]

  @ApiProperty({ description: 'Danh sách cột' })
  lstCol: any[]

  status: string

  incotermDescription: string

  intermediarySupplier: string

  @ApiProperty({ description: 'sloc' })
  sloc: string

  @ApiProperty({ description: 'lstExternalMaterialGroup' })
  lstExternalMaterialGroup: string[]

  fileList: FileMediaDto[]

  lstBusinessPlanTemplateSettingCol: any[]

  externalMaterialGroupId: string
}

export class FileMediaDto {
  @ApiProperty({ description: 'url file' })
  fileUrl: string

  @ApiProperty({ description: 'Tên file' })
  fileName: string
}

export class BussinessPlanColValueCreateDto {
  @ApiProperty({ description: 'Giá trị value tính' })
  value: string

  @ApiProperty({ description: 'Id PR' })
  prId: string

  @ApiProperty({ description: 'Id Material' })
  materialId: string

  @ApiProperty({ description: 'Id Tempalte' })
  businessPlanTemplateId: string

  @ApiProperty({ description: 'Id Tempalte Col' })
  businessPlanTemplateColId: string

  quantity: number

  lstGroup: any
}
