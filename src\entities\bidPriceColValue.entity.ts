import { BaseEntity } from './base.entity'
import { Enti<PERSON>, Column, ManyToOne, JoinC<PERSON>umn } from 'typeorm'
import { BidPriceEntity } from './bidPrice.entity'
import { BidPriceColEntity } from './bidPriceCol.entity'

@Entity('bid_price_col_value')
export class BidPriceColValueEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidPriceColValue)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidPriceColId: string
  @ManyToOne(() => BidPriceColEntity, (p) => p.bidPriceColValue)
  @JoinColumn({ name: 'bidPriceColId', referencedColumnName: 'id' })
  bidPriceCol: Promise<BidPriceColEntity>
}
