import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeConfigTable1752632007922 implements MigrationInterface {
  name = 'ChangeConfigTable1752632007922'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan_type" ADD "configTable" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan_type" DROP COLUMN "configTable"`)
  }
}
