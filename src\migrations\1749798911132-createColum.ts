import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateColum1749798911132 implements MigrationInterface {
  name = 'CreateColum1749798911132'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "material" ADD "priceControl" nvarchar(50)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "material" DROP COLUMN "priceControl"`)
  }
}
