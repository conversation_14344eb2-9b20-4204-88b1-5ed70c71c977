import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm'
import { BidPriceEntity } from './bidPrice.entity'
import { BidAuctionSupplierEntity } from './bidAuctionSupplier.entity'

@Entity('bid_auction_supplier_price_value')
export class BidAuctionSupplierPriceValueEntity extends BaseEntity {
  @Column({
    type: 'float',
    nullable: true,
  })
  score: number

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidAuctionSupplierId: string
  @ManyToOne(() => BidAuctionSupplierEntity, (p) => p.bidAuctionSupplierPriceValue)
  @JoinColumn({ name: 'bidAuctionSupplierId', referencedColumnName: 'id' })
  bidAuctionSupplier: BidAuctionSupplierEntity

  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidPriceId: string
  @ManyToOne(() => BidPriceEntity, (p) => p.bidAuctionSupplierPriceValue)
  @JoinColumn({ name: 'bidPriceId', referencedColumnName: 'id' })
  bidPrice: Promise<BidPriceEntity>
}
