import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { RfqEntity } from './rfq.entity'
import { BusinessPlanEntity } from './businessPlan.entity'

@Entity('business_plan_rfq')
export class BusinessPlanRfqEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessPlanId: string
  @ManyToOne(() => BusinessPlanEntity, (p) => p.businessPlanRfq)
  @JoinColumn({ name: 'businessPlanId', referencedColumnName: 'id' })
  businessPlan: Promise<BusinessPlanEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  rfqId: string
  @ManyToOne(() => RfqEntity, (p) => p.businessPlanRfq)
  @JoinColumn({ name: 'rfqId', referencedColumnName: 'id' })
  rfq: Promise<RfqEntity>
}
