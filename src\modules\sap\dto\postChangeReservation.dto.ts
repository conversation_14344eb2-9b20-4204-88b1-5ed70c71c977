import { IsString, IsO<PERSON>al, IsArray, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'

export class ReservationDetailDto {
  @IsString()
  item: string

  @IsString()
  @IsOptional()
  item_delete?: string

  @IsString()
  @IsOptional()
  item_complete?: string
}

export class CancelReservationDto {
  @IsString()
  company: string

  @IsString()
  reservation: string

  @IsString()
  @IsOptional()
  delete_header?: string

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReservationDetailDto)
  detail: ReservationDetailDto[]
}
