import { Module } from '@nestjs/common'
import { BidRateController } from './bidRate.controller'
import { BidRateService } from './bidRate.service'
import { EmailModule } from '../../email/email.module'
import {
  BidRepository,
  BidTechRepository,
  BidPriceRepository,
  BidTradeRepository,
  BidEmployeeAccessRepository,
  BidSupplierRepository,
  BidDealRepository,
  BidPriceColRepository,
  BidPrItemRepository,
  BidEmployeeRateRepository,
  PermissionApproveRepository,
  RateDataRepository,
  BidAuctionRepository,
  ServiceRepository,
  BidExMatGroupRepository,
  BidSupplierItemRepository,
  BidDealSupplierRepository,
} from '../../../repositories'
import { TypeOrmExModule } from '../../../typeorm'
import { BidRateService2 } from './bidRate2.service'
import { FlowApproveModule } from '../../flowApprove/flowApprove.module'
import { OrganizationalPositionModule } from '../../organizationalPosition/organizationalPosition.module'
import { RfqDetailsRepository, RfqRepository } from '../../../repositories/rfq.repository'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BidEmployeeAccessRepository,
      BidRepository,
      BidAuctionRepository,
      BidPrItemRepository,
      BidDealRepository,
      BidDealSupplierRepository,
      RfqRepository,
      BidEmployeeRateRepository,
      RfqDetailsRepository,
      BidTechRepository,
      BidTradeRepository,
      BidPriceRepository,
      ServiceRepository,
      BidExMatGroupRepository,
      BidPriceColRepository,
      BidPrItemRepository,
      RateDataRepository,
      BidSupplierRepository,
      PermissionApproveRepository,
      BidSupplierItemRepository,
    ]),
    EmailModule,
    FlowApproveModule,
    OrganizationalPositionModule,
  ],
  controllers: [BidRateController],
  providers: [BidRateService2, BidRateService],
  exports: [BidRateService2, BidRateService],
})
export class BidRateModule {}
