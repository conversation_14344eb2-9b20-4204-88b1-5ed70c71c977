import { ExtractJwt, Strategy } from 'passport-jwt'
import { PassportStrategy } from '@nestjs/passport'
import { Injectable, UnauthorizedException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { UserDto } from '../../dto'
import { EmployeeRepository, OrganizationalTreeRepository, UserRepository } from '../../repositories'
import { enumData } from 'src/constants'
import { coreHelper } from '../../helpers'

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly repo: UserRepository,
    public readonly configService: ConfigService,
    public readonly employeeRepo: EmployeeRepository,
    public readonly organizationalTreeRepo: OrganizationalTreeRepository,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    })
  }

  async validate(payload: { uid: string }): Promise<UserDto> {
    const user: any = await this.repo.findOne({
      where: { id: payload.uid },
      select: {
        username: true,
        id: true,
        type: true,
        employeeId: true,
        createdAt: true,
        updatedAt: true,
        companyId: true,
        supplierId: true,
        roleUpdated: true,
        queryParams: true,
        currentCompanyId: true,
      },
      relations: { employee: true },
    })
    if (!user) throw new UnauthorizedException('Không có quyền truy cập!')
    const employee: any = await this.employeeRepo.findOne({ where: { id: user.employeeId }, relations: { employeeRole: true } })
    user.employeePosition = []
    const positionInOrg = await this.organizationalTreeRepo.findOne({
      where: { targetId: employee.id, isDeleted: false },
    })
    if (employee && employee.position) user.employeePosition.push(employee.position)
    //#region "Employee Role"
    const dicRole: any = {}
    let dataReturn: any = {}
    const lstRoleSetting = employee?.__employeeRole__ || []
    for (const role of lstRoleSetting) {
      dicRole[role.roleCode] = role
    }

    //sinh ra danh sách quyền hiện tại
    const lstRole = coreHelper.convertObjToArray(enumData.RoleType)
    for (const item of lstRole) {
      dataReturn[item.code] = {}
      for (const dataCol of item.dbCol) {
        if (dicRole[item.code]) {
          const data = dicRole[item.code]
          const dataArr = data[dataCol]
          if (dataArr) dataReturn[item.code][dataCol] = dataArr.split(',')
        }
      }
    }
    //#endregion "Employee Role"
    return {
      id: user.id,
      username: user.username,
      rolesAction: dataReturn,
      employeePosition: user.employeePosition,
      employeeId: user?.employeeId,
      companyCode: user?.companyId,
      supplierId: user?.supplierId,
      companyId: user?.employee?.companyId,
      orgCompanyId: user?.employee?.orgCompanyId,
      purchasingOrgId: user?.employee?.purchasingOrgId,
      purchasingGroupId: user?.employee?.purchasingGroupId,
      departmentId: employee?.departmentId,
      orgDepartmentId: user?.employee?.orgDepartmentId,
      orgPositionId: user?.employee?.orgPositionId,
      employeeOrgPosition: positionInOrg?.id,
      type: user.type,
      roleUpdated: user.roleUpdated,
      roleViewUpdated: user.roleViewUpdated,
      viewPermission: user.viewPermission,
      queryParams: user.queryParams,
    }
  }
}
