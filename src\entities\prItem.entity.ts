import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, Index } from 'typeorm'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { ItemTechEntity } from './itemTech.entity'
import { PrEntity } from './pr.entity'
import { ServiceEntity } from './service.entity'
import { MaterialEntity } from './material.entity'
import { PlantEntity } from './plant.entity'
import { PurchasingGroupEntity } from './purchasingGroup.entity'
import { MaterialGroupEntity } from './materialGroup.entity'
import { GLAccountEntity } from './glAccount.entity'
import { UomEntity } from './uom.entity'
import { PrItemCompomentEntity } from './prItemComponent.entity'
import { BidPrItemEntity } from './bidPrItem.entity'
import { MediaFileEntity } from './mediaFile.entity'
import { AuctionSupplierPriceEntity } from './auctionSupPrice.entity'
import { OfferServiceEntity } from './offerService.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { PurchasingOrgEntity } from './purchasingOrg.entity'
import { BudgetReceiptItemEntity } from './budgetReceiptItem.entity'
import { PrItemChildEntity } from './prItemChild.entity'
import { ProductHierarchyEntity } from './productHierarchy.entity'
import { RequestQuoteDetailEntity } from './requestQuoteDetail.entity'

/** Danh mục Item của YCMH */
@Index('IX_PR_ITEMS_MAT', ['prId', 'materialId']) // <-- Index đúng như SQL
@Entity('pr_item')
export class PrItemEntity extends BaseEntity {
  /** Item Line */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  itemNo: string

  /** Category */
  @Column({
    type: 'varchar',
    nullable: true,
    length: 1,
  })
  category: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.prItems)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** tên hàng */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  shortText: string

  /** số lượng cần đặt hàng */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  /** Công ty */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.prItems)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /** PLANT CODE */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  plantCode: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  unitCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.prItems)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  /** Đơn vị tính mua hàng */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  ounCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  ounId: string
  @ManyToOne(() => UomEntity, (p) => p.prItem)
  @JoinColumn({ name: 'ounId', referencedColumnName: 'id' })
  oun: Promise<UomEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string

  @ManyToOne(() => MaterialGroupEntity, (p) => p.bidItems)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>

  /** Nhóm vật tư */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  materialGroupCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupId: string

  @ManyToOne(() => PurchasingGroupEntity, (p) => p.prItems)
  @JoinColumn({ name: 'purchasingGroupId', referencedColumnName: 'id' })
  purchasingGroup: Promise<PurchasingGroupEntity>

  /** valuation Type */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  valuationType: string

  /** G/L Account */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  glAccountCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  glAccountId: string
  @ManyToOne(() => GLAccountEntity, (p) => p.prItems)
  @JoinColumn({ name: 'glAccountId', referencedColumnName: 'id' })
  glAccount: Promise<GLAccountEntity>

  /** Cost center */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  costCenterCode: string

  /** Order */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  orderCode: string

  /** Valuation Price */
  @Column({
    nullable: true,
    type: 'bigint',
  })
  valuationPrice: number

  /** asset */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetCode: string

  /** asset Desc */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetDesc: string

  /** Sub No Aset */
  @Column({
    type: 'varchar',
    length: 3,
    nullable: true,
  })
  subNoAset: string

  /** Tracking Number */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  trackingNumber: string

  /** item_note */
  @Column({
    type: 'nvarchar',
    nullable: true,
  })
  itemNote: string

  /** item text */
  @Column({
    length: 'max',
    nullable: true,
  })
  itemText: string

  /** delivery text */
  @Column({
    length: 'max',
    nullable: true,
  })
  deliveryText: string

  /** Ghi chú về Đơn đặt hàng  */
  @Column({
    length: 'max',
    nullable: true,
  })
  materialPoText: string

  /** Batch */
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  batch: string

  @Column({
    nullable: true,
    type: 'datetime',
  })
  receivedTime: Date

  /** Ngày nhận */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  receivedDate: Date

  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  /** Item Closed */
  @Column({
    type: 'varchar',
    length: 1,
    nullable: true,
  })
  itemClosed: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.prItems)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId: string
  @ManyToOne(() => ServiceEntity, (p) => p.prItems)
  @JoinColumn({ name: 'serviceId', referencedColumnName: 'id' })
  service: Promise<ServiceEntity>

  /** Số lượng đã tạo thầu = tổng quantityItem Các gói thầu Chi tiết tạo từ prItem này */
  @Column({
    nullable: true,
    default: 0,
  })
  quantityBid: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  prStatus: string

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  approveStatus: string

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  blockStatus: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prParentId: string

  /** Số lượng của Item trong gói thầu chi tiết */
  @Column({
    nullable: true,
    default: 0,
  })
  quantityItem: number

  /** Trạng thái ngân sách của line */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  budgetStatus: string

  /** Ngân sách */
  @Column({
    nullable: true,
    type: 'bigint',
  })
  budget: number

  /** Min Delivery Date */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  minDeliveryDate: Date

  /** Max Delivery Date */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  maxDeliveryDate: Date

  /** 1 item có nhiều yêu cầu kỹ thuật */
  @OneToMany(() => ItemTechEntity, (p) => p.prItem)
  itemTechs: Promise<ItemTechEntity[]>

  @OneToMany(() => AuctionSupplierPriceEntity, (p) => p.prItem)
  auctionSupplierPrice: Promise<AuctionSupplierPriceEntity[]>

  /** Các gói thầu Chi tiết trong gói thầu tổng của Pr */
  @OneToMany(() => BidEntity, (p) => p.prItem)
  bids: Promise<BidEntity[]>

  /** Các gói thầu Chi tiết trong gói thầu tổng của Pr */
  @OneToMany(() => BidPrItemEntity, (p) => p.prItem)
  bidPrItem: Promise<BidPrItemEntity[]>

  @OneToMany(() => OfferServiceEntity, (p) => p.prItem)
  offerService: Promise<OfferServiceEntity[]>

  @OneToMany(() => PrItemCompomentEntity, (p) => p.prItem)
  prItemCompoments: Promise<PrItemCompomentEntity[]>

  @OneToMany(() => MediaFileEntity, (p) => p.prItem)
  mediaFiles: Promise<MediaFileEntity[]>

  /** số lượng phân bổ*/
  @Column({
    nullable: true,
    default: 0,
    type: 'bigint',
  })
  quantityAllocation: number

  /** số lượng còn lại */
  @Column({
    nullable: true,
    default: 0,
    type: 'bigint',
  })
  quantityRemaining: number

  /** fundCenter */
  @Column({ type: 'varchar', length: 1000, nullable: true })
  fundCenter: string

  /** fund program */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  fundProgram: string

  /** Lock PR không chỉnh sửa thì biến này = true */
  @Column({
    nullable: true,
    default: false,
  })
  isLockAllocation: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.prItem)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  /** io */
  @Column({
    length: 'max',
    nullable: true,
  })
  io: string

  /** io Name */
  @Column({
    length: 'max',
    nullable: true,
  })
  ioName: string

  /** iotype */
  @Column({
    length: 'max',
    nullable: true,
  })
  iotype: string

  /** io Name */
  @Column({
    length: 'max',
    nullable: true,
  })
  relstatus: string

  @Column({
    nullable: true,
    type: 'datetime',
  })
  requestDate: Date

  /** price Unit */
  @Column({
    nullable: true,
    type: 'bigint',
  })
  priceUnit: number

  /** io Name */
  @Column({
    length: 'max',
    nullable: true,
  })
  soItem: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingGroupCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgId: string

  @ManyToOne(() => PurchasingOrgEntity, (p) => p.prItems)
  @JoinColumn({ name: 'purchasingOrgId', referencedColumnName: 'id' })
  purchasingOrg: Promise<PurchasingOrgEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  purchasingOrgCode: string

  /** Người yêu cầu */
  @Column({
    length: 'max',
    nullable: true,
  })
  requistioner: string

  /** Dòng là Make to order / Make to sale */
  @Column({
    length: 'max',
    nullable: true,
  })
  prItemType: string

  @Column({
    length: 'max',
    nullable: true,
  })
  fp: string

  @Column({
    length: 'max',
    nullable: true,
  })
  fc: string

  @Column({
    length: 'max',
    nullable: true,
  })
  ci: string

  @Column({
    length: 'max',
    nullable: true,
  })
  ciname: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  budgetPeriod: string

  @OneToMany(() => BudgetReceiptItemEntity, (p) => p.prItem)
  budgetReceiptItems: Promise<BudgetReceiptItemEntity[]>

  @Column({
    nullable: true,
    type: 'bigint',
  })
  total: number

  @OneToMany(() => PrItemChildEntity, (p) => p.prItem)
  prItemChilds: Promise<PrItemChildEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  fund: string

  /** Product Hierarch */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  productHierarchyId: string

  @ManyToOne(() => ProductHierarchyEntity, (p) => p.prItems)
  @JoinColumn({ name: 'productHierarchyId', referencedColumnName: 'id' })
  productHierarchy: Promise<ProductHierarchyEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  fundGroup: string

  /** Số lượng dang dỡ */
  @Column({
    nullable: true,
    default: 0,
  })
  unloadedQuantity: number

  /** Số lượng đã lên PO */
  @Column({
    nullable: true,
    default: 0,
  })
  quantityPO: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  baseUnitId: string
  @ManyToOne(() => UomEntity, (p) => p.prItemBaseUnits)
  @JoinColumn({ name: 'baseUnitId', referencedColumnName: 'id' })
  baseUnit: Promise<UomEntity>

  /** Số lượng trước đó */
  @Column({
    nullable: true,
  })
  quantityBefore: number

  /** Ngân sách */
  @Column({
    nullable: true,
    type: 'bigint',
  })
  budgetBefore: number

  /** Lượng làm tròn Cont */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 4, default: 0 })
  roundedAmountCont: number

  /** Số lượng hỏi hàng NCC */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 4, default: 0 })
  supplierInquiries: number

  /** Số lượng tổng */
  @Column({ nullable: true, type: 'decimal', precision: 20, scale: 4, default: 0 })
  totalQuantity: number

  /** acc */
  @Column({
    type: 'varchar',
    nullable: true,
    length: 1,
  })
  acccate: string

  @OneToMany(() => RequestQuoteDetailEntity, (p) => p.prItem)
  requestQuoteDetails: Promise<RequestQuoteDetailEntity[]>
}
