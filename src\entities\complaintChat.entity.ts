import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { EmployeeEntity } from './employee.entity'
import { ComplaintEntity } from './complaint.entity'
import { SupplierEntity } from './supplier.entity'
import { ComplaintNotifyEntity } from './complaintNotify.entity'

/** Khiếu nại chat */
@Entity('complaint_chat')
export class ComplaintChatEntity extends BaseEntity {
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  content: string

  /** File đính kèm */
  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  attachedFile: string

  /** Người gửi */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  senderId: string
  @ManyToOne(() => EmployeeEntity, (p) => p.complaintChatSenders)
  @JoinColumn({ name: 'senderId', referencedColumnName: 'id' })
  employeeSender: Promise<EmployeeEntity>

  /** Người nhận */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  receiverId: string
  @ManyToOne(() => SupplierEntity, (p) => p.complaintChatReceivers)
  @JoinColumn({ name: 'receiverId', referencedColumnName: 'id' })
  receiver: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  complaintId: string
  @ManyToOne(() => ComplaintEntity, (p) => p.complaintChats)
  @JoinColumn({ name: 'complaintId', referencedColumnName: 'id' })
  complaint: Promise<ComplaintEntity>

  /** Thông báo khiếu nại */
  @OneToMany(() => ComplaintNotifyEntity, (p) => p.complaintChat)
  complaintNotifys: Promise<ComplaintNotifyEntity[]>
}
