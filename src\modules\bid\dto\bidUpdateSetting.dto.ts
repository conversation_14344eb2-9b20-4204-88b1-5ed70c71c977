import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsBoolean } from 'class-validator'
export class BidUpdateSettingDto {
  /** ID gói thầu */
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string

  /** C<PERSON> gửi thông báo mởi thầu Doanh nghiệp qua email không */
  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isSendEmailInviteBid: boolean

  /** Tự động chọn NCC thắng thầu và kết thúc thầu */
  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isAutoBid: boolean

  /** Tỉ lệ phần trăm kỹ thuật */
  @ApiPropertyOptional()
  percentTech: number

  /** Tỉ lệ phần trăm ĐKTM */
  @ApiPropertyOptional()
  percentTrade: number

  /** Tỉ lệ phần trăm giá */
  @ApiPropertyOptional()
  percentPrice: number

  /** Ng<PERSON>y hết hạn xác nhận tham gia đấu thầu */
  @ApiProperty()
  @IsNotEmpty()
  acceptEndDate: Date

  /** Ngày hết hạn nộp hồ sơ thầu */
  @ApiProperty()
  @IsNotEmpty()
  submitEndDate: Date

  /** Thời điểm mở thầu */
  @ApiProperty()
  @IsNotEmpty()
  startBidDate: Date

  /** Thời hạn thiết lập yêu cầu kỹ thuật, năng lực */
  @ApiProperty()
  @IsNotEmpty()
  timeTechDate: Date

  /** Thời hạn thiết lập các hạng mục báo giá, cơ cấu giá và điều kiện thương mại */
  @ApiProperty()
  @IsNotEmpty()
  timePriceDate: Date

  /** Thời hạn đánh giá yêu cầu kỹ thuật, năng lực */
  @ApiProperty()
  @IsNotEmpty()
  timeCheckTechDate: Date

  /** Thời hạn đánh giá các hạng mục báo giá, cơ cấu giá và điều kiện thương mại */
  @ApiProperty()
  @IsNotEmpty()
  timeCheckPriceDate: Date
}
